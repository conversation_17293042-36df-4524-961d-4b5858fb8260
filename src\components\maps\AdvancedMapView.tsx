"use client";

import React, { useState, useEffect, useRef } from 'react';
import AdvancedMapsService, { Location, DeliveryAgent, HeatmapData } from '@/services/advancedMapsService';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  MapPin,
  Navigation,
  Users,
  TrendingUp,
  Clock,
  Route,
  Layers,
  RefreshCw,
  Zap,
  Target
} from 'lucide-react';
import { toast } from 'sonner';

interface AdvancedMapViewProps {
  center?: Location;
  zoom?: number;
  showAgents?: boolean;
  showHeatmap?: boolean;
  showRoutes?: boolean;
  onLocationSelect?: (location: Location) => void;
  className?: string;
}

export default function AdvancedMapView({
  center = { latitude: 24.7136, longitude: 46.6753 }, // الرياض
  zoom = 12,
  showAgents = true,
  showHeatmap = false,
  showRoutes = false,
  onLocationSelect,
  className
}: AdvancedMapViewProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [agents, setAgents] = useState<DeliveryAgent[]>([]);
  const [heatmapData, setHeatmapData] = useState<HeatmapData[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<DeliveryAgent | null>(null);
  const [mapMode, setMapMode] = useState<'normal' | 'heatmap' | 'routes'>('normal');
  const [loading, setLoading] = useState(false);

  // مراجع للطبقات
  const agentMarkersRef = useRef<google.maps.Marker[]>([]);
  const heatmapLayerRef = useRef<google.maps.visualization.HeatmapLayer | null>(null);
  const routeRenderersRef = useRef<google.maps.DirectionsRenderer[]>([]);

  useEffect(() => {
    initializeMap();
    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    if (map) {
      updateMapView();
    }
  }, [mapMode, showAgents, showHeatmap, showRoutes]);

  const initializeMap = async () => {
    if (!mapRef.current) return;

    try {
      // تحميل Google Maps API
      if (!window.google) {
        await loadGoogleMapsAPI();
      }

      const mapOptions: google.maps.MapOptions = {
        center: { lat: center.latitude, lng: center.longitude },
        zoom,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ],
        streetViewControl: false,
        mapTypeControl: true,
        fullscreenControl: true,
        zoomControl: true
      };

      const newMap = new google.maps.Map(mapRef.current, mapOptions);
      setMap(newMap);

      // إضافة مستمع للنقر على الخريطة
      newMap.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (event.latLng && onLocationSelect) {
          const location: Location = {
            latitude: event.latLng.lat(),
            longitude: event.latLng.lng()
          };
          onLocationSelect(location);
        }
      });

      // تحميل البيانات الأولية
      await loadInitialData();

    } catch (error) {
      console.error('خطأ في تهيئة الخريطة:', error);
      toast.error('فشل في تحميل الخريطة');
    }
  };

  const loadGoogleMapsAPI = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.google) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=visualization`;
      script.async = true;
      script.defer = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('فشل في تحميل Google Maps API'));
      document.head.appendChild(script);
    });
  };

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // تحميل المندوبين المتاحين
      if (showAgents) {
        await loadAvailableAgents();
      }

      // تحميل بيانات الخريطة الحرارية
      if (showHeatmap) {
        await loadHeatmapData();
      }

    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableAgents = async () => {
    try {
      // في التطبيق الحقيقي، استدعي API للحصول على المندوبين
      const mockAgents: DeliveryAgent[] = [
        {
          id: '1',
          name: 'أحمد محمد',
          phone: '+966501234567',
          currentLocation: { latitude: 24.7136, longitude: 46.6753 },
          isOnline: true,
          isAvailable: true,
          vehicleType: 'motorcycle',
          rating: 4.8,
          completedDeliveries: 156
        },
        {
          id: '2',
          name: 'سارة أحمد',
          phone: '+966507654321',
          currentLocation: { latitude: 24.7236, longitude: 46.6853 },
          isOnline: true,
          isAvailable: false,
          vehicleType: 'car',
          rating: 4.9,
          completedDeliveries: 203
        },
        {
          id: '3',
          name: 'محمد علي',
          phone: '+966509876543',
          currentLocation: { latitude: 24.7036, longitude: 46.6653 },
          isOnline: true,
          isAvailable: true,
          vehicleType: 'bicycle',
          rating: 4.7,
          completedDeliveries: 89
        }
      ];

      setAgents(mockAgents);
    } catch (error) {
      console.error('خطأ في تحميل المندوبين:', error);
    }
  };

  const loadHeatmapData = async () => {
    try {
      const timeRange = {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // آخر 7 أيام
        end: new Date()
      };

      const data = await AdvancedMapsService.generateOrdersHeatmap(timeRange);
      setHeatmapData(data);
    } catch (error) {
      console.error('خطأ في تحميل بيانات الخريطة الحرارية:', error);
    }
  };

  const updateMapView = () => {
    if (!map) return;

    // مسح الطبقات الحالية
    clearMapLayers();

    switch (mapMode) {
      case 'normal':
        if (showAgents) renderAgentMarkers();
        break;
      case 'heatmap':
        renderHeatmapLayer();
        break;
      case 'routes':
        renderDeliveryRoutes();
        break;
    }
  };

  const clearMapLayers = () => {
    // مسح علامات المندوبين
    agentMarkersRef.current.forEach(marker => marker.setMap(null));
    agentMarkersRef.current = [];

    // مسح طبقة الخريطة الحرارية
    if (heatmapLayerRef.current) {
      heatmapLayerRef.current.setMap(null);
      heatmapLayerRef.current = null;
    }

    // مسح المسارات
    routeRenderersRef.current.forEach(renderer => renderer.setMap(null));
    routeRenderersRef.current = [];
  };

  const renderAgentMarkers = () => {
    if (!map) return;

    agents.forEach(agent => {
      const marker = new google.maps.Marker({
        position: {
          lat: agent.currentLocation.latitude,
          lng: agent.currentLocation.longitude
        },
        map,
        title: agent.name,
        icon: {
          url: getAgentIcon(agent),
          scaledSize: new google.maps.Size(40, 40)
        }
      });

      // إضافة نافذة معلومات
      const infoWindow = new google.maps.InfoWindow({
        content: createAgentInfoContent(agent)
      });

      marker.addListener('click', () => {
        infoWindow.open(map, marker);
        setSelectedAgent(agent);
      });

      agentMarkersRef.current.push(marker);
    });
  };

  const renderHeatmapLayer = () => {
    if (!map || !window.google.maps.visualization) return;

    const heatmapPoints = heatmapData.map(point => ({
      location: new google.maps.LatLng(
        point.location.latitude,
        point.location.longitude
      ),
      weight: point.intensity
    }));

    const heatmap = new google.maps.visualization.HeatmapLayer({
      data: heatmapPoints,
      map
    });

    heatmap.set('radius', 50);
    heatmap.set('opacity', 0.8);

    heatmapLayerRef.current = heatmap;
  };

  const renderDeliveryRoutes = () => {
    // محاكاة مسارات التوصيل
    const mockRoutes = [
      {
        start: { lat: 24.7136, lng: 46.6753 },
        end: { lat: 24.7236, lng: 46.6853 },
        color: '#4285F4'
      },
      {
        start: { lat: 24.7036, lng: 46.6653 },
        end: { lat: 24.7336, lng: 46.6953 },
        color: '#EA4335'
      }
    ];

    mockRoutes.forEach((route, index) => {
      const directionsService = new google.maps.DirectionsService();
      const directionsRenderer = new google.maps.DirectionsRenderer({
        polylineOptions: {
          strokeColor: route.color,
          strokeWeight: 4
        },
        suppressMarkers: false
      });

      directionsRenderer.setMap(map);

      directionsService.route({
        origin: route.start,
        destination: route.end,
        travelMode: google.maps.TravelMode.DRIVING
      }, (result, status) => {
        if (status === 'OK' && result) {
          directionsRenderer.setDirections(result);
        }
      });

      routeRenderersRef.current.push(directionsRenderer);
    });
  };

  const getAgentIcon = (agent: DeliveryAgent): string => {
    const baseUrl = '/icons/agents/';
    const status = agent.isAvailable ? 'available' : 'busy';
    return `${baseUrl}${agent.vehicleType}_${status}.png`;
  };

  const createAgentInfoContent = (agent: DeliveryAgent): string => {
    return `
      <div style="padding: 10px; min-width: 200px;">
        <h3 style="margin: 0 0 8px 0; color: #333;">${agent.name}</h3>
        <p style="margin: 4px 0; color: #666;">
          <strong>الهاتف:</strong> ${agent.phone}
        </p>
        <p style="margin: 4px 0; color: #666;">
          <strong>نوع المركبة:</strong> ${getVehicleTypeName(agent.vehicleType)}
        </p>
        <p style="margin: 4px 0; color: #666;">
          <strong>التقييم:</strong> ⭐ ${agent.rating}
        </p>
        <p style="margin: 4px 0; color: #666;">
          <strong>الطلبات المكتملة:</strong> ${agent.completedDeliveries}
        </p>
        <div style="margin-top: 8px;">
          <span style="
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 12px;
            background-color: ${agent.isAvailable ? '#10B981' : '#F59E0B'};
            color: white;
          ">
            ${agent.isAvailable ? 'متاح' : 'مشغول'}
          </span>
        </div>
      </div>
    `;
  };

  const getVehicleTypeName = (type: string): string => {
    const names: Record<string, string> = {
      motorcycle: 'دراجة نارية',
      car: 'سيارة',
      bicycle: 'دراجة هوائية',
      walking: 'مشي'
    };
    return names[type] || type;
  };

  const cleanup = () => {
    clearMapLayers();
  };

  const refreshData = async () => {
    await loadInitialData();
    updateMapView();
    toast.success('تم تحديث البيانات');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* أدوات التحكم */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              الخريطة التفاعلية
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshData}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Layers className="w-4 h-4" />
              <Select value={mapMode} onValueChange={(value: any) => setMapMode(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      عادي
                    </div>
                  </SelectItem>
                  <SelectItem value="heatmap">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      خريطة حرارية
                    </div>
                  </SelectItem>
                  <SelectItem value="routes">
                    <div className="flex items-center gap-2">
                      <Route className="w-4 h-4" />
                      المسارات
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-4">
              <Badge variant={showAgents ? "default" : "secondary"}>
                <Users className="w-3 h-3 mr-1" />
                المندوبين ({agents.filter(a => a.isOnline).length})
              </Badge>
              
              <Badge variant="outline">
                <Zap className="w-3 h-3 mr-1" />
                متاح ({agents.filter(a => a.isAvailable).length})
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الخريطة */}
      <Card>
        <CardContent className="p-0">
          <div
            ref={mapRef}
            className="w-full h-96 rounded-lg"
            style={{ minHeight: '400px' }}
          />
        </CardContent>
      </Card>

      {/* معلومات المندوب المحدد */}
      {selectedAgent && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              تفاصيل المندوب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium">{selectedAgent.name}</h4>
                <p className="text-sm text-muted-foreground">{selectedAgent.phone}</p>
              </div>
              <div>
                <p className="text-sm">
                  <strong>نوع المركبة:</strong> {getVehicleTypeName(selectedAgent.vehicleType)}
                </p>
                <p className="text-sm">
                  <strong>التقييم:</strong> ⭐ {selectedAgent.rating}
                </p>
              </div>
              <div>
                <p className="text-sm">
                  <strong>الطلبات المكتملة:</strong> {selectedAgent.completedDeliveries}
                </p>
              </div>
              <div>
                <Badge variant={selectedAgent.isAvailable ? "default" : "secondary"}>
                  {selectedAgent.isAvailable ? 'متاح' : 'مشغول'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{agents.filter(a => a.isOnline).length}</div>
                <div className="text-sm text-muted-foreground">مندوبين متصلين</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{agents.filter(a => a.isAvailable).length}</div>
                <div className="text-sm text-muted-foreground">مندوبين متاحين</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{heatmapData.length}</div>
                <div className="text-sm text-muted-foreground">نقاط الطلب النشطة</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
