#!/usr/bin/env node

/**
 * سكريبت إصلاح تكرارات مكونات ProductCard
 * يقوم بدمج المكونات المكررة وإنشاء مكون ProductCard موحد
 */

const fs = require('fs');
const path = require('path');

// مسارات المكونات
const COMPONENTS_DIR = path.join(process.cwd(), 'src', 'components');
const CUSTOMER_PRODUCT_CARD = path.join(COMPONENTS_DIR, 'customer', 'ProductCard.tsx');
const PRODUCTS_PRODUCT_CARD = path.join(COMPONENTS_DIR, 'products', 'ProductCard.tsx');
const UNIFIED_PRODUCT_CARD = path.join(COMPONENTS_DIR, 'common', 'ProductCard.tsx');

/**
 * إنشاء نسخة احتياطية من ملف
 */
function createBackup(filePath) {
  try {
    if (!fs.existsSync(filePath)) return true;
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في إنشاء نسخة احتياطية:`, error.message);
    return false;
  }
}

/**
 * كتابة ملف بشكل آمن
 */
function writeFileContent(filePath, content) {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في كتابة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * إنشاء مكون ProductCard موحد
 */
function createUnifiedProductCard() {
  console.log('🔧 إنشاء مكون ProductCard موحد...');
  
  // إنشاء نسخ احتياطية
  [CUSTOMER_PRODUCT_CARD, PRODUCTS_PRODUCT_CARD].forEach(filePath => {
    createBackup(filePath);
  });
  
  const unifiedProductCardContent = `"use client";

import { Card, CardContent, CardHeader, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, ShoppingCart, Heart, Eye, MapPin } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useLocale } from "@/hooks/use-locale";
import { useState } from "react";
import AddToCartButton from "@/components/cart/AddToCartButton";
import { formatCurrency, formatDistance, getStoreInitials } from "@/utils/formatters";
import { cn } from "@/lib/utils";
import type { ProductWithStore, ProductDocument } from "@/types";

interface ProductCardProps {
  product: ProductWithStore | ProductDocument;
  showStore?: boolean;
  showDistance?: boolean;
  variant?: "default" | "compact" | "featured" | "simple";
  className?: string;
  onAddToCart?: (productId: string) => void;
  onToggleWishlist?: (productId: string) => void;
  isInWishlist?: boolean;
}

export default function ProductCard({ 
  product, 
  showStore = true,
  showDistance = false,
  variant = "default",
  className = "",
  onAddToCart,
  onToggleWishlist,
  isInWishlist = false
}: ProductCardProps) {
  const { t, locale } = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleAddToCart = async () => {
    if (!onAddToCart) return;
    setIsLoading(true);
    try {
      await onAddToCart(product.id);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleWishlist = () => {
    if (onToggleWishlist) {
      onToggleWishlist(product.id);
    }
  };

  const productImage = !imageError && product.imageUrls && product.imageUrls.length > 0 
    ? product.imageUrls[0] 
    : null;

  const isProductWithStore = 'store' in product;
  const store = isProductWithStore ? product.store : null;
  const storeName = store?.storeName || (product as ProductDocument).storeName;

  // Simple variant (للاستخدام العام)
  if (variant === "simple") {
    return (
      <Card className={cn("flex flex-col overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out h-full", className)}>
        <CardHeader className="p-0 relative aspect-square">
          {productImage ? (
            <Image
              src={productImage}
              alt={product.name}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
              <Eye className="w-12 h-12" />
            </div>
          )}
        </CardHeader>
        
        <CardContent className="p-4 flex-grow">
          <h3 className="text-lg font-semibold mb-1 truncate" title={product.name}>
            {product.name}
          </h3>
          {storeName && (
            <p className="text-sm text-muted-foreground mb-2 truncate" title={storeName}>
              {t('byStore', { storeName })}
            </p>
          )}
          <p className="text-xl font-bold text-primary">
            {formatCurrency(product.price, product.currency, locale)}
          </p>
        </CardContent>
        
        <CardFooter className="p-4 pt-0 border-t mt-auto">
          <div className="flex w-full gap-2">
            <Button variant="outline" className="flex-1" asChild>
              <Link href={\`/\${locale}/product/\${product.id}\`}>
                <Eye className="me-2 rtl:ms-2 h-4 w-4" />
                {t('viewDetails')}
              </Link>
            </Button>
            <Button className="flex-1 bg-accent hover:bg-accent/90 text-accent-foreground">
              <ShoppingCart className="me-2 rtl:ms-2 h-4 w-4" />
              {t('addToCart')}
            </Button>
          </div>
        </CardFooter>
      </Card>
    );
  }

  // Compact variant
  if (variant === "compact") {
    return (
      <Card className={cn("hover:shadow-lg transition-all duration-300", className)}>
        <div className="flex">
          <div className="relative w-24 h-24 flex-shrink-0">
            <Link href={\`/\${locale}/products/\${product.id}\`}>
              {productImage ? (
                <img 
                  src={productImage}
                  alt={product.name}
                  className="w-full h-full object-cover rounded-l-lg"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-muted rounded-l-lg flex items-center justify-center">
                  <Eye className="w-8 h-8 text-muted-foreground" />
                </div>
              )}
            </Link>
            {onToggleWishlist && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0 bg-white/80 hover:bg-white"
                onClick={handleToggleWishlist}
              >
                <Heart className={cn("w-3 h-3", isInWishlist ? 'fill-red-500 text-red-500' : 'text-gray-600')} />
              </Button>
            )}
          </div>
          
          <CardContent className="flex-1 p-3">
            <Link href={\`/\${locale}/products/\${product.id}\`}>
              <h3 className="font-medium text-sm line-clamp-2 mb-1">{product.name}</h3>
            </Link>
            
            <div className="flex items-center justify-between mb-2">
              <span className="font-bold text-primary">
                {formatCurrency(product.price, product.currency, locale)}
              </span>
              {isProductWithStore && product.averageRating && (
                <div className="flex items-center text-xs">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400 mr-1" />
                  <span>{product.averageRating.toFixed(1)}</span>
                </div>
              )}
            </div>

            {showStore && store && (
              <div className="flex items-center text-xs text-muted-foreground mb-2">
                <Avatar className="h-4 w-4 mr-1">
                  <AvatarImage src={store.logoUrl} />
                  <AvatarFallback className="text-xs">
                    {getStoreInitials(store.storeName)}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate">{store.storeName}</span>
              </div>
            )}

            {isProductWithStore && (
              <AddToCartButton
                product={product}
                variant="default"
                size="sm"
                className="w-full h-7 text-xs"
              />
            )}
          </CardContent>
        </div>
      </Card>
    );
  }

  // Featured variant
  if (variant === "featured") {
    return (
      <Card className={cn("hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1", className)}>
        <div className="relative">
          <Link href={\`/\${locale}/products/\${product.id}\`}>
            <div className="relative h-48 overflow-hidden rounded-t-lg">
              {productImage ? (
                <img 
                  src={productImage}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-muted flex items-center justify-center">
                  <Eye className="w-12 h-12 text-muted-foreground" />
                </div>
              )}
            </div>
          </Link>
          
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {isProductWithStore && product.stockQuantity === 0 && (
              <Badge variant="destructive" className="text-xs">
                {t('outOfStock')}
              </Badge>
            )}
            {isProductWithStore && product.stockQuantity > 0 && product.stockQuantity <= 5 && (
              <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">
                {t('lowStock')}
              </Badge>
            )}
          </div>

          {onToggleWishlist && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 h-8 w-8 p-0 bg-white/80 hover:bg-white"
              onClick={handleToggleWishlist}
            >
              <Heart className={cn("w-4 h-4", isInWishlist ? 'fill-red-500 text-red-500' : 'text-gray-600')} />
            </Button>
          )}
        </div>

        <CardHeader className="pb-2">
          <Link href={\`/\${locale}/products/\${product.id}\`}>
            <h3 className="font-bold text-lg line-clamp-2 hover:text-primary transition-colors">
              {product.name}
            </h3>
          </Link>
          {product.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {product.description}
            </p>
          )}
        </CardHeader>

        <CardContent className="pt-0">
          <div className="flex items-center justify-between mb-3">
            <span className="text-2xl font-bold text-primary">
              {formatCurrency(product.price, product.currency, locale)}
            </span>
            {isProductWithStore && product.averageRating && (
              <div className="flex items-center">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                <span className="font-medium">{product.averageRating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground ml-1">
                  ({product.reviewCount || 0})
                </span>
              </div>
            )}
          </div>

          {showStore && store && (
            <div className="flex items-center justify-between mb-3 p-2 bg-muted/50 rounded-lg">
              <div className="flex items-center">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarImage src={store.logoUrl} />
                  <AvatarFallback className="text-xs">
                    {getStoreInitials(store.storeName)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium">{store.storeName}</span>
              </div>
              {showDistance && isProductWithStore && product.distance && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <MapPin className="w-3 h-3 mr-1" />
                  <span>{formatDistance(product.distance)}</span>
                </div>
              )}
            </div>
          )}

          {isProductWithStore && (
            <AddToCartButton
              product={product}
              variant="default"
              size="default"
              className="w-full"
            />
          )}
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className={cn("hover:shadow-lg transition-all duration-300", className)}>
      <div className="relative">
        <Link href={\`/\${locale}/products/\${product.id}\`}>
          <div className="relative h-40 overflow-hidden rounded-t-lg">
            {productImage ? (
              <img 
                src={productImage}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <Eye className="w-10 h-10 text-muted-foreground" />
              </div>
            )}
          </div>
        </Link>
        
        {onToggleWishlist && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-2 right-2 h-7 w-7 p-0 bg-white/80 hover:bg-white"
            onClick={handleToggleWishlist}
          >
            <Heart className={cn("w-3 h-3", isInWishlist ? 'fill-red-500 text-red-500' : 'text-gray-600')} />
          </Button>
        )}

        {isProductWithStore && product.stockQuantity === 0 && (
          <div className="absolute top-2 left-2">
            <Badge variant="destructive" className="text-xs">
              {t('outOfStock')}
            </Badge>
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <Link href={\`/\${locale}/products/\${product.id}\`}>
          <h3 className="font-semibold line-clamp-2 mb-2 hover:text-primary transition-colors">
            {product.name}
          </h3>
        </Link>

        <div className="flex items-center justify-between mb-2">
          <span className="text-lg font-bold text-primary">
            {formatCurrency(product.price, product.currency, locale)}
          </span>
          {isProductWithStore && product.averageRating && (
            <div className="flex items-center text-sm">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
              <span>{product.averageRating.toFixed(1)}</span>
            </div>
          )}
        </div>

        {showStore && store && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <Avatar className="h-5 w-5 mr-2">
              <AvatarImage src={store.logoUrl} />
              <AvatarFallback className="text-xs">
                {getStoreInitials(store.storeName)}
              </AvatarFallback>
            </Avatar>
            <span className="truncate">{store.storeName}</span>
            {showDistance && isProductWithStore && product.distance && (
              <>
                <span className="mx-1">•</span>
                <MapPin className="w-3 h-3 mr-1" />
                <span>{formatDistance(product.distance)}</span>
              </>
            )}
          </div>
        )}

        {isProductWithStore && (
          <AddToCartButton
            product={product}
            variant="default"
            size="sm"
            className="w-full"
          />
        )}
      </CardContent>
    </Card>
  );
}

// تصدير أنواع إضافية للتوافق مع الإصدارات السابقة
export const SimpleProductCard = (props: Omit<ProductCardProps, 'variant'>) => 
  <ProductCard variant="simple" {...props} />;

export const CompactProductCard = (props: Omit<ProductCardProps, 'variant'>) => 
  <ProductCard variant="compact" {...props} />;

export const FeaturedProductCard = (props: Omit<ProductCardProps, 'variant'>) => 
  <ProductCard variant="featured" {...props} />;
`;

  if (writeFileContent(UNIFIED_PRODUCT_CARD, unifiedProductCardContent)) {
    console.log('   ✅ تم إنشاء مكون ProductCard موحد');
    
    // حذف الملفات المكررة
    [CUSTOMER_PRODUCT_CARD, PRODUCTS_PRODUCT_CARD].forEach(filePath => {
      if (fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log(`   🗑️  تم حذف الملف المكرر: ${path.relative(COMPONENTS_DIR, filePath)}`);
        } catch (error) {
          console.error(`   ❌ خطأ في حذف ${filePath}:`, error.message);
        }
      }
    });
    
    return true;
  }
  
  return false;
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء إصلاح تكرارات مكونات ProductCard...\n');
  
  if (createUnifiedProductCard()) {
    console.log('\n🎉 تم إصلاح تكرارات ProductCard بنجاح!');
    console.log('💡 تم إنشاء نسخ احتياطية من الملفات المحذوفة');
    console.log('🔍 يرجى مراجعة الملف الجديد والتأكد من عمله');
    console.log('📁 الملف الجديد: src/components/common/ProductCard.tsx');
  } else {
    console.log('❌ فشل في إصلاح تكرارات ProductCard');
    console.log('🔄 يمكنك استعادة النسخ الاحتياطية إذا لزم الأمر');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  createUnifiedProductCard
};
