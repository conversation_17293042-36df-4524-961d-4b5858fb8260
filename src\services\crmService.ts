// src/services/crmService.ts
"use client";

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc,
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment,
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  CustomerProfile, 
  CustomerInteraction, 
  CustomerSegment,
  CustomerNote,
  CustomerAddress,
  OrderDocument,
  CustomerReview
} from '@/types';

export class CRMService {
  private customersCollection = collection(db, 'customer_profiles');
  private interactionsCollection = collection(db, 'customer_interactions');
  private segmentsCollection = collection(db, 'customer_segments');
  private ordersCollection = collection(db, 'orders');
  private reviewsCollection = collection(db, 'reviews');

  // ===== إدارة ملفات العملاء =====

  // إنشاء أو تحديث ملف عميل
  async createOrUpdateCustomerProfile(
    userId: string, 
    merchantId: string, 
    orderData?: OrderDocument
  ): Promise<CustomerProfile> {
    try {
      const customerRef = doc(this.customersCollection, `${merchantId}_${userId}`);
      const existingDoc = await getDoc(customerRef);

      if (existingDoc.exists()) {
        // تحديث ملف موجود
        const existingProfile = existingDoc.data() as CustomerProfile;
        const updatedProfile = await this.updateCustomerFromOrder(existingProfile, orderData);
        await updateDoc(customerRef, {
          ...updatedProfile,
          updatedAt: serverTimestamp(),
          lastInteractionDate: serverTimestamp()
        });
        return updatedProfile;
      } else {
        // إنشاء ملف جديد
        const newProfile: CustomerProfile = {
          id: customerRef.id,
          userId,
          merchantId,
          personalInfo: {
            name: orderData?.customerInfo.name || 'عميل جديد',
            email: orderData?.customerInfo.email || '',
            phone: orderData?.customerInfo.phone,
          },
          addresses: orderData ? [{
            id: `addr_${Date.now()}`,
            label: 'default',
            street: orderData.shippingInfo.address,
            city: orderData.shippingInfo.city,
            postalCode: orderData.shippingInfo.postalCode,
            country: 'SA',
            isDefault: true,
            createdAt: Timestamp.now()
          }] : [],
          shoppingBehavior: {
            totalOrders: orderData ? 1 : 0,
            totalSpent: orderData?.totalAmount || 0,
            averageOrderValue: orderData?.totalAmount || 0,
            lastOrderDate: orderData ? Timestamp.now() : undefined,
            firstOrderDate: orderData ? Timestamp.now() : undefined,
            favoriteCategories: [],
            averageTimeBetweenOrders: 0
          },
          preferences: {
            communicationChannel: 'email',
            marketingOptIn: true,
            language: 'ar',
            currency: 'SAR',
            notifications: {
              orderUpdates: true,
              promotions: true,
              newProducts: false,
              priceDrops: false
            }
          },
          segmentation: {
            tier: 'bronze',
            riskLevel: 'low',
            lifetimeValue: orderData?.totalAmount || 0,
            churnProbability: 0.1,
            engagementScore: 50
          },
          stats: {
            loyaltyPoints: 0,
            reviewsCount: 0,
            averageRating: 0,
            referralsCount: 0,
            complaintsCount: 0,
            returnRate: 0
          },
          tags: ['new_customer'],
          notes: [],
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
          lastInteractionDate: Timestamp.now()
        };

        await updateDoc(customerRef, newProfile);
        return newProfile;
      }
    } catch (error) {
      console.error('Error creating/updating customer profile:', error);
      throw new Error('فشل في إنشاء أو تحديث ملف العميل');
    }
  }

  // جلب ملف عميل
  async getCustomerProfile(merchantId: string, userId: string): Promise<CustomerProfile | null> {
    try {
      const customerRef = doc(this.customersCollection, `${merchantId}_${userId}`);
      const customerDoc = await getDoc(customerRef);
      
      if (customerDoc.exists()) {
        return { id: customerDoc.id, ...customerDoc.data() } as CustomerProfile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching customer profile:', error);
      throw new Error('فشل في جلب ملف العميل');
    }
  }

  // جلب جميع عملاء التاجر
  async getMerchantCustomers(
    merchantId: string,
    limitCount: number = 20,
    lastDoc?: any,
    filters?: {
      tier?: string;
      searchQuery?: string;
      segment?: string;
      tags?: string[];
    }
  ): Promise<{ customers: CustomerProfile[], hasMore: boolean }> {
    try {
      let q = query(
        this.customersCollection,
        where('merchantId', '==', merchantId),
        orderBy('lastInteractionDate', 'desc'),
        limit(limitCount + 1)
      );

      // تطبيق الفلاتر
      if (filters?.tier) {
        q = query(q, where('segmentation.tier', '==', filters.tier));
      }

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);
      let customers = snapshot.docs.slice(0, limitCount).map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerProfile[];

      // فلترة بحث نصي (يتم على العميل لأن Firestore لا يدعم البحث النصي المتقدم)
      if (filters?.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        customers = customers.filter(customer => 
          customer.personalInfo.name.toLowerCase().includes(searchTerm) ||
          customer.personalInfo.email.toLowerCase().includes(searchTerm) ||
          customer.personalInfo.phone?.includes(searchTerm)
        );
      }

      // فلترة بالعلامات
      if (filters?.tags && filters.tags.length > 0) {
        customers = customers.filter(customer =>
          filters.tags!.some(tag => customer.tags.includes(tag))
        );
      }

      return {
        customers,
        hasMore: snapshot.docs.length > limitCount
      };
    } catch (error) {
      console.error('Error fetching merchant customers:', error);
      throw new Error('فشل في جلب عملاء التاجر');
    }
  }

  // تحديث ملف العميل من بيانات الطلب
  private async updateCustomerFromOrder(
    profile: CustomerProfile, 
    orderData?: OrderDocument
  ): Promise<CustomerProfile> {
    if (!orderData) return profile;

    const updatedProfile = { ...profile };
    
    // تحديث سلوك التسوق
    updatedProfile.shoppingBehavior.totalOrders += 1;
    updatedProfile.shoppingBehavior.totalSpent += orderData.totalAmount;
    updatedProfile.shoppingBehavior.averageOrderValue = 
      updatedProfile.shoppingBehavior.totalSpent / updatedProfile.shoppingBehavior.totalOrders;
    updatedProfile.shoppingBehavior.lastOrderDate = Timestamp.now();

    // حساب متوسط الوقت بين الطلبات
    if (updatedProfile.shoppingBehavior.firstOrderDate) {
      const daysBetween = (Timestamp.now().toMillis() - updatedProfile.shoppingBehavior.firstOrderDate.toMillis()) 
        / (1000 * 60 * 60 * 24);
      updatedProfile.shoppingBehavior.averageTimeBetweenOrders = 
        daysBetween / updatedProfile.shoppingBehavior.totalOrders;
    }

    // تحديث المستوى بناءً على إجمالي الإنفاق
    const totalSpent = updatedProfile.shoppingBehavior.totalSpent;
    if (totalSpent >= 10000) {
      updatedProfile.segmentation.tier = 'platinum';
    } else if (totalSpent >= 5000) {
      updatedProfile.segmentation.tier = 'gold';
    } else if (totalSpent >= 2000) {
      updatedProfile.segmentation.tier = 'silver';
    }

    // تحديث قيمة العميل مدى الحياة
    updatedProfile.segmentation.lifetimeValue = totalSpent;

    // تحديث نقاط التفاعل
    updatedProfile.segmentation.engagementScore = Math.min(100, 
      50 + (updatedProfile.shoppingBehavior.totalOrders * 5)
    );

    // إضافة عنوان جديد إذا لم يكن موجوداً
    const addressExists = updatedProfile.addresses.some(addr => 
      addr.street === orderData.shippingInfo.address && 
      addr.city === orderData.shippingInfo.city
    );

    if (!addressExists) {
      updatedProfile.addresses.push({
        id: `addr_${Date.now()}`,
        label: 'other',
        street: orderData.shippingInfo.address,
        city: orderData.shippingInfo.city,
        postalCode: orderData.shippingInfo.postalCode,
        country: 'SA',
        isDefault: updatedProfile.addresses.length === 0,
        createdAt: Timestamp.now()
      });
    }

    return updatedProfile;
  }

  // إضافة ملاحظة للعميل
  async addCustomerNote(
    merchantId: string,
    userId: string,
    note: Omit<CustomerNote, 'id' | 'createdAt'>
  ): Promise<void> {
    try {
      const customerRef = doc(this.customersCollection, `${merchantId}_${userId}`);
      const newNote: CustomerNote = {
        ...note,
        id: `note_${Date.now()}`,
        createdAt: Timestamp.now()
      };

      await updateDoc(customerRef, {
        notes: increment(1),
        updatedAt: serverTimestamp()
      });

      // إضافة الملاحظة إلى مصفوفة الملاحظات
      const customerDoc = await getDoc(customerRef);
      if (customerDoc.exists()) {
        const currentNotes = customerDoc.data().notes || [];
        await updateDoc(customerRef, {
          notes: [...currentNotes, newNote]
        });
      }
    } catch (error) {
      console.error('Error adding customer note:', error);
      throw new Error('فشل في إضافة ملاحظة العميل');
    }
  }

  // تحديث علامات العميل
  async updateCustomerTags(
    merchantId: string,
    userId: string,
    tags: string[]
  ): Promise<void> {
    try {
      const customerRef = doc(this.customersCollection, `${merchantId}_${userId}`);
      await updateDoc(customerRef, {
        tags,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating customer tags:', error);
      throw new Error('فشل في تحديث علامات العميل');
    }
  }

  // ===== إدارة التفاعلات =====

  // إضافة تفاعل جديد
  async addCustomerInteraction(interaction: Omit<CustomerInteraction, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const interactionRef = await addDoc(this.interactionsCollection, {
        ...interaction,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // تحديث تاريخ آخر تفاعل في ملف العميل
      const customerRef = doc(this.customersCollection, `${interaction.merchantId}_${interaction.customerId}`);
      await updateDoc(customerRef, {
        lastInteractionDate: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return interactionRef.id;
    } catch (error) {
      console.error('Error adding customer interaction:', error);
      throw new Error('فشل في إضافة تفاعل العميل');
    }
  }

  // جلب تفاعلات العميل
  async getCustomerInteractions(
    customerId: string,
    merchantId: string,
    limitCount: number = 20,
    lastDoc?: any
  ): Promise<{ interactions: CustomerInteraction[], hasMore: boolean }> {
    try {
      let q = query(
        this.interactionsCollection,
        where('customerId', '==', customerId),
        where('merchantId', '==', merchantId),
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1)
      );

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);
      const interactions = snapshot.docs.slice(0, limitCount).map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerInteraction[];

      return {
        interactions,
        hasMore: snapshot.docs.length > limitCount
      };
    } catch (error) {
      console.error('Error fetching customer interactions:', error);
      throw new Error('فشل في جلب تفاعلات العميل');
    }
  }

  // ===== تحليل البيانات =====

  // حساب إحصائيات العميل
  async calculateCustomerStats(merchantId: string, userId: string): Promise<any> {
    try {
      const [orders, reviews, interactions] = await Promise.all([
        this.getCustomerOrders(merchantId, userId),
        this.getCustomerReviews(merchantId, userId),
        this.getCustomerInteractions(userId, merchantId, 100)
      ]);

      const totalOrders = orders.length;
      const totalSpent = orders.reduce((sum, order) => sum + order.totalAmount, 0);
      const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;

      const reviewsCount = reviews.length;
      const averageRating = reviewsCount > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviewsCount
        : 0;

      const complaintsCount = interactions.interactions.filter(
        interaction => interaction.type === 'complaint'
      ).length;

      const returnRate = this.calculateReturnRate(orders);

      return {
        totalOrders,
        totalSpent,
        averageOrderValue,
        reviewsCount,
        averageRating,
        complaintsCount,
        returnRate,
        lastOrderDate: orders[0]?.createdAt,
        firstOrderDate: orders[orders.length - 1]?.createdAt
      };
    } catch (error) {
      console.error('Error calculating customer stats:', error);
      throw new Error('فشل في حساب إحصائيات العميل');
    }
  }

  // جلب طلبات العميل
  private async getCustomerOrders(merchantId: string, customerId: string): Promise<OrderDocument[]> {
    const q = query(
      this.ordersCollection,
      where('merchantUid', '==', merchantId),
      where('customerId', '==', customerId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as OrderDocument[];
  }

  // جلب مراجعات العميل
  private async getCustomerReviews(merchantId: string, customerId: string): Promise<CustomerReview[]> {
    const q = query(
      this.reviewsCollection,
      where('customerId', '==', customerId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as CustomerReview[];
  }

  // حساب معدل الإرجاع
  private calculateReturnRate(orders: OrderDocument[]): number {
    if (orders.length === 0) return 0;

    const returnedOrders = orders.filter(order =>
      order.status === 'cancelled' ||
      (order as any).isReturned === true
    ).length;

    return (returnedOrders / orders.length) * 100;
  }

  // ===== البحث والفلترة =====

  // البحث في العملاء
  async searchCustomers(
    merchantId: string,
    searchQuery: string,
    filters?: {
      tier?: string;
      tags?: string[];
      dateRange?: { start: Date; end: Date };
    }
  ): Promise<CustomerProfile[]> {
    try {
      // جلب جميع العملاء (في التطبيق الحقيقي، نحتاج لحل أفضل للبحث)
      const { customers } = await this.getMerchantCustomers(merchantId, 1000);

      let filteredCustomers = customers;

      // فلترة بالبحث النصي
      if (searchQuery) {
        const searchTerm = searchQuery.toLowerCase();
        filteredCustomers = filteredCustomers.filter(customer =>
          customer.personalInfo.name.toLowerCase().includes(searchTerm) ||
          customer.personalInfo.email.toLowerCase().includes(searchTerm) ||
          customer.personalInfo.phone?.includes(searchTerm) ||
          customer.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      // فلترة بالمستوى
      if (filters?.tier) {
        filteredCustomers = filteredCustomers.filter(customer =>
          customer.segmentation.tier === filters.tier
        );
      }

      // فلترة بالعلامات
      if (filters?.tags && filters.tags.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          filters.tags!.some(tag => customer.tags.includes(tag))
        );
      }

      // فلترة بالتاريخ
      if (filters?.dateRange) {
        filteredCustomers = filteredCustomers.filter(customer => {
          const createdDate = customer.createdAt.toDate();
          return createdDate >= filters.dateRange!.start &&
                 createdDate <= filters.dateRange!.end;
        });
      }

      return filteredCustomers;
    } catch (error) {
      console.error('Error searching customers:', error);
      throw new Error('فشل في البحث عن العملاء');
    }
  }

  // ===== تحديث تلقائي للبيانات =====

  // مراقبة تغييرات ملف العميل
  subscribeToCustomerProfile(
    merchantId: string,
    userId: string,
    callback: (profile: CustomerProfile | null) => void
  ): Unsubscribe {
    const customerRef = doc(this.customersCollection, `${merchantId}_${userId}`);

    return onSnapshot(customerRef, (doc) => {
      if (doc.exists()) {
        callback({ id: doc.id, ...doc.data() } as CustomerProfile);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('Error in customer profile subscription:', error);
      callback(null);
    });
  }

  // مراقبة تغييرات التفاعلات
  subscribeToCustomerInteractions(
    customerId: string,
    merchantId: string,
    callback: (interactions: CustomerInteraction[]) => void
  ): Unsubscribe {
    const q = query(
      this.interactionsCollection,
      where('customerId', '==', customerId),
      where('merchantId', '==', merchantId),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    return onSnapshot(q, (snapshot) => {
      const interactions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerInteraction[];
      callback(interactions);
    }, (error) => {
      console.error('Error in interactions subscription:', error);
      callback([]);
    });
  }
}

export const crmService = new CRMService();
