import type { Metadata } from 'next';
import { Tajawal, Inter } from 'next/font/google';
import './globals.css';
import ConsoleErrorHandler from '@/components/debug/ConsoleErrorHandler';
import ErrorSuppressionSetup from '@/components/debug/ErrorSuppressionSetup';
import ChatbotWidget from '@/components/chatbot/ChatbotWidget';

// تحسين: تحميل خطوط محسن للأداء
const tajawal = Tajawal({
  weight: ['400', '500', '700'],
  subsets: ['arabic', 'latin'],
  variable: '--font-tajawal',
  display: 'swap',
  preload: true, // تحسين: preload للخط الأساسي
  fallback: ['Arial', 'sans-serif'],
});

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  preload: true, // تحسين: preload للخط الأساسي
  fallback: ['Arial', 'sans-serif'],
});

export const metadata: Metadata = {
  title: 'مِخْلاة - Mikhla',
  description: 'ربط المتاجر المحلية بالعملاء - Connecting local stores with customers',
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/icon-16.svg', sizes: '16x16', type: 'image/svg+xml' },
      { url: '/icon.svg', sizes: '32x32', type: 'image/svg+xml' },
      { url: '/favicon.ico', sizes: '16x16 32x32', type: 'image/x-icon' }
    ],
    apple: [
      { url: '/apple-touch-icon.svg', sizes: '180x180', type: 'image/svg+xml' }
    ],
    other: [
      { rel: 'icon', url: '/icon-192.svg', sizes: '192x192', type: 'image/svg+xml' }
    ]
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'مِخْلاة',
  },
};

export function generateViewport() {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: '#D3B594', // لون ثيم مخلاة - Sandstone
  };
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning>
      <head>
        <meta charSet="utf-8" />
      </head>
      <body className={`${tajawal.variable} ${inter.variable} font-inter bg-background text-foreground antialiased`}>
        <ErrorSuppressionSetup />
        <ConsoleErrorHandler enableInProduction={false} />
        {children}
        <ChatbotWidget />
      </body>
    </html>
  );
}