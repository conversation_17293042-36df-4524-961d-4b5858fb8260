// src/app/[locale]/merchant/layout.tsx
"use client";

import type { ReactNode } from 'react';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { Loader2, ShieldAlert } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { UserDocument, StoreDocument } from '@/types';

export default function MerchantLayout({
  children,
}: {
  children: ReactNode;
}) {
  const { user, loading, isMerchant, hasPermission } = useAuth();
  const { t, locale } = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    const verifyMerchantAccess = async () => {
      if (loading) return;

      // إذا كان المستخدم في صفحة pending-approval، لا نحتاج للتحقق
      if (pathname?.includes('/pending-approval')) {
        setIsVerifying(false);
        return;
      }

      if (!user) {
        router.push(`/${locale}/login?redirect=/merchant`);
        return;
      }

      // التحقق من نوع المستخدم باستخدام الدالة الجديدة
      if (!isMerchant()) {
        console.log('MerchantLayout: User is not a merchant, redirecting to dashboard');
        router.push(`/${locale}/dashboard`);
        return;
      }

      try {
        // التحقق من حالة الموافقة على المتجر
        const storeDocRef = doc(db, 'stores', user.uid);
        const storeDocSnap = await getDoc(storeDocRef);

        if (storeDocSnap.exists()) {
          const storeData = storeDocSnap.data() as StoreDocument;

          // إذا كان التاجر في انتظار الموافقة أو تم رفضه
          if (storeData.approvalStatus === 'pending' || storeData.approvalStatus === 'rejected') {
            router.push(`/${locale}/merchant/pending-approval`);
            return;
          }

          // إذا تم قبول التاجر لكن المتجر غير مفعل
          if (storeData.approvalStatus === 'approved' && !storeData.isActive) {
            router.push(`/${locale}/merchant/pending-approval`);
            return;
          }
        } else {
          // لا يوجد مستند متجر، توجيه لصفحة الانتظار
          router.push(`/${locale}/merchant/pending-approval`);
          return;
        }
      } catch (error) {
        console.error('Error verifying merchant access:', error);
        router.push(`/${locale}/login`);
        return;
      }

      setIsVerifying(false);
    };

    verifyMerchantAccess();
  }, [user, loading, router, locale, pathname, isMerchant]);


  // عرض شاشة التحميل أثناء التحقق
  if (loading || isVerifying) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('loadingMerchantSection')}</p>
      </div>
    );
  }

  if (!user) {
    // This case should be handled by useEffect redirect, but as a fallback:
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-8 text-center">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h2 className="text-2xl font-semibold mb-2 text-destructive">{t('accessDenied')}</h2>
        <p className="text-muted-foreground mb-6">{t('mustBeLoggedInToAccessMerchant')}</p>
        <Button asChild>
          <Link href={`/${locale}/login?redirect=${encodeURIComponent(`/${locale}/merchant/products/add`)}`}>{t('login')}</Link>
        </Button>
      </div>
    );
  }

  // إذا وصل المستخدم هنا، فهو تاجر معتمد ومفعل
  return (
    <div className="py-8">
      {/* مستقبلاً: إضافة شريط جانبي خاص بالتجار */}
      {children}
    </div>
  );
}
