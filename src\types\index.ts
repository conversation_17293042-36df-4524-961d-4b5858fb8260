import type { Timestamp } from 'firebase/firestore';

// Locale and Page Props Types
export type Locale = 'ar' | 'en';

export interface PageProps {
  params: Promise<{ locale: Locale; [key: string]: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export interface PlanFeature {
  nameKey: string; // Key for translation
  available: boolean;
}

export interface SubscriptionPlan {
  id: string;
  nameKey: string;
  priceDisplayKey: string; // e.g. "free", or a template like "{{price}} {{currency}} / {{period}}"
  priceValue?: number;
  currencyKey?: string;
  periodKey?: string;
  features: PlanFeature[];
  isPopular?: boolean;
  ctaKey: string;
  commission?: number;
  commissionKey?: string; // e.g. "commission"
  maxProductImages?: number;
}

export interface UserProfile {
  name?: string | null;
  email?: string | null;
  avatarUrl?: string | null;
}

// Firestore Document Types

export type UserType = 'customer' | 'merchant' | 'representative' | 'admin';

export interface UserDocument {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL?: string | null;
  userType: UserType;
  isAdmin?: boolean; // للمدراء
  createdAt: Timestamp;
  updatedAt: Timestamp;
  planId?: string; // For merchants
}

export type ApprovalStatus = 'pending' | 'approved' | 'rejected';

export interface StoreDocument {
  merchantUid: string;
  storeName: string;
  storeDescription?: string;
  logoUrl?: string;
  bannerUrl?: string;
  categories?: string[];
  address?: {
    street?: string;
    city?: string;
    country?: string;
    postalCode?: string;
  };
  phoneNumber?: string;
  email?: string;
  website?: string;
  socialMedia?: {
    instagram?: string;
    twitter?: string;
    facebook?: string;
  };
  businessHours?: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  isActive: boolean;
  isVerified: boolean;
  commercialRegistrationURL?: string;
  otherLicensesURL?: string;
  freelanceDocumentURL?: string;
  planId?: string;
  // نظام الموافقة الجديد
  approvalStatus: ApprovalStatus;
  approvalDate?: Timestamp;
  approvalNotes?: string;
  submittedAt: Timestamp;
  reviewedBy?: string; // UID للمدير الذي راجع الطلب
  settings?: {
    allowOnlineOrders: boolean;
    minimumOrderAmount?: number;
    deliveryFee?: number;
    deliveryRadius?: number;
  };
  stats?: {
    totalOrders: number;
    totalSales: number;
    averageRating: number;
    reviewCount: number;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ProductDocument {
  id: string; // Firestore document ID
  merchantUid: string;
  storeId: string;
  storeName?: string; // Added store name for easier display on product cards
  name: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  subCategory?: string;
  imageUrls: string[];
  stockQuantity: number;
  stock?: number; // مرادف لـ stockQuantity
  sku?: string;
  tags?: string[];
  variants?: ProductVariant[];
  averageRating?: number;
  reviewCount?: number;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ProductVariant {
  name: string;
  options: VariantOption[];
}

export interface VariantOption {
  value: string;
  priceModifier?: number;
  stockQuantity?: number;
  sku?: string;
  imageUrl?: string;
}

// Order Management Types
export interface OrderDocument {
  id: string;
  orderNumber: string;
  merchantUid: string;
  customerId: string;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  items: OrderItem[];
  totalAmount: number;
  finalTotal?: number; // المبلغ النهائي بعد الخصومات
  deliveryFee?: number; // رسوم التوصيل
  status: OrderStatus;
  shippingInfo: ShippingInfo;
  paymentInfo: PaymentInfo;

  // معلومات المندوب
  representativeUid?: string | null;
  representativeInfo?: {
    name: string;
    phone: string;
    vehicleInfo?: string;
  };

  // تواريخ مهمة للتوصيل
  pickedUpAt?: Timestamp; // وقت استلام المندوب للطلب
  deliveredAt?: Timestamp; // وقت التسليم للعميل
  estimatedDeliveryTime?: Timestamp; // الوقت المتوقع للتسليم

  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  imageUrl?: string;
}

export type OrderStatus =
  | 'pending'           // في انتظار تأكيد التاجر
  | 'confirmed'         // تم تأكيد الطلب من التاجر
  | 'preparing'         // جاري تحضير الطلب
  | 'ready'             // جاهز للاستلام من المندوب
  | 'picked_up'         // تم استلامه من المندوب
  | 'shipped'           // تم الشحن
  | 'out_for_delivery'  // في طريقه للتوصيل
  | 'delivered'         // تم التسليم
  | 'cancelled';        // ملغي

export interface ShippingInfo {
  address: string;
  city: string;
  postalCode?: string;
  phone: string;
  notes?: string;
}

export interface PaymentInfo {
  method: 'cash' | 'card' | 'online';
  status: 'pending' | 'paid' | 'failed';
  transactionId?: string;
}

// Merchant Dashboard Stats Types
export interface MerchantStats {
  totalProducts: number;
  activeProducts: number;
  totalOrders: number;
  newOrders: number;
  monthlySales: number;
  totalRevenue: number;
  averageRating: number;
  reviewCount: number;
}

// Customer Interface Types
export interface CartItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  storeId: string;
  storeName: string;
  maxQuantity: number;
  selectedVariants?: { [key: string]: string };
}

export interface CartSession {
  id: string;
  userId: string;
  items: CartItem[];
  totalAmount: number;
  totalItems: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  expiresAt: Timestamp;
}

export interface CustomerReview {
  id: string;
  customerId: string;
  customerName: string;
  customerAvatar?: string;
  productId?: string;
  storeId?: string;
  rating: number;
  comment: string;
  images?: string[];
  isVerified: boolean;
  helpfulCount: number;
  reportCount?: number;
  isReported?: boolean;
  isHidden?: boolean;
  moderationStatus?: 'pending' | 'approved' | 'rejected';
  moderationNotes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// نوع بيانات الإبلاغ عن المراجعات
export interface ReviewReport {
  id: string;
  reviewId: string;
  reporterId: string;
  reporterName: string;
  reason: 'spam' | 'inappropriate' | 'fake' | 'offensive' | 'other';
  description?: string;
  status: 'pending' | 'reviewed' | 'resolved';
  createdAt: Timestamp;
  reviewedAt?: Timestamp;
  reviewedBy?: string;
}

// نوع بيانات إحصائيات التقييمات
export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  verifiedReviews: number;
  recentReviews: number; // آخر 30 يوم
}

export interface SearchFilter {
  query?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  priceRange?: [number, number];
  rating?: number;
  inStock?: boolean;
  location?: {
    lat: number;
    lng: number;
    radius: number;
  } | string;
  sortBy?: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'distance' | 'newest';
  storeId?: string;
  isActive?: boolean;
}

export interface MapLocation {
  lat: number;
  lng: number;
  address?: string;
  city?: string;
  country?: string;
}

export interface StoreWithDistance extends StoreDocument {
  distance?: number;
  distanceText?: string;
  isOpen?: boolean;
  nextOpenTime?: string;
}

export interface ProductWithStore extends ProductDocument {
  store?: StoreDocument;
  distance?: number;
  reviews?: CustomerReview[];
  averageRating?: number;
  reviewCount?: number;
}

export interface CheckoutInfo {
  items: CartItem[];
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  shippingAddress: {
    street: string;
    city: string;
    postalCode?: string;
    notes?: string;
  };
  paymentMethod: 'cash' | 'card' | 'online';
  totalAmount: number;
  deliveryFee: number;
  notes?: string;
}

// CRM Types - نظام إدارة علاقات العملاء

// ملف العميل الشامل
export interface CustomerProfile {
  id: string;
  userId: string; // UID من Firebase Auth
  merchantId: string; // التاجر المرتبط بهذا العميل

  // المعلومات الأساسية
  personalInfo: {
    name: string;
    email: string;
    phone?: string;
    dateOfBirth?: Timestamp;
    gender?: 'male' | 'female' | 'other';
    avatar?: string;
  };

  // معلومات العنوان
  addresses: CustomerAddress[];
  defaultAddressId?: string;

  // معلومات التسوق
  shoppingBehavior: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: Timestamp;
    firstOrderDate?: Timestamp;
    favoriteCategories: string[];
    preferredPaymentMethod?: string;
    averageTimeBetweenOrders?: number; // بالأيام
  };

  // التفضيلات
  preferences: {
    communicationChannel: 'email' | 'sms' | 'push' | 'whatsapp';
    marketingOptIn: boolean;
    language: 'ar' | 'en';
    currency: string;
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      newProducts: boolean;
      priceDrops: boolean;
    };
  };

  // التقسيم والتصنيف
  segmentation: {
    tier: 'bronze' | 'silver' | 'gold' | 'platinum';
    riskLevel: 'low' | 'medium' | 'high';
    lifetimeValue: number;
    churnProbability: number; // احتمالية فقدان العميل (0-1)
    engagementScore: number; // نقاط التفاعل (0-100)
  };

  // الإحصائيات
  stats: {
    loyaltyPoints: number;
    reviewsCount: number;
    averageRating: number;
    referralsCount: number;
    complaintsCount: number;
    returnRate: number; // معدل الإرجاع
    satisfactionScore?: number; // نقاط الرضا (1-5)
  };

  // العلامات والملاحظات
  tags: string[];
  notes: CustomerNote[];

  // التواريخ
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastInteractionDate?: Timestamp;
}

export interface CustomerAddress {
  id: string;
  label: string; // 'home', 'work', 'other'
  street: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isDefault: boolean;
  createdAt: Timestamp;
}

export interface CustomerNote {
  id: string;
  content: string;
  type: 'general' | 'complaint' | 'compliment' | 'follow_up' | 'important';
  createdBy: string; // UID للموظف الذي أضاف الملاحظة
  createdByName: string;
  isPrivate: boolean;
  createdAt: Timestamp;
}

// تفاعلات العملاء
export interface CustomerInteraction {
  id: string;
  customerId: string;
  merchantId: string;

  // نوع التفاعل
  type: 'order' | 'inquiry' | 'complaint' | 'review' | 'support' | 'marketing' | 'visit' | 'call' | 'email' | 'chat';

  // تفاصيل التفاعل
  details: {
    title: string;
    description?: string;
    channel: 'website' | 'mobile_app' | 'phone' | 'email' | 'whatsapp' | 'in_store' | 'social_media';
    outcome?: 'resolved' | 'pending' | 'escalated' | 'cancelled';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    duration?: number; // بالدقائق
    cost?: number; // تكلفة التفاعل إن وجدت
  };

  // البيانات المرتبطة
  relatedData?: {
    orderId?: string;
    productId?: string;
    reviewId?: string;
    ticketId?: string;
    campaignId?: string;
  };

  // الموظف المسؤول
  handledBy?: {
    userId: string;
    name: string;
    department?: string;
  };

  // المرفقات
  attachments?: {
    type: 'image' | 'document' | 'audio' | 'video';
    url: string;
    name: string;
    size: number;
  }[];

  // التقييم
  rating?: {
    score: number; // 1-5
    feedback?: string;
  };

  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// تقسيم العملاء
export interface CustomerSegment {
  id: string;
  merchantId: string;
  name: string;
  description: string;

  // معايير التقسيم
  criteria: {
    // معايير ديموغرافية
    demographic?: {
      ageRange?: { min: number; max: number };
      gender?: ('male' | 'female' | 'other')[];
      location?: {
        cities?: string[];
        states?: string[];
        countries?: string[];
      };
    };

    // معايير سلوكية
    behavioral?: {
      totalOrdersRange?: { min: number; max: number };
      totalSpentRange?: { min: number; max: number };
      lastOrderDays?: number; // آخر طلب خلال X أيام
      averageOrderValueRange?: { min: number; max: number };
      favoriteCategories?: string[];
      paymentMethods?: string[];
    };

    // معايير التفاعل
    engagement?: {
      loyaltyPointsRange?: { min: number; max: number };
      reviewsCountRange?: { min: number; max: number };
      engagementScoreRange?: { min: number; max: number };
      churnProbabilityRange?: { min: number; max: number };
    };

    // معايير مخصصة
    custom?: {
      tags?: string[];
      hasComplaints?: boolean;
      isVIP?: boolean;
      marketingOptIn?: boolean;
    };
  };

  // الإحصائيات
  stats: {
    customerCount: number;
    totalRevenue: number;
    averageOrderValue: number;
    conversionRate: number;
    churnRate: number;
  };

  // الإعدادات
  settings: {
    isActive: boolean;
    autoUpdate: boolean; // تحديث تلقائي للعضوية
    color: string; // لون التمييز في الواجهة
    icon?: string;
  };

  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastCalculatedAt?: Timestamp;
}

// تحليلات CRM
export interface CRMAnalytics {
  merchantId: string;
  period: {
    startDate: Timestamp;
    endDate: Timestamp;
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };

  // مقاييس العملاء
  customerMetrics: {
    totalCustomers: number;
    newCustomers: number;
    activeCustomers: number;
    returningCustomers: number;
    lostCustomers: number;

    // معدلات مهمة
    customerRetentionRate: number;
    customerChurnRate: number;
    customerAcquisitionCost: number;
    customerLifetimeValue: number;

    // التوزيع
    customersByTier: {
      bronze: number;
      silver: number;
      gold: number;
      platinum: number;
    };

    customersBySegment: { [segmentId: string]: number };
  };

  // مقاييس التفاعل
  interactionMetrics: {
    totalInteractions: number;
    interactionsByType: { [type: string]: number };
    interactionsByChannel: { [channel: string]: number };
    averageResponseTime: number; // بالساعات
    resolutionRate: number;
    customerSatisfactionScore: number;
  };

  // مقاييس المبيعات
  salesMetrics: {
    totalRevenue: number;
    averageOrderValue: number;
    repeatPurchaseRate: number;
    crossSellRate: number;
    upsellRate: number;
    refundRate: number;
  };

  // التوقعات
  predictions: {
    nextMonthRevenue: number;
    churnRisk: {
      high: number;
      medium: number;
      low: number;
    };
    growthRate: number;
  };

  generatedAt: Timestamp;
}

// التواصل مع العملاء
export interface CustomerCommunication {
  id: string;
  merchantId: string;

  // المستهدفون
  targets: {
    type: 'all' | 'segment' | 'individual' | 'custom';
    segmentIds?: string[];
    customerIds?: string[];
    criteria?: any; // معايير مخصصة للاستهداف
  };

  // محتوى الرسالة
  content: {
    type: 'email' | 'sms' | 'push' | 'whatsapp' | 'in_app';
    subject?: string;
    message: string;
    template?: string;
    personalization: boolean;
    attachments?: {
      type: 'image' | 'document' | 'video';
      url: string;
      name: string;
    }[];
  };

  // الجدولة
  scheduling: {
    type: 'immediate' | 'scheduled' | 'recurring';
    scheduledAt?: Timestamp;
    timezone?: string;
    recurrence?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      interval: number;
      endDate?: Timestamp;
    };
  };

  // الحالة والإحصائيات
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed' | 'cancelled';
  stats: {
    targetCount: number;
    sentCount: number;
    deliveredCount: number;
    openedCount: number;
    clickedCount: number;
    repliedCount: number;
    unsubscribedCount: number;
    failedCount: number;

    // معدلات
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    responseRate: number;
    unsubscribeRate: number;
  };

  // الإعدادات
  settings: {
    trackOpens: boolean;
    trackClicks: boolean;
    allowUnsubscribe: boolean;
    priority: 'low' | 'normal' | 'high';
  };

  createdAt: Timestamp;
  updatedAt: Timestamp;
  sentAt?: Timestamp;
}

// أنواع بيانات التكامل مع أنظمة ERP و POS
export interface ERPIntegration {
  id: string;
  merchantId: string;
  systemType: 'sap' | 'oracle' | 'microsoft_dynamics' | 'odoo' | 'custom';
  systemName: string;
  isActive: boolean;
  configuration: {
    apiUrl: string;
    apiKey: string;
    username?: string;
    password?: string;
    database?: string;
    version?: string;
    customHeaders?: Record<string, string>;
  };
  syncSettings: {
    syncProducts: boolean;
    syncInventory: boolean;
    syncOrders: boolean;
    syncCustomers: boolean;
    syncAccounting: boolean;
    syncInterval: number; // بالدقائق
    lastSync?: Timestamp;
    autoSync: boolean;
  };
  fieldMapping: {
    productFields: Record<string, string>;
    customerFields: Record<string, string>;
    orderFields: Record<string, string>;
    inventoryFields: Record<string, string>;
  };
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  lastError?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface POSIntegration {
  id: string;
  merchantId: string;
  systemType: 'square' | 'shopify_pos' | 'lightspeed' | 'toast' | 'clover' | 'custom';
  systemName: string;
  isActive: boolean;
  configuration: {
    apiUrl: string;
    apiKey: string;
    accessToken?: string;
    refreshToken?: string;
    storeId?: string;
    locationId?: string;
    environment: 'sandbox' | 'production';
  };
  syncSettings: {
    syncProducts: boolean;
    syncInventory: boolean;
    syncSales: boolean;
    syncCustomers: boolean;
    syncPayments: boolean;
    syncInterval: number; // بالدقائق
    lastSync?: Timestamp;
    autoSync: boolean;
  };
  fieldMapping: {
    productFields: Record<string, string>;
    customerFields: Record<string, string>;
    saleFields: Record<string, string>;
    paymentFields: Record<string, string>;
  };
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  lastError?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface IntegrationLog {
  id: string;
  merchantId: string;
  integrationType: 'erp' | 'pos';
  integrationId: string;
  operation: 'sync' | 'create' | 'update' | 'delete';
  entityType: 'product' | 'customer' | 'order' | 'inventory' | 'payment';
  entityId?: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
  duration?: number; // بالميلي ثانية
  createdAt: Timestamp;
}

export interface SyncResult {
  success: boolean;
  totalRecords: number;
  successfulRecords: number;
  failedRecords: number;
  errors: Array<{
    record: any;
    error: string;
  }>;
  duration: number;
  timestamp: Timestamp;
}

export interface IntegrationStats {
  merchantId: string;
  totalIntegrations: number;
  activeIntegrations: number;
  lastSyncTime?: Timestamp;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  avgSyncDuration: number;
  dataVolume: {
    products: number;
    customers: number;
    orders: number;
    inventory: number;
  };
}

// Export all types from other files
export * from './terms';
export * from './representative';
export * from './loyalty';
