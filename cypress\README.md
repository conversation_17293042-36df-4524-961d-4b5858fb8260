# دليل اختبارات Cypress لمشروع مخلة

## نظرة عامة

يستخدم هذا المشروع Cypress كنظام اختبارات شامل يشمل:
- اختبارات End-to-End (E2E) - اختبار التطبيق كاملاً
- اختبارات المكونات (Component Testing) - اختبار المكونات منفردة
- اختبارات التكامل - اختبار التفاعل بين المكونات

## هيكل الاختبارات

```
cypress/
├── e2e/                    # اختبارات End-to-End
│   ├── homepage.cy.ts      # اختبارات الصفحة الرئيسية
│   ├── authentication.cy.ts # اختبارات المصادقة
│   ├── search-and-filters.cy.ts # اختبارات البحث والفلاتر
│   ├── admin-users-management.cy.ts # اختبارات إدارة المستخدمين
│   ├── admin-categories-management.cy.ts # اختبارات إدارة الفئات
│   ├── admin-system-settings.cy.ts # اختبارات إعدادات النظام
│   ├── merchant-dashboard.cy.ts # اختبارات لوحة تحكم التاجر
│   ├── delivery-system.cy.ts # اختبارات نظام التوصيل
│   ├── orders-payment-flow.cy.ts # اختبارات الطلبات والدفع
│   └── complete-system-integration.cy.ts # اختبار التكامل الشامل
├── fixtures/               # بيانات تجريبية
│   ├── large-users-list.json # قائمة مستخدمين كبيرة
│   └── settings-backup.json # نسخة احتياطية للإعدادات
├── support/                # ملفات الدعم
│   ├── commands.ts         # أوامر مخصصة
│   ├── e2e.ts             # إعدادات E2E
│   └── component.ts        # إعدادات اختبار المكونات
└── README.md              # هذا الملف

src/components/ui/
└── button.cy.tsx          # اختبار مكون الزر

scripts/
└── run-comprehensive-tests.js # سكريبت تشغيل الاختبارات الشاملة
```

## تشغيل الاختبارات

### تشغيل الاختبارات الأساسية

```bash
# تشغيل جميع الاختبارات (E2E)
bun run test

# فتح واجهة Cypress التفاعلية
bun run test:open

# تشغيل اختبارات E2E فقط
bun run test:e2e

# فتح واجهة E2E التفاعلية
bun run test:e2e:open

# تشغيل اختبارات المكونات
bun run test:component

# فتح واجهة اختبار المكونات
bun run test:component:open
```

### تشغيل الاختبارات الشاملة الجديدة

```bash
# تشغيل جميع الاختبارات الشاملة
bun run test:comprehensive

# فتح الاختبارات الشاملة في الواجهة التفاعلية
bun run test:comprehensive:open

# تشغيل اختبارات الإدارة فقط
bun run test:admin

# تشغيل اختبارات التجار فقط
bun run test:merchant

# تشغيل اختبارات التوصيل فقط
bun run test:delivery

# تشغيل اختبار التكامل الشامل
bun run test:integration

# تشغيل اختبارات الطلبات والدفع
bun run test:orders
```

## الاختبارات الجديدة المضافة

### 1. اختبارات صفحات الإدارة

#### إدارة المستخدمين (`admin-users-management.cy.ts`)
- عرض وإدارة جميع المستخدمين
- البحث والتصفية المتقدمة
- الإجراءات الجماعية
- إرسال الإشعارات
- تصدير البيانات
- تعديل وحذف المستخدمين

#### إدارة الفئات والمنتجات (`admin-categories-management.cy.ts`)
- إدارة شجرة الفئات
- مراجعة واعتماد المنتجات
- إدارة العلامات التجارية
- السحب والإفلات لترتيب الفئات
- المراجعة الجماعية للمنتجات

#### إعدادات النظام (`admin-system-settings.cy.ts`)
- الإعدادات العامة للمنصة
- إعدادات العمولات
- إعدادات الإشعارات
- إعدادات الدفع والأمان
- النسخ الاحتياطية
- استيراد/تصدير الإعدادات

### 2. اختبارات واجهة التجار

#### لوحة تحكم التاجر (`merchant-dashboard.cy.ts`)
- عرض الإحصائيات والتحليلات
- الإجراءات السريعة
- إدارة الطلبات والمنتجات
- الرسوم البيانية والتقارير
- الإشعارات والتنبيهات
- تخصيص لوحة التحكم

### 3. اختبارات نظام التوصيل

#### نظام التوصيل والمندوبين (`delivery-system.cy.ts`)
- واجهة المندوب وقبول الطلبات
- تتبع التوصيلات والخرائط
- إدارة التوصيل للتجار
- مراقبة التوصيلات للإدارة
- الاتصال بالعملاء
- تاريخ التوصيلات والأرباح

### 4. اختبارات الطلبات والدفع

#### تدفق الطلبات والدفع (`orders-payment-flow.cy.ts`)
- تدفق الطلب الكامل للعميل
- إدارة الطلبات للتاجر
- طرق الدفع المختلفة
- تتبع وإلغاء الطلبات
- معالجة الأخطاء والفشل
- الإشعارات في الوقت الفعلي

### 5. اختبار التكامل الشامل

#### النظام المتكامل (`complete-system-integration.cy.ts`)
- سيناريو كامل من التسجيل للتوصيل
- اختبار الأداء والتحميل
- اختبار الأمان والصلاحيات
- التوافق والوصولية
- دعم اللغات المختلفة
- معالجة انقطاع الاتصال

## الأوامر المخصصة

### أوامر المصادقة

```typescript
// تسجيل دخول وهمي
cy.mockLogin('customer') // أو 'merchant' أو 'representative'

// تسجيل خروج
cy.mockLogout()

// محاكاة Firebase Auth
cy.mockFirebaseAuth()
```

### أوامر التنقل

```typescript
// زيارة صفحة بلغة محددة
cy.visitWithLocale('/dashboard', 'ar') // أو 'en'

// انتظار انتهاء التحميل
cy.waitForLoadingToFinish()

// انتظار تحميل الصفحة
cy.waitForPageLoad()
```

### أوامر المحتوى

```typescript
// التحقق من النص العربي
cy.shouldContainArabicText('مرحبا')

// محاكاة الموقع الجغرافي
cy.mockGeolocation(24.7136, 46.6753) // الرياض
```

## أمثلة على الاختبارات

### اختبار الصفحة الرئيسية

```typescript
describe('الصفحة الرئيسية', () => {
  beforeEach(() => {
    cy.mockGeolocation()
    cy.visitWithLocale('/')
  })

  it('يجب أن تحمل الصفحة بنجاح', () => {
    cy.waitForLoadingToFinish()
    cy.get('header').should('be.visible')
    cy.get('main').should('be.visible')
    cy.get('footer').should('be.visible')
  })
})
```

### اختبار المكونات

```typescript
import { Button } from './button'

describe('Button Component', () => {
  it('يعرض النص بشكل صحيح', () => {
    cy.mount(<Button>انقر هنا</Button>)
    cy.contains('انقر هنا').should('be.visible')
  })
})
```

## إعدادات البيئة

### متغيرات البيئة للاختبار

```typescript
// في cypress.config.ts
env: {
  NEXT_PUBLIC_FIREBASE_API_KEY: 'demo-api-key',
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: 'demo-project.firebaseapp.com',
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: 'demo-project',
}
```

### محاكاة Firebase

يتم محاكاة Firebase تلقائياً في الاختبارات لتجنب التفاعل مع قاعدة البيانات الحقيقية.

## أفضل الممارسات

### 1. استخدام data-testid

```html
<button data-testid="submit-button">إرسال</button>
```

```typescript
cy.get('[data-testid="submit-button"]').click()
```

### 2. انتظار العناصر

```typescript
// جيد
cy.get('[data-testid="loading"]').should('not.exist')
cy.get('[data-testid="content"]').should('be.visible')

// تجنب
cy.wait(5000) // انتظار ثابت
```

### 3. تجميع الاختبارات المترابطة

```typescript
describe('نظام المصادقة', () => {
  context('تسجيل الدخول', () => {
    // اختبارات تسجيل الدخول
  })
  
  context('تسجيل الخروج', () => {
    // اختبارات تسجيل الخروج
  })
})
```

### 4. استخدام beforeEach للإعداد

```typescript
beforeEach(() => {
  cy.mockGeolocation()
  cy.mockFirebaseAuth()
  cy.visitWithLocale('/')
})
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ Firebase**: تأكد من تفعيل محاكاة Firebase
2. **عناصر غير مرئية**: استخدم `cy.waitForLoadingToFinish()`
3. **مشاكل الشبكة**: تحقق من تشغيل الخادم على المنفذ 9002

### تسجيل الأخطاء

```typescript
// في cypress/support/e2e.ts
cy.on('uncaught:exception', (err, runnable) => {
  console.log('Uncaught exception:', err.message)
  return false // منع فشل الاختبار
})
```

## التقارير

يتم حفظ التقارير في:
- `cypress/videos/` - فيديوهات الاختبارات
- `cypress/screenshots/` - لقطات الشاشة عند الفشل

---

## 🆕 **الاختبارات المتقدمة الجديدة (الإصدار 2.5.0)**

### 🎯 **الميزات المتقدمة المختبرة**

#### 🔌 **نظام التكامل ERP/POS**
**الملف**: `e2e/erp-pos-integration.cy.ts` (300+ سطر)
- ✅ إعداد وإدارة التكاملات مع أنظمة SAP، Oracle، وغيرها
- ✅ مزامنة البيانات (منتجات، عملاء، طلبات، مخزون)
- ✅ اختبار الاتصال مع الأنظمة الخارجية
- ✅ معالجة الأخطاء والاستثناءات
- ✅ تقارير الأداء والإحصائيات

#### 👥 **نظام CRM**
**الملف**: `e2e/crm-system.cy.ts` (300+ سطر)
- ✅ إدارة ملفات العملاء وتحليل السلوك
- ✅ تتبع التفاعلات والمتابعة
- ✅ تقسيم العملاء والتحليلات المتقدمة
- ✅ الحملات التسويقية والتواصل
- ✅ تحليلات دورة حياة العميل

#### 🎟️ **نظام الكوبونات**
**الملف**: `e2e/coupons-system.cy.ts` (300+ سطر)
- ✅ إنشاء جميع أنواع الكوبونات (نسبة، مبلغ ثابت، شحن مجاني)
- ✅ تطبيق الخصومات في عملية الشراء
- ✅ إدارة شروط الاستخدام والحدود الزمنية
- ✅ تحليلات الاستخدام والأداء
- ✅ إدارة دورة حياة الكوبون الكاملة

#### ⭐ **نظام الولاء**
**الملف**: `e2e/loyalty-system.cy.ts` (300+ سطر)
- ✅ إعداد برنامج الولاء والمستويات المختلفة
- ✅ إدارة النقاط والمكافآت
- ✅ استبدال النقاط بالمكافآت
- ✅ تحليلات أداء البرنامج
- ✅ إشعارات الولاء والترقيات

#### 🛡️ **لوحة تحكم الإدارة المتقدمة**
**الملف**: `e2e/admin-dashboard.cy.ts` (300+ سطر)
- ✅ إدارة المستخدمين والصلاحيات المتقدمة
- ✅ موافقات انضمام التجار
- ✅ إدارة فئات المنتجات
- ✅ إعدادات النظام والأمان
- ✅ التقارير الإدارية الشاملة

#### 🏪 **لوحة تحكم التاجر الشاملة**
**الملف**: `e2e/merchant-dashboard-comprehensive.cy.ts` (300+ سطر)
- ✅ إدارة المنتجات المتقدمة (بالجملة، استيراد/تصدير CSV)
- ✅ إدارة الطلبات المتقدمة (تتبع، مرتجعات، شحنات)
- ✅ التقارير المخصصة والتحليلات المتقدمة
- ✅ قواعد الأتمتة والتكامل الخارجي
- ✅ التجاوب على الأجهزة المختلفة

#### 👤 **لوحة تحكم المستخدم الشاملة**
**الملف**: `e2e/user-dashboard.cy.ts` (300+ سطر)
- ✅ إدارة الطلبات والتتبع المباشر
- ✅ إدارة الملف الشخصي والعناوين
- ✅ إدارة الإشعارات والإعدادات
- ✅ نقاط الولاء واستبدال المكافآت
- ✅ تجربة المستخدم على الأجهزة المختلفة

#### 🔗 **التكامل الشامل للنظام**
**الملف**: `e2e/comprehensive-system-integration.cy.ts` (300+ سطر)
- ✅ رحلة العميل الكاملة من التسجيل إلى الولاء
- ✅ سيناريوهات التاجر الشاملة
- ✅ إدارة النظام بالكامل
- ✅ التكامل بين جميع الأنظمة
- ✅ اختبار الأداء والأمان والحمولة العالية

### 🛠️ **الأوامر المخصصة الجديدة**

#### 📊 **أوامر البيانات الوهمية**
```typescript
cy.mockERPIntegration()      // محاكاة بيانات تكامل ERP
cy.mockCRMCustomers()        // محاكاة بيانات عملاء CRM
cy.mockCoupons()             // محاكاة بيانات الكوبونات
cy.mockLoyaltyProgram()      // محاكاة بيانات برنامج الولاء
```

#### 🔧 **أوامر المساعدة المتقدمة**
```typescript
cy.fillForm(formData)        // ملء النماذج تلقائياً
cy.waitForAPI(alias)         // انتظار استجابة API
cy.checkDashboardStats()     // فحص إحصائيات لوحة التحكم
```

### 📊 **إحصائيات الاختبارات الجديدة**
- **إجمالي ملفات الاختبار الجديدة**: 8 ملفات
- **إجمالي الاختبارات الجديدة**: 150+ اختبار فردي
- **إجمالي أسطر الكود الجديدة**: 2400+ سطر
- **التغطية**: جميع الميزات المتقدمة
- **السيناريوهات**: 25+ سيناريو تكامل شامل

### 🚀 **تشغيل الاختبارات الجديدة**

#### **تشغيل جميع الاختبارات الجديدة**
```bash
node scripts/run-cypress-tests.js
```

#### **تشغيل اختبار محدد**
```bash
npx cypress run --spec "cypress/e2e/erp-pos-integration.cy.ts"
npx cypress run --spec "cypress/e2e/crm-system.cy.ts"
npx cypress run --spec "cypress/e2e/coupons-system.cy.ts"
npx cypress run --spec "cypress/e2e/loyalty-system.cy.ts"
```

---

## المساهمة

عند إضافة اختبارات جديدة:

1. استخدم أسماء وصفية باللغة العربية
2. أضف تعليقات توضيحية
3. تأكد من تغطية الحالات الحدية
4. اختبر على أحجام شاشة مختلفة
5. تأكد من دعم اللغتين العربية والإنجليزية

---

> **تم إنشاء الاختبارات المتقدمة بواسطة**: فريق تطوير مِخْلاة
> **آخر تحديث**: 15 يونيو 2025
> **الإصدار**: v2.5.0
