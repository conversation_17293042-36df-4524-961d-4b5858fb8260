/**
 * خدمة الخرائط المتقدمة - نظام شامل للخرائط والتتبع والتحليل الجغرافي
 * يدعم تتبع المندوبين، تحسين المسارات، والتحليلات الجغرافية
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  doc, 
  getDocs,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  GeoPoint 
} from 'firebase/firestore';

interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: Date;
}

interface DeliveryAgent {
  id: string;
  name: string;
  phone: string;
  currentLocation: Location;
  isOnline: boolean;
  isAvailable: boolean;
  vehicleType: 'motorcycle' | 'car' | 'bicycle' | 'walking';
  rating: number;
  completedDeliveries: number;
}

interface DeliveryRoute {
  id: string;
  agentId: string;
  orderId: string;
  startLocation: Location;
  endLocation: Location;
  waypoints: Location[];
  estimatedDuration: number; // minutes
  estimatedDistance: number; // kilometers
  actualDuration?: number;
  actualDistance?: number;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

interface HeatmapData {
  location: Location;
  intensity: number;
  orderCount: number;
  revenue: number;
  timeSlot: string;
}

interface GeofenceZone {
  id: string;
  name: string;
  type: 'delivery_zone' | 'restricted_zone' | 'high_demand_zone';
  coordinates: Location[];
  isActive: boolean;
  rules: {
    maxDeliveryTime?: number;
    priorityLevel?: number;
    specialInstructions?: string;
  };
}

class AdvancedMapsService {
  private static readonly GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  private static readonly TRACKING_INTERVAL = 30000; // 30 seconds
  private static readonly MAX_TRACKING_DISTANCE = 50; // kilometers

  /**
   * تتبع موقع المندوب في الوقت الفعلي
   */
  static async startAgentTracking(agentId: string): Promise<void> {
    try {
      if (!navigator.geolocation) {
        throw new Error('خدمة تحديد الموقع غير مدعومة في هذا المتصفح');
      }

      const trackingOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 5000
      };

      // بدء التتبع المستمر
      const watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const location: Location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date()
          };

          await this.updateAgentLocation(agentId, location);
        },
        (error) => {
          console.error('خطأ في تتبع الموقع:', error);
        },
        trackingOptions
      );

      // حفظ معرف التتبع لإيقافه لاحقاً
      localStorage.setItem(`tracking_${agentId}`, watchId.toString());

      console.log('✅ تم بدء تتبع المندوب:', agentId);
    } catch (error) {
      console.error('❌ خطأ في بدء تتبع المندوب:', error);
      throw new Error('فشل في بدء تتبع الموقع');
    }
  }

  /**
   * إيقاف تتبع المندوب
   */
  static stopAgentTracking(agentId: string): void {
    const watchId = localStorage.getItem(`tracking_${agentId}`);
    if (watchId) {
      navigator.geolocation.clearWatch(parseInt(watchId));
      localStorage.removeItem(`tracking_${agentId}`);
      console.log('⏹️ تم إيقاف تتبع المندوب:', agentId);
    }
  }

  /**
   * تحديث موقع المندوب
   */
  static async updateAgentLocation(agentId: string, location: Location): Promise<void> {
    try {
      const agentRef = doc(db, 'delivery_agents', agentId);
      await updateDoc(agentRef, {
        currentLocation: new GeoPoint(location.latitude, location.longitude),
        lastLocationUpdate: serverTimestamp(),
        locationAccuracy: location.accuracy || 0
      });

      // حفظ تاريخ المواقع للتحليل
      const locationHistoryRef = collection(db, 'agent_location_history');
      await addDoc(locationHistoryRef, {
        agentId,
        location: new GeoPoint(location.latitude, location.longitude),
        accuracy: location.accuracy || 0,
        timestamp: serverTimestamp()
      });

    } catch (error) {
      console.error('❌ خطأ في تحديث موقع المندوب:', error);
    }
  }

  /**
   * العثور على أقرب مندوب متاح
   */
  static async findNearestAvailableAgent(
    customerLocation: Location,
    maxDistance: number = 10
  ): Promise<DeliveryAgent | null> {
    try {
      const agentsRef = collection(db, 'delivery_agents');
      const q = query(
        agentsRef,
        where('isOnline', '==', true),
        where('isAvailable', '==', true)
      );

      const snapshot = await getDocs(q);
      let nearestAgent: DeliveryAgent | null = null;
      let minDistance = Infinity;

      snapshot.forEach(doc => {
        const data = doc.data();
        const agentLocation = {
          latitude: data.currentLocation.latitude,
          longitude: data.currentLocation.longitude
        };

        const distance = this.calculateDistance(customerLocation, agentLocation);
        
        if (distance <= maxDistance && distance < minDistance) {
          minDistance = distance;
          nearestAgent = {
            id: doc.id,
            name: data.name,
            phone: data.phone,
            currentLocation: agentLocation,
            isOnline: data.isOnline,
            isAvailable: data.isAvailable,
            vehicleType: data.vehicleType,
            rating: data.rating,
            completedDeliveries: data.completedDeliveries
          };
        }
      });

      return nearestAgent;
    } catch (error) {
      console.error('❌ خطأ في العثور على أقرب مندوب:', error);
      return null;
    }
  }

  /**
   * تحسين مسار التوصيل
   */
  static async optimizeDeliveryRoute(
    startLocation: Location,
    deliveryPoints: Location[],
    vehicleType: string = 'car'
  ): Promise<{
    optimizedRoute: Location[];
    totalDistance: number;
    totalDuration: number;
    waypoints: Location[];
  }> {
    try {
      if (!this.GOOGLE_MAPS_API_KEY) {
        throw new Error('مفتاح Google Maps API غير متوفر');
      }

      // استخدام Google Maps Directions API لتحسين المسار
      const waypoints = deliveryPoints.map(point => 
        `${point.latitude},${point.longitude}`
      ).join('|');

      const url = `https://maps.googleapis.com/maps/api/directions/json?` +
        `origin=${startLocation.latitude},${startLocation.longitude}&` +
        `destination=${startLocation.latitude},${startLocation.longitude}&` +
        `waypoints=optimize:true|${waypoints}&` +
        `mode=${this.getGoogleMapsMode(vehicleType)}&` +
        `key=${this.GOOGLE_MAPS_API_KEY}`;

      const response = await fetch(url);
      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`خطأ في Google Maps API: ${data.status}`);
      }

      const route = data.routes[0];
      const optimizedWaypoints = route.waypoint_order.map((index: number) => 
        deliveryPoints[index]
      );

      return {
        optimizedRoute: [startLocation, ...optimizedWaypoints, startLocation],
        totalDistance: route.legs.reduce((total: number, leg: any) => 
          total + leg.distance.value, 0) / 1000, // convert to km
        totalDuration: route.legs.reduce((total: number, leg: any) => 
          total + leg.duration.value, 0) / 60, // convert to minutes
        waypoints: optimizedWaypoints
      };

    } catch (error) {
      console.error('❌ خطأ في تحسين المسار:', error);
      
      // في حالة فشل Google Maps، استخدم خوارزمية بسيطة
      return this.simpleRouteOptimization(startLocation, deliveryPoints);
    }
  }

  /**
   * إنشاء خريطة حرارية للطلبات
   */
  static async generateOrdersHeatmap(
    timeRange: { start: Date; end: Date },
    zoomLevel: number = 12
  ): Promise<HeatmapData[]> {
    try {
      const ordersRef = collection(db, 'orders');
      const q = query(
        ordersRef,
        where('createdAt', '>=', timeRange.start),
        where('createdAt', '<=', timeRange.end),
        where('status', '==', 'completed')
      );

      const snapshot = await getDocs(q);
      const locationCounts: Map<string, HeatmapData> = new Map();

      snapshot.forEach(doc => {
        const data = doc.data();
        const location = data.deliveryLocation;
        
        if (location && location.latitude && location.longitude) {
          // تجميع النقاط القريبة حسب مستوى التكبير
          const gridKey = this.getGridKey(
            location.latitude, 
            location.longitude, 
            zoomLevel
          );

          const existing = locationCounts.get(gridKey);
          if (existing) {
            existing.orderCount++;
            existing.revenue += data.totalAmount || 0;
            existing.intensity = existing.orderCount;
          } else {
            locationCounts.set(gridKey, {
              location: {
                latitude: location.latitude,
                longitude: location.longitude
              },
              intensity: 1,
              orderCount: 1,
              revenue: data.totalAmount || 0,
              timeSlot: this.getTimeSlot(data.createdAt.toDate())
            });
          }
        }
      });

      return Array.from(locationCounts.values());
    } catch (error) {
      console.error('❌ خطأ في إنشاء الخريطة الحرارية:', error);
      return [];
    }
  }

  /**
   * تحليل المناطق الجغرافية
   */
  static async analyzeGeographicAreas(timeRange: { start: Date; end: Date }): Promise<{
    topAreas: Array<{
      area: string;
      orderCount: number;
      revenue: number;
      averageDeliveryTime: number;
    }>;
    demandPatterns: Array<{
      hour: number;
      orderCount: number;
      areas: string[];
    }>;
  }> {
    try {
      const ordersRef = collection(db, 'orders');
      const q = query(
        ordersRef,
        where('createdAt', '>=', timeRange.start),
        where('createdAt', '<=', timeRange.end)
      );

      const snapshot = await getDocs(q);
      const areaStats: Map<string, any> = new Map();
      const hourlyDemand: Map<number, any> = new Map();

      snapshot.forEach(doc => {
        const data = doc.data();
        const area = data.deliveryArea || 'غير محدد';
        const hour = data.createdAt.toDate().getHours();
        const deliveryTime = data.deliveryTime || 0;

        // إحصائيات المناطق
        const areaData = areaStats.get(area) || {
          orderCount: 0,
          revenue: 0,
          totalDeliveryTime: 0
        };
        
        areaData.orderCount++;
        areaData.revenue += data.totalAmount || 0;
        areaData.totalDeliveryTime += deliveryTime;
        areaStats.set(area, areaData);

        // أنماط الطلب حسب الساعة
        const hourData = hourlyDemand.get(hour) || {
          orderCount: 0,
          areas: new Set()
        };
        
        hourData.orderCount++;
        hourData.areas.add(area);
        hourlyDemand.set(hour, hourData);
      });

      // تحويل البيانات للتنسيق المطلوب
      const topAreas = Array.from(areaStats.entries())
        .map(([area, stats]) => ({
          area,
          orderCount: stats.orderCount,
          revenue: stats.revenue,
          averageDeliveryTime: stats.totalDeliveryTime / stats.orderCount
        }))
        .sort((a, b) => b.orderCount - a.orderCount)
        .slice(0, 10);

      const demandPatterns = Array.from(hourlyDemand.entries())
        .map(([hour, data]) => ({
          hour,
          orderCount: data.orderCount,
          areas: Array.from(data.areas)
        }))
        .sort((a, b) => a.hour - b.hour);

      return { topAreas, demandPatterns };
    } catch (error) {
      console.error('❌ خطأ في تحليل المناطق الجغرافية:', error);
      return { topAreas: [], demandPatterns: [] };
    }
  }

  /**
   * إدارة المناطق الجغرافية (Geofencing)
   */
  static async createGeofenceZone(zone: Omit<GeofenceZone, 'id'>): Promise<string> {
    try {
      const zonesRef = collection(db, 'geofence_zones');
      const docRef = await addDoc(zonesRef, {
        ...zone,
        createdAt: serverTimestamp()
      });

      console.log('✅ تم إنشاء منطقة جغرافية:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ خطأ في إنشاء المنطقة الجغرافية:', error);
      throw new Error('فشل في إنشاء المنطقة الجغرافية');
    }
  }

  /**
   * التحقق من وجود نقطة داخل منطقة جغرافية
   */
  static isPointInGeofence(point: Location, zone: GeofenceZone): boolean {
    // خوارزمية Ray Casting للتحقق من وجود النقطة داخل المضلع
    const { latitude: x, longitude: y } = point;
    const vertices = zone.coordinates;
    let inside = false;

    for (let i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
      const xi = vertices[i].latitude;
      const yi = vertices[i].longitude;
      const xj = vertices[j].latitude;
      const yj = vertices[j].longitude;

      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }

    return inside;
  }

  // ===== PRIVATE HELPER METHODS =====

  private static calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private static getGoogleMapsMode(vehicleType: string): string {
    switch (vehicleType) {
      case 'walking': return 'walking';
      case 'bicycle': return 'bicycling';
      case 'motorcycle':
      case 'car':
      default: return 'driving';
    }
  }

  private static simpleRouteOptimization(
    startLocation: Location,
    deliveryPoints: Location[]
  ): {
    optimizedRoute: Location[];
    totalDistance: number;
    totalDuration: number;
    waypoints: Location[];
  } {
    // خوارزمية بسيطة: أقرب نقطة أولاً
    const optimizedWaypoints: Location[] = [];
    const remainingPoints = [...deliveryPoints];
    let currentLocation = startLocation;
    let totalDistance = 0;

    while (remainingPoints.length > 0) {
      let nearestIndex = 0;
      let nearestDistance = Infinity;

      remainingPoints.forEach((point, index) => {
        const distance = this.calculateDistance(currentLocation, point);
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestIndex = index;
        }
      });

      const nearestPoint = remainingPoints.splice(nearestIndex, 1)[0];
      optimizedWaypoints.push(nearestPoint);
      totalDistance += nearestDistance;
      currentLocation = nearestPoint;
    }

    // العودة إلى نقطة البداية
    totalDistance += this.calculateDistance(currentLocation, startLocation);

    return {
      optimizedRoute: [startLocation, ...optimizedWaypoints, startLocation],
      totalDistance,
      totalDuration: totalDistance * 2, // تقدير: 2 دقيقة لكل كيلومتر
      waypoints: optimizedWaypoints
    };
  }

  private static getGridKey(lat: number, lng: number, zoomLevel: number): string {
    const precision = Math.pow(10, Math.max(0, zoomLevel - 10));
    const gridLat = Math.round(lat * precision) / precision;
    const gridLng = Math.round(lng * precision) / precision;
    return `${gridLat},${gridLng}`;
  }

  private static getTimeSlot(date: Date): string {
    const hour = date.getHours();
    if (hour < 6) return 'فجر';
    if (hour < 12) return 'صباح';
    if (hour < 18) return 'ظهر';
    return 'مساء';
  }
}

export default AdvancedMapsService;
export type { 
  Location, 
  DeliveryAgent, 
  DeliveryRoute, 
  HeatmapData, 
  GeofenceZone 
};
