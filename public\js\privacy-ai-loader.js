// محمل النماذج المحلية للخصوصية الكاملة
class PrivacyAILoader {
  constructor() {
    this.loaded = false;
    this.loading = false;
    this.libraries = {
      tesseract: {
        url: 'https://unpkg.com/tesseract.js@v5.1.1/dist/tesseract.min.js',
        size: '~2MB',
        loaded: false
      },
      compromise: {
        url: 'https://unpkg.com/compromise@latest/builds/compromise.min.js',
        size: '~500KB',
        loaded: false
      },
      ml5: {
        url: 'https://unpkg.com/ml5@latest/dist/ml5.min.js',
        size: '~1MB',
        loaded: false
      }
    };
  }

  async loadPrivacyAI() {
    if (this.loaded) return true;
    if (this.loading) return false;

    console.log('🔒 تحميل نظام الذكاء الاصطناعي المحلي للخصوصية...');
    this.loading = true;

    try {
      // تحميل المكتبات بالتوازي
      await Promise.all([
        this.loadScript('tesseract'),
        this.loadScript('compromise'),
        this.loadScript('ml5')
      ]);

      // التحقق من التحميل
      if (window.Tesseract && window.nlp && window.ml5) {
        this.loaded = true;
        console.log('✅ تم تحميل نظام الخصوصية بنجاح');
        
        // إظهار رسالة للمستخدم
        this.showPrivacyMessage();
        
        return true;
      } else {
        throw new Error('فشل في تحميل بعض المكتبات');
      }
    } catch (error) {
      console.error('❌ فشل في تحميل نظام الخصوصية:', error);
      this.loading = false;
      return false;
    }
  }

  async loadScript(libraryName) {
    const lib = this.libraries[libraryName];
    
    return new Promise((resolve, reject) => {
      if (lib.loaded) {
        resolve();
        return;
      }

      console.log(`📥 تحميل ${libraryName} (${lib.size})...`);
      
      const script = document.createElement('script');
      script.src = lib.url;
      script.async = true;
      
      script.onload = () => {
        lib.loaded = true;
        console.log(`✅ تم تحميل ${libraryName}`);
        resolve();
      };
      
      script.onerror = () => {
        console.error(`❌ فشل تحميل ${libraryName}`);
        reject(new Error(`فشل تحميل ${libraryName}`));
      };
      
      document.head.appendChild(script);
    });
  }

  showPrivacyMessage() {
    // إظهار رسالة تأكيد الخصوصية
    const message = document.createElement('div');
    message.className = 'privacy-confirmation';
    message.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 300px;
        font-family: system-ui, -apple-system, sans-serif;
      ">
        <div style="display: flex; align-items: center; gap: 10px;">
          <span style="font-size: 20px;">🔒</span>
          <div>
            <div style="font-weight: bold; margin-bottom: 5px;">
              نظام الخصوصية مفعل
            </div>
            <div style="font-size: 14px; opacity: 0.9;">
              جميع البيانات تُعالج محلياً - لا إرسال خارجي
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(message);
    
    // إخفاء الرسالة بعد 5 ثوانٍ
    setTimeout(() => {
      if (message.parentNode) {
        message.parentNode.removeChild(message);
      }
    }, 5000);
  }

  getLoadingStatus() {
    return {
      loaded: this.loaded,
      loading: this.loading,
      libraries: Object.fromEntries(
        Object.entries(this.libraries).map(([name, lib]) => [
          name, 
          { loaded: lib.loaded, size: lib.size }
        ])
      )
    };
  }
}

// إنشاء مثيل عام
window.privacyAILoader = new PrivacyAILoader();

// دالة سهلة للاستخدام
window.enablePrivacyMode = async function() {
  const success = await window.privacyAILoader.loadPrivacyAI();
  if (success) {
    console.log('🔒 تم تفعيل وضع الخصوصية الكاملة');
    return true;
  } else {
    console.error('❌ فشل في تفعيل وضع الخصوصية');
    return false;
  }
};

// تحميل تلقائي إذا كان مطلوباً
if (localStorage.getItem('privacy_mode_enabled') === 'true') {
  window.enablePrivacyMode();
}
