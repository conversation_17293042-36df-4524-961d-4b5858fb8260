// src/app/[locale]/admin/merchant-approvals/page.tsx
"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useRouter } from 'next/navigation';
import { collection, query, where, getDocs, doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { CheckCircle, XCircle, Clock, FileText, User, Store, Calendar, Brain, Zap } from 'lucide-react';
import type { StoreDocument, UserDocument } from '@/types';
import { AIApprovalService } from '@/services/aiApprovalService';

interface MerchantApplication {
  storeData: StoreDocument;
  userData: UserDocument;
}

export default function MerchantApprovalsPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const router = useRouter();
  const [applications, setApplications] = useState<MerchantApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [aiProcessingId, setAiProcessingId] = useState<string | null>(null);
  const [stats, setStats] = useState({
    pending: 0,
    approved: 0,
    rejected: 0,
    total: 0
  });

  useEffect(() => {
    const checkAdminAccess = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      // التحقق من صلاحيات المدير
      try {
        // التحقق من نوع المستخدم في قاعدة البيانات
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnap = await getDocs(query(
          collection(db, 'users'),
          where('uid', '==', user.uid),
          where('userType', '==', 'admin')
        ));

        if (userDocSnap.empty) {
          console.log('User is not admin, redirecting...');
          router.push('/');
          return;
        }

        // إذا كان المستخدم مدير، جلب الطلبات
        fetchPendingApplications();
      } catch (error) {
        console.error('Error checking admin access:', error);
        router.push('/');
      }
    };

    checkAdminAccess();
  }, [user, router]);

  const fetchPendingApplications = async () => {
    try {
      setLoading(true);

      // جلب إحصائيات جميع المتاجر
      const allStoresQuery = query(collection(db, 'stores'));
      const allStoresSnapshot = await getDocs(allStoresQuery);

      let pendingCount = 0;
      let approvedCount = 0;
      let rejectedCount = 0;

      allStoresSnapshot.docs.forEach(doc => {
        const data = doc.data();
        switch (data.approvalStatus) {
          case 'pending':
            pendingCount++;
            break;
          case 'approved':
            approvedCount++;
            break;
          case 'rejected':
            rejectedCount++;
            break;
        }
      });

      setStats({
        pending: pendingCount,
        approved: approvedCount,
        rejected: rejectedCount,
        total: allStoresSnapshot.docs.length
      });

      // جلب المتاجر في انتظار الموافقة
      const storesQuery = query(
        collection(db, 'stores'),
        where('approvalStatus', '==', 'pending')
      );

      const storesSnapshot = await getDocs(storesQuery);
      const pendingApplications: MerchantApplication[] = [];

      for (const storeDoc of storesSnapshot.docs) {
        const storeData = { id: storeDoc.id, ...storeDoc.data() } as StoreDocument;

        // جلب بيانات المستخدم المقترنة
        const userDoc = await getDocs(query(
          collection(db, 'users'),
          where('uid', '==', storeData.merchantUid)
        ));

        if (!userDoc.empty) {
          const userData = userDoc.docs[0].data() as UserDocument;
          pendingApplications.push({ storeData, userData });
        }
      }

      setApplications(pendingApplications);
    } catch (err) {
      console.error('Error fetching applications:', err);
      setError('حدث خطأ في جلب الطلبات');
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async (merchantUid: string, approved: boolean, notes?: string) => {
    try {
      setProcessingId(merchantUid);

      const storeDocRef = doc(db, 'stores', merchantUid);
      await updateDoc(storeDocRef, {
        approvalStatus: approved ? 'approved' : 'rejected',
        isActive: approved,
        approvalDate: serverTimestamp(),
        approvalNotes: notes || '',
        reviewedBy: user?.uid,
        updatedAt: serverTimestamp(),
      });

      // تحديث الإحصائيات
      setStats(prev => ({
        ...prev,
        pending: prev.pending - 1,
        approved: approved ? prev.approved + 1 : prev.approved,
        rejected: approved ? prev.rejected : prev.rejected + 1
      }));

      // إزالة الطلب من القائمة
      setApplications(prev => prev.filter(app => app.storeData.merchantUid !== merchantUid));

      // إشعار محسن
      const message = approved
        ? 'تم قبول التاجر بنجاح! سيتمكن الآن من الوصول لوحة التحكم.'
        : 'تم رفض طلب التاجر. سيتم إشعاره بالقرار.';

      alert(message);

    } catch (err) {
      console.error('Error processing approval:', err);
      alert('حدث خطأ في معالجة الطلب. يرجى المحاولة مرة أخرى.');
    } finally {
      setProcessingId(null);
    }
  };

  // الموافقة الذكية بالذكاء الاصطناعي
  const handleAIApproval = async (merchantUid: string) => {
    try {
      setAiProcessingId(merchantUid);

      // العثور على بيانات التاجر
      const application = applications.find(app => app.storeData.merchantUid === merchantUid);
      if (!application) {
        alert('لم يتم العثور على بيانات التاجر');
        return;
      }

      // تحليل الطلب بالذكاء الاصطناعي
      const decision = await AIApprovalService.makeAutoApprovalDecision(
        merchantUid,
        application.userData,
        application.storeData
      );

      // تطبيق القرار
      const success = await AIApprovalService.applyAutoApprovalDecision(merchantUid, decision);

      if (success) {
        // تحديث الإحصائيات والقائمة
        if (decision.decision === 'approve') {
          setStats(prev => ({
            ...prev,
            pending: prev.pending - 1,
            approved: prev.approved + 1
          }));
          setApplications(prev => prev.filter(app => app.storeData.merchantUid !== merchantUid));
          alert(`تم قبول التاجر تلقائياً بالذكاء الاصطناعي (ثقة: ${decision.confidence}%)`);
        } else if (decision.decision === 'reject') {
          setStats(prev => ({
            ...prev,
            pending: prev.pending - 1,
            rejected: prev.rejected + 1
          }));
          setApplications(prev => prev.filter(app => app.storeData.merchantUid !== merchantUid));
          alert(`تم رفض التاجر تلقائياً: ${decision.reasons.join(', ')}`);
        } else {
          alert(`يتطلب مراجعة يدوية: ${decision.reasons.join(', ')}`);
        }
      } else {
        alert('فشل في تطبيق قرار الذكاء الاصطناعي');
      }

    } catch (err) {
      console.error('Error in AI approval:', err);
      alert('حدث خطأ في الموافقة الذكية');
    } finally {
      setAiProcessingId(null);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">إدارة موافقات التجار</h1>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-10 w-32" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">إدارة موافقات التجار</h1>
        <p className="text-muted-foreground">
          مراجعة وموافقة طلبات انضمام التجار الجدد
        </p>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-sm text-muted-foreground">في الانتظار</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-sm text-muted-foreground">مقبول</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-sm text-muted-foreground">مرفوض</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Store className="h-8 w-8 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-sm text-muted-foreground">إجمالي الطلبات</p>
          </CardContent>
        </Card>
      </div>

      {applications.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">لا توجد طلبات في الانتظار</h3>
            <p className="text-muted-foreground">
              جميع طلبات التجار تم مراجعتها
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {applications.map((application) => (
            <ApplicationCard
              key={application.storeData.merchantUid}
              application={application}
              onApprove={(notes) => handleApproval(application.storeData.merchantUid, true, notes)}
              onReject={(notes) => handleApproval(application.storeData.merchantUid, false, notes)}
              onAIApproval={() => handleAIApproval(application.storeData.merchantUid)}
              isProcessing={processingId === application.storeData.merchantUid}
              isAIProcessing={aiProcessingId === application.storeData.merchantUid}
            />
          ))}
        </div>
      )}
    </div>
  );
}

interface ApplicationCardProps {
  application: MerchantApplication;
  onApprove: (notes?: string) => void;
  onReject: (notes?: string) => void;
  onAIApproval: () => void;
  isProcessing: boolean;
  isAIProcessing: boolean;
}

function ApplicationCard({ application, onApprove, onReject, onAIApproval, isProcessing, isAIProcessing }: ApplicationCardProps) {
  const [notes, setNotes] = useState('');
  const [showActions, setShowActions] = useState(false);
  const { storeData, userData } = application;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            {storeData.storeName}
          </CardTitle>
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            في الانتظار
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* معلومات التاجر */}
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-semibold flex items-center gap-2">
              <User className="h-4 w-4" />
              معلومات التاجر
            </h4>
            <div className="space-y-2 text-sm">
              <div><strong>الاسم:</strong> {userData.displayName}</div>
              <div><strong>البريد الإلكتروني:</strong> {userData.email}</div>
              <div><strong>تاريخ التقديم:</strong> {new Date(storeData.submittedAt.toDate()).toLocaleDateString('ar-SA')}</div>
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-semibold flex items-center gap-2">
              <FileText className="h-4 w-4" />
              الملفات المرفقة
            </h4>
            <div className="space-y-2">
              {storeData.commercialRegistrationURL && (
                <a 
                  href={storeData.commercialRegistrationURL} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="block text-primary hover:underline text-sm"
                >
                  📄 السجل التجاري
                </a>
              )}
              {storeData.otherLicensesURL && (
                <a 
                  href={storeData.otherLicensesURL} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="block text-primary hover:underline text-sm"
                >
                  📋 التراخيص الأخرى
                </a>
              )}
            </div>
          </div>
        </div>

        {/* ملاحظات */}
        {showActions && (
          <div className="space-y-3">
            <label className="text-sm font-medium">ملاحظات (اختيارية):</label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="أضف ملاحظات حول قرار الموافقة أو الرفض..."
              rows={3}
            />
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="space-y-3">
          {/* زر الموافقة الذكية */}
          <div className="flex gap-3">
            <Button
              onClick={onAIApproval}
              disabled={isProcessing || isAIProcessing}
              className="flex-1 bg-purple-600 hover:bg-purple-700"
              data-testid="ai-approval-button"
            >
              {isAIProcessing ? (
                <>
                  <Zap className="h-4 w-4 mr-2 animate-spin" />
                  جاري التحليل...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  موافقة ذكية بالـ AI
                </>
              )}
            </Button>
          </div>

          {/* أزرار المراجعة اليدوية */}
          <div className="flex gap-3">
            {!showActions ? (
              <Button
                onClick={() => setShowActions(true)}
                variant="outline"
                className="flex-1"
                disabled={isProcessing || isAIProcessing}
              >
                مراجعة يدوية
              </Button>
            ) : (
              <>
                <Button
                  onClick={() => onApprove(notes)}
                  disabled={isProcessing || isAIProcessing}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  قبول
                </Button>
                <Button
                  onClick={() => onReject(notes)}
                  disabled={isProcessing || isAIProcessing}
                  variant="destructive"
                  className="flex-1"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  رفض
                </Button>
                <Button
                  onClick={() => setShowActions(false)}
                  variant="outline"
                  disabled={isProcessing || isAIProcessing}
                >
                  إلغاء
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
