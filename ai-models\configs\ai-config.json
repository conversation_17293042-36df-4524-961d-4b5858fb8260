{"version": "2.0.0", "mode": "cloud-based", "description": "نظام ذكاء اصطناعي متقدم بدون نماذج محلية", "providers": {"google": {"enabled": true, "models": {"gemini-2.0-flash": {"capabilities": ["text-analysis", "document-processing", "ocr", "validation"], "maxTokens": 1000000, "supportedLanguages": ["ar", "en"], "costPerToken": 1e-06}}, "apiEndpoint": "https://generativelanguage.googleapis.com/v1beta", "rateLimits": {"requestsPerMinute": 60, "tokensPerMinute": 32000}}, "openai": {"enabled": false, "models": {"gpt-4-vision": {"capabilities": ["text-analysis", "document-processing", "ocr"], "maxTokens": 128000, "supportedLanguages": ["ar", "en"]}}}, "huggingface": {"enabled": false, "note": "للاستخدام المستقبلي مع نماذج مخصصة"}}, "processing": {"textAnalysis": {"provider": "google", "model": "gemini-2.0-flash", "confidence": 0.85, "features": ["entity-extraction", "text-similarity", "document-classification", "arabic-ner"]}, "ocr": {"provider": "google", "model": "gemini-2.0-flash", "confidence": 0.8, "languages": ["ar", "en"], "features": ["text-extraction", "layout-analysis", "table-detection"]}, "validation": {"provider": "google", "model": "gemini-2.0-flash", "confidence": 0.9, "features": ["document-validation", "fraud-detection", "format-verification", "content-consistency"]}}, "documentTemplates": {"commercial_registration": {"requiredFields": ["businessName", "ownerName", "registrationNumber", "issueDate", "expiryDate", "businessActivity"], "validationRules": {"registrationNumber": {"pattern": "^\\d{10}$", "description": "رقم السجل التجاري يجب أن يكون 10 أرقام"}, "businessName": {"minLength": 3, "maxLength": 100, "description": "اسم المنشأة يجب أن يكون بين 3-100 حرف"}}, "aiPrompts": {"extraction": "استخرج المعلومات التالية من السجل التجاري: اسم المنشأة، اسم التاجر، رقم السجل، تاريخ الإصدار، تاريخ الانتهاء، النشاط التجاري", "validation": "تحقق من صحة البيانات المستخرجة وتأكد من عدم وجود تناقضات"}}, "freelance_document": {"requiredFields": ["ownerName", "documentNumber", "issueDate", "expiryDate", "activityType"], "validationRules": {"documentNumber": {"pattern": "^[A-Z0-9]{8,12}$", "description": "رقم وثيقة العمل الحر"}}, "aiPrompts": {"extraction": "استخرج معلومات وثيقة العمل الحر: اسم صاحب الوثيقة، رقم الوثيقة، تاريخ الإصدار، تاريخ الانتهاء، نوع النشاط", "validation": "تحقق من صحة وثيقة العمل الحر وتواريخها"}}, "driving_license": {"requiredFields": ["<PERSON><PERSON><PERSON>", "licenseNumber", "issueDate", "expiryDate", "licenseClass"], "validationRules": {"licenseNumber": {"pattern": "^\\d{10}$", "description": "رقم رخصة القيادة"}}, "aiPrompts": {"extraction": "استخرج معلومات رخصة القيادة: اسم حامل الرخصة، رقم الرخصة، تاريخ الإصدار، تاريخ الانتهاء، فئة الرخصة", "validation": "تحقق من صحة رخصة القيادة وصلاحيتها"}}}, "arabicVocabulary": {"commonWords": ["اسم", "رقم", "تاريخ", "مكان", "شركة", "مؤسسة", "سجل", "تجاري", "هوية", "رخصة", "قيادة", "فحص"], "entities": {"PERSON": ["اسم", "صاحب", "مالك", "مدير", "<PERSON>ا<PERSON><PERSON>"], "ORG": ["شركة", "مؤسسة", "منشأة", "مكتب", "<PERSON><PERSON><PERSON>"], "LOC": ["مدينة", "منطقة", "حي", "شارع", "عنوان"], "DATE": ["تاريخ", "يوم", "شهر", "سنة", "إصدار", "انتهاء"]}, "abbreviations": {"ش.م.م": "شركة مساهمة مقفلة", "ذ.م.م": "ذات مسؤولية محدودة", "م.ت": "مؤسسة تجارية", "ت.ب": "تاجر بسيط"}}, "security": {"dataEncryption": true, "localProcessing": false, "dataRetention": "none", "privacyMode": "strict", "auditLogging": true}, "performance": {"caching": {"enabled": true, "ttl": 3600, "maxSize": "50MB"}, "batching": {"enabled": true, "maxBatchSize": 10, "timeout": 30000}, "retries": {"maxAttempts": 3, "backoffMs": 1000}}}