// src/hooks/useNotifications.ts
"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { notificationService, type NotificationData, type NotificationSettings } from '@/services/notificationService';
import { collection, query, where, onSnapshot, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface UseNotificationsReturn {
  notifications: NotificationData[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  requestPermission: () => Promise<boolean>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refetch: () => void;
}

export function useNotifications(limitCount: number = 20): UseNotificationsReturn {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // طلب إذن الإشعارات
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!user) return false;

    try {
      const token = await notificationService.requestPermission(user.uid);
      return !!token;
    } catch (err) {
      console.error('Error requesting notification permission:', err);
      return false;
    }
  }, [user]);

  // تحديد إشعار كمقروء
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      
      // تحديث الحالة المحلية
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, read: true }
            : notification
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError('فشل في تحديث حالة الإشعار');
    }
  }, []);

  // تحديد جميع الإشعارات كمقروءة
  const markAllAsRead = useCallback(async () => {
    if (!user) return;

    try {
      await notificationService.markAllAsRead(user.uid);
      
      // تحديث الحالة المحلية
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      );
      
      setUnreadCount(0);
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError('فشل في تحديث حالة الإشعارات');
    }
  }, [user]);

  // جلب الإشعارات
  const fetchNotifications = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const userNotifications = await notificationService.getUserNotifications(user.uid, limitCount);
      setNotifications(userNotifications);
      
      // حساب عدد الإشعارات غير المقروءة
      const unread = userNotifications.filter(n => !n.read).length;
      setUnreadCount(unread);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('فشل في جلب الإشعارات');
    } finally {
      setLoading(false);
    }
  }, [user, limitCount]);

  // الاستماع للإشعارات في الوقت الفعلي
  useEffect(() => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      setLoading(false);
      return;
    }

    const q = query(
      collection(db, 'notifications'),
      where('userId', '==', user.uid),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const unsubscribe = onSnapshot(q, 
      (querySnapshot) => {
        const userNotifications: NotificationData[] = [];
        
        querySnapshot.forEach((doc) => {
          userNotifications.push({
            id: doc.id,
            ...doc.data()
          } as NotificationData);
        });

        setNotifications(userNotifications);
        
        // حساب عدد الإشعارات غير المقروءة
        const unread = userNotifications.filter(n => !n.read).length;
        setUnreadCount(unread);
        setLoading(false);
        setError(null);
      },
      (err) => {
        console.error('Error listening to notifications:', err);
        setError('فشل في الاستماع للإشعارات');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user, limitCount]);

  // الاستماع للرسائل الواردة
  useEffect(() => {
    const unsubscribe = notificationService.onMessage((payload) => {
      console.log('Message received:', payload);
      
      // يمكن إضافة منطق إضافي هنا مثل عرض toast notification
      if (payload.notification) {
        // عرض إشعار في المتصفح إذا كانت الصفحة مفتوحة
        if (document.visibilityState === 'visible') {
          // يمكن استخدام toast هنا
          console.log('New notification:', payload.notification);
        }
      }
    });

    return unsubscribe;
  }, []);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    requestPermission,
    markAsRead,
    markAllAsRead,
    refetch: fetchNotifications
  };
}

// Hook لإعدادات الإشعارات
interface UseNotificationSettingsReturn {
  settings: NotificationSettings | null;
  loading: boolean;
  error: string | null;
  updateSettings: (newSettings: Partial<NotificationSettings>) => Promise<void>;
}

export function useNotificationSettings(): UseNotificationSettingsReturn {
  const { user } = useAuth();
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تحديث الإعدادات
  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>) => {
    if (!user || !settings) return;

    try {
      const updatedSettings = { ...settings, ...newSettings };
      await notificationService.saveNotificationSettings(updatedSettings);
      setSettings(updatedSettings);
    } catch (err) {
      console.error('Error updating notification settings:', err);
      setError('فشل في تحديث إعدادات الإشعارات');
    }
  }, [user, settings]);

  // جلب الإعدادات
  useEffect(() => {
    if (!user) {
      setSettings(null);
      setLoading(false);
      return;
    }

    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const userSettings = await notificationService.getNotificationSettings(user.uid);
        setSettings(userSettings);
      } catch (err) {
        console.error('Error fetching notification settings:', err);
        setError('فشل في جلب إعدادات الإشعارات');
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [user]);

  return {
    settings,
    loading,
    error,
    updateSettings
  };
}

// Hook لإرسال الإشعارات (للمدراء والنظام)
interface UseSendNotificationReturn {
  sendNotification: (notificationData: Omit<NotificationData, 'id' | 'createdAt'>) => Promise<string | null>;
  sending: boolean;
  error: string | null;
}

export function useSendNotification(): UseSendNotificationReturn {
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendNotification = useCallback(async (notificationData: Omit<NotificationData, 'id' | 'createdAt'>): Promise<string | null> => {
    try {
      setSending(true);
      setError(null);
      
      const notificationId = await notificationService.sendNotification(notificationData);
      return notificationId;
    } catch (err) {
      console.error('Error sending notification:', err);
      setError('فشل في إرسال الإشعار');
      return null;
    } finally {
      setSending(false);
    }
  }, []);

  return {
    sendNotification,
    sending,
    error
  };
}
