#!/usr/bin/env node

/**
 * سكريبت إزالة المفاتيح المكررة الفعلية من ملفات الترجمة
 * يقوم بإزالة المفاتيح المكررة مع الاحتفاظ بالقيمة الأولى
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'en.json');

/**
 * إزالة المفاتيح المكررة من كائن JSON بشكل تكراري
 */
function removeDuplicatesFromObject(obj, path = '', removedKeys = []) {
  if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
    return obj;
  }

  const cleanedObj = {};
  const seenKeys = new Set();

  for (const [key, value] of Object.entries(obj)) {
    const fullPath = path ? `${path}.${key}` : key;
    
    if (seenKeys.has(key)) {
      console.log(`   ❌ إزالة مفتاح مكرر: "${fullPath}"`);
      removedKeys.push(fullPath);
      continue;
    }
    
    seenKeys.add(key);
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      cleanedObj[key] = removeDuplicatesFromObject(value, fullPath, removedKeys);
    } else {
      cleanedObj[key] = value;
    }
  }

  return cleanedObj;
}

/**
 * معالجة ملف ترجمة واحد
 */
function processTranslationFile(filePath) {
  console.log(`🔧 معالجة الملف: ${filePath}`);
  
  try {
    // قراءة الملف
    const content = fs.readFileSync(filePath, 'utf8');
    const jsonData = JSON.parse(content);
    
    // إنشاء نسخة احتياطية
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    
    // إزالة المفاتيح المكررة
    const removedKeys = [];
    const cleanedData = removeDuplicatesFromObject(jsonData, '', removedKeys);
    
    // كتابة الملف المنظف
    const cleanedContent = JSON.stringify(cleanedData, null, 2);
    fs.writeFileSync(filePath, cleanedContent);
    
    console.log(`   ✅ تم إزالة ${removedKeys.length} مفتاح مكرر`);
    if (removedKeys.length > 0) {
      console.log(`   📝 المفاتيح المحذوفة: ${removedKeys.join(', ')}`);
    }
    
    // التحقق من صحة JSON
    try {
      JSON.parse(fs.readFileSync(filePath, 'utf8'));
      console.log(`   ✅ الملف صحيح بعد التنظيف`);
      return { success: true, duplicatesRemoved: removedKeys.length, removedKeys };
    } catch (error) {
      console.error(`   ❌ الملف غير صحيح بعد التنظيف:`, error.message);
      // استعادة النسخة الاحتياطية
      fs.writeFileSync(filePath, content);
      console.log(`   🔄 تم استعادة النسخة الاحتياطية`);
      return { success: false, duplicatesRemoved: 0 };
    }
    
  } catch (error) {
    console.error(`   ❌ خطأ في معالجة الملف:`, error.message);
    return { success: false, duplicatesRemoved: 0 };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧹 بدء إزالة المفاتيح المكررة الفعلية من ملفات الترجمة...\n');
  
  let totalSuccess = 0;
  let totalDuplicatesRemoved = 0;
  
  // معالجة الملف العربي
  console.log('📝 معالجة الملف العربي:');
  const arResult = processTranslationFile(AR_TRANSLATIONS_PATH);
  if (arResult.success) {
    totalSuccess++;
    totalDuplicatesRemoved += arResult.duplicatesRemoved;
  }
  
  console.log('');
  
  // معالجة الملف الإنجليزي
  console.log('📝 معالجة الملف الإنجليزي:');
  const enResult = processTranslationFile(EN_TRANSLATIONS_PATH);
  if (enResult.success) {
    totalSuccess++;
    totalDuplicatesRemoved += enResult.duplicatesRemoved;
  }
  
  console.log('');
  
  if (totalSuccess === 2) {
    console.log('🎉 تم إزالة جميع المفاتيح المكررة بنجاح!');
    console.log(`📊 إجمالي المفاتيح المكررة المحذوفة: ${totalDuplicatesRemoved}`);
    console.log('💡 تم إنشاء نسخ احتياطية من الملفات');
    
    // تشغيل التحقق التلقائي
    console.log('\n🔍 تشغيل التحقق التلقائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في معالجة بعض الملفات');
    console.log(`📊 الملفات المعالجة بنجاح: ${totalSuccess}/2`);
    console.log('🔄 يمكنك استعادة النسخ الاحتياطية إذا لزم الأمر');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  removeDuplicatesFromObject,
  processTranslationFile
};
