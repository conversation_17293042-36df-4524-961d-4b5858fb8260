describe('اختبار التدفق البصري لإنشاء الحساب مع تبديل اللغة', () => {
  beforeEach(() => {
    // بدء من الصفحة الرئيسية
    cy.visit('http://localhost:9002')
    cy.wait(3000)
  })

  it('التدفق الكامل: النقر على إنشاء حساب → اختيار النوع → تبديل اللغة مع التقاط الصور', () => {
    // الخطوة 1: التقاط صورة للصفحة الرئيسية
    cy.screenshot('01-homepage-initial')
    
    // الخطوة 2: البحث عن زر أو رابط "إنشاء حساب" والنقر عليه
    cy.get('body').then(($body) => {
      // البحث عن رابط إنشاء حساب
      if ($body.find('a:contains("إنشاء حساب")').length > 0) {
        cy.get('a:contains("إنشاء حساب")').first().click()
      } else if ($body.find('button:contains("إنشاء حساب")').length > 0) {
        cy.get('button:contains("إنشاء حساب")').first().click()
      } else if ($body.find('a[href*="signup"]').length > 0) {
        cy.get('a[href*="signup"]').first().click()
      } else if ($body.find('a:contains("تسجيل")').length > 0) {
        cy.get('a:contains("تسجيل")').first().click()
      } else {
        // إذا لم نجد الرابط، ننتقل مباشرة
        cy.visit('http://localhost:9002/ar/signup')
      }
    })
    
    cy.wait(3000)
    cy.screenshot('02-after-clicking-signup')
    
    // الخطوة 3: التحقق من وصولنا لصفحة اختيار نوع المستخدم أو التسجيل
    cy.url().then((url) => {
      if (url.includes('user-type-selection')) {
        cy.log('✅ وصلنا لصفحة اختيار نوع المستخدم')
        cy.screenshot('03-user-type-selection-page')
        
        // اختيار نوع التاجر
        cy.get('body').then(($body) => {
          if ($body.find('button:contains("تاجر")').length > 0) {
            cy.get('button:contains("تاجر")').first().click()
          } else if ($body.find('[data-testid*="merchant"]').length > 0) {
            cy.get('[data-testid*="merchant"]').first().click()
          } else if ($body.find('input[value="merchant"]').length > 0) {
            cy.get('input[value="merchant"]').click()
          }
        })
        
        cy.wait(2000)
        cy.screenshot('04-after-selecting-merchant')
        
      } else if (url.includes('signup')) {
        cy.log('✅ وصلنا لصفحة التسجيل مباشرة')
        cy.screenshot('03-signup-page-direct')
        
        // اختيار نوع المستخدم في صفحة التسجيل
        cy.get('body').then(($body) => {
          if ($body.find('input[value="merchant"]').length > 0) {
            cy.get('input[value="merchant"]').click()
          } else if ($body.find('button:contains("تاجر")').length > 0) {
            cy.get('button:contains("تاجر")').first().click()
          }
        })
        
        cy.wait(2000)
        cy.screenshot('04-after-selecting-merchant-in-signup')
      }
    })
    
    // الخطوة 4: التقاط صورة قبل تبديل اللغة
    cy.screenshot('05-before-language-switch')
    
    // الخطوة 5: البحث عن زر تبديل اللغة والنقر عليه
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.log('🔄 وجدت زر EN - سأنقر عليه')
        cy.get('button:contains("EN")').first().click()
      } else if ($body.find('[data-testid="language-switcher"]').length > 0) {
        cy.log('🔄 وجدت زر تبديل اللغة - سأنقر عليه')
        cy.get('[data-testid="language-switcher"]').click()
      } else if ($body.find('button[aria-label*="language"]').length > 0) {
        cy.get('button[aria-label*="language"]').first().click()
      } else {
        cy.log('❌ لم أجد زر تبديل اللغة')
      }
    })
    
    // الخطوة 6: انتظار وتسجيل ما يحدث أثناء التبديل
    cy.wait(1000)
    cy.screenshot('06-during-language-switch-1sec')
    
    cy.wait(2000)
    cy.screenshot('07-during-language-switch-3sec')
    
    cy.wait(3000)
    cy.screenshot('08-after-language-switch-6sec')
    
    // الخطوة 7: التحقق من تغيير اللغة
    cy.url().then((url) => {
      if (url.includes('/en/')) {
        cy.log('✅ تم تبديل اللغة إلى الإنجليزية')
        cy.screenshot('09-language-switched-to-english')
      } else {
        cy.log('❌ لم يتم تبديل اللغة')
        cy.screenshot('09-language-switch-failed')
      }
    })
    
    // الخطوة 8: التقاط صورة نهائية للحالة
    cy.wait(2000)
    cy.screenshot('10-final-state')
    
    // الخطوة 9: محاولة العودة للعربية لرؤية التأثير
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("ع")').length > 0) {
        cy.log('🔄 سأعود للعربية')
        cy.get('button:contains("ع")').first().click()
        cy.wait(3000)
        cy.screenshot('11-back-to-arabic')
      }
    })
  })

  it('اختبار تبديل اللغة مع نوع مندوب', () => {
    // الانتقال لصفحة اختيار نوع المستخدم
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(3000)
    cy.screenshot('01-representative-user-type-page')
    
    // اختيار نوع المندوب
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("مندوب")').length > 0) {
        cy.get('button:contains("مندوب")').first().click()
      } else if ($body.find('[data-testid*="representative"]').length > 0) {
        cy.get('[data-testid*="representative"]').first().click()
      }
    })
    
    cy.wait(2000)
    cy.screenshot('02-representative-selected')
    
    // تبديل اللغة
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.get('button:contains("EN")').first().click()
        cy.wait(1000)
        cy.screenshot('03-rep-language-switch-1sec')
        cy.wait(2000)
        cy.screenshot('04-rep-language-switch-3sec')
        cy.wait(3000)
        cy.screenshot('05-rep-final-state')
      }
    })
  })

  it('اختبار التشتت البصري - تسجيل مفصل', () => {
    // بدء من صفحة اختيار نوع المستخدم
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(5000)
    cy.screenshot('visual-test-01-initial-page')
    
    // اختيار تاجر
    cy.contains('تاجر').click()
    cy.wait(1000)
    cy.screenshot('visual-test-02-merchant-selected')
    
    // النقر على تبديل اللغة ومراقبة التغييرات
    cy.get('button:contains("EN")').click()
    
    // التقاط صور متتالية لرؤية التشتت
    for (let i = 0; i < 10; i++) {
      cy.wait(500)
      cy.screenshot(`visual-test-03-transition-${i + 1}`)
    }
    
    // صورة نهائية
    cy.wait(2000)
    cy.screenshot('visual-test-04-final-result')
  })
})
