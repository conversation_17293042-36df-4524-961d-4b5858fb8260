#!/usr/bin/env node

/**
 * سكريبت متقدم لإصلاح المفاتيح المكررة في ملفات الترجمة
 * يحافظ على أحدث قيمة ويحذف المكررات
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  fs.copyFileSync(filePath, backupPath);
  console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
  return backupPath;
}

/**
 * تحليل المفاتيح المكررة في الملف
 */
function analyzeDuplicateKeys(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const keyOccurrences = {};
  const duplicates = [];

  lines.forEach((line, index) => {
    const match = line.match(/^\s*"([^"]+)"\s*:/);
    if (match) {
      const key = match[1];
      if (keyOccurrences[key]) {
        keyOccurrences[key].push({
          line: index + 1,
          content: line.trim(),
          index: index
        });
      } else {
        keyOccurrences[key] = [{
          line: index + 1,
          content: line.trim(),
          index: index
        }];
      }
    }
  });

  Object.keys(keyOccurrences).forEach(key => {
    if (keyOccurrences[key].length > 1) {
      duplicates.push({
        key,
        occurrences: keyOccurrences[key]
      });
    }
  });

  return { duplicates, lines };
}

/**
 * إصلاح المفاتيح المكررة بطريقة متقدمة عبر معالجة JSON مباشرة
 */
function fixDuplicateKeys(filePath) {
  console.log(`\n🔧 إصلاح المفاتيح المكررة في: ${path.basename(filePath)}`);

  // إنشاء نسخة احتياطية
  createBackup(filePath);

  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // تحليل المفاتيح المكررة
    const { duplicates } = analyzeDuplicateKeys(filePath);

    if (duplicates.length === 0) {
      console.log('✅ لا توجد مفاتيح مكررة في هذا الملف');
      return [];
    }

    console.log(`📊 تم العثور على ${duplicates.length} مفتاح مكرر`);

    // تحليل JSON وإزالة المفاتيح المكررة
    const jsonData = JSON.parse(content);
    const fixedDuplicates = [];

    // معالجة كل مفتاح مكرر
    duplicates.forEach(duplicate => {
      const { key, occurrences } = duplicate;
      console.log(`\n🔍 معالجة المفتاح المكرر: "${key}"`);

      // عرض جميع التكرارات
      occurrences.forEach((occurrence, index) => {
        console.log(`   ${index + 1}. السطر ${occurrence.line}: ${occurrence.content}`);
      });

      // الاحتفاظ بآخر قيمة فقط
      const keepIndex = occurrences.length - 1;
      console.log(`✅ سيتم الاحتفاظ بالتكرار رقم ${keepIndex + 1} (السطر ${occurrences[keepIndex].line})`);

      // حذف التكرارات الأخرى
      for (let i = 0; i < occurrences.length - 1; i++) {
        console.log(`❌ سيتم حذف السطر ${occurrences[i].line}`);
      }

      fixedDuplicates.push({
        key,
        removedCount: occurrences.length - 1,
        keptLine: occurrences[keepIndex].line
      });
    });

    // إعادة كتابة الملف بتنسيق JSON صحيح
    const cleanedContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync(filePath, cleanedContent, 'utf8');

    console.log(`\n✅ تم إصلاح ${fixedDuplicates.length} مفتاح مكرر`);
    console.log(`📝 تم إعادة تنسيق الملف بشكل صحيح`);

    return fixedDuplicates;

  } catch (error) {
    console.error(`❌ خطأ في إصلاح الملف ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * إضافة الترجمات المفقودة الأساسية
 */
function addMissingBasicTranslations() {
  console.log('\n🔧 إضافة الترجمات المفقودة الأساسية...');
  
  // الترجمات الأساسية للدفع والشراء (أولوية عالية)
  const basicCheckoutTranslations = {
    ar: {
      "checkout": "الدفع",
      "customerInformation": "معلومات العميل",
      "shippingAddress": "عنوان الشحن",
      "paymentMethod": "طريقة الدفع",
      "orderSummary": "ملخص الطلب",
      "placeOrder": "تأكيد الطلب",
      "advancedSearch": "البحث المتقدم",
      "searchFilters": "فلاتر البحث",
      "sortBy": "ترتيب حسب",
      "filterByCategory": "تصفية حسب الفئة",
      "priceRange": "نطاق السعر",
      "storesFound": "تم العثور على {{count}} متجر"
    },
    en: {
      "checkout": "Checkout",
      "customerInformation": "Customer Information",
      "shippingAddress": "Shipping Address",
      "paymentMethod": "Payment Method",
      "orderSummary": "Order Summary",
      "placeOrder": "Place Order",
      "advancedSearch": "Advanced Search",
      "searchFilters": "Search Filters",
      "sortBy": "Sort By",
      "filterByCategory": "Filter by Category",
      "priceRange": "Price Range",
      "storesFound": "Found {{count}} stores"
    }
  };
  
  // إضافة الترجمات للملف العربي
  try {
    const arContent = fs.readFileSync(AR_TRANSLATIONS_PATH, 'utf8');
    const arTranslations = JSON.parse(arContent);
    
    let addedCount = 0;
    Object.entries(basicCheckoutTranslations.ar).forEach(([key, value]) => {
      if (!arTranslations[key]) {
        arTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للعربية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(AR_TRANSLATIONS_PATH, JSON.stringify(arTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة عربية جديدة`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات العربية:', error.message);
  }
  
  // إضافة الترجمات للملف الإنجليزي
  try {
    const enContent = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const enTranslations = JSON.parse(enContent);
    
    let addedCount = 0;
    Object.entries(basicCheckoutTranslations.en).forEach(([key, value]) => {
      if (!enTranslations[key]) {
        enTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للإنجليزية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(EN_TRANSLATIONS_PATH, JSON.stringify(enTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة إنجليزية جديدة`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات الإنجليزية:', error.message);
  }
}

/**
 * إصلاح مشكلة المتغيرات
 */
function fixVariableIssues() {
  console.log('\n🔧 إصلاح مشاكل المتغيرات...');
  
  try {
    const enContent = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const enTranslations = JSON.parse(enContent);
    
    // إصلاح مشكلة freeDeliveryNotice
    if (enTranslations.freeDeliveryNotice === "Free delivery on orders over 100 SAR") {
      enTranslations.freeDeliveryNotice = "Add {{amount}} SAR more for free delivery";
      fs.writeFileSync(EN_TRANSLATIONS_PATH, JSON.stringify(enTranslations, null, 2), 'utf8');
      console.log('✅ تم إصلاح مشكلة المتغير في freeDeliveryNotice');
    }
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح المتغيرات:', error.message);
  }
}

/**
 * إنشاء تقرير مفصل عن الإصلاحات
 */
function generateReport(arFixed, enFixed) {
  const reportPath = path.join(__dirname, '../docs/duplicate-fix-report.md');
  const timestamp = new Date().toLocaleString('ar-SA');

  let report = `# تقرير إصلاح المفاتيح المكررة\n\n`;
  report += `**تاريخ الإصلاح**: ${timestamp}\n\n`;

  report += `## ملخص الإصلاحات\n\n`;
  report += `- **الملف العربي**: ${arFixed.length} مفتاح مكرر تم إصلاحه\n`;
  report += `- **الملف الإنجليزي**: ${enFixed.length} مفتاح مكرر تم إصلاحه\n`;
  report += `- **إجمالي المفاتيح المُصلحة**: ${arFixed.length + enFixed.length}\n\n`;

  if (arFixed.length > 0) {
    report += `## المفاتيح المُصلحة في الملف العربي\n\n`;
    arFixed.forEach((fix, index) => {
      report += `${index + 1}. **${fix.key}**: حُذف ${fix.removedCount} تكرار، احتُفظ بالسطر ${fix.keptLine}\n`;
    });
    report += `\n`;
  }

  if (enFixed.length > 0) {
    report += `## المفاتيح المُصلحة في الملف الإنجليزي\n\n`;
    enFixed.forEach((fix, index) => {
      report += `${index + 1}. **${fix.key}**: حُذف ${fix.removedCount} تكرار، احتُفظ بالسطر ${fix.keptLine}\n`;
    });
    report += `\n`;
  }

  report += `## التوصيات\n\n`;
  report += `1. تشغيل سكريبت التحقق للتأكد من الإصلاحات: \`node scripts/validate-translations.js\`\n`;
  report += `2. اختبار التطبيق للتأكد من عمل الترجمات بشكل صحيح\n`;
  report += `3. مراجعة النسخ الاحتياطية في حالة الحاجة للتراجع\n\n`;

  fs.writeFileSync(reportPath, report, 'utf8');
  console.log(`📄 تم إنشاء تقرير مفصل: ${path.basename(reportPath)}`);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء إصلاح المفاتيح المكررة المتقدم...\n');

  try {
    // إصلاح المفاتيح المكررة في كلا الملفين
    const arFixed = fixDuplicateKeys(AR_TRANSLATIONS_PATH);
    const enFixed = fixDuplicateKeys(EN_TRANSLATIONS_PATH);

    // إضافة الترجمات المفقودة الأساسية
    addMissingBasicTranslations();

    // إصلاح مشاكل المتغيرات
    fixVariableIssues();

    // إنشاء تقرير مفصل
    generateReport(arFixed, enFixed);

    // ملخص النتائج
    console.log('\n📋 ملخص الإصلاحات:');
    console.log(`✅ الملف العربي: ${arFixed.length} مفتاح مكرر تم إصلاحه`);
    console.log(`✅ الملف الإنجليزي: ${enFixed.length} مفتاح مكرر تم إصلاحه`);
    console.log(`✅ إجمالي المفاتيح المُصلحة: ${arFixed.length + enFixed.length}`);
    console.log('✅ تم إضافة الترجمات الأساسية المفقودة');
    console.log('✅ تم إصلاح مشاكل المتغيرات');

    if (arFixed.length > 0 || enFixed.length > 0) {
      console.log('\n🎉 تم الانتهاء من إصلاح المفاتيح المكررة بنجاح!');
      console.log('💡 يُنصح بتشغيل سكريبت التحقق للتأكد من الإصلاحات:');
      console.log('   node scripts/validate-translations.js');
    } else {
      console.log('\n✨ ممتاز! لا توجد مفاتيح مكررة تحتاج إصلاح');
    }

  } catch (error) {
    console.error('\n❌ فشل في تنفيذ الإصلاحات:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  fixDuplicateKeys,
  analyzeDuplicateKeys,
  addMissingBasicTranslations,
  fixVariableIssues,
  generateReport,
  createBackup
};
