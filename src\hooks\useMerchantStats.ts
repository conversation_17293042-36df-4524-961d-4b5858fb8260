// src/hooks/useMerchantStats.ts
"use client";

import { useState, useEffect } from 'react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/context/AuthContext';
import type { MerchantStats, ProductDocument, OrderDocument } from '@/types';

export function useMerchantStats() {
  const { user } = useAuth();
  const [stats, setStats] = useState<MerchantStats>({
    totalProducts: 0,
    activeProducts: 0,
    totalOrders: 0,
    newOrders: 0,
    monthlySales: 0,
    totalRevenue: 0,
    averageRating: 0,
    reviewCount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user?.uid) {
      setLoading(false);
      return;
    }

    const fetchMerchantStats = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch products stats
        let products: ProductDocument[] = [];
        try {
          const productsQuery = query(
            collection(db, 'products'),
            where('merchantUid', '==', user.uid)
          );
          const productsSnapshot = await getDocs(productsQuery);
          products = productsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as ProductDocument[];
        } catch (productsError) {
          console.log('Products collection not found or empty, using default values');
        }

        const totalProducts = products.length;
        const activeProducts = products.filter(p => p.isActive).length;

        // Calculate average rating from products
        const productsWithRating = products.filter(p => p.averageRating && p.averageRating > 0);
        const averageRating = productsWithRating.length > 0 
          ? productsWithRating.reduce((sum, p) => sum + (p.averageRating || 0), 0) / productsWithRating.length
          : 0;
        
        const reviewCount = products.reduce((sum, p) => sum + (p.reviewCount || 0), 0);

        // Fetch orders stats (placeholder - orders collection might not exist yet)
        let totalOrders = 0;
        let newOrders = 0;
        let monthlySales = 0;
        let totalRevenue = 0;

        try {
          const ordersQuery = query(
            collection(db, 'orders'),
            where('merchantUid', '==', user.uid)
          );
          const ordersSnapshot = await getDocs(ordersQuery);
          const orders = ordersSnapshot.docs.map(doc => ({ 
            id: doc.id, 
            ...doc.data() 
          })) as OrderDocument[];

          totalOrders = orders.length;
          
          // Count new orders (pending status)
          newOrders = orders.filter(order => order.status === 'pending').length;
          
          // Calculate total revenue
          totalRevenue = orders
            .filter(order => order.status !== 'cancelled')
            .reduce((sum, order) => sum + order.totalAmount, 0);

          // Calculate monthly sales (current month)
          const currentMonth = new Date().getMonth();
          const currentYear = new Date().getFullYear();
          
          monthlySales = orders
            .filter(order => {
              const orderDate = order.createdAt.toDate();
              return orderDate.getMonth() === currentMonth && 
                     orderDate.getFullYear() === currentYear &&
                     order.status !== 'cancelled';
            })
            .reduce((sum, order) => sum + order.totalAmount, 0);

        } catch (ordersError) {
          // Orders collection might not exist yet, which is fine
          console.log('Orders collection not found or empty, using default values');
        }

        setStats({
          totalProducts,
          activeProducts,
          totalOrders,
          newOrders,
          monthlySales,
          totalRevenue,
          averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
          reviewCount,
        });

      } catch (err) {
        console.error('Error fetching merchant stats:', err);
        setError('Failed to fetch merchant statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchMerchantStats();
  }, [user?.uid]);

  const refreshStats = async () => {
    if (!user?.uid) return;
    
    setLoading(true);
    // Re-trigger the effect by updating a dependency
    // In a real app, you might want to extract the fetch logic to a separate function
    window.location.reload(); // Simple refresh for now
  };

  return {
    stats,
    loading,
    error,
    refreshStats,
  };
}
