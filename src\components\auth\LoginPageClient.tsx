// src/components/auth/LoginPageClient.tsx
"use client";

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Loader2, ShieldCheck } from 'lucide-react';
import type { Locale } from '@/lib/i18n';
import AuthErrorHandler from './AuthErrorHandler';
import { determineUserRedirectPath, executeRedirectWithFallback } from '@/utils/authRedirect';

interface LoginPageClientProps {
  locale: Locale;
  children: React.ReactNode;
}

export default function LoginPageClient({ locale, children }: LoginPageClientProps) {
  const { user, loading, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const { t } = useLocale();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [redirectError, setRedirectError] = useState<string | null>(null);

  useEffect(() => {
    if (initialLoadingCompleted && user) {
      setIsRedirecting(true);

      // User is already logged in, redirect to appropriate dashboard
      const redirectUser = async () => {
        try {
          const result = await determineUserRedirectPath(user, locale);

          if (result.success && result.redirectPath) {
            const clearFallback = executeRedirectWithFallback(result.redirectPath, router);

            // مسح fallback timeout بعد 5 ثوان (إذا نجح التوجيه)
            setTimeout(clearFallback, 5000);
          } else {
            throw new Error(result.error || 'Failed to determine redirect path');
          }
        } catch (error) {
          console.error("Error during redirect:", error);
          setRedirectError(t('authenticationErrorMessage'));
          setIsRedirecting(false);

          // آلية fallback: إعادة تحميل الصفحة بعد 3 ثوان
          setTimeout(() => {
            window.location.href = `/${locale}/dashboard`;
          }, 3000);
        }
      };

      // تقليل التأخير وإضافة آلية fallback
      const timeoutId = setTimeout(redirectUser, 800);

      // آلية fallback إضافية: إذا لم يحدث التوجيه خلال 10 ثوان
      const fallbackTimeoutId = setTimeout(() => {
        console.warn('Redirect timeout, forcing navigation...');
        window.location.href = `/${locale}/dashboard`;
      }, 10000);

      return () => {
        clearTimeout(timeoutId);
        clearTimeout(fallbackTimeoutId);
      };
    }
  }, [user, initialLoadingCompleted, router, locale, t]);

  // Show loading while checking auth state
  if (loading || !initialLoadingCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    );
  }

  // Show redirecting message if user is logged in
  if (user && isRedirecting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <ShieldCheck className="h-16 w-16 text-primary" />
          <h2 className="text-2xl font-semibold">{t('alreadyLoggedIn')}</h2>
          <p className="text-muted-foreground">{t('redirectingToYourDashboard')}</p>
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  // Show error handler if there's a redirect error
  if (redirectError) {
    return (
      <AuthErrorHandler
        locale={locale}
        error={redirectError}
        onRetry={() => {
          setRedirectError(null);
          setIsRedirecting(true);
          // Retry redirect logic
          window.location.reload();
        }}
      />
    );
  }

  // User is not logged in, show login form
  if (!user) {
    return <>{children}</>;
  }

  // Fallback (shouldn't reach here)
  return null;
}
