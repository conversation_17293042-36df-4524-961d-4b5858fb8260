#!/usr/bin/env node

/**
 * سكريبت دمج ملفات الترجمة
 * يدمج ملفي ar.json و en.json في ملف واحد موحد
 */

const fs = require('fs');
const path = require('path');

// مسارات الملفات
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');
const MERGED_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/translations.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  if (!fs.existsSync(filePath)) return null;
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content, 'utf8');
    console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    return backupPath;
  } catch (error) {
    console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error.message);
    return null;
  }
}

/**
 * قراءة ملف الترجمة
 */
function loadTranslations(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ الملف غير موجود: ${filePath}`);
      return {};
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return {};
  }
}

/**
 * دمج الترجمات في هيكل موحد
 */
function mergeTranslations(arTranslations, enTranslations) {
  const merged = {
    ar: arTranslations,
    en: enTranslations
  };
  
  return merged;
}

/**
 * حفظ الملف المدموج
 */
function saveMergedTranslations(mergedData, outputPath) {
  try {
    // إنشاء نسخة احتياطية إذا كان الملف موجوداً
    createBackup(outputPath);
    
    // حفظ الملف الجديد
    const jsonContent = JSON.stringify(mergedData, null, 2);
    fs.writeFileSync(outputPath, jsonContent, 'utf8');
    
    console.log(`✅ تم حفظ الملف المدموج: ${path.basename(outputPath)}`);
    return true;
  } catch (error) {
    console.error('❌ خطأ في حفظ الملف المدموج:', error.message);
    return false;
  }
}

/**
 * إحصائيات الدمج
 */
function printMergeStats(arTranslations, enTranslations, mergedData) {
  const arKeys = Object.keys(arTranslations).length;
  const enKeys = Object.keys(enTranslations).length;
  const totalKeys = arKeys + enKeys;
  
  console.log('\n📊 إحصائيات الدمج:');
  console.log(`   🇸🇦 الترجمات العربية: ${arKeys} مفتاح`);
  console.log(`   🇺🇸 الترجمات الإنجليزية: ${enKeys} مفتاح`);
  console.log(`   📦 إجمالي المفاتيح: ${totalKeys} مفتاح`);
  console.log(`   💾 حجم الملف المدموج: ${JSON.stringify(mergedData).length} حرف`);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔄 بدء عملية دمج ملفات الترجمة...\n');
  
  // قراءة ملفات الترجمة
  console.log('📖 قراءة ملفات الترجمة...');
  const arTranslations = loadTranslations(AR_TRANSLATIONS_PATH);
  const enTranslations = loadTranslations(EN_TRANSLATIONS_PATH);
  
  if (Object.keys(arTranslations).length === 0 && Object.keys(enTranslations).length === 0) {
    console.error('❌ لا توجد ترجمات للدمج!');
    process.exit(1);
  }
  
  // دمج الترجمات
  console.log('🔗 دمج الترجمات...');
  const mergedTranslations = mergeTranslations(arTranslations, enTranslations);
  
  // حفظ الملف المدموج
  console.log('💾 حفظ الملف المدموج...');
  const success = saveMergedTranslations(mergedTranslations, MERGED_TRANSLATIONS_PATH);
  
  if (success) {
    // طباعة الإحصائيات
    printMergeStats(arTranslations, enTranslations, mergedTranslations);
    
    console.log('\n✅ تم دمج ملفات الترجمة بنجاح!');
    console.log(`📁 الملف المدموج: ${path.basename(MERGED_TRANSLATIONS_PATH)}`);
    console.log('\n💡 لاستخدام الملف المدموج، قم بتحديث ملف i18n.ts');
  } else {
    console.error('\n❌ فشل في دمج ملفات الترجمة!');
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  mergeTranslations,
  loadTranslations,
  saveMergedTranslations
};
