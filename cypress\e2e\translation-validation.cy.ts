/// <reference types="cypress" />

describe('التحقق من صحة ملفات الترجمة', () => {
  
  describe('التحقق من صحة JSON', () => {
    it('يجب أن تكون ملفات JSON صحيحة التنسيق', () => {
      // التحقق من ملف الترجمة العربية
      cy.readFile('src/locales/ar.json').should('be.an', 'object');
      
      // التحقق من ملف الترجمة الإنجليزية
      cy.readFile('src/locales/en.json').should('be.an', 'object');
    });

    it('يجب أن تحتوي ملفات الترجمة على المفاتيح الأساسية', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        // التحقق من وجود المفاتيح الأساسية المضافة حديثاً
        const requiredKeys = [
          'advancedSearch',
          'searchProductsAndStores', 
          'searchResultsFor',
          'noSearchResults',
          'browseByCategories',
          'featuredStores',
          'food',
          'fashion',
          'electronics'
        ];
        
        requiredKeys.forEach(key => {
          expect(enTranslations).to.have.property(key);
        });
      });
    });

    it('يجب أن تحتوي على ترجمات المندوبين', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        expect(enTranslations).to.have.property('representative');
        expect(enTranslations.representative).to.have.property('nav');
        expect(enTranslations.representative).to.have.property('dashboard');
        expect(enTranslations.representative).to.have.property('orders');
        expect(enTranslations.representative).to.have.property('earnings');
      });
    });
  });

  describe('التحقق من اكتمال الترجمات', () => {
    it('يجب أن تكون الترجمات الجديدة موجودة في كلا الملفين', () => {
      cy.readFile('src/locales/ar.json').then((arTranslations) => {
        cy.readFile('src/locales/en.json').then((enTranslations) => {
          // قائمة المفاتيح التي تم إضافتها حديثاً
          const newlyAddedKeys = [
            'advancedSearch',
            'searchProductsAndStores',
            'browseByCategories',
            'featuredStores',
            'food',
            'fashion',
            'electronics',
            'homeAndGarden',
            'beautyAndHealth'
          ];
          
          newlyAddedKeys.forEach(key => {
            expect(arTranslations, `Arabic missing key: ${key}`).to.have.property(key);
            expect(enTranslations, `English missing key: ${key}`).to.have.property(key);
          });
        });
      });
    });

    it('يجب ألا تحتوي الترجمات على قيم فارغة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const checkEmptyValues = (obj, path = '') => {
          Object.keys(obj).forEach(key => {
            const currentPath = path ? `${path}.${key}` : key;
            const value = obj[key];
            
            if (typeof value === 'string') {
              expect(value.trim(), `Empty value at ${currentPath}`).to.not.be.empty;
            } else if (typeof value === 'object' && value !== null) {
              checkEmptyValues(value, currentPath);
            }
          });
        };
        
        checkEmptyValues(enTranslations);
      });
    });
  });

  describe('التحقق من تناسق الترجمات', () => {
    it('يجب أن تكون أسماء الفئات متناسقة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const categoryKeys = [
          'food',
          'fashion', 
          'electronics',
          'homeAndGarden',
          'beautyAndHealth',
          'sportsAndFitness',
          'automotive',
          'booksAndMedia',
          'artsAndCrafts',
          'other'
        ];
        
        categoryKeys.forEach(key => {
          expect(enTranslations).to.have.property(key);
          expect(enTranslations[key]).to.be.a('string');
          expect(enTranslations[key].length).to.be.greaterThan(0);
        });
      });
    });

    it('يجب أن تكون ترجمات خيارات الترتيب متناسقة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const sortingKeys = [
          'relevance',
          'priceLowToHigh',
          'priceHighToLow', 
          'highestRated',
          'newest',
          'mostPopular'
        ];
        
        sortingKeys.forEach(key => {
          expect(enTranslations).to.have.property(key);
          expect(enTranslations[key]).to.be.a('string');
        });
      });
    });
  });

  describe('التحقق من الترجمات المتداخلة', () => {
    it('يجب أن تكون ترجمات المندوبين مكتملة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        // التحقق من بنية المندوبين
        expect(enTranslations.representative).to.be.an('object');
        
        // التحقق من التنقل
        expect(enTranslations.representative.nav).to.have.property('dashboard');
        expect(enTranslations.representative.nav).to.have.property('orders');
        expect(enTranslations.representative.nav).to.have.property('earnings');
        expect(enTranslations.representative.nav).to.have.property('profile');
        
        // التحقق من لوحة التحكم
        expect(enTranslations.representative.dashboard).to.have.property('welcome');
        expect(enTranslations.representative.dashboard).to.have.property('totalDeliveries');
        expect(enTranslations.representative.dashboard).to.have.property('monthlyEarnings');
        
        // التحقق من الطلبات
        expect(enTranslations.representative.orders).to.have.property('title');
        expect(enTranslations.representative.orders).to.have.property('availableOrders');
        expect(enTranslations.representative.orders).to.have.property('acceptOrder');
        
        // التحقق من الأرباح
        expect(enTranslations.representative.earnings).to.have.property('title');
        expect(enTranslations.representative.earnings).to.have.property('totalEarnings');
        expect(enTranslations.representative.earnings).to.have.property('payoutHistory');
      });
    });
  });

  describe('التحقق من الترجمات الخاصة', () => {
    it('يجب أن تحتوي على ترجمات الموافقة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const approvalKeys = [
          'merchantApproval',
          'pendingApproval',
          'approvalPending',
          'approvalApproved',
          'approvalRejected'
        ];
        
        approvalKeys.forEach(key => {
          expect(enTranslations).to.have.property(key);
        });
      });
    });

    it('يجب أن تحتوي على ترجمات الخريطة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const mapKeys = [
          'interactiveMap',
          'findNearbyStores',
          'detectingLocation',
          'locationDetected',
          'distanceCalculator',
          'getDirections',
          'walking',
          'cycling',
          'driving'
        ];
        
        mapKeys.forEach(key => {
          expect(enTranslations).to.have.property(key);
        });
      });
    });
  });

  describe('التحقق من جودة الترجمات', () => {
    it('يجب ألا تحتوي الترجمات على نصوص مؤقتة', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const checkForPlaceholders = (obj, path = '') => {
          Object.keys(obj).forEach(key => {
            const currentPath = path ? `${path}.${key}` : key;
            const value = obj[key];
            
            if (typeof value === 'string') {
              // التحقق من عدم وجود نصوص مؤقتة
              expect(value.toLowerCase()).to.not.include('todo');
              expect(value.toLowerCase()).to.not.include('placeholder');
              expect(value.toLowerCase()).to.not.include('translate');
              expect(value).to.not.include('[');
              expect(value).to.not.include(']');
            } else if (typeof value === 'object' && value !== null) {
              checkForPlaceholders(value, currentPath);
            }
          });
        };
        
        checkForPlaceholders(enTranslations);
      });
    });

    it('يجب أن تكون الترجمات بالطول المناسب', () => {
      cy.readFile('src/locales/en.json').then((enTranslations) => {
        const checkLength = (obj, path = '') => {
          Object.keys(obj).forEach(key => {
            const currentPath = path ? `${path}.${key}` : key;
            const value = obj[key];
            
            if (typeof value === 'string') {
              // التحقق من أن النص ليس قصيراً جداً أو طويلاً جداً
              expect(value.length, `Text too short at ${currentPath}`).to.be.at.least(1);
              expect(value.length, `Text too long at ${currentPath}`).to.be.at.most(200);
            } else if (typeof value === 'object' && value !== null) {
              checkLength(value, currentPath);
            }
          });
        };
        
        checkLength(enTranslations);
      });
    });
  });
});
