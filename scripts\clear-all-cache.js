#!/usr/bin/env node

/**
 * سكريبت شامل لمسح جميع أنواع التخزين المؤقت
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * حذف مجلد بشكل آمن
 */
function removeDirectory(dirPath) {
  try {
    if (fs.existsSync(dirPath)) {
      console.log(`🗑️  حذف مجلد: ${dirPath}`);
      
      // استخدام PowerShell لحذف المجلد
      if (process.platform === 'win32') {
        execSync(`Remove-Item -Recurse -Force "${dirPath}"`, { stdio: 'inherit' });
      } else {
        execSync(`rm -rf "${dirPath}"`, { stdio: 'inherit' });
      }
      
      console.log(`✅ تم حذف: ${dirPath}`);
      return true;
    } else {
      console.log(`ℹ️  المجلد غير موجود: ${dirPath}`);
      return true;
    }
  } catch (error) {
    console.error(`❌ خطأ في حذف ${dirPath}:`, error.message);
    return false;
  }
}

/**
 * حذف ملف بشكل آمن
 */
function removeFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      console.log(`🗑️  حذف ملف: ${filePath}`);
      fs.unlinkSync(filePath);
      console.log(`✅ تم حذف: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  الملف غير موجود: ${filePath}`);
      return true;
    }
  } catch (error) {
    console.error(`❌ خطأ في حذف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * تنفيذ أمر بشكل آمن
 */
function runCommand(command, description) {
  try {
    console.log(`🔧 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ تم: ${description}`);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في ${description}:`, error.message);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧹 بدء مسح جميع أنواع التخزين المؤقت...\n');
  
  let totalSuccess = 0;
  let totalAttempts = 0;
  
  // قائمة المجلدات والملفات للحذف
  const itemsToRemove = [
    // Next.js cache
    { type: 'dir', path: '.next', description: 'Next.js build cache' },
    
    // Node modules cache
    { type: 'dir', path: 'node_modules/.cache', description: 'Node modules cache' },
    
    // TypeScript cache
    { type: 'file', path: 'tsconfig.tsbuildinfo', description: 'TypeScript build info' },
    
    // ESLint cache
    { type: 'file', path: '.eslintcache', description: 'ESLint cache' },
    
    // Jest cache
    { type: 'dir', path: 'node_modules/.cache/jest', description: 'Jest cache' },
    
    // Cypress cache
    { type: 'dir', path: 'node_modules/.cache/cypress', description: 'Cypress cache' },
    
    // Webpack cache
    { type: 'dir', path: 'node_modules/.cache/webpack', description: 'Webpack cache' },
    
    // Babel cache
    { type: 'dir', path: 'node_modules/.cache/babel-loader', description: 'Babel cache' },
    
    // SWC cache
    { type: 'dir', path: 'node_modules/.cache/swc', description: 'SWC cache' },
    
    // Turbo cache
    { type: 'dir', path: '.turbo', description: 'Turbo cache' },
    
    // Vercel cache
    { type: 'dir', path: '.vercel', description: 'Vercel cache' },
    
    // OS cache files
    { type: 'file', path: '.DS_Store', description: 'macOS cache file' },
    { type: 'file', path: 'Thumbs.db', description: 'Windows cache file' },
  ];
  
  // حذف المجلدات والملفات
  console.log('📁 حذف مجلدات وملفات التخزين المؤقت:');
  itemsToRemove.forEach(item => {
    totalAttempts++;
    
    if (item.type === 'dir') {
      if (removeDirectory(item.path)) {
        totalSuccess++;
      }
    } else {
      if (removeFile(item.path)) {
        totalSuccess++;
      }
    }
  });
  
  console.log('\n💻 تنفيذ أوامر مسح التخزين المؤقت:');
  
  // أوامر مسح التخزين المؤقت
  const commands = [
    { cmd: 'npm cache clean --force', desc: 'مسح npm cache' },
  ];
  
  // تنفيذ الأوامر
  commands.forEach(command => {
    totalAttempts++;
    if (runCommand(command.cmd, command.desc)) {
      totalSuccess++;
    }
  });
  
  // النتيجة النهائية
  console.log('\n📊 ملخص النتائج:');
  console.log(`✅ نجح: ${totalSuccess}/${totalAttempts}`);
  console.log(`❌ فشل: ${totalAttempts - totalSuccess}/${totalAttempts}`);
  
  if (totalSuccess === totalAttempts) {
    console.log('\n🎉 تم مسح جميع أنواع التخزين المؤقت بنجاح!');
    console.log('💡 يمكنك الآن إعادة بناء المشروع:');
    console.log('   npm run build');
    console.log('   npm run dev');
  } else {
    console.log('\n⚠️  تم مسح معظم التخزين المؤقت، لكن بعض العناصر فشلت');
    console.log('🔄 جرب إعادة تشغيل المحطة الطرفية وتشغيل السكريبت مرة أخرى');
  }
  
  console.log('\n🔄 الخطوات التالية الموصى بها:');
  console.log('1. إعادة تشغيل محرر الكود (VS Code)');
  console.log('2. إعادة تشغيل خادم التطوير');
  console.log('3. مسح cache المتصفح (Ctrl+Shift+R)');
  console.log('4. التحقق من أن المشكلة تم حلها');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  removeDirectory,
  removeFile,
  runCommand
};
