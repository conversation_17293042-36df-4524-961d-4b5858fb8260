"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Ticket,
  Plus,
  Search,
  Filter,
  Clock,
  User,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  XCircle,
  Eye,
  Edit,
  Send,
  Paperclip,
  TrendingUp,
  Users,
  Timer
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import IntelligentTicketingService, { SupportTicket, TicketStats } from '@/services/intelligentTicketingService';

/**
 * واجهة إدارة التذاكر الذكية
 * نظام شامل لإدارة تذاكر الدعم مع تصنيف تلقائي
 */
const TicketManagement: React.FC = () => {
  const { user } = useAuth();
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [stats, setStats] = useState<TicketStats>({
    totalTickets: 0,
    openTickets: 0,
    resolvedTickets: 0,
    averageResolutionTime: 0,
    customerSatisfaction: 0,
    ticketsByCategory: [],
    ticketsByPriority: []
  });
  
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // نموذج إنشاء تذكرة جديدة
  const [newTicket, setNewTicket] = useState({
    subject: '',
    description: '',
    category: 'general' as any,
    priority: 'medium' as any,
    userId: ''
  });

  // نموذج الرد على التذكرة
  const [replyForm, setReplyForm] = useState({
    message: '',
    isInternal: false
  });

  useEffect(() => {
    loadTicketsData();
  }, []);

  const loadTicketsData = async () => {
    setIsLoading(true);
    try {
      // تحميل الإحصائيات
      const statsData = await IntelligentTicketingService.getTicketStats();
      setStats(statsData);

      // تحميل التذاكر (محاكاة)
      const mockTickets: SupportTicket[] = [
        {
          id: '1',
          ticketNumber: 'MKH-123456-001',
          userId: 'user1',
          subject: 'مشكلة في الدفع',
          description: 'لا أستطيع إكمال عملية الدفع باستخدام بطاقة مدى',
          category: 'payment',
          priority: 'high',
          status: 'open',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          source: 'chatbot',
          tags: ['دفع', 'مدى'],
          attachments: [],
          responses: []
        },
        {
          id: '2',
          ticketNumber: 'MKH-123456-002',
          userId: 'user2',
          subject: 'تأخير في التوصيل',
          description: 'طلبي متأخر أكثر من المدة المحددة',
          category: 'delivery',
          priority: 'medium',
          status: 'in_progress',
          assignedTo: 'support1',
          assignedToName: 'أحمد محمد',
          createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 30 * 60 * 1000),
          source: 'web_form',
          tags: ['توصيل', 'تأخير'],
          attachments: [],
          responses: [
            {
              id: 'r1',
              ticketId: '2',
              userId: 'support1',
              userName: 'أحمد محمد',
              userType: 'support',
              message: 'نعتذر عن التأخير، سنتواصل مع المندوب فوراً',
              isInternal: false,
              createdAt: new Date(Date.now() - 30 * 60 * 1000),
              attachments: []
            }
          ]
        },
        {
          id: '3',
          ticketNumber: 'MKH-123456-003',
          userId: 'user3',
          subject: 'اقتراح تحسين التطبيق',
          description: 'أقترح إضافة ميزة البحث الصوتي للتطبيق',
          category: 'suggestion',
          priority: 'low',
          status: 'resolved',
          assignedTo: 'support2',
          assignedToName: 'فاطمة أحمد',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          resolvedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          source: 'mobile_app',
          tags: ['اقتراح', 'تحسين'],
          attachments: [],
          responses: [],
          satisfactionRating: 5
        }
      ];

      setTickets(mockTickets);

    } catch (error) {
      console.error('خطأ في تحميل بيانات التذاكر:', error);
      toast.error('حدث خطأ في تحميل البيانات');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTicket = async () => {
    if (!newTicket.subject.trim() || !newTicket.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      const ticket = await IntelligentTicketingService.createTicket({
        subject: newTicket.subject,
        description: newTicket.description,
        category: newTicket.category,
        priority: newTicket.priority,
        userId: newTicket.userId || user?.uid,
        source: 'web_form'
      });

      setTickets(prev => [ticket, ...prev]);
      setNewTicket({
        subject: '',
        description: '',
        category: 'general',
        priority: 'medium',
        userId: ''
      });
      setIsCreateDialogOpen(false);
      toast.success('تم إنشاء التذكرة بنجاح');

    } catch (error) {
      console.error('خطأ في إنشاء التذكرة:', error);
      toast.error('حدث خطأ في إنشاء التذكرة');
    }
  };

  const handleReplyToTicket = async () => {
    if (!selectedTicket || !replyForm.message.trim()) {
      toast.error('يرجى كتابة رد');
      return;
    }

    try {
      const response = await IntelligentTicketingService.addTicketResponse(
        selectedTicket.id,
        {
          userId: user?.uid || 'admin',
          userName: user?.displayName || 'المسؤول',
          userType: 'support',
          message: replyForm.message,
          isInternal: replyForm.isInternal
        }
      );

      // تحديث التذكرة المحلية
      setTickets(prev => 
        prev.map(ticket => 
          ticket.id === selectedTicket.id 
            ? { ...ticket, responses: [...ticket.responses, response], status: 'in_progress' }
            : ticket
        )
      );

      setReplyForm({ message: '', isInternal: false });
      toast.success('تم إرسال الرد بنجاح');

    } catch (error) {
      console.error('خطأ في إرسال الرد:', error);
      toast.error('حدث خطأ في إرسال الرد');
    }
  };

  const handleUpdateTicketStatus = async (ticketId: string, status: any) => {
    try {
      await IntelligentTicketingService.updateTicketStatus(
        ticketId,
        status,
        user?.uid || 'admin'
      );

      setTickets(prev => 
        prev.map(ticket => 
          ticket.id === ticketId 
            ? { ...ticket, status, updatedAt: new Date() }
            : ticket
        )
      );

      toast.success('تم تحديث حالة التذكرة');

    } catch (error) {
      console.error('خطأ في تحديث حالة التذكرة:', error);
      toast.error('حدث خطأ في تحديث الحالة');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { label: 'مفتوحة', variant: 'destructive' as const, icon: AlertCircle },
      in_progress: { label: 'قيد المعالجة', variant: 'default' as const, icon: Clock },
      pending_customer: { label: 'في انتظار العميل', variant: 'secondary' as const, icon: User },
      resolved: { label: 'محلولة', variant: 'default' as const, icon: CheckCircle },
      closed: { label: 'مغلقة', variant: 'outline' as const, icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center space-x-1 rtl:space-x-reverse">
        <Icon className="w-3 h-3" />
        <span>{config.label}</span>
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'منخفضة', className: 'bg-green-100 text-green-800' },
      medium: { label: 'متوسطة', className: 'bg-yellow-100 text-yellow-800' },
      high: { label: 'عالية', className: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'عاجلة', className: 'bg-red-100 text-red-800' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;

    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div className="space-y-6">
      {/* إحصائيات التذاكر */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي التذاكر</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalTickets.toLocaleString()}</p>
              </div>
              <Ticket className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">التذاكر المفتوحة</p>
                <p className="text-2xl font-bold text-red-600">{stats.openTickets}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">التذاكر المحلولة</p>
                <p className="text-2xl font-bold text-green-600">{stats.resolvedTickets.toLocaleString()}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط وقت الحل</p>
                <p className="text-2xl font-bold text-purple-600">{stats.averageResolutionTime}س</p>
              </div>
              <Timer className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رضا العملاء</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.customerSatisfaction}/5</p>
              </div>
              <TrendingUp className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* إدارة التذاكر */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                <Ticket className="w-6 h-6" />
                <span>إدارة التذاكر</span>
              </CardTitle>
              <CardDescription>
                نظام إدارة تذاكر الدعم مع تصنيف تلقائي وتوجيه ذكي
              </CardDescription>
            </div>
            
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  تذكرة جديدة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>إنشاء تذكرة دعم جديدة</DialogTitle>
                  <DialogDescription>
                    املأ البيانات التالية لإنشاء تذكرة دعم
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="subject">الموضوع</Label>
                    <Input
                      id="subject"
                      value={newTicket.subject}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, subject: e.target.value }))}
                      placeholder="موضوع التذكرة..."
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">الوصف</Label>
                    <Textarea
                      id="description"
                      value={newTicket.description}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="وصف المشكلة أو الاستفسار..."
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="category">الفئة</Label>
                      <Select
                        value={newTicket.category}
                        onValueChange={(value) => setNewTicket(prev => ({ ...prev, category: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="technical">تقني</SelectItem>
                          <SelectItem value="payment">دفع</SelectItem>
                          <SelectItem value="delivery">توصيل</SelectItem>
                          <SelectItem value="account">حساب</SelectItem>
                          <SelectItem value="product">منتج</SelectItem>
                          <SelectItem value="general">عام</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="priority">الأولوية</Label>
                      <Select
                        value={newTicket.priority}
                        onValueChange={(value) => setNewTicket(prev => ({ ...prev, priority: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">منخفضة</SelectItem>
                          <SelectItem value="medium">متوسطة</SelectItem>
                          <SelectItem value="high">عالية</SelectItem>
                          <SelectItem value="urgent">عاجلة</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Button onClick={handleCreateTicket} className="flex-1">
                      إنشاء التذكرة
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsCreateDialogOpen(false)}
                    >
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>

        <CardContent>
          {/* أدوات البحث والفلترة */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="البحث في التذاكر..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="open">مفتوحة</SelectItem>
                <SelectItem value="in_progress">قيد المعالجة</SelectItem>
                <SelectItem value="resolved">محلولة</SelectItem>
                <SelectItem value="closed">مغلقة</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="الأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأولويات</SelectItem>
                <SelectItem value="urgent">عاجلة</SelectItem>
                <SelectItem value="high">عالية</SelectItem>
                <SelectItem value="medium">متوسطة</SelectItem>
                <SelectItem value="low">منخفضة</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* جدول التذاكر */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم التذكرة</TableHead>
                  <TableHead>الموضوع</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الأولوية</TableHead>
                  <TableHead>المختص</TableHead>
                  <TableHead>تاريخ الإنشاء</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTickets.map((ticket) => (
                  <TableRow key={ticket.id}>
                    <TableCell className="font-medium">
                      {ticket.ticketNumber}
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{ticket.subject}</p>
                        <p className="text-sm text-gray-600 truncate max-w-xs">
                          {ticket.description}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(ticket.status)}
                    </TableCell>
                    <TableCell>
                      {getPriorityBadge(ticket.priority)}
                    </TableCell>
                    <TableCell>
                      {ticket.assignedToName || 'غير مُعيَّن'}
                    </TableCell>
                    <TableCell>
                      {ticket.createdAt.toLocaleDateString('ar-SA')}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedTicket(ticket)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        
                        <Select
                          value={ticket.status}
                          onValueChange={(value) => handleUpdateTicketStatus(ticket.id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="open">مفتوحة</SelectItem>
                            <SelectItem value="in_progress">قيد المعالجة</SelectItem>
                            <SelectItem value="resolved">محلولة</SelectItem>
                            <SelectItem value="closed">مغلقة</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* تفاصيل التذكرة المحددة */}
          {selectedTicket && (
            <Dialog open={!!selectedTicket} onOpenChange={() => setSelectedTicket(null)}>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Ticket className="w-5 h-5" />
                    <span>تذكرة رقم: {selectedTicket.ticketNumber}</span>
                  </DialogTitle>
                  <DialogDescription>
                    تفاصيل التذكرة والردود
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* معلومات التذكرة */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>الموضوع</Label>
                      <p className="font-medium">{selectedTicket.subject}</p>
                    </div>
                    <div>
                      <Label>الحالة</Label>
                      <div className="mt-1">
                        {getStatusBadge(selectedTicket.status)}
                      </div>
                    </div>
                    <div>
                      <Label>الأولوية</Label>
                      <div className="mt-1">
                        {getPriorityBadge(selectedTicket.priority)}
                      </div>
                    </div>
                    <div>
                      <Label>المختص</Label>
                      <p>{selectedTicket.assignedToName || 'غير مُعيَّن'}</p>
                    </div>
                  </div>

                  <div>
                    <Label>الوصف</Label>
                    <p className="mt-1 p-3 bg-gray-50 rounded-lg">
                      {selectedTicket.description}
                    </p>
                  </div>

                  {/* الردود */}
                  <div>
                    <Label>الردود ({selectedTicket.responses.length})</Label>
                    <div className="mt-2 space-y-3 max-h-60 overflow-y-auto">
                      {selectedTicket.responses.map((response) => (
                        <div key={response.id} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              <Badge variant={response.userType === 'support' ? 'default' : 'secondary'}>
                                {response.userName}
                              </Badge>
                              {response.isInternal && (
                                <Badge variant="outline" className="text-xs">
                                  داخلي
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-gray-500">
                              {response.createdAt.toLocaleString('ar-SA')}
                            </span>
                          </div>
                          <p className="text-sm">{response.message}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* نموذج الرد */}
                  <div className="border-t pt-4">
                    <Label>إضافة رد</Label>
                    <div className="mt-2 space-y-3">
                      <Textarea
                        value={replyForm.message}
                        onChange={(e) => setReplyForm(prev => ({ ...prev, message: e.target.value }))}
                        placeholder="اكتب ردك هنا..."
                        rows={3}
                      />
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <input
                            type="checkbox"
                            id="isInternal"
                            checked={replyForm.isInternal}
                            onChange={(e) => setReplyForm(prev => ({ ...prev, isInternal: e.target.checked }))}
                          />
                          <Label htmlFor="isInternal" className="text-sm">
                            رد داخلي (لن يراه العميل)
                          </Label>
                        </div>
                        
                        <Button onClick={handleReplyToTicket} size="sm">
                          <Send className="w-4 h-4 mr-2" />
                          إرسال الرد
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TicketManagement;
