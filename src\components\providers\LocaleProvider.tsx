'use client';

import type { Locale } from '@/lib/i18n';
import NoSSR from './NoSSR';
import FontProvider from './FontProvider';

interface LocaleProviderProps {
  locale: Locale;
  children: React.ReactNode;
}

export default function LocaleProvider({ locale, children }: LocaleProviderProps) {
  return (
    <NoSSR>
      <FontProvider locale={locale}>
        {children}
      </FontProvider>
    </NoSSR>
  );
}
