// src/app/[locale]/dashboard/page.tsx
"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useRouter } from 'next/navigation';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { UserDocument } from '@/types';
import { Loader2, ShoppingBag, Package, UserCog, Eye, AlertTriangle, ShieldCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function CustomerDashboardPage() {
  const { user, initialLoadingCompleted, loading: authLoading } = useAuth();
  const { t, locale } = useLocale();
  const router = useRouter();
  const [userDoc, setUserDoc] = useState<UserDocument | null>(null);
  const [isLoadingUserDoc, setIsLoadingUserDoc] = useState(true);
  const [errorUserDoc, setErrorUserDoc] = useState<string | null>(null);

  useEffect(() => {
    if (initialLoadingCompleted && !user) {
      router.push(`/${locale}/login?redirect=/${locale}/dashboard`);
    } else if (initialLoadingCompleted && user) {
      const fetchUserDocument = async () => {
        setIsLoadingUserDoc(true);
        setErrorUserDoc(null);
        try {
          const userRef = doc(db, "users", user.uid);
          const userSnap = await getDoc(userRef);
          if (userSnap.exists()) {
            const fetchedUserDoc = userSnap.data() as UserDocument;
            setUserDoc(fetchedUserDoc);
            if (fetchedUserDoc.userType === 'merchant') {
              // Redirect merchant to their dashboard
              router.replace(`/${locale}/merchant/dashboard`);
            }
          } else {
            setErrorUserDoc(t('errorFetchingUserData'));
          }
        } catch (error) {
          console.error("Error fetching user document:", error);
          setErrorUserDoc(t('errorFetchingUserData'));
        } finally {
          setIsLoadingUserDoc(false);
        }
      };
      fetchUserDocument();
    }
  }, [user, initialLoadingCompleted, router, locale, t]);

  if (authLoading || isLoadingUserDoc || !initialLoadingCompleted) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('loadingDashboard')}</p>
      </div>
    );
  }

  if (errorUserDoc) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-8 text-center">
        <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
        <h2 className="text-2xl font-semibold mb-2 text-destructive">{t('errorTitle')}</h2>
        <p className="text-muted-foreground mb-6">{errorUserDoc}</p>
        <Button onClick={() => router.push(`/${locale}/`)}>{t('goToHomepage')}</Button>
      </div>
    );
  }

  // If userDoc exists but userType is merchant, this page shouldn't render for them (redirect handled above)
  if (!userDoc || userDoc.userType !== 'customer') {
     // This case should ideally be caught by the redirect logic,
     // but as a fallback or if redirect is in progress:
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-8 text-center">
        <ShieldCheck className="h-16 w-16 text-primary mb-4" />
        <h2 className="text-2xl font-semibold mb-2">{t('redirectingTitle')}</h2>
        <p className="text-muted-foreground mb-6">{t('redirectingToYourDashboard')}</p>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const getInitials = (name?: string | null) => {
    if (!name) return "CU"; // Customer User
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 rtl:sm:space-x-reverse p-6">
          <Avatar className="h-24 w-24 text-3xl">
            <AvatarImage src={user?.photoURL ?? undefined} alt={userDoc.displayName || 'User Avatar'} data-ai-hint="user avatar" />
            <AvatarFallback className="bg-primary text-primary-foreground">
              {getInitials(userDoc.displayName)}
            </AvatarFallback>
          </Avatar>
          <div className="text-center sm:text-start">
            <CardTitle className="text-3xl font-bold">{t('welcomeUser', { name: userDoc.displayName || t('customer') })}</CardTitle>
            <CardDescription className="text-lg text-muted-foreground">{t('customerDashboardSubtitle')}</CardDescription>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingBag className="h-6 w-6 text-primary" />
              {t('quickActions')}
            </CardTitle>
            <CardDescription>{t('quickActionsSubtitle')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href={`/${locale}/products`}>
                <ShoppingBag className="me-2 rtl:ms-2 h-5 w-5" />
                {t('browseProductsDashboard')}
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href={`/${locale}/dashboard/orders`}>
                <Package className="me-2 rtl:ms-2 h-5 w-5" />
                {t('myOrders')}
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href={`/${locale}/profile`}>
                <UserCog className="me-2 rtl:ms-2 h-5 w-5" />
                {t('manageProfile')}
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="md:col-span-2 lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-6 w-6 text-primary" />
              {t('accountOverview')}
            </CardTitle>
             <CardDescription>{t('accountOverviewSubtitle')}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{t('accountOverviewPlaceholder')}</p>
            {/* Future: Display order count, wishlist items, etc. */}
          </CardContent>
        </Card>

        {/* Placeholder for future recommendations or promotions */}
        <Card className="md:col-span-2 lg:col-span-1">
          <CardHeader>
            <CardTitle>{t('recommendationsTitle')}</CardTitle>
            <CardDescription>{t('recommendationsSubtitle')}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{t('recommendationsPlaceholder')}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
