/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// أوامر مخصصة للمشروع

// تسجيل دخول وهمي
Cypress.Commands.add('mockLogin', (userType: 'customer' | 'merchant' | 'representative' = 'customer') => {
  cy.window().then((win) => {
    // Mock user data
    const mockUser = {
      uid: `test-${userType}-uid`,
      email: `test-${userType}@example.com`,
      displayName: `Test ${userType}`,
      emailVerified: true
    }
    
    // Store in localStorage to simulate authentication
    win.localStorage.setItem('mockUser', JSON.stringify(mockUser))
    win.localStorage.setItem('userType', userType)
  })
})

// تسجيل خروج وهمي
Cypress.Commands.add('mockLogout', () => {
  cy.window().then((win) => {
    win.localStorage.removeItem('mockUser')
    win.localStorage.removeItem('userType')
  })
})

// انتظار تحميل الصفحة
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="loading"]', { timeout: 10000 }).should('not.exist')
  cy.get('body').should('be.visible')
})

// التنقل إلى صفحة بلغة محددة
Cypress.Commands.add('visitWithLocale', (path: string, locale: 'ar' | 'en' = 'ar') => {
  const fullPath = `/${locale}${path.startsWith('/') ? path : `/${path}`}`
  cy.visit(fullPath)
})

// انتظار اختفاء شاشة التحميل
Cypress.Commands.add('waitForLoadingToFinish', () => {
  cy.get('[data-testid="auth-loading"]', { timeout: 15000 }).should('not.exist')
  cy.get('[data-testid="page-loading"]', { timeout: 10000 }).should('not.exist')
})

// التحقق من وجود عنصر بنص معين
Cypress.Commands.add('shouldContainArabicText', (text: string) => {
  cy.contains(text).should('be.visible')
})

// التحقق من وجود نص إنجليزي
Cypress.Commands.add('shouldContainEnglishText', (text: string) => {
  cy.contains(text).should('be.visible')
})

// محاكاة تسجيل حساب جديد
Cypress.Commands.add('mockSignup', (userType: 'customer' | 'merchant' | 'representative' = 'customer') => {
  cy.window().then((win) => {
    // Mock successful signup
    const mockUser = {
      uid: `new-${userType}-${Date.now()}`,
      email: `new-${userType}@test.com`,
      displayName: `New ${userType}`,
      emailVerified: false,
      userType: userType
    }

    // Store in localStorage to simulate successful signup
    win.localStorage.setItem('mockUser', JSON.stringify(mockUser))
    win.localStorage.setItem('userType', userType)
    win.localStorage.setItem('justSignedUp', 'true')
  })
})

// Mock Firebase Auth
Cypress.Commands.add('mockFirebaseAuth', () => {
  cy.window().then((win) => {
    // Mock Firebase auth methods
    if (win.firebase) {
      cy.stub(win.firebase.auth(), 'onAuthStateChanged').callsFake((callback) => {
        const mockUser = win.localStorage.getItem('mockUser')
        if (mockUser) {
          callback(JSON.parse(mockUser))
        } else {
          callback(null)
        }
        return () => {} // unsubscribe function
      })
    }
  })
})

// Mock Geolocation
Cypress.Commands.add('mockGeolocation', (lat: number = 24.7136, lng: number = 46.6753) => {
  cy.window().then((win) => {
    cy.stub(win.navigator.geolocation, 'getCurrentPosition').callsFake((success) => {
      success({
        coords: {
          latitude: lat,
          longitude: lng,
          accuracy: 100,
          altitude: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      })
    })
  })
})

// Mock ERP/POS Integration Data
Cypress.Commands.add('mockERPIntegration', () => {
  cy.window().then((win) => {
    const mockERPData = {
      id: 'test-erp-integration',
      merchantId: 'test-merchant-id',
      systemType: 'sap',
      systemName: 'SAP Test System',
      isActive: true,
      configuration: {
        apiUrl: 'https://test-sap-api.example.com',
        apiKey: 'test-api-key',
        username: 'test-user',
        database: 'test-db'
      },
      syncSettings: {
        syncProducts: true,
        syncInventory: true,
        syncOrders: true,
        syncCustomers: true,
        syncAccounting: false,
        syncInterval: 60,
        autoSync: true
      }
    }
    win.localStorage.setItem('mockERPIntegration', JSON.stringify(mockERPData))
  })
})

// Mock CRM Customer Data
Cypress.Commands.add('mockCRMCustomers', () => {
  cy.window().then((win) => {
    const mockCustomers = [
      {
        id: 'customer-1',
        userId: 'user-1',
        merchantId: 'test-merchant-id',
        profile: {
          firstName: 'أحمد',
          lastName: 'محمد',
          email: '<EMAIL>',
          phone: '+************',
          dateOfBirth: '1990-01-01',
          gender: 'male'
        },
        behavior: {
          totalOrders: 15,
          totalSpent: 2500,
          averageOrderValue: 166.67,
          lastOrderDate: new Date().toISOString(),
          preferredCategories: ['electronics', 'clothing']
        },
        tags: ['vip', 'frequent_buyer'],
        segment: 'high_value'
      }
    ]
    win.localStorage.setItem('mockCRMCustomers', JSON.stringify(mockCustomers))
  })
})

// Mock Coupon Data
Cypress.Commands.add('mockCoupons', () => {
  cy.window().then((win) => {
    const mockCoupons = [
      {
        id: 'coupon-1',
        code: 'SAVE20',
        type: 'percentage',
        value: 20,
        merchantId: 'test-merchant-id',
        isActive: true,
        usageLimit: 100,
        usedCount: 25,
        minOrderAmount: 100,
        validFrom: new Date().toISOString(),
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
    win.localStorage.setItem('mockCoupons', JSON.stringify(mockCoupons))
  })
})

// Mock Loyalty Program Data
Cypress.Commands.add('mockLoyaltyProgram', () => {
  cy.window().then((win) => {
    const mockLoyalty = {
      customerId: 'user-1',
      merchantId: 'test-merchant-id',
      totalPoints: 1250,
      availablePoints: 1000,
      tier: 'gold',
      nextTierPoints: 500,
      transactions: [
        {
          id: 'loyalty-1',
          type: 'earned',
          points: 50,
          description: 'نقاط من طلب #12345',
          date: new Date().toISOString()
        }
      ]
    }
    win.localStorage.setItem('mockLoyaltyProgram', JSON.stringify(mockLoyalty))
  })
})

// Fill Form Helper
Cypress.Commands.add('fillForm', (formData: Record<string, string>) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[data-testid="${field}"], [name="${field}"], #${field}`)
      .should('be.visible')
      .clear()
      .type(value)
  })
})

// Wait for API Response
Cypress.Commands.add('waitForAPI', (alias: string, timeout: number = 10000) => {
  cy.wait(alias, { timeout })
})

// Check Dashboard Stats
Cypress.Commands.add('checkDashboardStats', () => {
  cy.get('[data-testid="dashboard-stats"]').should('be.visible')
  cy.get('[data-testid="total-orders"]').should('contain.text', '0')
  cy.get('[data-testid="total-revenue"]').should('be.visible')
})

// تسجيل دخول كنوع مستخدم محدد
Cypress.Commands.add('loginAs', (userType: string, userData: any) => {
  cy.window().then((win) => {
    win.localStorage.setItem('mockUser', JSON.stringify(userData))
    win.localStorage.setItem('userType', userType)
  })
})

// محاكاة بيانات المتجر
Cypress.Commands.add('mockStoreData', (storeData: any) => {
  cy.window().then((win) => {
    win.localStorage.setItem('mockStoreData', JSON.stringify(storeData))
  })
})

// محاكاة طلبات التجار في الانتظار
Cypress.Commands.add('mockPendingMerchants', (merchants: any[]) => {
  cy.window().then((win) => {
    win.localStorage.setItem('mockPendingMerchants', JSON.stringify(merchants))
  })
})

// تسجيل كتاجر
Cypress.Commands.add('signupAsMerchant', (name: string, email: string, password: string) => {
  // اختيار نوع المستخدم
  cy.get('[data-cy="user-type-merchant"]').click()
  cy.get('[data-cy="next-step"]').click()

  // الموافقة على الشروط
  cy.get('[data-cy="terms-checkbox"]').check()
  cy.get('[data-cy="next-step"]').click()

  // تأكيد الاختيار
  cy.get('[data-cy="next-step"]').click()

  // ملء البيانات الأساسية
  cy.get('[data-cy="username"]').type(name)
  cy.get('[data-cy="email"]').type(email)
  cy.get('[data-cy="next-detailed-step"]').click()

  // كلمة المرور
  cy.get('[data-cy="password"]').type(password)
  cy.get('[data-cy="confirm-password"]').type(password)
  cy.get('[data-cy="next-detailed-step"]').click()

  // رفع الملفات (محاكاة)
  cy.get('[data-cy="commercial-registration"]').selectFile('cypress/fixtures/test-document.pdf')
  cy.get('[data-cy="freelance-document"]').selectFile('cypress/fixtures/test-document.pdf')
  cy.get('[data-cy="next-detailed-step"]').click()

  // إرسال الطلب
  cy.get('[data-cy="submit-signup"]').click()
})

// موافقة على التاجر
Cypress.Commands.add('approveMerchant', (email: string, notes: string) => {
  // البحث عن التاجر
  cy.contains(email).parents('[data-cy="merchant-application"]').within(() => {
    // النقر على مراجعة الطلب
    cy.get('[data-cy="review-application"]').click()

    // إضافة ملاحظات
    cy.get('[data-cy="approval-notes"]').type(notes)

    // النقر على قبول
    cy.get('[data-cy="approve-merchant"]').click()
  })
})

// محاكاة Firestore للموافقات
Cypress.Commands.add('mockFirestore', () => {
  cy.intercept('GET', '**/firestore/**', { fixture: 'firestore-response.json' })
  cy.intercept('POST', '**/firestore/**', { statusCode: 200, body: { success: true } })
  cy.intercept('PATCH', '**/firestore/**', { statusCode: 200, body: { success: true } })
})

// إضافة تعريفات TypeScript للأوامر المخصصة
declare global {
  namespace Cypress {
    interface Chainable {
      mockLogin(userType?: 'customer' | 'merchant' | 'representative'): Chainable<void>
      mockLogout(): Chainable<void>
      waitForPageLoad(): Chainable<void>
      visitWithLocale(path: string, locale?: 'ar' | 'en'): Chainable<void>
      waitForLoadingToFinish(): Chainable<void>
      shouldContainArabicText(text: string): Chainable<void>
      shouldContainEnglishText(text: string): Chainable<void>
      mockSignup(userType?: 'customer' | 'merchant' | 'representative'): Chainable<void>
      mockFirebaseAuth(): Chainable<void>
      mockGeolocation(lat?: number, lng?: number): Chainable<void>
      mockERPIntegration(): Chainable<void>
      mockCRMCustomers(): Chainable<void>
      mockCoupons(): Chainable<void>
      mockLoyaltyProgram(): Chainable<void>
      fillForm(formData: Record<string, string>): Chainable<void>
      waitForAPI(alias: string, timeout?: number): Chainable<void>
      checkDashboardStats(): Chainable<void>
      loginAs(userType: string, userData: any): Chainable<void>
      mockStoreData(storeData: any): Chainable<void>
      mockPendingMerchants(merchants: any[]): Chainable<void>
      signupAsMerchant(name: string, email: string, password: string): Chainable<void>
      approveMerchant(email: string, notes: string): Chainable<void>
      mockFirestore(): Chainable<void>
    }
  }
}
