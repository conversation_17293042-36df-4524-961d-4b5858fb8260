// src/hooks/use-performance.ts
"use client";

import { useEffect, useState, useCallback } from 'react';

interface PerformanceMetrics {
  isSlowConnection: boolean;
  prefersReducedMotion: boolean;
  isLowEndDevice: boolean;
  connectionType: string;
}

/**
 * Hook محسن لمراقبة أداء الجهاز والاتصال
 * يساعد في تحسين التجربة بناءً على قدرات الجهاز
 */
export function usePerformance(): PerformanceMetrics {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    isSlowConnection: false,
    prefersReducedMotion: false,
    isLowEndDevice: false,
    connectionType: 'unknown',
  });

  const checkPerformance = useCallback(() => {
    if (typeof window === 'undefined') return;

    // فحص تفضيل تقليل الرسوم المتحركة
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    // فحص نوع الاتصال
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    const isSlowConnection = connection ? 
      (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g' || connection.saveData) : 
      false;

    // فحص قدرات الجهاز
    const isLowEndDevice = navigator.hardwareConcurrency ? navigator.hardwareConcurrency <= 2 : false;

    const connectionType = connection ? connection.effectiveType || 'unknown' : 'unknown';

    setMetrics({
      isSlowConnection,
      prefersReducedMotion,
      isLowEndDevice,
      connectionType,
    });
  }, []);

  useEffect(() => {
    checkPerformance();

    // مراقبة تغييرات الاتصال
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    if (connection) {
      connection.addEventListener('change', checkPerformance);
      return () => connection.removeEventListener('change', checkPerformance);
    }
  }, [checkPerformance]);

  return metrics;
}

/**
 * Hook لتحسين الرسوم المتحركة بناءً على الأداء
 */
export function useOptimizedAnimations() {
  const { prefersReducedMotion, isSlowConnection, isLowEndDevice } = usePerformance();
  
  const shouldReduceAnimations = prefersReducedMotion || isSlowConnection || isLowEndDevice;
  
  return {
    shouldReduceAnimations,
    animationDuration: shouldReduceAnimations ? 0 : 300,
    enableComplexAnimations: !shouldReduceAnimations,
  };
}

/**
 * Hook لتحسين تحميل الصور بناءً على الأداء
 */
export function useOptimizedImages() {
  const { isSlowConnection } = usePerformance();
  
  return {
    quality: isSlowConnection ? 60 : 80,
    format: isSlowConnection ? 'webp' : 'auto',
    loading: 'lazy' as const,
    placeholder: 'blur' as const,
  };
}
