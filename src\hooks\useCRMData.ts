// src/hooks/useCRMData.ts
'use client';

import { useState, useEffect, useCallback } from 'react';
import { crmService } from '@/services/crmService';
import { customerAnalyticsService } from '@/services/customerAnalyticsService';
import { customerSegmentationService } from '@/services/customerSegmentationService';
import { communicationService } from '@/services/communicationService';
import type { 
  CustomerProfile, 
  CustomerInteraction, 
  CustomerSegment,
  CRMAnalytics,
  CustomerCommunication,
  CustomerNote
} from '@/types';

export interface CRMFilters {
  searchQuery?: string;
  tier?: string;
  segment?: string;
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  status?: string;
}

export interface UseCRMDataReturn {
  // بيانات العملاء
  customers: CustomerProfile[];
  selectedCustomer: CustomerProfile | null;
  customerInteractions: CustomerInteraction[];
  
  // التقسيمات
  segments: CustomerSegment[];
  
  // التحليلات
  analytics: CRMAnalytics | null;
  
  // حملات التواصل
  communications: CustomerCommunication[];
  
  // حالات التحميل
  loading: {
    customers: boolean;
    customer: boolean;
    interactions: boolean;
    segments: boolean;
    analytics: boolean;
    communications: boolean;
  };
  
  // الأخطاء
  errors: {
    customers: string | null;
    customer: string | null;
    interactions: string | null;
    segments: string | null;
    analytics: string | null;
    communications: string | null;
  };
  
  // الإجراءات
  actions: {
    // إدارة العملاء
    loadCustomers: (merchantId: string, filters?: CRMFilters) => Promise<void>;
    loadCustomer: (merchantId: string, userId: string) => Promise<void>;
    searchCustomers: (merchantId: string, query: string, filters?: CRMFilters) => Promise<void>;
    addCustomerNote: (merchantId: string, userId: string, note: Omit<CustomerNote, 'id' | 'createdAt'>) => Promise<void>;
    updateCustomerTags: (merchantId: string, userId: string, tags: string[]) => Promise<void>;
    
    // إدارة التفاعلات
    loadCustomerInteractions: (customerId: string, merchantId: string) => Promise<void>;
    addCustomerInteraction: (interaction: Omit<CustomerInteraction, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
    
    // إدارة التقسيمات
    loadSegments: (merchantId: string) => Promise<void>;
    createSegment: (segmentData: Omit<CustomerSegment, 'id' | 'createdAt' | 'updatedAt' | 'lastCalculatedAt'>) => Promise<void>;
    updateSegment: (segmentId: string, updates: Partial<CustomerSegment>) => Promise<void>;
    deleteSegment: (segmentId: string) => Promise<void>;
    createDefaultSegments: (merchantId: string) => Promise<void>;
    
    // التحليلات
    generateAnalytics: (merchantId: string, period: { startDate: Date; endDate: Date; type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' }) => Promise<void>;
    
    // حملات التواصل
    loadCommunications: (merchantId: string) => Promise<void>;
    createCommunication: (communicationData: Omit<CustomerCommunication, 'id' | 'createdAt' | 'updatedAt' | 'sentAt' | 'stats'>) => Promise<void>;
    sendCommunication: (communicationId: string) => Promise<void>;
    
    // إعادة تعيين البيانات
    resetCustomers: () => void;
    resetSelectedCustomer: () => void;
    resetErrors: () => void;
  };
}

export const useCRMData = (): UseCRMDataReturn => {
  // حالات البيانات
  const [customers, setCustomers] = useState<CustomerProfile[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerProfile | null>(null);
  const [customerInteractions, setCustomerInteractions] = useState<CustomerInteraction[]>([]);
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [analytics, setAnalytics] = useState<CRMAnalytics | null>(null);
  const [communications, setCommunications] = useState<CustomerCommunication[]>([]);

  // حالات التحميل
  const [loading, setLoading] = useState({
    customers: false,
    customer: false,
    interactions: false,
    segments: false,
    analytics: false,
    communications: false
  });

  // حالات الأخطاء
  const [errors, setErrors] = useState({
    customers: null as string | null,
    customer: null as string | null,
    interactions: null as string | null,
    segments: null as string | null,
    analytics: null as string | null,
    communications: null as string | null
  });

  // مساعد لتحديث حالة التحميل
  const setLoadingState = useCallback((key: keyof typeof loading, value: boolean) => {
    setLoading(prev => ({ ...prev, [key]: value }));
  }, []);

  // مساعد لتحديث حالة الخطأ
  const setErrorState = useCallback((key: keyof typeof errors, value: string | null) => {
    setErrors(prev => ({ ...prev, [key]: value }));
  }, []);

  // ===== إدارة العملاء =====

  const loadCustomers = useCallback(async (merchantId: string, filters?: CRMFilters) => {
    setLoadingState('customers', true);
    setErrorState('customers', null);

    try {
      let result;
      
      if (filters?.searchQuery) {
        result = await crmService.searchCustomers(merchantId, filters.searchQuery, {
          tier: filters.tier,
          tags: filters.tags,
          dateRange: filters.dateRange
        });
        setCustomers(result);
      } else {
        const { customers: fetchedCustomers } = await crmService.getMerchantCustomers(merchantId, 50, undefined, {
          tier: filters?.tier,
          searchQuery: filters?.searchQuery,
          tags: filters?.tags
        });
        setCustomers(fetchedCustomers);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
      setErrorState('customers', error instanceof Error ? error.message : 'فشل في تحميل العملاء');
    } finally {
      setLoadingState('customers', false);
    }
  }, [setLoadingState, setErrorState]);

  const loadCustomer = useCallback(async (merchantId: string, userId: string) => {
    setLoadingState('customer', true);
    setErrorState('customer', null);

    try {
      const customer = await crmService.getCustomerProfile(merchantId, userId);
      setSelectedCustomer(customer);
    } catch (error) {
      console.error('Error loading customer:', error);
      setErrorState('customer', error instanceof Error ? error.message : 'فشل في تحميل بيانات العميل');
    } finally {
      setLoadingState('customer', false);
    }
  }, [setLoadingState, setErrorState]);

  const searchCustomers = useCallback(async (merchantId: string, query: string, filters?: CRMFilters) => {
    setLoadingState('customers', true);
    setErrorState('customers', null);

    try {
      const result = await crmService.searchCustomers(merchantId, query, {
        tier: filters?.tier,
        tags: filters?.tags,
        dateRange: filters?.dateRange
      });
      setCustomers(result);
    } catch (error) {
      console.error('Error searching customers:', error);
      setErrorState('customers', error instanceof Error ? error.message : 'فشل في البحث عن العملاء');
    } finally {
      setLoadingState('customers', false);
    }
  }, [setLoadingState, setErrorState]);

  const addCustomerNote = useCallback(async (
    merchantId: string, 
    userId: string, 
    note: Omit<CustomerNote, 'id' | 'createdAt'>
  ) => {
    try {
      await crmService.addCustomerNote(merchantId, userId, note);
      // إعادة تحميل بيانات العميل
      await loadCustomer(merchantId, userId);
    } catch (error) {
      console.error('Error adding customer note:', error);
      throw error;
    }
  }, [loadCustomer]);

  const updateCustomerTags = useCallback(async (
    merchantId: string, 
    userId: string, 
    tags: string[]
  ) => {
    try {
      await crmService.updateCustomerTags(merchantId, userId, tags);
      // إعادة تحميل بيانات العميل
      await loadCustomer(merchantId, userId);
    } catch (error) {
      console.error('Error updating customer tags:', error);
      throw error;
    }
  }, [loadCustomer]);

  // ===== إدارة التفاعلات =====

  const loadCustomerInteractions = useCallback(async (customerId: string, merchantId: string) => {
    setLoadingState('interactions', true);
    setErrorState('interactions', null);

    try {
      const { interactions } = await crmService.getCustomerInteractions(customerId, merchantId, 50);
      setCustomerInteractions(interactions);
    } catch (error) {
      console.error('Error loading customer interactions:', error);
      setErrorState('interactions', error instanceof Error ? error.message : 'فشل في تحميل تفاعلات العميل');
    } finally {
      setLoadingState('interactions', false);
    }
  }, [setLoadingState, setErrorState]);

  const addCustomerInteraction = useCallback(async (
    interaction: Omit<CustomerInteraction, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    try {
      await crmService.addCustomerInteraction(interaction);
      // إعادة تحميل التفاعلات
      await loadCustomerInteractions(interaction.customerId, interaction.merchantId);
    } catch (error) {
      console.error('Error adding customer interaction:', error);
      throw error;
    }
  }, [loadCustomerInteractions]);

  // ===== إدارة التقسيمات =====

  const loadSegments = useCallback(async (merchantId: string) => {
    setLoadingState('segments', true);
    setErrorState('segments', null);

    try {
      const fetchedSegments = await customerSegmentationService.getMerchantSegments(merchantId);
      setSegments(fetchedSegments);
    } catch (error) {
      console.error('Error loading segments:', error);
      setErrorState('segments', error instanceof Error ? error.message : 'فشل في تحميل التقسيمات');
    } finally {
      setLoadingState('segments', false);
    }
  }, [setLoadingState, setErrorState]);

  const createSegment = useCallback(async (
    segmentData: Omit<CustomerSegment, 'id' | 'createdAt' | 'updatedAt' | 'lastCalculatedAt'>
  ) => {
    try {
      await customerSegmentationService.createSegment(segmentData);
      // إعادة تحميل التقسيمات
      await loadSegments(segmentData.merchantId);
    } catch (error) {
      console.error('Error creating segment:', error);
      throw error;
    }
  }, [loadSegments]);

  const updateSegment = useCallback(async (segmentId: string, updates: Partial<CustomerSegment>) => {
    try {
      await customerSegmentationService.updateSegment(segmentId, updates);
      // تحديث التقسيم في الحالة المحلية
      setSegments(prev => prev.map(segment => 
        segment.id === segmentId ? { ...segment, ...updates } : segment
      ));
    } catch (error) {
      console.error('Error updating segment:', error);
      throw error;
    }
  }, []);

  const deleteSegment = useCallback(async (segmentId: string) => {
    try {
      await customerSegmentationService.deleteSegment(segmentId);
      // إزالة التقسيم من الحالة المحلية
      setSegments(prev => prev.filter(segment => segment.id !== segmentId));
    } catch (error) {
      console.error('Error deleting segment:', error);
      throw error;
    }
  }, []);

  const createDefaultSegments = useCallback(async (merchantId: string) => {
    try {
      await customerSegmentationService.createDefaultSegments(merchantId);
      // إعادة تحميل التقسيمات
      await loadSegments(merchantId);
    } catch (error) {
      console.error('Error creating default segments:', error);
      throw error;
    }
  }, [loadSegments]);

  // ===== التحليلات =====

  const generateAnalytics = useCallback(async (
    merchantId: string, 
    period: { startDate: Date; endDate: Date; type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' }
  ) => {
    setLoadingState('analytics', true);
    setErrorState('analytics', null);

    try {
      const analyticsData = await customerAnalyticsService.generateCRMAnalytics(merchantId, period);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error generating analytics:', error);
      setErrorState('analytics', error instanceof Error ? error.message : 'فشل في إنشاء التحليلات');
    } finally {
      setLoadingState('analytics', false);
    }
  }, [setLoadingState, setErrorState]);

  // ===== حملات التواصل =====

  const loadCommunications = useCallback(async (merchantId: string) => {
    setLoadingState('communications', true);
    setErrorState('communications', null);

    try {
      const fetchedCommunications = await communicationService.getMerchantCommunications(merchantId);
      setCommunications(fetchedCommunications);
    } catch (error) {
      console.error('Error loading communications:', error);
      setErrorState('communications', error instanceof Error ? error.message : 'فشل في تحميل حملات التواصل');
    } finally {
      setLoadingState('communications', false);
    }
  }, [setLoadingState, setErrorState]);

  const createCommunication = useCallback(async (
    communicationData: Omit<CustomerCommunication, 'id' | 'createdAt' | 'updatedAt' | 'sentAt' | 'stats'>
  ) => {
    try {
      await communicationService.createCommunication(communicationData);
      // إعادة تحميل حملات التواصل
      await loadCommunications(communicationData.merchantId);
    } catch (error) {
      console.error('Error creating communication:', error);
      throw error;
    }
  }, [loadCommunications]);

  const sendCommunication = useCallback(async (communicationId: string) => {
    try {
      await communicationService.sendCommunication(communicationId);
      // تحديث حالة الحملة في الحالة المحلية
      setCommunications(prev => prev.map(comm => 
        comm.id === communicationId ? { ...comm, status: 'sent' } : comm
      ));
    } catch (error) {
      console.error('Error sending communication:', error);
      throw error;
    }
  }, []);

  // ===== إعادة تعيين البيانات =====

  const resetCustomers = useCallback(() => {
    setCustomers([]);
    setErrorState('customers', null);
  }, [setErrorState]);

  const resetSelectedCustomer = useCallback(() => {
    setSelectedCustomer(null);
    setCustomerInteractions([]);
    setErrorState('customer', null);
    setErrorState('interactions', null);
  }, [setErrorState]);

  const resetErrors = useCallback(() => {
    setErrors({
      customers: null,
      customer: null,
      interactions: null,
      segments: null,
      analytics: null,
      communications: null
    });
  }, []);

  return {
    // البيانات
    customers,
    selectedCustomer,
    customerInteractions,
    segments,
    analytics,
    communications,
    
    // حالات التحميل والأخطاء
    loading,
    errors,
    
    // الإجراءات
    actions: {
      // إدارة العملاء
      loadCustomers,
      loadCustomer,
      searchCustomers,
      addCustomerNote,
      updateCustomerTags,
      
      // إدارة التفاعلات
      loadCustomerInteractions,
      addCustomerInteraction,
      
      // إدارة التقسيمات
      loadSegments,
      createSegment,
      updateSegment,
      deleteSegment,
      createDefaultSegments,
      
      // التحليلات
      generateAnalytics,
      
      // حملات التواصل
      loadCommunications,
      createCommunication,
      sendCommunication,
      
      // إعادة تعيين البيانات
      resetCustomers,
      resetSelectedCustomer,
      resetErrors
    }
  };
};
