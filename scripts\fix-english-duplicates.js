#!/usr/bin/env node

/**
 * سكريبت إصلاح المفاتيح المكررة في ملف الترجمة الإنجليزية
 * يقوم بإزالة المفاتيح المكررة في نهاية الملف والاحتفاظ بالنسخة الأصلية في قسم common
 */

const fs = require('fs');
const path = require('path');

// مسارات الملفات
const EN_FILE_PATH = path.join(__dirname, '..', 'src', 'locales', 'en.json');
const BACKUP_DIR = path.join(__dirname, '..', 'src', 'locales');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(BACKUP_DIR, `en_backup_${timestamp}.json`);
    
    try {
        fs.copyFileSync(filePath, backupPath);
        console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
        return backupPath;
    } catch (error) {
        console.error(`❌ فشل في إنشاء النسخة الاحتياطية: ${error.message}`);
        throw error;
    }
}

/**
 * تحميل وتحليل ملف JSON
 */
function loadJsonFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error(`❌ فشل في تحميل الملف ${filePath}: ${error.message}`);
        throw error;
    }
}

/**
 * حفظ ملف JSON مع تنسيق جميل
 */
function saveJsonFile(filePath, data) {
    try {
        const content = JSON.stringify(data, null, 2);
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ تم حفظ الملف: ${filePath}`);
    } catch (error) {
        console.error(`❌ فشل في حفظ الملف ${filePath}: ${error.message}`);
        throw error;
    }
}

/**
 * العثور على المفاتيح المكررة
 */
function findDuplicateKeys(obj, path = '') {
    const duplicates = [];
    const seen = new Map();
    
    function traverse(current, currentPath) {
        if (typeof current === 'object' && current !== null) {
            for (const [key, value] of Object.entries(current)) {
                const fullPath = currentPath ? `${currentPath}.${key}` : key;
                
                if (typeof value === 'string') {
                    if (seen.has(key)) {
                        duplicates.push({
                            key,
                            path1: seen.get(key),
                            path2: fullPath,
                            value1: seen.get(key + '_value'),
                            value2: value
                        });
                    } else {
                        seen.set(key, fullPath);
                        seen.set(key + '_value', value);
                    }
                } else {
                    traverse(value, fullPath);
                }
            }
        }
    }
    
    traverse(obj, path);
    return duplicates;
}

/**
 * إزالة المفاتيح المكررة من نهاية الملف
 */
function removeDuplicatesFromEnd(data) {
    // قائمة المفاتيح المكررة المعروفة في نهاية الملف
    const duplicateKeysAtEnd = [
        'sar', 'status', 'processing', 'cancel', 'loading', 'close', 'actions',
        'category', 'price', 'stock', 'active', 'inactive', 'delete', 'back',
        'next', 'previous', 'search', 'filter', 'store', 'available', 'outOfStock',
        'address', 'businessHours', 'totalOrders', 'productNotFound', 'storeNotFound',
        'description', 'moderationNotes'
    ];
    
    const cleanedData = { ...data };
    let removedCount = 0;
    
    // إزالة المفاتيح المكررة من المستوى الأعلى
    duplicateKeysAtEnd.forEach(key => {
        if (cleanedData.hasOwnProperty(key)) {
            delete cleanedData[key];
            removedCount++;
            console.log(`🗑️  تم حذف المفتاح المكرر: "${key}"`);
        }
    });
    
    return { cleanedData, removedCount };
}

/**
 * التحقق من وجود المفاتيح في قسم common
 */
function verifyCommonSection(data) {
    if (!data.common || typeof data.common !== 'object') {
        console.warn('⚠️  تحذير: قسم "common" غير موجود أو غير صحيح');
        return false;
    }
    
    const commonKeys = Object.keys(data.common);
    console.log(`✅ قسم "common" يحتوي على ${commonKeys.length} مفتاح`);
    
    // عرض بعض المفاتيح المهمة
    const importantKeys = ['loading', 'cancel', 'delete', 'save', 'close'];
    const foundKeys = importantKeys.filter(key => data.common.hasOwnProperty(key));
    
    if (foundKeys.length > 0) {
        console.log(`✅ المفاتيح المهمة موجودة في common: ${foundKeys.join(', ')}`);
        return true;
    }
    
    return false;
}

/**
 * الدالة الرئيسية
 */
async function main() {
    console.log('🚀 بدء إصلاح المفاتيح المكررة في الملف الإنجليزي...\n');
    
    try {
        // التحقق من وجود الملف
        if (!fs.existsSync(EN_FILE_PATH)) {
            throw new Error(`الملف غير موجود: ${EN_FILE_PATH}`);
        }
        
        // إنشاء نسخة احتياطية
        console.log('📋 إنشاء نسخة احتياطية...');
        createBackup(EN_FILE_PATH);
        
        // تحميل الملف
        console.log('\n📖 تحميل ملف الترجمة الإنجليزية...');
        const originalData = loadJsonFile(EN_FILE_PATH);
        
        // العثور على المفاتيح المكررة
        console.log('\n🔍 البحث عن المفاتيح المكررة...');
        const duplicates = findDuplicateKeys(originalData);
        
        if (duplicates.length > 0) {
            console.log(`❌ تم العثور على ${duplicates.length} مفتاح مكرر:`);
            duplicates.forEach(dup => {
                console.log(`   - "${dup.key}": ${dup.path1} و ${dup.path2}`);
            });
        } else {
            console.log('✅ لم يتم العثور على مفاتيح مكررة');
        }
        
        // التحقق من قسم common
        console.log('\n🔍 التحقق من قسم common...');
        const hasValidCommon = verifyCommonSection(originalData);
        
        if (!hasValidCommon) {
            throw new Error('قسم "common" غير صحيح أو غير مكتمل');
        }
        
        // إزالة المفاتيح المكررة
        console.log('\n🧹 إزالة المفاتيح المكررة...');
        const { cleanedData, removedCount } = removeDuplicatesFromEnd(originalData);
        
        if (removedCount === 0) {
            console.log('✅ لا توجد مفاتيح مكررة لإزالتها');
            return;
        }
        
        // حفظ الملف المُنظف
        console.log('\n💾 حفظ الملف المُنظف...');
        saveJsonFile(EN_FILE_PATH, cleanedData);
        
        // التحقق النهائي
        console.log('\n🔍 التحقق النهائي...');
        const finalData = loadJsonFile(EN_FILE_PATH);
        const finalDuplicates = findDuplicateKeys(finalData);
        
        console.log('\n📊 ملخص النتائج:');
        console.log(`✅ تم حذف ${removedCount} مفتاح مكرر`);
        console.log(`✅ المفاتيح المكررة المتبقية: ${finalDuplicates.length}`);
        
        if (finalDuplicates.length === 0) {
            console.log('🎉 تم إصلاح جميع المفاتيح المكررة بنجاح!');
        } else {
            console.log('⚠️  لا تزال هناك مفاتيح مكررة تحتاج إلى مراجعة يدوية:');
            finalDuplicates.forEach(dup => {
                console.log(`   - "${dup.key}": ${dup.path1} و ${dup.path2}`);
            });
        }
        
        console.log('\n✅ انتهى إصلاح المفاتيح المكررة');
        
    } catch (error) {
        console.error(`\n❌ خطأ في إصلاح المفاتيح المكررة: ${error.message}`);
        process.exit(1);
    }
}

// تشغيل السكريبت
if (require.main === module) {
    main();
}

module.exports = {
    findDuplicateKeys,
    removeDuplicatesFromEnd,
    verifyCommonSection
};
