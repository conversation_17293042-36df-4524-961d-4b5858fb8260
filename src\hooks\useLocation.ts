// src/hooks/useLocation.ts
"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { locationService, type LocationData, type RepresentativeLocation, type DeliveryRoute } from '@/services/locationService';

// Hook للموقع الحالي
interface UseCurrentLocationReturn {
  location: LocationData | null;
  loading: boolean;
  error: string | null;
  requestLocation: () => Promise<void>;
  hasPermission: boolean;
}

export function useCurrentLocation(): UseCurrentLocationReturn {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);

  // طلب الموقع الحالي
  const requestLocation = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const hasPermission = await locationService.requestLocationPermission();
      setHasPermission(hasPermission);

      if (!hasPermission) {
        setError('لم يتم منح إذن الوصول للموقع');
        return;
      }

      const currentLocation = await locationService.getCurrentLocation();
      if (currentLocation) {
        setLocation(currentLocation);
      } else {
        setError('فشل في الحصول على الموقع الحالي');
      }
    } catch (err) {
      console.error('Error requesting location:', err);
      setError('حدث خطأ في الحصول على الموقع');
    } finally {
      setLoading(false);
    }
  }, []);

  // طلب الموقع عند التحميل الأول
  useEffect(() => {
    requestLocation();
  }, [requestLocation]);

  return {
    location,
    loading,
    error,
    requestLocation,
    hasPermission
  };
}

// Hook لتتبع موقع المندوب
interface UseLocationTrackingReturn {
  isTracking: boolean;
  currentLocation: LocationData | null;
  error: string | null;
  startTracking: (orderId?: string) => Promise<boolean>;
  stopTracking: () => void;
}

export function useLocationTracking(): UseLocationTrackingReturn {
  const { user } = useAuth();
  const [isTracking, setIsTracking] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // بدء التتبع
  const startTracking = useCallback(async (orderId?: string): Promise<boolean> => {
    if (!user) {
      setError('يجب تسجيل الدخول أولاً');
      return false;
    }

    try {
      setError(null);
      const success = await locationService.startLocationTracking(user.uid, orderId);
      
      if (success) {
        setIsTracking(true);
        
        // الحصول على الموقع الحالي
        const location = await locationService.getCurrentLocation();
        setCurrentLocation(location);
      } else {
        setError('فشل في بدء تتبع الموقع');
      }
      
      return success;
    } catch (err) {
      console.error('Error starting location tracking:', err);
      setError('حدث خطأ في بدء تتبع الموقع');
      return false;
    }
  }, [user]);

  // إيقاف التتبع
  const stopTracking = useCallback(() => {
    locationService.stopLocationTracking();
    setIsTracking(false);
    setCurrentLocation(null);
  }, []);

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (isTracking) {
        stopTracking();
      }
    };
  }, [isTracking, stopTracking]);

  return {
    isTracking,
    currentLocation,
    error,
    startTracking,
    stopTracking
  };
}

// Hook لموقع المندوب (للعملاء والتجار)
interface UseRepresentativeLocationReturn {
  location: RepresentativeLocation | null;
  loading: boolean;
  error: string | null;
}

export function useRepresentativeLocation(representativeUid: string): UseRepresentativeLocationReturn {
  const [location, setLocation] = useState<RepresentativeLocation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!representativeUid) {
      setLocation(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // الاستماع لتحديثات الموقع في الوقت الفعلي
    const unsubscribe = locationService.subscribeToRepresentativeLocation(
      representativeUid,
      (repLocation) => {
        setLocation(repLocation);
        setLoading(false);
        
        if (!repLocation) {
          setError('لم يتم العثور على موقع المندوب');
        }
      }
    );

    return unsubscribe;
  }, [representativeUid]);

  return {
    location,
    loading,
    error
  };
}

// Hook للمندوبين القريبين
interface UseNearbyRepresentativesReturn {
  representatives: RepresentativeLocation[];
  loading: boolean;
  error: string | null;
  findNearby: (location: LocationData, radiusKm?: number) => Promise<void>;
}

export function useNearbyRepresentatives(): UseNearbyRepresentativesReturn {
  const [representatives, setRepresentatives] = useState<RepresentativeLocation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const findNearby = useCallback(async (location: LocationData, radiusKm: number = 10) => {
    try {
      setLoading(true);
      setError(null);

      const nearbyReps = await locationService.getNearbyRepresentatives(location, radiusKm);
      setRepresentatives(nearbyReps);
    } catch (err) {
      console.error('Error finding nearby representatives:', err);
      setError('فشل في البحث عن المندوبين القريبين');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    representatives,
    loading,
    error,
    findNearby
  };
}

// Hook لإدارة مسار التوصيل
interface UseDeliveryRouteReturn {
  route: DeliveryRoute | null;
  loading: boolean;
  error: string | null;
  startRoute: (orderId: string, startLocation: LocationData, endLocation: LocationData) => Promise<string | null>;
  completeRoute: (routeId: string, finalLocation: LocationData) => Promise<void>;
}

export function useDeliveryRoute(): UseDeliveryRouteReturn {
  const { user } = useAuth();
  const [route, setRoute] = useState<DeliveryRoute | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // بدء مسار جديد
  const startRoute = useCallback(async (
    orderId: string, 
    startLocation: LocationData, 
    endLocation: LocationData
  ): Promise<string | null> => {
    if (!user) {
      setError('يجب تسجيل الدخول أولاً');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      const routeId = await locationService.startDeliveryRoute(
        orderId,
        user.uid,
        startLocation,
        endLocation
      );

      if (routeId) {
        // يمكن إضافة منطق لجلب تفاصيل المسار هنا
        return routeId;
      } else {
        setError('فشل في بدء مسار التوصيل');
        return null;
      }
    } catch (err) {
      console.error('Error starting delivery route:', err);
      setError('حدث خطأ في بدء مسار التوصيل');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // إنهاء المسار
  const completeRoute = useCallback(async (routeId: string, finalLocation: LocationData) => {
    try {
      setLoading(true);
      setError(null);

      await locationService.completeDeliveryRoute(routeId, finalLocation);
      setRoute(null);
    } catch (err) {
      console.error('Error completing delivery route:', err);
      setError('حدث خطأ في إنهاء مسار التوصيل');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    route,
    loading,
    error,
    startRoute,
    completeRoute
  };
}

// Hook لحساب المسافة والوقت
interface UseDistanceCalculatorReturn {
  calculateDistance: (point1: LocationData, point2: LocationData) => number;
  formatDistance: (distance: number) => string;
  estimateDeliveryTime: (distance: number) => string;
}

export function useDistanceCalculator(): UseDistanceCalculatorReturn {
  const calculateDistance = useCallback((point1: LocationData, point2: LocationData): number => {
    return locationService.calculateDistance(point1, point2);
  }, []);

  const formatDistance = useCallback((distance: number): string => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)} متر`;
    }
    return `${distance.toFixed(1)} كم`;
  }, []);

  const estimateDeliveryTime = useCallback((distance: number): string => {
    const averageSpeed = 30; // كم/ساعة
    const timeInHours = distance / averageSpeed;
    const timeInMinutes = Math.ceil(timeInHours * 60);
    
    if (timeInMinutes < 60) {
      return `${timeInMinutes} دقيقة`;
    }
    
    const hours = Math.floor(timeInMinutes / 60);
    const minutes = timeInMinutes % 60;
    
    if (minutes === 0) {
      return `${hours} ساعة`;
    }
    
    return `${hours} ساعة و ${minutes} دقيقة`;
  }, []);

  return {
    calculateDistance,
    formatDistance,
    estimateDeliveryTime
  };
}
