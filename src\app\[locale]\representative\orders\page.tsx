'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  MapPin, 
  Clock, 
  Phone, 
  CheckCircle, 
  XCircle,
  Navigation,
  DollarSign
} from 'lucide-react';
import { useOrders } from '@/hooks/useOrders';
import { OrderDocument, OrderStatus } from '@/types';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

export default function RepresentativeOrdersPage() {
  const { user } = useAuth();
  const t = useTranslations();
  const [activeTab, setActiveTab] = useState('available');
  
  // جلب الطلبات المتاحة للتوصيل
  const { 
    orders: availableOrders, 
    loading: availableLoading 
  } = useOrders({ 
    status: 'ready',
    representativeUid: null // الطلبات غير المعينة لمندوب
  });
  
  // جلب طلبات المندوب الحالية
  const { 
    orders: myOrders, 
    loading: myOrdersLoading 
  } = useOrders({ 
    representativeUid: user?.uid,
    realtime: true
  });

  const handleAcceptOrder = async (orderId: string) => {
    try {
      // تحديث الطلب لتعيين المندوب
      const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');
      
      await updateDoc(doc(db, 'orders', orderId), {
        representativeUid: user?.uid,
        status: 'picked_up' as OrderStatus,
        pickedUpAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      toast.success(t('representative.orders.acceptSuccess'));
    } catch (error) {
      console.error('Error accepting order:', error);
      toast.error(t('representative.orders.acceptError'));
    }
  };

  const handleCompleteDelivery = async (orderId: string) => {
    try {
      const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');
      
      await updateDoc(doc(db, 'orders', orderId), {
        status: 'delivered' as OrderStatus,
        deliveredAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      toast.success(t('representative.orders.deliverySuccess'));
    } catch (error) {
      console.error('Error completing delivery:', error);
      toast.error(t('representative.orders.deliveryError'));
    }
  };

  const getStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      ready: { label: t('orders.status.ready'), variant: 'secondary' as const },
      picked_up: { label: t('orders.status.pickedUp'), variant: 'default' as const },
      out_for_delivery: { label: t('orders.status.outForDelivery'), variant: 'default' as const },
      delivered: { label: t('orders.status.delivered'), variant: 'success' as const },
    };
    
    const config = statusConfig[status] || { label: status, variant: 'secondary' as const };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const OrderCard = ({ order, showActions = false }: { order: OrderDocument; showActions?: boolean }) => (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">
              {t('orders.orderNumber', { number: order.orderNumber })}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {order.createdAt?.toDate().toLocaleDateString('ar-SA')}
            </p>
          </div>
          {getStatusBadge(order.status)}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* معلومات العميل */}
        <div className="flex items-center gap-2">
          <Phone className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{order.shippingInfo?.phoneNumber}</span>
        </div>
        
        {/* عنوان التوصيل */}
        <div className="flex items-start gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
          <div className="text-sm">
            <p>{order.shippingInfo?.address}</p>
            {order.shippingInfo?.city && (
              <p className="text-muted-foreground">{order.shippingInfo.city}</p>
            )}
          </div>
        </div>
        
        {/* تفاصيل الطلب */}
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {order.items?.length} {t('orders.items')}
          </span>
        </div>
        
        {/* المبلغ الإجمالي */}
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">
            {formatCurrency(order.totalAmount)}
          </span>
        </div>
        
        {/* الإجراءات */}
        {showActions && (
          <div className="flex gap-2 pt-2">
            {order.status === 'ready' && (
              <Button 
                onClick={() => handleAcceptOrder(order.id)}
                className="flex-1"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('representative.orders.acceptOrder')}
              </Button>
            )}
            
            {(order.status === 'picked_up' || order.status === 'out_for_delivery') && (
              <>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    // فتح خرائط جوجل للتوجه للعنوان
                    const address = encodeURIComponent(order.shippingInfo?.address || '');
                    window.open(`https://maps.google.com/maps?q=${address}`, '_blank');
                  }}
                >
                  <Navigation className="h-4 w-4 mr-2" />
                  {t('representative.orders.navigate')}
                </Button>
                
                <Button 
                  onClick={() => handleCompleteDelivery(order.id)}
                  className="flex-1"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('representative.orders.completeDelivery')}
                </Button>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{t('representative.orders.title')}</h1>
        <p className="text-muted-foreground">
          {t('representative.orders.description')}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="available">
            {t('representative.orders.available')}
          </TabsTrigger>
          <TabsTrigger value="active">
            {t('representative.orders.active')}
          </TabsTrigger>
          <TabsTrigger value="completed">
            {t('representative.orders.completed')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="available" className="mt-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">
              {t('representative.orders.availableOrders')}
            </h2>
            
            {availableLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
              </div>
            ) : availableOrders.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {t('representative.orders.noAvailableOrders')}
                  </p>
                </CardContent>
              </Card>
            ) : (
              availableOrders.map(order => (
                <OrderCard key={order.id} order={order} showActions={true} />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="active" className="mt-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">
              {t('representative.orders.activeOrders')}
            </h2>
            
            {myOrdersLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
              </div>
            ) : myOrders.filter(o => ['picked_up', 'out_for_delivery'].includes(o.status)).length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {t('representative.orders.noActiveOrders')}
                  </p>
                </CardContent>
              </Card>
            ) : (
              myOrders
                .filter(order => ['picked_up', 'out_for_delivery'].includes(order.status))
                .map(order => (
                  <OrderCard key={order.id} order={order} showActions={true} />
                ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="completed" className="mt-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">
              {t('representative.orders.completedOrders')}
            </h2>
            
            {myOrdersLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
              </div>
            ) : myOrders.filter(o => o.status === 'delivered').length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {t('representative.orders.noCompletedOrders')}
                  </p>
                </CardContent>
              </Card>
            ) : (
              myOrders
                .filter(order => order.status === 'delivered')
                .map(order => (
                  <OrderCard key={order.id} order={order} />
                ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
