// src/app/api/ai/auto-approve-merchant/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { AIApprovalService } from '@/services/aiApprovalService';
import type { StoreDocument, UserDocument } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { merchantUid } = await request.json();

    // التحقق من صحة البيانات
    if (!merchantUid) {
      return NextResponse.json(
        { error: 'مطلوب معرف التاجر' },
        { status: 400 }
      );
    }

    // جلب بيانات التاجر
    const userDocRef = doc(db, 'users', merchantUid);
    const userDocSnap = await getDoc(userDocRef);

    if (!userDocSnap.exists()) {
      return NextResponse.json(
        { error: 'التاجر غير موجود' },
        { status: 404 }
      );
    }

    const userData = userDocSnap.data() as UserDocument;

    // جلب بيانات المتجر
    const storeDocRef = doc(db, 'stores', merchantUid);
    const storeDocSnap = await getDoc(storeDocRef);

    if (!storeDocSnap.exists()) {
      return NextResponse.json(
        { error: 'بيانات المتجر غير موجودة' },
        { status: 404 }
      );
    }

    const storeData = storeDocSnap.data() as StoreDocument;

    // التحقق من حالة التاجر
    if (storeData.approvalStatus !== 'pending') {
      return NextResponse.json(
        { error: 'التاجر ليس في حالة انتظار الموافقة' },
        { status: 400 }
      );
    }

    // اتخاذ قرار الموافقة التلقائية
    const decision = await AIApprovalService.makeAutoApprovalDecision(
      merchantUid,
      userData,
      storeData
    );

    // تطبيق القرار
    const success = await AIApprovalService.applyAutoApprovalDecision(
      merchantUid,
      decision
    );

    if (!success) {
      return NextResponse.json(
        { error: 'فشل في تطبيق قرار الموافقة' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      decision: decision.decision,
      confidence: decision.confidence,
      reasons: decision.reasons,
      riskFactors: decision.riskFactors,
      recommendations: decision.recommendations,
      extractedData: decision.extractedData
    });

  } catch (error) {
    console.error('خطأ في الموافقة التلقائية للتاجر:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في النظام' },
      { status: 500 }
    );
  }
}

// GET endpoint لجلب إحصائيات الموافقة التلقائية للتجار
export async function GET(request: NextRequest) {
  try {
    // يمكن إضافة إحصائيات الموافقة التلقائية هنا
    const stats = {
      totalProcessed: 0,
      autoApproved: 0,
      autoRejected: 0,
      manualReview: 0,
      averageConfidence: 0,
      averageProcessingTime: 0,
      successRate: 0
    };

    // في التطبيق الحقيقي، ستجلب هذه البيانات من قاعدة البيانات
    // const statsQuery = query(
    //   collection(db, 'stores'),
    //   where('aiAnalysis', '!=', null)
    // );
    // const statsSnapshot = await getDocs(statsQuery);
    // ... حساب الإحصائيات

    return NextResponse.json(stats);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الموافقة التلقائية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإحصائيات' },
      { status: 500 }
    );
  }
}
