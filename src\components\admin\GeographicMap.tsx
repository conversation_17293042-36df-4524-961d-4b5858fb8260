// src/components/admin/GeographicMap.tsx
"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGeographicData } from '@/hooks/useAdminStats';
import { useLocale } from '@/hooks/use-locale';
import { 
  MapPin, 
  Users, 
  ShoppingCart, 
  DollarSign,
  Map,
  BarChart3,
  PieChart,
  TrendingUp
} from 'lucide-react';

interface GeographicMapProps {
  className?: string;
  height?: number;
  showControls?: boolean;
}

export function GeographicMap({
  className,
  height = 400,
  showControls = true
}: GeographicMapProps) {
  const { t } = useLocale();
  const { geographicData, loading } = useGeographicData();
  const [viewMode, setViewMode] = useState<'map' | 'table' | 'chart'>('table');
  const [metric, setMetric] = useState<'orders' | 'revenue' | 'users'>('orders');

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getMetricValue = (data: any) => {
    switch (metric) {
      case 'orders':
        return data.orders;
      case 'revenue':
        return formatCurrency(data.revenue);
      case 'users':
        return data.users;
      default:
        return data.orders;
    }
  };

  const getMetricIcon = () => {
    switch (metric) {
      case 'orders':
        return <ShoppingCart className="h-4 w-4" />;
      case 'revenue':
        return <DollarSign className="h-4 w-4" />;
      case 'users':
        return <Users className="h-4 w-4" />;
      default:
        return <ShoppingCart className="h-4 w-4" />;
    }
  };

  const getMetricLabel = () => {
    switch (metric) {
      case 'orders':
        return t('ordersByRegion');
      case 'revenue':
        return t('revenueByRegion');
      case 'users':
        return t('usersByRegion');
      default:
        return t('ordersByRegion');
    }
  };

  const sortedData = [...geographicData].sort((a, b) => {
    switch (metric) {
      case 'orders':
        return b.orders - a.orders;
      case 'revenue':
        return b.revenue - a.revenue;
      case 'users':
        return b.users - a.users;
      default:
        return b.orders - a.orders;
    }
  });

  const totalMetric = geographicData.reduce((sum, item) => {
    switch (metric) {
      case 'orders':
        return sum + item.orders;
      case 'revenue':
        return sum + item.revenue;
      case 'users':
        return sum + item.users;
      default:
        return sum + item.orders;
    }
  }, 0);

  const getPercentage = (value: number) => {
    return totalMetric > 0 ? ((value / totalMetric) * 100).toFixed(1) : '0';
  };

  const getBarWidth = (value: number) => {
    const maxValue = Math.max(...geographicData.map(item => {
      switch (metric) {
        case 'orders':
          return item.orders;
        case 'revenue':
          return item.revenue;
        case 'users':
          return item.users;
        default:
          return item.orders;
      }
    }));
    return maxValue > 0 ? (value / maxValue) * 100 : 0;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full h-64" />
        </CardContent>
      </Card>
    );
  }

  const renderTableView = () => (
    <div className="space-y-3">
      {sortedData.map((location, index) => (
        <div
          key={`${location.region}-${location.city}`}
          className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
        >
          {/* ترتيب */}
          <div className="flex-shrink-0 w-8 text-center">
            <span className="text-sm font-bold text-muted-foreground">
              #{index + 1}
            </span>
          </div>

          {/* أيقونة الموقع */}
          <div className="flex-shrink-0">
            <MapPin className="h-5 w-5 text-primary" />
          </div>

          {/* معلومات الموقع */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium">{location.city}</h4>
            <p className="text-xs text-muted-foreground">{location.region}</p>
          </div>

          {/* الإحصائيات */}
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm font-medium">
                {metric === 'revenue' 
                  ? formatCurrency(location[metric])
                  : location[metric].toLocaleString('ar-SA')
                }
              </p>
              <p className="text-xs text-muted-foreground">
                {getPercentage(location[metric])}% من الإجمالي
              </p>
            </div>

            {/* شريط التقدم */}
            <div className="w-20">
              <div className="h-2 bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-primary transition-all duration-300"
                  style={{ width: `${getBarWidth(location[metric])}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderChartView = () => (
    <div className="space-y-4">
      {/* إجمالي المقياس */}
      <div className="text-center p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center justify-center gap-2 mb-2">
          {getMetricIcon()}
          <h3 className="text-lg font-semibold">
            {metric === 'revenue' 
              ? formatCurrency(totalMetric)
              : totalMetric.toLocaleString('ar-SA')
            }
          </h3>
        </div>
        <p className="text-sm text-muted-foreground">
          إجمالي {getMetricLabel()}
        </p>
      </div>

      {/* الرسم البياني الشريطي */}
      <div className="space-y-3">
        {sortedData.slice(0, 8).map((location, index) => (
          <div key={`${location.region}-${location.city}`} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{location.city}</span>
                <Badge variant="outline" className="text-xs">
                  {getPercentage(location[metric])}%
                </Badge>
              </div>
              <span className="text-sm text-muted-foreground">
                {metric === 'revenue' 
                  ? formatCurrency(location[metric])
                  : location[metric].toLocaleString('ar-SA')
                }
              </span>
            </div>
            <div className="h-3 bg-muted rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-primary to-primary/70 transition-all duration-500"
                style={{ width: `${getBarWidth(location[metric])}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderMapPlaceholder = () => (
    <div className="flex items-center justify-center h-64 bg-muted/50 rounded-lg">
      <div className="text-center">
        <Map className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2">خريطة تفاعلية</h3>
        <p className="text-sm text-muted-foreground">
          {t('featureInDevelopment')}
        </p>
        <Button variant="outline" size="sm" className="mt-4">
          عرض في الخريطة
        </Button>
      </div>
    </div>
  );

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {t('geographicDistribution')}
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {getMetricLabel()}
          </p>
        </div>

        {showControls && (
          <div className="flex items-center gap-2">
            {/* أزرار المقياس */}
            <div className="flex items-center gap-1">
              <Button
                variant={metric === 'orders' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMetric('orders')}
              >
                <ShoppingCart className="h-4 w-4 mr-1" />
                طلبات
              </Button>
              <Button
                variant={metric === 'revenue' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMetric('revenue')}
              >
                <DollarSign className="h-4 w-4 mr-1" />
                إيرادات
              </Button>
              <Button
                variant={metric === 'users' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMetric('users')}
              >
                <Users className="h-4 w-4 mr-1" />
                مستخدمين
              </Button>
            </div>

            {/* أزرار العرض */}
            <div className="flex items-center gap-1">
              <Button
                variant={viewMode === 'map' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('map')}
              >
                <Map className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('table')}
              >
                <BarChart3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'chart' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('chart')}
              >
                <PieChart className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div style={{ minHeight: `${height}px` }}>
          {viewMode === 'map' && renderMapPlaceholder()}
          {viewMode === 'table' && renderTableView()}
          {viewMode === 'chart' && renderChartView()}
        </div>
      </CardContent>
    </Card>
  );
}

// مكون مبسط لعرض أهم المناطق
interface TopRegionsProps {
  data: any[];
  metric: 'orders' | 'revenue' | 'users';
  limit?: number;
  className?: string;
}

export function TopRegions({
  data,
  metric,
  limit = 5,
  className
}: TopRegionsProps) {
  const { t } = useLocale();

  const sortedData = [...data]
    .sort((a, b) => b[metric] - a[metric])
    .slice(0, limit);

  const formatValue = (value: number) => {
    if (metric === 'revenue') {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
      }).format(value);
    }
    return value.toLocaleString('ar-SA');
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm font-medium">
          أهم المناطق - {metric === 'orders' ? 'الطلبات' : metric === 'revenue' ? 'الإيرادات' : 'المستخدمين'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {sortedData.map((location, index) => (
            <div key={`${location.region}-${location.city}`} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-xs font-bold text-muted-foreground w-6">
                  #{index + 1}
                </span>
                <div>
                  <p className="text-sm font-medium">{location.city}</p>
                  <p className="text-xs text-muted-foreground">{location.region}</p>
                </div>
              </div>
              <span className="text-sm font-medium">
                {formatValue(location[metric])}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
