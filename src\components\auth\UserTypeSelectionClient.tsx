"use client";

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState, useRef } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Loader2 } from 'lucide-react';
import type { Locale } from '@/lib/i18n';
import type { UserType } from '@/types';
import UserTypeSelection from './UserTypeSelection';
import { auth, db, googleProvider } from '@/lib/firebase';
import { signInWithPopup, setPersistence, browserLocalPersistence } from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp, type Timestamp } from 'firebase/firestore';
import { useToast } from "@/hooks/use-toast";
import type { UserDocument } from '@/types';

interface UserTypeSelectionClientProps {
  locale: Locale;
  children: React.ReactNode;
}

export default function UserTypeSelectionClient({ locale, children }: UserTypeSelectionClientProps) {
  const { t } = useLocale();
  const { user, loading, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const hasRedirectedRef = useRef(false);
  const currentLocaleRef = useRef(locale);

  // تحديث locale ref عند تغيير اللغة
  useEffect(() => {
    if (currentLocaleRef.current !== locale) {
      console.log('🌐 [UserTypeSelectionClient] Language changed:', {
        from: currentLocaleRef.current,
        to: locale
      });
      currentLocaleRef.current = locale;
      // لا نعيد تعيين hasRedirectedRef لمنع الحلقة اللا نهائية
      // hasRedirectedRef.current = false; // تم تعطيل هذا السطر
    }
  }, [locale]);

  useEffect(() => {
    console.log('🔄 [UserTypeSelectionClient] useEffect triggered:', {
      initialLoadingCompleted,
      hasUser: !!user,
      userUid: user?.uid,
      locale,
      hasRedirected: hasRedirectedRef.current
    });

    // منع إعادة التوجيه المتكررة
    if (hasRedirectedRef.current) {
      console.log('⏭️ [UserTypeSelectionClient] Already redirected, skipping...');
      return;
    }

    if (initialLoadingCompleted && user) {
      // User is already logged in, redirect to appropriate dashboard
      console.log('✅ [UserTypeSelectionClient] User authenticated, checking redirect...');
      setShouldRedirect(true);
      hasRedirectedRef.current = true;

      // Check user type and redirect accordingly
      const checkUserTypeAndRedirect = async () => {
        try {
          console.log('🔍 [UserTypeSelectionClient] Checking user document...');
          const userDocRef = doc(db, "users", user.uid);
          const userDocSnap = await getDoc(userDocRef);

          if (userDocSnap.exists()) {
            const userData = userDocSnap.data() as UserDocument;
            console.log('📄 [UserTypeSelectionClient] User document found:', userData.userType);

            let redirectPath = '';
            if (userData.userType === 'merchant') {
              redirectPath = `/${currentLocaleRef.current}/merchant/dashboard`;
            } else if (userData.userType === 'representative') {
              redirectPath = `/${currentLocaleRef.current}/representative/dashboard`;
            } else {
              redirectPath = `/${currentLocaleRef.current}/dashboard`;
            }

            console.log('🔄 [UserTypeSelectionClient] Redirecting to:', redirectPath);
            router.push(redirectPath);
          } else {
            // User exists but no document, redirect to signup to complete profile
            console.log('📝 [UserTypeSelectionClient] No user document, redirecting to signup');
            router.push(`/${currentLocaleRef.current}/signup`);
          }
        } catch (error) {
          console.error("❌ [UserTypeSelectionClient] Error checking user type:", error);
          router.push(`/${currentLocaleRef.current}/dashboard`);
        }
      };

      checkUserTypeAndRedirect();
    } else {
      console.log('⏳ [UserTypeSelectionClient] Waiting for auth completion or no user');
    }
  }, [user, initialLoadingCompleted, router]); // إزالة locale من dependencies

  const handleUserTypeSelect = (userType: UserType) => {
    // Redirect to signup with selected user type
    const signupPath = `/${currentLocaleRef.current}/signup?userType=${userType}`;
    console.log('🔄 [UserTypeSelectionClient] Manual signup redirect:', signupPath);
    router.push(signupPath);
  };

  const handleGoogleSignIn = async (userType: UserType) => {
    // التحقق من أن نوع المستخدم هو عميل فقط
    if (userType !== 'customer') {
      toast({
        title: t('errorTitle'),
        description: t('googleSignInCustomersOnly'),
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      console.log("🔄 Starting Google sign-in process...");

      // Set persistence
      await setPersistence(auth, browserLocalPersistence);
      console.log("✅ Firebase persistence set");

      // Sign in with Google
      const result = await signInWithPopup(auth, googleProvider);
      const firebaseUser = result.user;

      console.log("✅ Google sign-in successful, user:", firebaseUser.uid);

      // Check if user document exists using retry mechanism
      const { getDocumentWithRetry } = await import('@/lib/firestore-utils');
      const userDocResult = await getDocumentWithRetry(
        `users/${firebaseUser.uid}`,
        {
          retries: 3,
          timeout: 10000,
          enableOffline: true
        }
      );

      let redirectPath = `/${locale}/dashboard`;

      // معالجة نتيجة التحقق من وثيقة المستخدم
      if (!userDocResult.success) {
        // فشل في التحقق من وثيقة المستخدم
        console.error("❌ Failed to check user document:", userDocResult.error);
        throw new Error(userDocResult.error || 'فشل في التحقق من بيانات المستخدم');
      }

      if (userDocResult.exists && userDocResult.data) {
        // User exists, handle login based on existing user type
        const userData = userDocResult.data as UserDocument;
        console.log("📄 Existing user found, type:", userData.userType);

        if (userData.userType === 'merchant') {
          redirectPath = `/${currentLocaleRef.current}/merchant/dashboard`;
        } else if (userData.userType === 'representative') {
          redirectPath = `/${currentLocaleRef.current}/representative/dashboard`;
        } else {
          redirectPath = `/${currentLocaleRef.current}/dashboard`;
        }

        toast({
          title: t('loginSuccessTitle'),
          description: t('loginSuccessMessage'),
        });
      } else {
        // User doesn't exist, create new user document with selected type
        console.log("📝 Creating new user document...");

        const defaultPlanId = userType === 'customer' ? 'customer-basic' :
                             userType === 'merchant' ? 'merchant-basic' :
                             'representative-basic';

        const userDocData: UserDocument = {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'مستخدم',
          photoURL: firebaseUser.photoURL,
          userType: userType,
          planId: defaultPlanId,
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp,
        };

        // استخدام دالة setDocumentWithRetry المحسنة
        const { setDocumentWithRetry } = await import('@/lib/firestore-utils');
        const createResult = await setDocumentWithRetry(
          `users/${firebaseUser.uid}`,
          userDocData,
          {
            retries: 3,
            timeout: 10000
          }
        );

        if (!createResult.success) {
          throw new Error(createResult.error || 'فشل في إنشاء حساب المستخدم');
        }

        console.log("✅ User document created successfully");

        // Handle different user types
        if (userType === 'merchant') {
          redirectPath = `/${currentLocaleRef.current}/merchant/pending-approval`;
        } else if (userType === 'representative') {
          redirectPath = `/${currentLocaleRef.current}/representative/signup`;
        } else {
          redirectPath = `/${currentLocaleRef.current}/dashboard`;
        }

        toast({
          title: t('signupSuccessTitle'),
          description: t('signupSuccessMessage'),
        });
      }

      console.log("🔄 Redirecting to:", redirectPath);

      // تحسين آلية إعادة التوجيه مع fallback
      const executeRedirect = () => {
        try {
          router.push(redirectPath);

          // آلية fallback: استخدام window.location إذا فشل router
          setTimeout(() => {
            if (window.location.pathname !== redirectPath.replace(`/${currentLocaleRef.current}`, '')) {
              console.warn("⚠️ Router redirect timeout, using window.location...");
              window.location.href = redirectPath;
            }
          }, 3000);
        } catch (redirectError) {
          console.error("❌ Router redirect failed:", redirectError);
          window.location.href = redirectPath;
        }
      };

      // تأخير قصير للسماح للـ toast بالظهور
      setTimeout(executeRedirect, 500);

    } catch (error: any) {
      console.error("❌ Google sign-in error:", error);

      let errorMessage = t('googleSignInFailed');

      // معالجة أخطاء Google Sign-In المختلفة
      switch (error.code) {
        case 'auth/popup-closed-by-user':
          errorMessage = t('googleSignInCancelled');
          break;
        case 'auth/popup-blocked':
          errorMessage = t('googleSignInPopupBlocked');
          break;
        case 'auth/network-request-failed':
          errorMessage = t('networkError');
          break;
        case 'auth/too-many-requests':
          errorMessage = t('tooManyRequests');
          break;
        case 'auth/timeout':
          errorMessage = 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
          break;
        default:
          if (error.message) {
            // معالجة رسائل الأخطاء المختلفة
            if (error.message.includes('timeout') || error.message.includes('Request timeout')) {
              errorMessage = 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
            } else if (error.message.includes('offline') || error.message.includes('network')) {
              errorMessage = 'مشكلة في الاتصال بالإنترنت، يرجى التحقق من الاتصال';
            } else if (error.message.includes('فشل في التحقق من بيانات المستخدم')) {
              errorMessage = 'فشل في التحقق من بيانات المستخدم، يرجى المحاولة مرة أخرى';
            } else if (error.message.includes('فشل في إنشاء حساب المستخدم')) {
              errorMessage = 'فشل في إنشاء حساب المستخدم، يرجى المحاولة مرة أخرى';
            } else {
              errorMessage = error.message;
            }
          }
          break;
      }

      toast({
        title: t('errorTitle'),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (loading || !initialLoadingCompleted || shouldRedirect || isProcessing) {
    return (
      <div className="container mx-auto px-4 py-12 flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <div className="text-center space-y-1">
            <p className="text-muted-foreground font-medium">
              {shouldRedirect ? t('redirectingToYourDashboard') :
               isProcessing ? t('processingRequest') :
               t('authenticationInProgress')}
            </p>
            {isProcessing && (
              <p className="text-muted-foreground text-xs">
                {t('redirectingPleaseWait')}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {children}
      <div className="container mx-auto px-4 -mt-2">
        <UserTypeSelection
          onUserTypeSelect={handleUserTypeSelect}
          onContinueWithGoogle={handleGoogleSignIn}
        />
      </div>
    </div>
  );
}
