// src/app/[locale]/page.tsx
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import Link from 'next/link';
import { ShoppingCart, Gift, Shirt, Sparkles, Store, CheckCircle, MapPin, AlertTriangle, Navigation, Truck, Star, ArrowRight } from 'lucide-react';
import { useLocale } from '@/hooks/use-locale';
import { useState, useEffect } from 'react';

import ProductCard from '@/components/common/ProductCard';
import StoreCard from '@/components/customer/StoreCard';
import type { ProductDocument, StoreDocument } from '@/types';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export default function HomePage() {
  const { t, locale } = useLocale();

  const [featuredStores, setFeaturedStores] = useState<StoreDocument[]>([]);
  const [popularProducts, setPopularProducts] = useState<ProductDocument[]>([]);
  const [isLoadingStores, setIsLoadingStores] = useState(true);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);

  // الفئات الشائعة
  const categories = [
    {
      nameKey: 'categoryGroceries',
      descKey: 'categoryGroceriesDesc',
      icon: <ShoppingCart className="h-10 w-10 text-primary mb-3" />,
    },
    {
      nameKey: 'categoryHandicrafts',
      descKey: 'categoryHandicraftsDesc',
      icon: <Gift className="h-10 w-10 text-primary mb-3" />,
    },
    {
      nameKey: 'categoryFashion',
      descKey: 'categoryFashionDesc',
      icon: <Shirt className="h-10 w-10 text-primary mb-3" />,
    },
    {
      nameKey: 'categoryHealthBeauty',
      descKey: 'categoryHealthBeautyDesc',
      icon: <Sparkles className="h-10 w-10 text-primary mb-3" />,
    },
  ];

  // مزايا التجار
  const merchantBenefits = [
    'merchantBenefit1',
    'merchantBenefit2',
    'merchantBenefit3',
    'merchantBenefit4',
  ];

  // Fetch featured stores
  useEffect(() => {
    const fetchFeaturedStores = async () => {
      try {
        const storesRef = collection(db, 'stores');
        const storesQuery = query(
          storesRef,
          where('isActive', '==', true),
          where('approvalStatus', '==', 'approved'),
          orderBy('stats.averageRating', 'desc'),
          limit(6)
        );

        const snapshot = await getDocs(storesQuery);
        const storesData: StoreDocument[] = [];

        snapshot.forEach((doc) => {
          storesData.push({ ...doc.data(), id: doc.id } as StoreDocument);
        });

        setFeaturedStores(storesData);
      } catch (error) {
        console.error('Error fetching featured stores:', error);
      } finally {
        setIsLoadingStores(false);
      }
    };

    fetchFeaturedStores();
  }, []);

  // Fetch popular products
  useEffect(() => {
    const fetchPopularProducts = async () => {
      try {
        const productsRef = collection(db, 'products');
        const productsQuery = query(
          productsRef,
          where('isActive', '==', true),
          orderBy('totalSales', 'desc'),
          limit(8)
        );

        const snapshot = await getDocs(productsQuery);
        const productsData: ProductDocument[] = [];

        snapshot.forEach((doc) => {
          productsData.push({ ...doc.data(), id: doc.id } as ProductDocument);
        });

        setPopularProducts(productsData);
      } catch (error) {
        console.error('Error fetching popular products:', error);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchPopularProducts();
  }, []);

  return (
    <div className="flex flex-col items-center text-center space-y-16 md:space-y-24">


      {/* Hero Section */}
      <section className="w-full pt-12 md:pt-24 lg:pt-32">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-6 text-center">
            <div className="space-y-3">
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-primary">
                {t('homeHeroTitle')}
              </h1>
              <p className="max-w-[700px] text-muted-foreground md:text-xl mx-auto">
                {t('homeHeroSubtitle')}
              </p>
            </div>
            <div className="flex flex-col gap-3 min-[400px]:flex-row justify-center pt-4">
              <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground shadow-md hover:shadow-lg transition-shadow">
                <Link href={`/${locale}/user-type-selection`}>{t('getStarted')}</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="shadow-sm hover:shadow-md transition-shadow border-primary/50 hover:border-primary text-primary">
                <Link href={`/${locale}/products`}>{t('browseStores')}</Link>
              </Button>
            </div>


          </div>
        </div>
      </section>




      {/* قسم الفئات الشائعة */}
      <section className="w-full py-12 md:py-16 bg-secondary/30 rounded-lg">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl text-primary">
              {t('popularCategoriesTitle')}
            </h2>
            <p className="max-w-[700px] text-muted-foreground md:text-lg">
              {t('popularCategoriesSubtitle')}
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {categories.map((category) => (
              <Card key={category.nameKey} className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="flex flex-col items-center p-6 text-center">
                  {category.icon}
                  <h3 className="text-xl font-semibold text-card-foreground mb-1.5 group-hover:text-primary transition-colors">
                    {t(category.nameKey)}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {t(category.descKey)}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* قسم المتاجر المميزة */}
      <section className="w-full py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl text-primary">
              {t('featuredStores')}
            </h2>
            <p className="max-w-[700px] text-muted-foreground md:text-lg">
              {t('discoverTopRatedStores')}
            </p>
          </div>

          {isLoadingStores ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                    <Skeleton className="h-20 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : featuredStores.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredStores.map((store) => (
                  <StoreCard
                    key={store.id}
                    store={store}
                    variant="featured"
                    showDistance={false}
                  />
                ))}
              </div>
              <div className="text-center mt-8">
                <Button asChild variant="outline" size="lg">
                  <Link href={`/${locale}/stores`}>
                    {t('viewAllStores')}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Store className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">{t('noFeaturedStores')}</h3>
                <p className="text-muted-foreground">
                  {t('checkBackLaterForFeaturedStores')}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </section>

      {/* قسم المنتجات الأكثر مبيعاً */}
      <section className="w-full py-12 md:py-16 bg-secondary/30 rounded-lg">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl text-primary">
              {t('popularProducts')}
            </h2>
            <p className="max-w-[700px] text-muted-foreground md:text-lg">
              {t('discoverBestSellingProducts')}
            </p>
          </div>

          {isLoadingProducts ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i}>
                  <Skeleton className="h-40 w-full" />
                  <CardContent className="p-4 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-8 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : popularProducts.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {popularProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    variant="featured"
                    showStore={true}
                  />
                ))}
              </div>
              <div className="text-center mt-8">
                <Button asChild variant="outline" size="lg">
                  <Link href={`/${locale}/products`}>
                    {t('viewAllProducts')}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <ShoppingCart className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">{t('noPopularProducts')}</h3>
                <p className="text-muted-foreground">
                  {t('checkBackLaterForPopularProducts')}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </section>

      {/* قسم دعوة التجار */}
      <section className="w-full py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <Card className="bg-gradient-to-br from-primary/20 to-accent/20 border-none shadow-lg hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="text-center space-y-4">
              <Store className="h-12 w-12 text-primary mx-auto" />
              <CardTitle className="text-3xl font-bold tracking-tight sm:text-4xl text-center mx-auto">
                {t('merchantCtaTitle')}
              </CardTitle>
              <CardDescription className="max-w-2xl text-center md:text-lg mx-auto">
                {t('merchantCtaSubtitle')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="w-full max-w-md mx-auto">
                <h3 className="text-xl font-semibold mb-3 text-primary text-center">
                  {t('merchantBenefitsTitle')}
                </h3>
                <ul className="space-y-2">
                  {merchantBenefits.map((benefitKey) => (
                    <li key={benefitKey} className="flex items-center text-muted-foreground">
                      <CheckCircle className="h-5 w-5 text-green-500 me-2 rtl:ms-2 flex-shrink-0" />
                      {t(benefitKey)}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="text-center">
                <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg transition-shadow">
                  <Link href={`/${locale}/user-type-selection`}>
                    <Store className="w-5 h-5 ml-2" />
                    {t('listYourStore')}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* قسم دعوة المندوبين */}
      <section className="w-full py-12 md:py-16 bg-muted/50">
        <div className="container px-4 md:px-6">
          <Card className="bg-gradient-to-br from-blue-500/20 to-green-500/20 border-none shadow-lg hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="text-center space-y-4">
              <Truck className="h-12 w-12 text-blue-600 mx-auto" />
              <CardTitle className="text-3xl font-bold tracking-tight sm:text-4xl text-center mx-auto">
                {t('becomeRepresentative')}
              </CardTitle>
              <CardDescription className="max-w-2xl text-center md:text-lg mx-auto">
                {t('joinDeliveryTeamSubtitle')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="w-full max-w-md mx-auto">
                <h3 className="text-xl font-semibold mb-3 text-blue-600 text-center">
                  {t('representativeBenefitsTitle')}
                </h3>
                <ul className="space-y-2">
                  {[
                    'flexibleWorkingHours',
                    'competitiveCommissions',
                    'weeklyPayments',
                    'comprehensiveSupport'
                  ].map((benefitKey) => (
                    <li key={benefitKey} className="flex items-center text-muted-foreground">
                      <CheckCircle className="h-5 w-5 text-green-500 me-2 rtl:ms-2 flex-shrink-0" />
                      {t(benefitKey)}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="text-center">
                <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-shadow">
                  <Link href={`/${locale}/representative/signup`}>
                    <Truck className="w-5 h-5 ml-2" />
                    {t('joinDeliveryTeam')}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>




    </div>
  );
}
