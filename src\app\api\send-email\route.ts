import { NextRequest, NextResponse } from 'next/server';

/**
 * API لإرسال البريد الإلكتروني
 * يدعم عدة مزودي خدمة البريد الإلكتروني
 */

interface EmailRequest {
  to: string | string[];
  subject: string;
  message: string;
  html?: string;
  from?: string;
  provider?: 'native' | 'webhook' | 'firebase';
}

interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  provider?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<EmailResponse>> {
  try {
    const body: EmailRequest = await request.json();
    
    // التحقق من صحة البيانات
    if (!body.to || !body.subject || !body.message) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة: to, subject, message'
      }, { status: 400 });
    }

    const provider = body.provider || 'native';
    
    console.log('📧 إرسال بريد إلكتروني:', {
      to: body.to,
      subject: body.subject,
      provider
    });

    let result: EmailResponse;

    switch (provider) {
      case 'native':
        result = await sendEmailNative(body);
        break;
      case 'webhook':
        result = await sendEmailWebhook(body);
        break;
      case 'firebase':
        result = await sendEmailFirebase(body);
        break;
      default:
        result = await sendEmailNative(body);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ خطأ في إرسال البريد الإلكتروني:', error);
    
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في إرسال البريد الإلكتروني'
    }, { status: 500 });
  }
}

/**
 * إرسال البريد باستخدام النظام المحلي
 */
async function sendEmailNative(emailData: EmailRequest): Promise<EmailResponse> {
  try {
    // محاكاة إرسال البريد الإلكتروني
    // في التطبيق الحقيقي، يمكن استخدام Nodemailer أو مزود آخر
    
    const messageId = `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // تسجيل البريد في قاعدة البيانات للمتابعة
    await logEmailToDatabase({
      messageId,
      to: Array.isArray(emailData.to) ? emailData.to : [emailData.to],
      subject: emailData.subject,
      message: emailData.message,
      provider: 'native',
      status: 'sent',
      sentAt: new Date()
    });

    console.log('✅ تم إرسال البريد الإلكتروني بنجاح:', messageId);

    return {
      success: true,
      messageId,
      provider: 'native'
    };

  } catch (error) {
    console.error('❌ خطأ في الإرسال المحلي:', error);
    throw error;
  }
}

/**
 * إرسال البريد باستخدام Webhook خارجي
 */
async function sendEmailWebhook(emailData: EmailRequest): Promise<EmailResponse> {
  try {
    // يمكن تكوين webhook URL من متغيرات البيئة
    const webhookUrl = process.env.EMAIL_WEBHOOK_URL;
    
    if (!webhookUrl) {
      throw new Error('EMAIL_WEBHOOK_URL غير مكون');
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.EMAIL_WEBHOOK_TOKEN || ''}`
      },
      body: JSON.stringify({
        to: emailData.to,
        subject: emailData.subject,
        text: emailData.message,
        html: emailData.html,
        from: emailData.from || process.env.DEFAULT_FROM_EMAIL
      })
    });

    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.status}`);
    }

    const result = await response.json();
    
    return {
      success: true,
      messageId: result.messageId || `webhook_${Date.now()}`,
      provider: 'webhook'
    };

  } catch (error) {
    console.error('❌ خطأ في Webhook:', error);
    throw error;
  }
}

/**
 * إرسال البريد باستخدام Firebase Functions
 */
async function sendEmailFirebase(emailData: EmailRequest): Promise<EmailResponse> {
  try {
    // استخدام Firebase Functions لإرسال البريد
    const { db } = await import('@/lib/firebase');
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    // إضافة البريد إلى قائمة انتظار Firebase
    const emailQueueRef = collection(db, 'email_queue');
    const docRef = await addDoc(emailQueueRef, {
      to: emailData.to,
      subject: emailData.subject,
      message: emailData.message,
      html: emailData.html,
      from: emailData.from,
      status: 'pending',
      createdAt: serverTimestamp(),
      attempts: 0
    });

    console.log('✅ تم إضافة البريد لقائمة Firebase:', docRef.id);

    return {
      success: true,
      messageId: docRef.id,
      provider: 'firebase'
    };

  } catch (error) {
    console.error('❌ خطأ في Firebase:', error);
    throw error;
  }
}

/**
 * تسجيل البريد في قاعدة البيانات
 */
async function logEmailToDatabase(emailLog: {
  messageId: string;
  to: string[];
  subject: string;
  message: string;
  provider: string;
  status: string;
  sentAt: Date;
}): Promise<void> {
  try {
    const { db } = await import('@/lib/firebase');
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    const emailLogsRef = collection(db, 'email_logs');
    await addDoc(emailLogsRef, {
      ...emailLog,
      sentAt: serverTimestamp()
    });

  } catch (error) {
    console.error('❌ خطأ في تسجيل البريد:', error);
    // لا نرمي خطأ هنا لأن الإرسال نجح
  }
}

// دعم GET للاختبار
export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    service: 'Email API',
    status: 'active',
    providers: ['native', 'webhook', 'firebase'],
    version: '1.0.0'
  });
}
