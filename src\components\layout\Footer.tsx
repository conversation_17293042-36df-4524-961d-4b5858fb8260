import type { Locale } from '@/lib/i18n';
import { getTranslations } from '@/context/locale-context';

export default async function Footer({ locale }: { locale: Locale }) {
  const { t } = await getTranslations(locale);
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t border-border/40 bg-background">
      <div className="container mx-auto px-4 py-6 text-center text-sm text-muted-foreground">
        <p>{t('copyright', { year: currentYear })}</p>
      </div>
    </footer>
  );
}
