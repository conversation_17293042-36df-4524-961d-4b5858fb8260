import type { Locale } from '@/lib/i18n';
import { getTranslations } from '@/context/locale-context';
import UserTypeSelectionClient from '@/components/auth/UserTypeSelectionClient';
import Link from 'next/link';
import { BrandLogo } from '@/components/Logo';

export default async function UserTypeSelectionPage({ params }: { params: { locale: Locale } }) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale);

  return (
    <UserTypeSelectionClient locale={locale}>
      <div className="bg-background py-6 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-4xl mx-auto">
          <div className="text-center mb-4">
            <Link href={`/${locale}`} className="inline-block">
              <BrandLogo size="default" className="mx-auto" />
            </Link>
          </div>

          {/* The main content will be handled by the client component */}
          <div id="user-type-selection-content">
            {/* This will be replaced by the client component */}
          </div>
        </div>
      </div>
    </UserTypeSelectionClient>
  );
}
