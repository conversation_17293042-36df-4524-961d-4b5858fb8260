
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ===== APEX SECURITY FUNCTIONS =====

    // التحقق من صحة الجلسة والأمان المتقدم
    function validateUserIntegrity(auth) {
      return auth != null
        && auth.uid != null
        && auth.token.email_verified == true
        && auth.token.auth_time > (request.time.toMillis() - 86400000); // Session max 24h
    }

    // التحقق من صحة الجلسة
    function checkSessionValidity(token) {
      return token.auth_time > (request.time.toMillis() - 1800000) // 30 min session
        && token.iss == 'https://securetoken.google.com/' + resource.data.projectId;
    }

    // التحقق من بصمة الجهاز (محاكاة)
    function verifyDeviceFingerprint(request) {
      return request.headers != null; // سيتم تطويرها لاحقاً
    }

    // التحقق من سلامة البيانات
    function validateDataIntegrity(data) {
      return data.keys().size() <= 20 // حد أقصى للحقول
        && !data.keys().hasAny(['__proto__', 'constructor', 'prototype']); // منع prototype pollution
    }

    // فحص معدل الطلبات (محاكاة)
    function checkRateLimit(uid) {
      return true; // سيتم تطويرها مع نظام العد
    }

    // التحقق من صلاحيات الكتابة
    function verifyWritePermissions(token) {
      return token.firebase.sign_in_provider != 'anonymous';
    }

    // التحقق من صلاحيات الإدارة
    function hasAdminRole(uid) {
      return exists(/databases/$(database)/documents/admins/$(uid))
        && get(/databases/$(database)/documents/admins/$(uid)).data.isActive == true;
    }

    // التحقق من المصادقة الثنائية للإدارة
    function verifyMFA(token) {
      return token.firebase.identities.size() >= 2; // متعدد المصادر
    }

    // فحص القائمة البيضاء للـ IP (محاكاة)
    function checkIPWhitelist(token) {
      return true; // سيتم تطويرها مع نظام IP tracking
    }

    // التحقق من جلسة الإدارة
    function validateAdminSession(uid) {
      return exists(/databases/$(database)/documents/admin_sessions/$(uid))
        && get(/databases/$(database)/documents/admin_sessions/$(uid)).data.expiresAt > request.time;
    }

    // ===== ENHANCED USER RULES =====
    match /users/{userId} {
      allow create: if request.auth != null
        && request.auth.uid == userId
        && validateUserIntegrity(request.auth)
        && request.resource.data.uid == request.auth.uid
        && request.resource.data.email is string
        && request.resource.data.email.matches('.*@.*\\..*') // Email validation
        && (request.resource.data.userType in ['customer', 'merchant', 'representative'])
        && request.resource.data.createdAt == request.time
        && request.resource.data.updatedAt == request.time
        && validateDataIntegrity(request.resource.data);

      allow read: if request.auth != null
        && request.auth.uid == userId
        && validateUserIntegrity(request.auth)
        && checkSessionValidity(request.auth.token);

      allow update: if request.auth != null
        && request.auth.uid == userId
        && validateUserIntegrity(request.auth)
        && checkSessionValidity(request.auth.token)
        && verifyDeviceFingerprint(request)
        && checkRateLimit(request.auth.uid)
        && verifyWritePermissions(request.auth.token)
        && request.resource.data.updatedAt == request.time
        && validateDataIntegrity(request.resource.data)
        // Immutable fields protection
        && request.resource.data.userType == resource.data.userType
        && request.resource.data.email == resource.data.email
        && request.resource.data.uid == resource.data.uid
        && request.resource.data.createdAt == resource.data.createdAt
        // Allowed updates with strict validation
        && (request.resource.data.keys().hasOnly(['displayName', 'updatedAt'])
        || (request.resource.data.keys().hasOnly(['displayName', 'photoURL', 'updatedAt'])
            && resource.data.userType in ['merchant', 'representative']));

      allow delete: if false; // Complete deletion protection
    }

    // STORE RULES
    // Merchants can create their own store document if authenticated, userType is 'merchant',
    // and merchantUid matches their auth.uid. Stores start inactive.
    // Stores are publicly readable.
    // Merchants can update their own store (specific fields).
    match /stores/{storeId} {
      allow create: if request.auth != null && request.auth.uid == storeId
                    && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.userType == 'merchant'
                    && request.resource.data.merchantUid == request.auth.uid
                    && request.resource.data.isActive == false // Stores start inactive
                    && request.resource.data.planId is string // planId must be provided
                    && request.resource.data.createdAt == request.time
                    && request.resource.data.updatedAt == request.time;
      allow read: if true; // Stores can be publicly readable
      allow update: if request.auth != null && request.auth.uid == storeId
                    && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.userType == 'merchant'
                    // Merchant can update storeName, description, logo, banner, categories, address, phone, planId, document URLs
                    // Cannot update merchantUid, isActive (admin task), createdAt
                    && !(request.resource.data.keys().hasAny(['merchantUid', 'isActive', 'createdAt']))
                    && request.resource.data.updatedAt == request.time;
      allow delete: if false; // Or allow merchant to delete their store under certain conditions
    }

    // PRODUCT RULES
    // Merchants can create products if authenticated, userType is 'merchant',
    // and merchantUid/storeId in product match their auth.uid.
    // Basic validation for required product fields.
    // Products are publicly readable.
    // Merchants can update and delete their own products.
    match /products/{productId} {
      allow read: if true; // Products are publicly readable
      allow create: if request.auth != null
                    && request.resource.data.merchantUid == request.auth.uid
                    && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.userType == 'merchant'
                    && request.resource.data.storeId == request.auth.uid // Assuming storeId is merchantUid
                    && request.resource.data.storeName is string && request.resource.data.storeName.size() > 0
                    && request.resource.data.name is string && request.resource.data.name.size() > 0
                    && request.resource.data.price is number && request.resource.data.price > 0
                    && request.resource.data.stockQuantity is number && request.resource.data.stockQuantity >= 0
                    && request.resource.data.category is string && request.resource.data.category.size() > 0
                    && request.resource.data.imageUrls is list
                    && request.resource.data.isActive is bool
                    && request.resource.data.currency is string // Ensure currency is provided
                    && request.resource.data.createdAt == request.time
                    && request.resource.data.updatedAt == request.time;
      allow update: if request.auth != null
                    && resource.data.merchantUid == request.auth.uid // Can only update their own products
                    && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.userType == 'merchant'
                    // Prevent changing critical fields like merchantUid, storeId, createdAt
                    && request.resource.data.merchantUid == resource.data.merchantUid
                    && request.resource.data.storeId == resource.data.storeId
                    && request.resource.data.createdAt == resource.data.createdAt
                    // Validate common updatable fields if they exist in the request
                    && (request.resource.data.keys().has('storeName') ? (request.resource.data.storeName is string && request.resource.data.storeName.size() > 0) : true)
                    && (request.resource.data.keys().has('name') ? (request.resource.data.name is string && request.resource.data.name.size() > 0) : true)
                    && (request.resource.data.keys().has('price') ? (request.resource.data.price is number && request.resource.data.price > 0) : true)
                    && (request.resource.data.keys().has('stockQuantity') ? (request.resource.data.stockQuantity is number && request.resource.data.stockQuantity >= 0) : true)
                    && (request.resource.data.keys().has('category') ? (request.resource.data.category is string && request.resource.data.category.size() > 0) : true)
                    && (request.resource.data.keys().has('imageUrls') ? request.resource.data.imageUrls is list : true)
                    && (request.resource.data.keys().has('isActive') ? request.resource.data.isActive is bool : true)
                    && (request.resource.data.keys().has('currency') ? request.resource.data.currency is string : true)
                    && request.resource.data.updatedAt == request.time;
      allow delete: if request.auth != null
                    && resource.data.merchantUid == request.auth.uid
                    && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.userType == 'merchant';
    }

    // REPRESENTATIVE RULES
    // Representatives can create their own representative document if authenticated and uid matches.
    // Representatives can read and update their own document.
    // Admins can read all representative documents.
    // Critical fields like uid, email, approvalStatus cannot be changed by representatives.
    match /representatives/{representativeId} {
      allow create: if request.auth != null && request.auth.uid == representativeId
                    && request.resource.data.uid == request.auth.uid
                    && request.resource.data.email is string
                    && request.resource.data.displayName is string
                    && request.resource.data.phoneNumber is string
                    && request.resource.data.nationalId is string
                    && request.resource.data.approvalStatus == 'pending'
                    && request.resource.data.createdAt == request.time
                    && request.resource.data.submittedAt == request.time;

      allow read: if request.auth != null &&
                  (request.auth.uid == representativeId || isAdmin(request.auth.uid));

      allow update: if request.auth != null && request.auth.uid == representativeId
                    && request.resource.data.uid == resource.data.uid
                    && request.resource.data.email == resource.data.email
                    && request.resource.data.approvalStatus == resource.data.approvalStatus
                    && request.resource.data.createdAt == resource.data.createdAt
                    && request.resource.data.submittedAt == resource.data.submittedAt;
    }

    // DELIVERY ORDERS RULES
    // Delivery orders can be created by the system or merchants.
    // Representatives can read orders assigned to them.
    // Representatives can update delivery status and location.
    match /delivery_orders/{orderId} {
      allow read: if request.auth != null &&
                  (resource.data.representativeId == request.auth.uid ||
                   resource.data.customerId == request.auth.uid ||
                   resource.data.merchantId == request.auth.uid ||
                   isAdmin(request.auth.uid));

      allow create: if request.auth != null &&
                    (request.resource.data.merchantId == request.auth.uid ||
                     isAdmin(request.auth.uid));

      allow update: if request.auth != null &&
                    (resource.data.representativeId == request.auth.uid ||
                     resource.data.merchantId == request.auth.uid ||
                     isAdmin(request.auth.uid));
    }

    // COUPON RULES
    // Merchants can create, read, update, and delete their own coupons
    // Customers can read active coupons for validation
    match /coupons/{couponId} {
      allow create: if request.auth != null
                    && request.auth.uid == request.resource.data.merchantId
                    && request.resource.data.merchantId is string
                    && request.resource.data.code is string
                    && request.resource.data.type in ['percentage', 'fixed', 'free_shipping']
                    && request.resource.data.value is number
                    && request.resource.data.usageLimit is number
                    && request.resource.data.validFrom is timestamp
                    && request.resource.data.validUntil is timestamp
                    && request.resource.data.createdAt is timestamp
                    && request.resource.data.updatedAt is timestamp;

      allow read: if request.auth != null
                  && (request.auth.uid == resource.data.merchantId
                      || resource.data.isActive == true);

      allow update: if request.auth != null
                    && request.auth.uid == resource.data.merchantId
                    && request.resource.data.merchantId == resource.data.merchantId
                    && request.resource.data.updatedAt is timestamp;

      allow delete: if request.auth != null
                    && request.auth.uid == resource.data.merchantId;
    }

    // COUPON USAGE LOGS RULES
    // System can create usage logs, merchants can read their own logs
    match /coupon_usage_logs/{logId} {
      allow create: if request.auth != null
                    && request.resource.data.customerId is string
                    && request.resource.data.couponId is string
                    && request.resource.data.orderId is string
                    && request.resource.data.usedAt is timestamp;

      allow read: if request.auth != null
                  && (request.auth.uid == resource.data.merchantId
                      || request.auth.uid == resource.data.customerId);
    }

    // LOYALTY PROGRAMS RULES
    // Merchants can create, read, update their own loyalty programs
    // Customers can read active programs
    match /loyalty_programs/{programId} {
      allow create: if request.auth != null
                    && request.auth.uid == request.resource.data.merchantId
                    && request.resource.data.merchantId is string
                    && request.resource.data.name is string
                    && request.resource.data.description is string
                    && request.resource.data.settings is map
                    && request.resource.data.createdAt is timestamp
                    && request.resource.data.updatedAt is timestamp;

      allow read: if request.auth != null
                  && (request.auth.uid == resource.data.merchantId
                      || resource.data.isActive == true);

      allow update: if request.auth != null
                    && request.auth.uid == resource.data.merchantId
                    && request.resource.data.merchantId == resource.data.merchantId
                    && request.resource.data.updatedAt is timestamp;

      allow delete: if request.auth != null
                    && request.auth.uid == resource.data.merchantId;
    }

    // LOYALTY MEMBERS RULES
    // Customers can read their own membership, merchants can read their members
    match /loyalty_members/{memberId} {
      allow create: if request.auth != null
                    && request.resource.data.customerId is string
                    && request.resource.data.merchantId is string
                    && request.resource.data.programId is string
                    && request.resource.data.joinedAt is timestamp;

      allow read: if request.auth != null
                  && (request.auth.uid == resource.data.customerId
                      || request.auth.uid == resource.data.merchantId);

      allow update: if request.auth != null
                    && (request.auth.uid == resource.data.customerId
                        || request.auth.uid == resource.data.merchantId);
    }

    // LOYALTY TRANSACTIONS RULES
    // System can create transactions, users can read their own
    match /loyalty_transactions/{transactionId} {
      allow create: if request.auth != null
                    && request.resource.data.customerId is string
                    && request.resource.data.merchantId is string
                    && request.resource.data.type is string
                    && request.resource.data.points is number
                    && request.resource.data.createdAt is timestamp;

      allow read: if request.auth != null
                  && (request.auth.uid == resource.data.customerId
                      || request.auth.uid == resource.data.merchantId);
    }

    // LOYALTY REDEMPTIONS RULES
    // System can create redemptions, users can read their own
    match /loyalty_redemptions/{redemptionId} {
      allow create: if request.auth != null
                    && request.resource.data.customerId is string
                    && request.resource.data.merchantId is string
                    && request.resource.data.rewardId is string
                    && request.resource.data.pointsUsed is number
                    && request.resource.data.redeemedAt is timestamp;

      allow read: if request.auth != null
                  && (request.auth.uid == resource.data.customerId
                      || request.auth.uid == resource.data.merchantId);

      allow update: if request.auth != null
                    && request.auth.uid == resource.data.merchantId;
    }

    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
