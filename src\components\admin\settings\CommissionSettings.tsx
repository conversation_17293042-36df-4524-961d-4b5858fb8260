'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { Percent, Users, Truck, DollarSign } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CommissionSettingsProps {
  onSettingsChange: () => void;
}

export function CommissionSettings({ onSettingsChange }: CommissionSettingsProps) {
  const { t } = useLocale();
  const [settings, setSettings] = useState({
    merchantCommission: {
      type: 'percentage',
      value: 5,
      minAmount: 1,
      maxAmount: 100
    },
    representativeCommission: {
      type: 'percentage',
      value: 10,
      minAmount: 5,
      maxAmount: 50
    },
    platformCommission: {
      type: 'percentage',
      value: 2,
      minAmount: 0.5,
      maxAmount: 20
    },
    paymentGatewayFee: {
      type: 'percentage',
      value: 2.9,
      fixedFee: 1.5
    }
  });

  const handleCommissionChange = (category: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [field]: value
      }
    }));
    onSettingsChange();
  };

  return (
    <div className="space-y-6">
      {/* عمولة التجار */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            عمولة التجار
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>نوع العمولة</Label>
              <Select
                value={settings.merchantCommission.type}
                onValueChange={(value) => handleCommissionChange('merchantCommission', 'type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">نسبة مئوية</SelectItem>
                  <SelectItem value="fixed">مبلغ ثابت</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>
                قيمة العمولة {settings.merchantCommission.type === 'percentage' ? '(%)' : '(ر.س)'}
              </Label>
              <Input
                type="number"
                value={settings.merchantCommission.value}
                onChange={(e) => handleCommissionChange('merchantCommission', 'value', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label>الحد الأدنى (ر.س)</Label>
              <Input
                type="number"
                value={settings.merchantCommission.minAmount}
                onChange={(e) => handleCommissionChange('merchantCommission', 'minAmount', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label>الحد الأقصى (ر.س)</Label>
              <Input
                type="number"
                value={settings.merchantCommission.maxAmount}
                onChange={(e) => handleCommissionChange('merchantCommission', 'maxAmount', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>مثال:</strong> على طلب بقيمة 100 ر.س، ستكون العمولة {settings.merchantCommission.value}
              {settings.merchantCommission.type === 'percentage' ? '%' : ' ر.س'} = {' '}
              {settings.merchantCommission.type === 'percentage' 
                ? (100 * settings.merchantCommission.value / 100).toFixed(2)
                : settings.merchantCommission.value
              } ر.س
            </p>
          </div>
        </CardContent>
      </Card>

      {/* عمولة المندوبين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            عمولة المندوبين
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>نوع العمولة</Label>
              <Select
                value={settings.representativeCommission.type}
                onValueChange={(value) => handleCommissionChange('representativeCommission', 'type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">نسبة مئوية</SelectItem>
                  <SelectItem value="fixed">مبلغ ثابت</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>
                قيمة العمولة {settings.representativeCommission.type === 'percentage' ? '(%)' : '(ر.س)'}
              </Label>
              <Input
                type="number"
                value={settings.representativeCommission.value}
                onChange={(e) => handleCommissionChange('representativeCommission', 'value', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label>الحد الأدنى (ر.س)</Label>
              <Input
                type="number"
                value={settings.representativeCommission.minAmount}
                onChange={(e) => handleCommissionChange('representativeCommission', 'minAmount', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label>الحد الأقصى (ر.س)</Label>
              <Input
                type="number"
                value={settings.representativeCommission.maxAmount}
                onChange={(e) => handleCommissionChange('representativeCommission', 'maxAmount', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* عمولة المنصة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            عمولة المنصة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>نوع العمولة</Label>
              <Select
                value={settings.platformCommission.type}
                onValueChange={(value) => handleCommissionChange('platformCommission', 'type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">نسبة مئوية</SelectItem>
                  <SelectItem value="fixed">مبلغ ثابت</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>
                قيمة العمولة {settings.platformCommission.type === 'percentage' ? '(%)' : '(ر.س)'}
              </Label>
              <Input
                type="number"
                value={settings.platformCommission.value}
                onChange={(e) => handleCommissionChange('platformCommission', 'value', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* رسوم بوابة الدفع */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            رسوم بوابة الدفع
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>النسبة المئوية (%)</Label>
              <Input
                type="number"
                value={settings.paymentGatewayFee.value}
                onChange={(e) => handleCommissionChange('paymentGatewayFee', 'value', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label>الرسم الثابت (ر.س)</Label>
              <Input
                type="number"
                value={settings.paymentGatewayFee.fixedFee}
                onChange={(e) => handleCommissionChange('paymentGatewayFee', 'fixedFee', parseFloat(e.target.value))}
                min="0"
                step="0.1"
              />
            </div>
          </div>

          <div className="p-3 bg-green-50 rounded-lg">
            <p className="text-sm text-green-800">
              <strong>مثال:</strong> على دفعة بقيمة 100 ر.س، ستكون الرسوم {' '}
              {(100 * settings.paymentGatewayFee.value / 100 + settings.paymentGatewayFee.fixedFee).toFixed(2)} ر.س
            </p>
          </div>
        </CardContent>
      </Card>

      {/* ملخص العمولات */}
      <Card>
        <CardHeader>
          <CardTitle>ملخص العمولات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium">عمولة التجار</span>
              <span className="text-blue-600">
                {settings.merchantCommission.value}
                {settings.merchantCommission.type === 'percentage' ? '%' : ' ر.س'}
              </span>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium">عمولة المندوبين</span>
              <span className="text-green-600">
                {settings.representativeCommission.value}
                {settings.representativeCommission.type === 'percentage' ? '%' : ' ر.س'}
              </span>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium">عمولة المنصة</span>
              <span className="text-purple-600">
                {settings.platformCommission.value}
                {settings.platformCommission.type === 'percentage' ? '%' : ' ر.س'}
              </span>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium">رسوم بوابة الدفع</span>
              <span className="text-orange-600">
                {settings.paymentGatewayFee.value}% + {settings.paymentGatewayFee.fixedFee} ر.س
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
