// src/components/debug/NetworkTestPanel.tsx
"use client";

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Wifi, 
  WifiOff,
  Database,
  RefreshCw,
  Trash2,
  Activity
} from 'lucide-react';
import { 
  runAllNetworkTests,
  quickTest,
  testFirestoreConnectivity,
  testNetworkStatus,
  testCacheClear,
  testPerformance
} from '@/utils/test-network-fixes';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  duration?: number;
}

export default function NetworkTestPanel() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Network Status', status: 'pending' },
    { name: 'Firestore Connectivity', status: 'pending' },
    { name: 'Cache Clear', status: 'pending' },
    { name: 'Performance Test', status: 'pending' }
  ]);
  const [isRunning, setIsRunning] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator?.onLine ?? true);

  // تحديث حالة الشبكة
  useState(() => {
    const updateOnlineStatus = () => setIsOnline(navigator.onLine);
    
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    
    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  });

  const updateTestStatus = (testName: string, status: TestResult['status'], message?: string, duration?: number) => {
    setTests(prev => prev.map(test => 
      test.name === testName 
        ? { ...test, status, message, duration }
        : test
    ));
  };

  const runSingleTest = async (testName: string, testFunction: () => Promise<any>) => {
    updateTestStatus(testName, 'running');
    const startTime = performance.now();
    
    try {
      await testFunction();
      const duration = performance.now() - startTime;
      updateTestStatus(testName, 'success', 'Test completed successfully', duration);
    } catch (error: any) {
      const duration = performance.now() - startTime;
      updateTestStatus(testName, 'error', error.message || 'Test failed', duration);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    // إعادة تعيين حالة الاختبارات
    setTests(prev => prev.map(test => ({ ...test, status: 'pending' as const })));
    
    // تشغيل الاختبارات واحداً تلو الآخر
    await runSingleTest('Network Status', async () => {
      testNetworkStatus();
      return Promise.resolve();
    });
    
    await runSingleTest('Firestore Connectivity', testFirestoreConnectivity);
    
    await runSingleTest('Cache Clear', testCacheClear);
    
    await runSingleTest('Performance Test', async () => {
      await testPerformance(3);
    });
    
    setIsRunning(false);
  };

  const runQuickTest = async () => {
    setIsRunning(true);
    try {
      await quickTest();
    } catch (error) {
      console.error('Quick test failed:', error);
    }
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full bg-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Badge variant="secondary">Running</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Network Test Panel
            </CardTitle>
            <CardDescription>
              Test network connectivity and Firestore functionality
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {isOnline ? (
              <Badge variant="default" className="bg-green-500">
                <Wifi className="h-3 w-3 mr-1" />
                Online
              </Badge>
            ) : (
              <Badge variant="destructive">
                <WifiOff className="h-3 w-3 mr-1" />
                Offline
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* أزرار التحكم */}
        <div className="flex gap-3">
          <Button 
            onClick={runAllTests} 
            disabled={isRunning}
            className="flex-1"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Run All Tests
          </Button>
          
          <Button 
            onClick={runQuickTest} 
            disabled={isRunning}
            variant="outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Quick Test
          </Button>
        </div>

        {/* نتائج الاختبارات */}
        <div className="space-y-3">
          <h4 className="font-semibold text-sm">Test Results:</h4>
          
          {tests.map((test, index) => (
            <div 
              key={index}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                {getStatusIcon(test.status)}
                <div>
                  <p className="font-medium text-sm">{test.name}</p>
                  {test.message && (
                    <p className="text-xs text-muted-foreground">{test.message}</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {test.duration && (
                  <span className="text-xs text-muted-foreground">
                    {test.duration.toFixed(0)}ms
                  </span>
                )}
                {getStatusBadge(test.status)}
              </div>
            </div>
          ))}
        </div>

        {/* معلومات إضافية */}
        <Alert>
          <Database className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>Note:</strong> This panel is for testing network fixes and should be removed in production.
            Check the browser console for detailed logs.
          </AlertDescription>
        </Alert>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-500">
              {tests.filter(t => t.status === 'success').length}
            </p>
            <p className="text-xs text-muted-foreground">Passed</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-red-500">
              {tests.filter(t => t.status === 'error').length}
            </p>
            <p className="text-xs text-muted-foreground">Failed</p>
          </div>
        </div>

        {/* أزرار إضافية */}
        <div className="flex gap-2 pt-4 border-t">
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline" 
            size="sm"
            className="flex-1"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Reload Page
          </Button>
          
          <Button 
            onClick={async () => {
              await testCacheClear();
              window.location.reload();
            }} 
            variant="outline" 
            size="sm"
            className="flex-1"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Clear Cache
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
