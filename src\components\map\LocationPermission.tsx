// src/components/map/LocationPermission.tsx
"use client";

import { FC, useState } from 'react';
import { MapPin, Shield, Eye, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface LocationPermissionProps {
  onAllow: () => void;
  onDeny: () => void;
  loading?: boolean;
  error?: string | null;
}

const LocationPermission: FC<LocationPermissionProps> = ({
  onAllow,
  onDeny,
  loading = false,
  error = null
}) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto shadow-2xl border-2">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <MapPin className="w-8 h-8 text-primary" />
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-right">
              🗺️ الوصول إلى موقعك
            </CardTitle>
            <CardDescription className="text-right mt-2">
              نحتاج إلى إذن للوصول إلى موقعك لعرض المتاجر القريبة منك
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3 flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
              <div className="text-right">
                <p className="text-sm font-medium text-destructive">خطأ في الوصول للموقع</p>
                <p className="text-xs text-destructive/80 mt-1">{error}</p>
              </div>
            </div>
          )}

          <div className="space-y-3">
            <div className="flex items-center gap-3 text-right">
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">عرض المتاجر القريبة</p>
                <p className="text-xs text-muted-foreground">اكتشف المتاجر والخدمات في منطقتك</p>
              </div>
            </div>

            <div className="flex items-center gap-3 text-right">
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">تجربة تسوق محسنة</p>
                <p className="text-xs text-muted-foreground">توصيات مخصصة حسب موقعك</p>
              </div>
            </div>

            <div className="flex items-center gap-3 text-right">
              <Shield className="w-5 h-5 text-blue-500 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">خصوصية محمية</p>
                <p className="text-xs text-muted-foreground">لا نحفظ أو نشارك بيانات موقعك</p>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="w-full text-xs text-muted-foreground hover:text-foreground"
            >
              <Eye className="w-4 h-4 ml-2" />
              {showDetails ? 'إخفاء التفاصيل' : 'عرض تفاصيل الخصوصية'}
            </Button>

            {showDetails && (
              <div className="mt-3 p-3 bg-muted/50 rounded-lg text-right">
                <h4 className="text-sm font-medium mb-2">كيف نستخدم موقعك:</h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• عرض المتاجر القريبة منك فقط</li>
                  <li>• حساب المسافات والاتجاهات</li>
                  <li>• تحسين نتائج البحث</li>
                  <li>• لا نحفظ الموقع على خوادمنا</li>
                  <li>• لا نشارك البيانات مع أطراف ثالثة</li>
                </ul>
              </div>
            )}
          </div>

          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={onDeny}
              disabled={loading}
              className="flex-1"
            >
              ليس الآن
            </Button>
            <Button
              onClick={onAllow}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  جاري التحميل...
                </div>
              ) : (
                <>
                  <MapPin className="w-4 h-4 ml-2" />
                  السماح بالوصول
                </>
              )}
            </Button>
          </div>

          <p className="text-xs text-muted-foreground text-center">
            يمكنك تغيير هذا الإعداد لاحقاً من إعدادات المتصفح
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default LocationPermission;
