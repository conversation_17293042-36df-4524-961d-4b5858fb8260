"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ShoppingCart, Check, Plus, Minus } from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import { useCart } from "./CartProvider";
import type { ProductDocument } from "@/types";

interface AddToCartButtonProps {
  product: ProductDocument;
  quantity?: number;
  selectedVariants?: { [key: string]: string };
  variant?: "default" | "outline" | "secondary";
  size?: "sm" | "default" | "lg";
  showQuantityControls?: boolean;
  className?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export default function AddToCartButton({
  product,
  quantity = 1,
  selectedVariants = {},
  variant = "default",
  size = "default",
  showQuantityControls = false,
  className = "",
  onSuccess,
  onError
}: AddToCartButtonProps) {
  const { t } = useLocale();
  const { toast } = useToast();
  const { addItem, getItemQuantity, isInCart } = useCart();
  const [isLoading, setIsLoading] = useState(false);
  const [localQuantity, setLocalQuantity] = useState(quantity);
  const [justAdded, setJustAdded] = useState(false);

  const currentQuantityInCart = getItemQuantity(product.id);
  const isProductInCart = isInCart(product.id);
  const isOutOfStock = product.stockQuantity === 0;
  const maxAvailableQuantity = Math.max(0, product.stockQuantity - currentQuantityInCart);

  const handleAddToCart = async () => {
    if (isOutOfStock || localQuantity <= 0) return;

    setIsLoading(true);
    
    try {
      await addItem({
        productId: product.id,
        productName: product.name,
        productImage: product.imageUrls?.[0] || '',
        price: product.price,
        quantity: localQuantity,
        storeId: product.storeId,
        storeName: product.storeName || '',
        maxQuantity: product.stockQuantity,
        selectedVariants
      });

      // Show success animation
      setJustAdded(true);
      setTimeout(() => setJustAdded(false), 2000);

      // Show success toast
      toast({
        title: t('addedToCart'),
        description: t('productAddedToCart', { 
          productName: product.name,
          quantity: localQuantity 
        }),
        duration: 3000,
      });

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error adding to cart:', error);
      
      const errorMessage = t('failedToAddToCart');
      toast({
        title: t('error'),
        description: errorMessage,
        variant: "destructive",
        duration: 5000,
      });

      // Call error callback
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    const clampedQuantity = Math.max(1, Math.min(newQuantity, maxAvailableQuantity));
    setLocalQuantity(clampedQuantity);
  };

  // If product is out of stock
  if (isOutOfStock) {
    return (
      <Button
        variant="secondary"
        size={size}
        disabled
        className={`${className} cursor-not-allowed`}
      >
        {t('outOfStock')}
      </Button>
    );
  }

  // If no more stock available (all in cart)
  if (maxAvailableQuantity === 0) {
    return (
      <Button
        variant="secondary"
        size={size}
        disabled
        className={`${className} cursor-not-allowed`}
      >
        {t('maxQuantityInCart')}
      </Button>
    );
  }

  return (
    <div className="flex items-center space-x-2 rtl:space-x-reverse">
      {/* Quantity Controls */}
      {showQuantityControls && (
        <div className="flex items-center border rounded-lg">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuantityChange(localQuantity - 1)}
            disabled={localQuantity <= 1}
            className="h-8 w-8 p-0"
          >
            <Minus className="w-3 h-3" />
          </Button>
          <span className="px-3 py-1 text-sm font-medium min-w-[2rem] text-center">
            {localQuantity}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuantityChange(localQuantity + 1)}
            disabled={localQuantity >= maxAvailableQuantity}
            className="h-8 w-8 p-0"
          >
            <Plus className="w-3 h-3" />
          </Button>
        </div>
      )}

      {/* Add to Cart Button */}
      <Button
        variant={justAdded ? "secondary" : variant}
        size={size}
        onClick={handleAddToCart}
        disabled={isLoading || localQuantity <= 0}
        className={`${className} ${justAdded ? 'bg-green-100 text-green-700 border-green-200' : ''} transition-all duration-200`}
      >
        {isLoading ? (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        ) : justAdded ? (
          <>
            <Check className="w-4 h-4 mr-2" />
            {t('added')}
          </>
        ) : (
          <>
            <ShoppingCart className="w-4 h-4 mr-2" />
            {isProductInCart ? t('addMore') : t('addToCart')}
          </>
        )}
      </Button>

      {/* Stock Warning */}
      {maxAvailableQuantity <= 5 && maxAvailableQuantity > 0 && (
        <span className="text-xs text-orange-600">
          {t('onlyXLeft', { count: maxAvailableQuantity })}
        </span>
      )}
    </div>
  );
}
