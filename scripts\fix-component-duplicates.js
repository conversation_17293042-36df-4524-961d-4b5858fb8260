#!/usr/bin/env node

/**
 * سكريبت إصلاح تكرارات المكونات في مشروع مِخْلاة
 * يقوم بدمج المكونات المكررة وإنشاء مكونات موحدة
 */

const fs = require('fs');
const path = require('path');

// مسارات المكونات
const COMPONENTS_DIR = path.join(process.cwd(), 'src', 'components');
const UTILS_DIR = path.join(process.cwd(), 'src', 'utils');

/**
 * قراءة ملف بشكل آمن
 */
function readFileContent(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * كتابة ملف بشكل آمن
 */
function writeFileContent(filePath, content) {
  try {
    // إنشاء المجلد إذا لم يكن موجوداً
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في كتابة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * إنشاء نسخة احتياطية من ملف
 */
function createBackup(filePath) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${backupPath}`);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في إنشاء نسخة احتياطية:`, error.message);
    return false;
  }
}

/**
 * دمج مكونات Logo المتعددة
 */
function consolidateLogoComponents() {
  console.log('🔧 دمج مكونات Logo...');
  
  const logoPath = path.join(COMPONENTS_DIR, 'Logo.tsx');
  const brandLogoPath = path.join(COMPONENTS_DIR, 'BrandLogo.tsx');
  const animatedLogoPath = path.join(COMPONENTS_DIR, 'AnimatedLogo.tsx');
  
  // إنشاء نسخ احتياطية
  [logoPath, brandLogoPath, animatedLogoPath].forEach(filePath => {
    if (fs.existsSync(filePath)) {
      createBackup(filePath);
    }
  });
  
  // إنشاء مكون Logo موحد ومحسن
  const unifiedLogoContent = `"use client";

import { useState, useEffect, useCallback } from 'react';
import type { SVGProps } from 'react';
import { cn } from '@/lib/utils';
import { useLocale } from '@/hooks/use-locale';

interface LogoProps extends SVGProps<SVGSVGElement> {
  variant?: 'simple' | 'animated' | 'brand';
  size?: 'small' | 'default' | 'large';
  interactive?: boolean;
  showText?: boolean;
  animated?: boolean;
}

export default function Logo({
  variant = 'simple',
  size = 'default',
  interactive = true,
  showText = false,
  animated = false,
  className,
  ...props
}: LogoProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const { t } = useLocale();

  // تحديد الحجم
  const logoSize = size === 'small' ? 40 : size === 'large' ? 120 : 80;
  const textSize = size === 'small' ? 'text-lg' : size === 'large' ? 'text-3xl' : 'text-2xl';

  // تفعيل الرسوم المتحركة عند الحاجة
  useEffect(() => {
    if (animated || variant === 'animated') {
      const timer = setTimeout(() => setIsAnimating(true), 1000);
      return () => clearTimeout(timer);
    }
  }, [animated, variant]);

  const handleMouseEnter = useCallback(() => {
    if (interactive) {
      setIsHovered(true);
      setIsAnimating(true);
    }
  }, [interactive]);

  const handleMouseLeave = useCallback(() => {
    if (interactive) {
      setIsHovered(false);
    }
  }, [interactive]);

  // مكون Logo البسيط
  if (variant === 'simple') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        width={logoSize}
        height={logoSize}
        aria-label="مِخْلاة"
        role="img"
        className={cn('logo', className)}
        {...props}
      >
        <defs>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: 'hsl(var(--primary))', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: 'hsl(var(--accent))', stopOpacity: 1 }} />
          </linearGradient>
        </defs>
        <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" />
        <text
          x="50%"
          y="50%"
          dy=".3em"
          textAnchor="middle"
          fontSize="40"
          fontWeight="bold"
          fill="hsl(var(--primary-foreground))"
          fontFamily="var(--font-geist-sans)"
        >
          م
        </text>
      </svg>
    );
  }

  // مكون Logo مع العلامة التجارية
  if (variant === 'brand') {
    return (
      <div 
        className={cn(
          'flex items-center gap-3 transition-all duration-300',
          isHovered && 'scale-105',
          className
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className={cn(
          'relative transition-all duration-300',
          isHovered && 'rotate-6'
        )}>
          <Logo 
            variant="animated"
            size={size}
            animated={isAnimating}
            className={cn(
              'transition-all duration-500',
              isHovered && 'filter drop-shadow-lg'
            )}
          />
          
          {isHovered && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="w-full h-full absolute animate-ping rounded-full bg-accent/20 scale-110" />
            </div>
          )}
        </div>

        {showText && (
          <div className={cn(
            'font-bold transition-all duration-300 text-primary-foreground',
            textSize,
            isHovered && 'text-accent'
          )}>
            {t('appName')}
          </div>
        )}
      </div>
    );
  }

  // مكون Logo المتحرك (افتراضي)
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 100"
      width={logoSize}
      height={logoSize}
      aria-label="مِخْلاة"
      role="img"
      className={cn('logo', className)}
      {...props}
    >
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{ stopColor: 'hsl(var(--primary))', stopOpacity: 1 }} />
          <stop offset="100%" style={{ stopColor: 'hsl(var(--accent))', stopOpacity: 1 }} />
        </linearGradient>
        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="2" result="blur" />
          <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
      </defs>

      <circle 
        className={cn(isAnimating && "animate-pulse")} 
        cx="50" 
        cy="50" 
        r="45" 
        fill="url(#logoGradient)" 
        filter="url(#glow)"
      />

      <g stroke="hsl(var(--primary-foreground))" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M30,30 C30,25 40,20 50,20 C60,20 70,25 70,30"
        />
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M70,30 L70,65 C70,70 60,75 50,75 C40,75 30,70 30,65 L30,30"
        />
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M40,40 C40,40 50,45 60,40"
        />
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M40,50 C40,50 50,55 60,50"
        />
      </g>

      <text
        className={cn(isAnimating && "animate-pulse")}
        x="50%"
        y="50%"
        dy=".3em"
        textAnchor="middle"
        fontSize="40"
        fontWeight="bold"
        fill="hsl(var(--primary-foreground))"
        fontFamily="var(--font-geist-sans)"
      >
        م
      </text>
    </svg>
  );
}

// تصدير أنواع إضافية للتوافق مع الإصدارات السابقة
export const SimpleLogo = (props: SVGProps<SVGSVGElement>) => <Logo variant="simple" {...props} />;
export const AnimatedLogo = (props: LogoProps) => <Logo variant="animated" {...props} />;
export const BrandLogo = (props: LogoProps) => <Logo variant="brand" showText {...props} />;
`;

  if (writeFileContent(logoPath, unifiedLogoContent)) {
    console.log('   ✅ تم إنشاء مكون Logo موحد');
    
    // حذف الملفات المكررة
    [brandLogoPath, animatedLogoPath].forEach(filePath => {
      if (fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log(`   🗑️  تم حذف الملف المكرر: ${path.basename(filePath)}`);
        } catch (error) {
          console.error(`   ❌ خطأ في حذف ${filePath}:`, error.message);
        }
      }
    });
    
    return true;
  }
  
  return false;
}

/**
 * إنشاء دوال مساعدة موحدة
 */
function createUnifiedUtils() {
  console.log('🔧 إنشاء دوال مساعدة موحدة...');
  
  const utilsPath = path.join(UTILS_DIR, 'formatters.ts');
  
  const formattersContent = `// src/utils/formatters.ts
// دوال التنسيق الموحدة لمشروع مِخْلاة

/**
 * تنسيق العملة بالريال السعودي
 */
export function formatCurrency(
  amount: number, 
  currency: string = 'SAR',
  locale: string = 'ar-SA'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency === 'SAR' ? 'SAR' : 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * تنسيق المسافة (متر/كيلومتر)
 */
export function formatDistance(distance?: number): string {
  if (!distance) return '';
  if (distance < 1) {
    return \`\${Math.round(distance * 1000)} م\`;
  }
  return \`\${distance.toFixed(1)} كم\`;
}

/**
 * الحصول على الأحرف الأولى من اسم المتجر
 */
export function getStoreInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

/**
 * تنسيق التاريخ والوقت
 */
export function formatDateTime(
  date: Date | string,
  locale: string = 'ar-SA',
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options
  };
  
  return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
}

/**
 * تنسيق الأرقام
 */
export function formatNumber(
  number: number,
  locale: string = 'ar-SA',
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(locale, options).format(number);
}

/**
 * اقتطاع النص مع إضافة نقاط
 */
export function truncateText(text: string, maxLength: number = 100): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
export function isValidSaudiPhone(phone: string): boolean {
  const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * تنسيق رقم الهاتف السعودي
 */
export function formatSaudiPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.startsWith('966')) {
    return \`+\${cleaned}\`;
  } else if (cleaned.startsWith('0')) {
    return \`+966\${cleaned.substring(1)}\`;
  } else if (cleaned.length === 9) {
    return \`+966\${cleaned}\`;
  }
  
  return phone;
}
`;

  if (writeFileContent(utilsPath, formattersContent)) {
    console.log('   ✅ تم إنشاء ملف الدوال المساعدة الموحدة');
    return true;
  }
  
  return false;
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء إصلاح تكرارات المكونات...\n');
  
  let success = true;
  let tasksCompleted = 0;
  
  // دمج مكونات Logo
  if (consolidateLogoComponents()) {
    tasksCompleted++;
  } else {
    success = false;
  }
  
  console.log('');
  
  // إنشاء دوال مساعدة موحدة
  if (createUnifiedUtils()) {
    tasksCompleted++;
  } else {
    success = false;
  }
  
  console.log('');
  
  if (success) {
    console.log('🎉 تم إصلاح تكرارات المكونات بنجاح!');
    console.log(`📊 المهام المكتملة: ${tasksCompleted}/2`);
    console.log('💡 تم إنشاء نسخ احتياطية من الملفات المحذوفة');
    console.log('🔍 يرجى مراجعة الملفات الجديدة والتأكد من عملها');
  } else {
    console.log('❌ فشل في إصلاح بعض التكرارات');
    console.log('🔄 يمكنك استعادة النسخ الاحتياطية إذا لزم الأمر');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  consolidateLogoComponents,
  createUnifiedUtils
};
