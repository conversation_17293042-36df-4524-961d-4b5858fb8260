// src/components/merchant/RecentOrders.tsx
"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useLocale } from '@/hooks/use-locale';
import { useAuth } from '@/context/AuthContext';
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Link from 'next/link';
import { ShoppingCart, Eye, Calendar, User, DollarSign } from 'lucide-react';
import type { OrderDocument } from '@/types';

interface RecentOrdersProps {
  className?: string;
}

export default function RecentOrders({ className }: RecentOrdersProps) {
  const { user } = useAuth();
  const { t, locale } = useLocale();
  const [orders, setOrders] = useState<OrderDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecentOrders = async () => {
      if (!user?.uid) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Try to fetch recent orders
        const ordersQuery = query(
          collection(db, 'orders'),
          where('merchantUid', '==', user.uid),
          orderBy('createdAt', 'desc'),
          limit(5)
        );

        const ordersSnapshot = await getDocs(ordersQuery);
        const recentOrders = ordersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as OrderDocument[];

        setOrders(recentOrders);
      } catch (err) {
        console.log('Orders collection not found or empty, which is expected for new merchants');
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentOrders();
  }, [user?.uid]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'confirmed':
        return 'default';
      case 'preparing':
        return 'outline';
      case 'ready':
        return 'default';
      case 'shipped':
        return 'default';
      case 'delivered':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return t('orderStatusPending');
      case 'confirmed':
        return t('orderStatusConfirmed');
      case 'preparing':
        return t('orderStatusPreparing');
      case 'ready':
        return t('orderStatusReady');
      case 'shipped':
        return t('orderStatusShipped');
      case 'delivered':
        return t('orderStatusDelivered');
      case 'cancelled':
        return t('orderStatusCancelled');
      default:
        return status;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp || !timestamp.toDate) return '';
    return timestamp.toDate().toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ${t('SAR')}`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-center">{t('recentOrders')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-32" />
                </div>
                <div className="text-right space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-center flex-1">{t('recentOrders')}</CardTitle>
        {orders.length > 0 && (
          <Button asChild variant="outline" size="sm">
            <Link href={`/${locale}/merchant/orders`}>
              {t('viewAllOrders')}
            </Link>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-center py-8">
            <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-2">
              {t('noRecentOrders')}
            </p>
            <p className="text-sm text-muted-foreground">
              ستظهر الطلبات الحديثة هنا عند توفرها
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <div
                key={order.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">#{order.orderNumber}</span>
                    <Badge variant={getStatusBadgeVariant(order.status)}>
                      {getStatusText(order.status)}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span>{order.customerInfo.name}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(order.createdAt)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center gap-1 font-medium mb-1">
                    <DollarSign className="h-4 w-4" />
                    <span>{formatCurrency(order.totalAmount)}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {order.items.length} {t('items')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
