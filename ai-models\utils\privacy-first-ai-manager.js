// مدير الذكاء الاصطناعي المحلي - خصوصية 100%
class PrivacyFirstAIManager {
  constructor() {
    this.config = null;
    this.models = new Map();
    this.cache = new Map();
    this.initialized = false;
    this.privacyMode = 'strict';
    this.auditLog = [];
  }

  async initialize() {
    console.log('🔒 تهيئة نظام الذكاء الاصطناعي المحلي الآمن...');
    
    try {
      // تحميل تكوين الخصوصية
      const configPath = '../configs/privacy-first-ai-config.json';
      this.config = await this.loadConfig(configPath);
      
      // التحقق من وضع الخصوصية
      this.validatePrivacySettings();
      
      // تهيئة النماذج المحلية فقط
      await this.initializeLocalModels();
      
      // تسجيل بداية الجلسة
      this.logAudit('system_initialized', { 
        privacy_mode: this.privacyMode,
        local_only: true,
        timestamp: new Date().toISOString()
      });
      
      this.initialized = true;
      console.log('✅ تم تهيئة النظام الآمن بنجاح - خصوصية 100%');
      
    } catch (error) {
      console.error('❌ فشل في تهيئة النظام الآمن:', error.message);
      throw error;
    }
  }

  async loadConfig(configPath) {
    // في البيئة الحقيقية، سيتم تحميل الملف
    // هنا نستخدم التكوين المدمج للأمان
    return {
      version: "3.0.0",
      mode: "privacy-first-local",
      providers: {
        browser_local: { enabled: true }
      },
      privacy_policy: {
        data_processing: "local_only",
        network_requests: "none"
      }
    };
  }

  validatePrivacySettings() {
    console.log('🛡️ التحقق من إعدادات الخصوصية...');
    
    const policy = this.config.privacy_policy;
    
    // التأكد من المعالجة المحلية فقط
    if (policy.data_processing !== 'local_only') {
      throw new Error('انتهاك الخصوصية: المعالجة يجب أن تكون محلية فقط');
    }
    
    // التأكد من عدم وجود طلبات شبكة
    if (policy.network_requests !== 'none') {
      throw new Error('انتهاك الخصوصية: لا يُسمح بطلبات الشبكة');
    }
    
    console.log('✅ إعدادات الخصوصية صحيحة');
  }

  async initializeLocalModels() {
    console.log('🤖 تهيئة النماذج المحلية...');
    
    // تهيئة Tesseract.js للـ OCR
    if (typeof window !== 'undefined' && window.Tesseract) {
      this.models.set('tesseract_ocr', {
        engine: window.Tesseract,
        initialized: false,
        privacy: 'local_only'
      });
      console.log('✅ تم تحميل Tesseract OCR');
    }
    
    // تهيئة Compromise.js للتحليل النصي
    if (typeof window !== 'undefined' && window.nlp) {
      this.models.set('compromise_nlp', {
        engine: window.nlp,
        initialized: true,
        privacy: 'local_only'
      });
      console.log('✅ تم تحميل Compromise NLP');
    }
    
    // تهيئة ML5.js للتصنيف
    if (typeof window !== 'undefined' && window.ml5) {
      this.models.set('ml5_classifier', {
        engine: window.ml5,
        initialized: false,
        privacy: 'local_only'
      });
      console.log('✅ تم تحميل ML5 Classifier');
    }
  }

  async processDocument(file, type) {
    if (!this.initialized) {
      await this.initialize();
    }

    console.log(`📄 معالجة مستند محلياً: ${type}`);
    
    // تسجيل بداية المعالجة
    this.logAudit('document_processing_started', {
      document_type: type,
      file_size: file.size,
      privacy_guaranteed: true
    });

    try {
      // 1. استخراج النص محلياً
      const ocrResult = await this.extractTextLocally(file);
      
      // 2. تحليل النص محلياً
      const analysisResult = await this.analyzeTextLocally(ocrResult.text, type);
      
      // 3. التحقق من صحة البيانات محلياً
      const validationResult = await this.validateDataLocally(analysisResult.extractedData, type);
      
      // 4. تجميع النتائج
      const finalResult = {
        success: true,
        privacy_guaranteed: true,
        processing_location: 'local_browser',
        data_sent_externally: false,
        extractedText: ocrResult.text,
        extractedData: analysisResult.extractedData,
        validation: validationResult,
        confidence: this.calculateLocalConfidence(ocrResult, analysisResult, validationResult),
        timestamp: new Date().toISOString()
      };
      
      // تسجيل نجاح المعالجة
      this.logAudit('document_processing_completed', {
        success: true,
        confidence: finalResult.confidence,
        privacy_maintained: true
      });
      
      return finalResult;
      
    } catch (error) {
      console.error('❌ فشل في معالجة المستند محلياً:', error.message);
      
      this.logAudit('document_processing_failed', {
        error: error.message,
        privacy_maintained: true
      });
      
      throw error;
    }
  }

  async extractTextLocally(file) {
    console.log('📝 استخراج النص محلياً باستخدام Tesseract...');
    
    const ocrModel = this.models.get('tesseract_ocr');
    if (!ocrModel) {
      throw new Error('نموذج OCR المحلي غير متاح');
    }

    try {
      // تحويل الملف إلى صورة
      const imageData = await this.fileToImageData(file);
      
      // استخراج النص باستخدام Tesseract
      const result = await ocrModel.engine.recognize(imageData, 'ara+eng', {
        logger: m => console.log(`OCR: ${m.status} ${m.progress}`)
      });
      
      return {
        text: result.data.text,
        confidence: result.data.confidence / 100,
        processing_location: 'local_browser',
        privacy_safe: true
      };
      
    } catch (error) {
      console.error('❌ فشل في استخراج النص محلياً:', error.message);
      
      // استخدام استخراج بسيط كبديل
      return {
        text: 'فشل في استخراج النص - يتطلب مراجعة يدوية',
        confidence: 0.1,
        processing_location: 'local_browser',
        privacy_safe: true,
        fallback_used: true
      };
    }
  }

  async analyzeTextLocally(text, documentType) {
    console.log('🔍 تحليل النص محلياً...');
    
    const nlpModel = this.models.get('compromise_nlp');
    if (!nlpModel) {
      return this.analyzeTextWithRegex(text, documentType);
    }

    try {
      // تحليل النص باستخدام Compromise
      const doc = nlpModel.engine(text);
      
      // استخراج الكيانات
      const entities = {
        people: doc.people().out('array'),
        places: doc.places().out('array'),
        organizations: doc.organizations().out('array'),
        dates: doc.dates().out('array'),
        numbers: doc.values().out('array')
      };
      
      // استخراج البيانات حسب نوع المستند
      const extractedData = this.extractDataByType(text, documentType, entities);
      
      return {
        extractedData,
        entities,
        processing_location: 'local_browser',
        privacy_safe: true,
        confidence: 0.85
      };
      
    } catch (error) {
      console.error('❌ فشل في التحليل المحلي:', error.message);
      return this.analyzeTextWithRegex(text, documentType);
    }
  }

  analyzeTextWithRegex(text, documentType) {
    console.log('📋 تحليل النص باستخدام الأنماط المحلية...');
    
    const template = this.config.documentTemplates[documentType];
    if (!template) {
      throw new Error(`قالب غير موجود للنوع: ${documentType}`);
    }

    const extractedData = {};
    
    // استخراج البيانات باستخدام الأنماط المحددة
    for (const [field, patterns] of Object.entries(template.extractionPatterns)) {
      for (const pattern of patterns) {
        const regex = new RegExp(pattern, 'gi');
        const match = text.match(regex);
        if (match && match[1]) {
          extractedData[field] = match[1].trim();
          break;
        }
      }
    }
    
    return {
      extractedData,
      processing_location: 'local_browser',
      privacy_safe: true,
      confidence: 0.75,
      method: 'regex_patterns'
    };
  }

  async validateDataLocally(data, documentType) {
    console.log('✅ التحقق من صحة البيانات محلياً...');
    
    const template = this.config.documentTemplates[documentType];
    if (!template) {
      throw new Error(`قالب التحقق غير موجود للنوع: ${documentType}`);
    }

    const validation = {
      valid: true,
      issues: [],
      privacy_safe: true,
      processing_location: 'local_browser'
    };

    // التحقق من الحقول المطلوبة
    for (const field of template.requiredFields) {
      if (!data[field] || data[field].trim() === '') {
        validation.valid = false;
        validation.issues.push(`حقل مطلوب مفقود: ${field}`);
      }
    }

    // التحقق من قواعد التحقق
    for (const [field, rules] of Object.entries(template.validationRules)) {
      if (data[field]) {
        if (rules.pattern) {
          const regex = new RegExp(rules.pattern);
          if (!regex.test(data[field])) {
            validation.valid = false;
            validation.issues.push(`تنسيق غير صحيح للحقل ${field}: ${rules.description}`);
          }
        }
        
        if (rules.minLength && data[field].length < rules.minLength) {
          validation.valid = false;
          validation.issues.push(`الحقل ${field} قصير جداً`);
        }
        
        if (rules.maxLength && data[field].length > rules.maxLength) {
          validation.valid = false;
          validation.issues.push(`الحقل ${field} طويل جداً`);
        }
      }
    }

    return validation;
  }

  extractDataByType(text, documentType, entities) {
    // استخراج البيانات المخصص لكل نوع مستند
    const extractedData = {};
    
    switch (documentType) {
      case 'commercial_registration':
        extractedData.businessName = this.findBusinessName(text, entities);
        extractedData.ownerName = this.findOwnerName(text, entities);
        extractedData.registrationNumber = this.findRegistrationNumber(text);
        break;
        
      case 'freelance_document':
        extractedData.ownerName = this.findOwnerName(text, entities);
        extractedData.documentNumber = this.findDocumentNumber(text);
        break;
        
      case 'driving_license':
        extractedData.holderName = this.findOwnerName(text, entities);
        extractedData.licenseNumber = this.findLicenseNumber(text);
        break;
    }
    
    // استخراج التواريخ
    extractedData.issueDate = this.findIssueDate(text);
    extractedData.expiryDate = this.findExpiryDate(text);
    
    return extractedData;
  }

  findBusinessName(text, entities) {
    // البحث في الكيانات المستخرجة أولاً
    if (entities.organizations && entities.organizations.length > 0) {
      return entities.organizations[0];
    }
    
    // البحث باستخدام الأنماط
    const patterns = [
      /اسم المنشأة[:\s]*([^\n]+)/gi,
      /اسم الشركة[:\s]*([^\n]+)/gi,
      /المنشأة[:\s]*([^\n]+)/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    
    return null;
  }

  findOwnerName(text, entities) {
    // البحث في الكيانات المستخرجة أولاً
    if (entities.people && entities.people.length > 0) {
      return entities.people[0];
    }
    
    // البحث باستخدام الأنماط
    const patterns = [
      /اسم التاجر[:\s]*([^\n]+)/gi,
      /اسم المالك[:\s]*([^\n]+)/gi,
      /اسم حامل الرخصة[:\s]*([^\n]+)/gi,
      /الاسم[:\s]*([^\n]+)/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    
    return null;
  }

  findRegistrationNumber(text) {
    const patterns = [
      /رقم السجل[:\s]*(\d{10})/gi,
      /السجل التجاري[:\s]*(\d{10})/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    return null;
  }

  findDocumentNumber(text) {
    const patterns = [
      /رقم الوثيقة[:\s]*([A-Z0-9]{8,12})/gi,
      /رقم العمل الحر[:\s]*([A-Z0-9]{8,12})/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    return null;
  }

  findLicenseNumber(text) {
    const patterns = [
      /رقم الرخصة[:\s]*(\d{10})/gi,
      /رقم رخصة القيادة[:\s]*(\d{10})/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    return null;
  }

  findIssueDate(text) {
    const patterns = [
      /تاريخ الإصدار[:\s]*([^\n]+)/gi,
      /تاريخ الإنشاء[:\s]*([^\n]+)/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    
    return null;
  }

  findExpiryDate(text) {
    const patterns = [
      /تاريخ الانتهاء[:\s]*([^\n]+)/gi,
      /تاريخ الصلاحية[:\s]*([^\n]+)/gi
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    
    return null;
  }

  calculateLocalConfidence(ocrResult, analysisResult, validationResult) {
    let confidence = 0;
    
    // ثقة OCR (40%)
    confidence += (ocrResult.confidence || 0.5) * 0.4;
    
    // ثقة التحليل (40%)
    confidence += (analysisResult.confidence || 0.5) * 0.4;
    
    // ثقة التحقق (20%)
    const validationConfidence = validationResult.valid ? 0.9 : 0.3;
    confidence += validationConfidence * 0.2;
    
    return Math.min(confidence, 0.95); // حد أقصى 95% للمعالجة المحلية
  }

  async fileToImageData(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  logAudit(action, details) {
    const auditEntry = {
      timestamp: new Date().toISOString(),
      action,
      details,
      privacy_maintained: true,
      local_processing: true
    };
    
    this.auditLog.push(auditEntry);
    
    // الاحتفاظ بآخر 100 إدخال فقط
    if (this.auditLog.length > 100) {
      this.auditLog.shift();
    }
  }

  getPrivacyReport() {
    return {
      privacy_mode: this.privacyMode,
      data_processing: 'local_only',
      network_requests: 'none',
      data_retention: 'session_only',
      models_used: Array.from(this.models.keys()),
      audit_entries: this.auditLog.length,
      privacy_guaranteed: true,
      compliance: ['GDPR', 'Saudi_Data_Protection_Law']
    };
  }

  clearSession() {
    console.log('🧹 تنظيف الجلسة وحماية الخصوصية...');
    
    // مسح الكاش
    this.cache.clear();
    
    // مسح سجل المراجعة
    this.auditLog = [];
    
    // تنظيف الذاكرة
    if (typeof window !== 'undefined' && window.gc) {
      window.gc();
    }
    
    console.log('✅ تم تنظيف الجلسة - الخصوصية محمية');
  }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { PrivacyFirstAIManager };
} else if (typeof window !== 'undefined') {
  window.PrivacyFirstAIManager = PrivacyFirstAIManager;
}
