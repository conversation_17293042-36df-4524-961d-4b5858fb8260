"use client";

import { useState } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ProfileCustomization from './ProfileCustomization';
import PasswordChange from './PasswordChange';
import SubscriptionInfo from './SubscriptionInfo';
import OrderHistory from './OrderHistory';
import { UserCircle, Lock, Crown, ShoppingBag } from 'lucide-react';
import type { UserProfile } from '@/types';

interface ProfileTabsProps {
  user: UserProfile;
}

export default function ProfileTabs({ user }: ProfileTabsProps) {
  const { t } = useLocale();
  const [activeTab, setActiveTab] = useState("profile");

  return (
    <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-4 mb-8">
        <TabsTrigger value="profile" className="flex items-center justify-center gap-2">
          <UserCircle className="h-4 w-4" />
          <span className="hidden sm:inline">{t('profile')}</span>
        </TabsTrigger>
        <TabsTrigger value="password" className="flex items-center justify-center gap-2">
          <Lock className="h-4 w-4" />
          <span className="hidden sm:inline">{t('password')}</span>
        </TabsTrigger>
        <TabsTrigger value="subscription" className="flex items-center justify-center gap-2">
          <Crown className="h-4 w-4" />
          <span className="hidden sm:inline">{t('subscription')}</span>
        </TabsTrigger>
        <TabsTrigger value="orders" className="flex items-center justify-center gap-2">
          <ShoppingBag className="h-4 w-4" />
          <span className="hidden sm:inline">{t('orders')}</span>
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="profile" className="mt-0">
        <ProfileCustomization user={user} />
      </TabsContent>
      
      <TabsContent value="password" className="mt-0">
        <PasswordChange />
      </TabsContent>
      
      <TabsContent value="subscription" className="mt-0">
        <SubscriptionInfo />
      </TabsContent>
      
      <TabsContent value="orders" className="mt-0">
        <OrderHistory />
      </TabsContent>
    </Tabs>
  );
}
