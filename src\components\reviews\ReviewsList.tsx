'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Star, ThumbsUp, Flag, Trash2, MoreVertical, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useReviews } from '@/hooks/useReviews';
import { useAuth } from '@/context/AuthContext';
import type { CustomerReview } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { useLocale } from 'next-intl';
import { ReportReviewDialog } from './ReportReviewDialog';
import { ReviewImagesDialog } from './ReviewImagesDialog';

interface ReviewsListProps {
  targetId: string;
  type: 'store' | 'product';
  className?: string;
}

export function ReviewsList({ targetId, type, className }: ReviewsListProps) {
  const t = useTranslations();
  const locale = useLocale();
  const { user } = useAuth();
  const { reviews, loading, hasMore, markHelpful, deleteReview, loadMore } = useReviews({
    targetId,
    type,
    limit: 10
  });

  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [imagesDialogOpen, setImagesDialogOpen] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  const handleMarkHelpful = async (reviewId: string) => {
    await markHelpful(reviewId);
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (confirm(t('reviews.confirmDelete'))) {
      await deleteReview(reviewId);
    }
  };

  const handleReportReview = (reviewId: string) => {
    setSelectedReviewId(reviewId);
    setReportDialogOpen(true);
  };

  const handleViewImages = (images: string[]) => {
    setSelectedImages(images);
    setImagesDialogOpen(true);
  };

  const formatDate = (timestamp: any) => {
    const date = timestamp?.toDate?.() || new Date(timestamp);
    return formatDistanceToNow(date, {
      addSuffix: true,
      locale: locale === 'ar' ? ar : enUS
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating 
            ? 'fill-yellow-400 text-yellow-400' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  if (loading && reviews.length === 0) {
    return (
      <div className={`space-y-4 ${className}`}>
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-gray-200 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                  <div className="h-16 bg-gray-200 rounded" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (reviews.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t('reviews.noReviews')}
        </h3>
        <p className="text-gray-500">
          {t('reviews.beFirstToReview')}
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {reviews.map((review) => (
        <Card key={review.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4 rtl:space-x-reverse flex-1">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={review.customerAvatar} />
                  <AvatarFallback>
                    {review.customerName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                    <h4 className="font-medium text-gray-900">
                      {review.customerName}
                    </h4>
                    {review.isVerified && (
                      <Badge variant="secondary" className="text-xs">
                        {t('reviews.verified')}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                    <div className="flex items-center">
                      {renderStars(review.rating)}
                    </div>
                    <span className="text-sm text-gray-500">
                      {formatDate(review.createdAt)}
                    </span>
                  </div>
                  
                  <p className="text-gray-700 mb-3 leading-relaxed">
                    {review.comment}
                  </p>
                  
                  {review.images && review.images.length > 0 && (
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewImages(review.images!)}
                        className="text-xs"
                      >
                        <ImageIcon className="h-3 w-3 me-1" />
                        {t('reviews.viewImages')} ({review.images.length})
                      </Button>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMarkHelpful(review.id)}
                      className="text-gray-500 hover:text-blue-600"
                    >
                      <ThumbsUp className="h-4 w-4 me-1" />
                      {t('reviews.helpful')} ({review.helpfulCount})
                    </Button>
                  </div>
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {user?.uid === review.customerId && (
                    <DropdownMenuItem
                      onClick={() => handleDeleteReview(review.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 me-2" />
                      {t('reviews.delete')}
                    </DropdownMenuItem>
                  )}
                  {user?.uid !== review.customerId && (
                    <DropdownMenuItem
                      onClick={() => handleReportReview(review.id)}
                    >
                      <Flag className="h-4 w-4 me-2" />
                      {t('reviews.report')}
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {hasMore && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={loadMore}
            disabled={loading}
          >
            {loading ? t('common.loading') : t('reviews.loadMore')}
          </Button>
        </div>
      )}
      
      <ReportReviewDialog
        open={reportDialogOpen}
        onOpenChange={setReportDialogOpen}
        reviewId={selectedReviewId}
      />
      
      <ReviewImagesDialog
        open={imagesDialogOpen}
        onOpenChange={setImagesDialogOpen}
        images={selectedImages}
      />
    </div>
  );
}
