#!/usr/bin/env node

/**
 * اختبار ترجمات صفحة الخرائط
 * يتحقق من أن ترجمة storesFound تعمل بشكل صحيح
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'en.json');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * دالة ترجمة مبسطة
 */
function t(key, params = {}, translations = {}) {
  let text = translations[key] || key;
  
  // استبدال المتغيرات
  Object.keys(params).forEach(param => {
    text = text.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
  });
  
  return text;
}

/**
 * اختبار ترجمات صفحة الخرائط
 */
function testMapTranslations() {
  console.log('🗺️  اختبار ترجمات صفحة الخرائط\n');
  
  // قراءة ملفات الترجمة
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    return false;
  }
  
  let allTestsPassed = true;
  
  // اختبار الترجمات الأساسية لصفحة الخرائط
  console.log('📝 اختبار الترجمات الأساسية:');
  
  const basicTests = [
    { key: 'interactiveMap', ar: 'الخريطة التفاعلية', en: 'Interactive Map' },
    { key: 'findNearbyStores', ar: 'اعثر على المتاجر القريبة منك', en: 'Find nearby stores' },
    { key: 'storesMap', ar: 'خريطة المتاجر', en: 'Stores Map' },
    { key: 'nearbyStores', ar: 'المتاجر القريبة', en: 'Nearby Stores' },
    { key: 'noStoresFound', ar: 'لم يتم العثور على متاجر', en: 'No stores found' }
  ];
  
  basicTests.forEach(test => {
    const arResult = arTranslations[test.key];
    const enResult = enTranslations[test.key];
    
    const arSuccess = arResult === test.ar;
    const enSuccess = enResult === test.en;
    
    console.log(`   ${arSuccess ? '✅' : '❌'} ${test.key} (AR): "${arResult}"`);
    console.log(`   ${enSuccess ? '✅' : '❌'} ${test.key} (EN): "${enResult}"`);
    
    if (!arSuccess || !enSuccess) {
      allTestsPassed = false;
    }
  });
  
  console.log('');
  
  // اختبار ترجمة storesFound مع المتغيرات
  console.log('📝 اختبار ترجمة storesFound مع المتغيرات:');
  
  const storesFoundTests = [
    { count: 0, arExpected: 'تم العثور على 0 متجر', enExpected: 'Found 0 stores' },
    { count: 1, arExpected: 'تم العثور على 1 متجر', enExpected: 'Found 1 stores' },
    { count: 5, arExpected: 'تم العثور على 5 متجر', enExpected: 'Found 5 stores' },
    { count: 10, arExpected: 'تم العثور على 10 متجر', enExpected: 'Found 10 stores' }
  ];
  
  storesFoundTests.forEach(test => {
    const arResult = t('storesFound', { count: test.count }, arTranslations);
    const enResult = t('storesFound', { count: test.count }, enTranslations);
    
    const arSuccess = arResult === test.arExpected;
    const enSuccess = enResult === test.enExpected;
    
    console.log(`   ${arSuccess ? '✅' : '❌'} العدد ${test.count} (AR): "${arResult}"`);
    console.log(`   ${enSuccess ? '✅' : '❌'} Count ${test.count} (EN): "${enResult}"`);
    
    if (!arSuccess || !enSuccess) {
      allTestsPassed = false;
      if (!arSuccess) {
        console.log(`      المتوقع (AR): "${test.arExpected}"`);
      }
      if (!enSuccess) {
        console.log(`      Expected (EN): "${test.enExpected}"`);
      }
    }
  });
  
  console.log('');
  
  // التحقق من عدم وجود مفاتيح مكررة
  console.log('🔍 التحقق من عدم وجود مفاتيح مكررة:');
  
  const checkDuplicates = (translations, language) => {
    const keys = Object.keys(translations);
    const duplicates = keys.filter((key, index) => keys.indexOf(key) !== index);
    
    if (duplicates.length > 0) {
      console.log(`   ❌ مفاتيح مكررة في ${language}: ${duplicates.join(', ')}`);
      return false;
    } else {
      console.log(`   ✅ لا توجد مفاتيح مكررة في ${language}`);
      return true;
    }
  };
  
  const arNoDuplicates = checkDuplicates(arTranslations, 'العربية');
  const enNoDuplicates = checkDuplicates(enTranslations, 'الإنجليزية');
  
  if (!arNoDuplicates || !enNoDuplicates) {
    allTestsPassed = false;
  }
  
  console.log('');
  
  // النتيجة النهائية
  if (allTestsPassed) {
    console.log('🎉 ممتاز! جميع اختبارات ترجمات صفحة الخرائط نجحت');
    console.log('✅ المشكلة تم حلها بنجاح');
    console.log('💡 يمكنك الآن زيارة http://localhost:9002/ar/map للتحقق من النتيجة');
  } else {
    console.log('❌ بعض اختبارات ترجمات صفحة الخرائط فشلت');
    console.log('⚠️  يرجى مراجعة الأخطاء أعلاه');
  }
  
  return allTestsPassed;
}

// تشغيل الاختبار
if (require.main === module) {
  testMapTranslations();
}

module.exports = { testMapTranslations };
