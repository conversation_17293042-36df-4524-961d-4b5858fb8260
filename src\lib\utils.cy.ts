import { cn, generateId, debounce, throttle, isValidEmail, isValidPhone } from './utils'

describe('Utils Library', () => {
  describe('cn (className utility)', () => {
    it('يدمج الفئات بشكل صحيح', () => {
      const result = cn('class1', 'class2', 'class3')
      expect(result).to.include('class1')
      expect(result).to.include('class2')
      expect(result).to.include('class3')
    })

    it('يتعامل مع الفئات الشرطية', () => {
      const result = cn('base-class', true && 'conditional-class', false && 'hidden-class')
      expect(result).to.include('base-class')
      expect(result).to.include('conditional-class')
      expect(result).to.not.include('hidden-class')
    })

    it('يزيل الفئات المكررة', () => {
      const result = cn('duplicate', 'other', 'duplicate')
      const classes = result.split(' ')
      const duplicateCount = classes.filter(c => c === 'duplicate').length
      expect(duplicateCount).to.equal(1)
    })
  })

  describe('generateId', () => {
    it('ينشئ معرفات فريدة', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).to.not.equal(id2)
    })

    it('ينشئ معرفات بالطول المحدد', () => {
      const shortId = generateId(5)
      const longId = generateId(20)
      expect(shortId).to.have.length(5)
      expect(longId).to.have.length(20)
    })

    it('ينشئ معرفات تحتوي على أحرف وأرقام فقط', () => {
      const id = generateId(10)
      expect(id).to.match(/^[a-zA-Z0-9]+$/)
    })
  })

  describe('debounce', () => {
    it('يؤخر تنفيذ الدالة', (done) => {
      let called = false
      const debouncedFn = debounce(() => {
        called = true
        expect(called).to.be.true
        done()
      }, 100)

      debouncedFn()
      expect(called).to.be.false
    })

    it('يلغي الاستدعاءات السابقة', (done) => {
      let callCount = 0
      const debouncedFn = debounce(() => {
        callCount++
      }, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      setTimeout(() => {
        expect(callCount).to.equal(1)
        done()
      }, 150)
    })
  })

  describe('throttle', () => {
    it('يحد من معدل تنفيذ الدالة', (done) => {
      let callCount = 0
      const throttledFn = throttle(() => {
        callCount++
      }, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      setTimeout(() => {
        expect(callCount).to.equal(1)
      }, 50)

      setTimeout(() => {
        throttledFn()
        expect(callCount).to.equal(2)
        done()
      }, 150)
    })
  })

  describe('isValidEmail', () => {
    it('يتحقق من صحة البريد الإلكتروني', () => {
      expect(isValidEmail('<EMAIL>')).to.be.true
      expect(isValidEmail('<EMAIL>')).to.be.true
      expect(isValidEmail('invalid-email')).to.be.false
      expect(isValidEmail('test@')).to.be.false
      expect(isValidEmail('@domain.com')).to.be.false
      expect(isValidEmail('')).to.be.false
    })
  })

  describe('isValidPhone', () => {
    it('يتحقق من صحة أرقام الهاتف السعودية', () => {
      expect(isValidPhone('0501234567')).to.be.true
      expect(isValidPhone('966501234567')).to.be.true
      expect(isValidPhone('+966501234567')).to.be.true
      expect(isValidPhone('123456')).to.be.false
      expect(isValidPhone('05012345678901')).to.be.false
      expect(isValidPhone('')).to.be.false
    })
  })
})
