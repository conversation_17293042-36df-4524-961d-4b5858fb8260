import type { Locale } from '@/lib/i18n';
import { getTranslations } from '@/context/locale-context';
import FeaturesTabs from '@/components/features/FeaturesTabs';
import { merchantPlans, customerPlans } from '@/constants/plans';

export default async function FeaturesPage({ params }: { params: { locale: Locale } }) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale);

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-primary mb-4">{t('featuresPageTitle')}</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('featuresPageSubtitle')}
        </p>
      </div>
      <FeaturesTabs
        merchantPlans={merchantPlans}
        customerPlans={customerPlans}
        locale={locale}
      />
    </div>
  );
}
