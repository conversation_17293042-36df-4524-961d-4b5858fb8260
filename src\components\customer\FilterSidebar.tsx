"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Filter, X, Star, MapPin, SlidersHorizontal } from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import type { SearchFilter } from "@/types";

interface FilterSidebarProps {
  filters: SearchFilter;
  onFiltersChange: (filters: SearchFilter) => void;
  onClearFilters: () => void;
  isMobile?: boolean;
  className?: string;
}

interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

export default function FilterSidebar({
  filters,
  onFiltersChange,
  onClearFilters,
  isMobile = false,
  className = ""
}: FilterSidebarProps) {
  const { t } = useLocale();
  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.minPrice || 0,
    filters.maxPrice || 1000
  ]);
  const [isOpen, setIsOpen] = useState(false);

  // Categories with mock counts
  const categories: FilterOption[] = [
    { id: "groceries", label: t('categoryGroceries'), count: 156 },
    { id: "handicrafts", label: t('categoryHandicrafts'), count: 89 },
    { id: "fashion", label: t('categoryFashion'), count: 234 },
    { id: "health-beauty", label: t('categoryHealthBeauty'), count: 67 },
    { id: "electronics", label: t('electronics'), count: 45 },
    { id: "home-garden", label: t('homeAndGarden'), count: 78 },
    { id: "sports", label: t('sportsAndFitness'), count: 34 },
    { id: "books", label: t('booksAndMedia'), count: 23 }
  ];

  // Distance options
  const distanceOptions: FilterOption[] = [
    { id: "1", label: t('oneKm') },
    { id: "5", label: t('fiveKm') },
    { id: "10", label: t('tenKm') },
    { id: "25", label: t('twentyFiveKm') },
    { id: "50", label: t('fiftyKm') }
  ];

  // Rating options
  const ratingOptions = [
    { value: 4, label: t('fourStarsAndUp') },
    { value: 3, label: t('threeStarsAndUp') },
    { value: 2, label: t('twoStarsAndUp') },
    { value: 1, label: t('oneStarAndUp') }
  ];

  // Sort options
  const sortOptions = [
    { value: "relevance", label: t('sortByRelevance') },
    { value: "price_low", label: t('sortByPriceLow') },
    { value: "price_high", label: t('sortByPriceHigh') },
    { value: "rating", label: t('sortByRating') },
    { value: "distance", label: t('sortByDistance') },
    { value: "newest", label: t('sortByNewest') }
  ];

  // Update price range when filters change
  useEffect(() => {
    setPriceRange([filters.minPrice || 0, filters.maxPrice || 1000]);
  }, [filters.minPrice, filters.maxPrice]);

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const currentCategory = filters.category;
    if (checked) {
      onFiltersChange({ ...filters, category: categoryId });
    } else if (currentCategory === categoryId) {
      onFiltersChange({ ...filters, category: undefined });
    }
  };

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange([values[0], values[1]]);
  };

  const handlePriceRangeCommit = (values: number[]) => {
    onFiltersChange({
      ...filters,
      minPrice: values[0],
      maxPrice: values[1]
    });
  };

  const handleRatingChange = (rating: number) => {
    onFiltersChange({
      ...filters,
      rating: filters.rating === rating ? undefined : rating
    });
  };

  const handleDistanceChange = (distance: string) => {
    const radius = parseInt(distance);
    onFiltersChange({
      ...filters,
      location: filters.location ? {
        ...filters.location,
        radius
      } : undefined
    });
  };

  const handleSortChange = (sortBy: string) => {
    onFiltersChange({
      ...filters,
      sortBy: sortBy as SearchFilter['sortBy']
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.rating) count++;
    if (filters.location?.radius) count++;
    return count;
  };

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Sort By */}
      <div>
        <h3 className="font-semibold mb-3 flex items-center">
          <SlidersHorizontal className="w-4 h-4 mr-2" />
          {t('sortBy')}
        </h3>
        <Select value={filters.sortBy || "relevance"} onValueChange={handleSortChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Separator />

      {/* Categories */}
      <div>
        <h3 className="font-semibold mb-3">{t('categories')}</h3>
        <ScrollArea className="h-48">
          <div className="space-y-2">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Checkbox
                    id={category.id}
                    checked={filters.category === category.id}
                    onCheckedChange={(checked) => 
                      handleCategoryChange(category.id, checked as boolean)
                    }
                  />
                  <label 
                    htmlFor={category.id}
                    className="text-sm cursor-pointer"
                  >
                    {category.label}
                  </label>
                </div>
                {category.count && (
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      <Separator />

      {/* Price Range */}
      <div>
        <h3 className="font-semibold mb-3">{t('priceRange')}</h3>
        <div className="space-y-4">
          <Slider
            value={priceRange}
            onValueChange={handlePriceRangeChange}
            onValueCommit={handlePriceRangeCommit}
            max={1000}
            min={0}
            step={10}
            className="w-full"
          />
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{priceRange[0]} {t('sar')}</span>
            <span>{priceRange[1]} {t('sar')}</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* Rating */}
      <div>
        <h3 className="font-semibold mb-3 flex items-center">
          <Star className="w-4 h-4 mr-2" />
          {t('rating')}
        </h3>
        <div className="space-y-2">
          {ratingOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-2 rtl:space-x-reverse">
              <Checkbox
                id={`rating-${option.value}`}
                checked={filters.rating === option.value}
                onCheckedChange={() => handleRatingChange(option.value)}
              />
              <label 
                htmlFor={`rating-${option.value}`}
                className="text-sm cursor-pointer flex items-center"
              >
                <div className="flex items-center mr-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`w-3 h-3 ${
                        i < option.value 
                          ? 'fill-yellow-400 text-yellow-400' 
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Distance */}
      <div>
        <h3 className="font-semibold mb-3 flex items-center">
          <MapPin className="w-4 h-4 mr-2" />
          {t('distance')}
        </h3>
        <div className="space-y-2">
          {distanceOptions.map((option) => (
            <div key={option.id} className="flex items-center space-x-2 rtl:space-x-reverse">
              <Checkbox
                id={`distance-${option.id}`}
                checked={filters.location?.radius === parseInt(option.id)}
                onCheckedChange={() => handleDistanceChange(option.id)}
              />
              <label 
                htmlFor={`distance-${option.id}`}
                className="text-sm cursor-pointer"
              >
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Clear Filters */}
      <Button 
        variant="outline" 
        onClick={onClearFilters}
        className="w-full"
        disabled={getActiveFiltersCount() === 0}
      >
        <X className="w-4 h-4 mr-2" />
        {t('clearFilters')}
      </Button>
    </div>
  );

  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" className={className}>
            <Filter className="w-4 h-4 mr-2" />
            {t('filters')}
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-80">
          <SheetHeader>
            <SheetTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              {t('filters')}
            </SheetTitle>
          </SheetHeader>
          <ScrollArea className="h-full mt-6">
            <FilterContent />
          </ScrollArea>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            {t('filters')}
          </div>
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <FilterContent />
      </CardContent>
    </Card>
  );
}
