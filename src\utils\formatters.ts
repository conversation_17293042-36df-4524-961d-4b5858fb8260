// src/utils/formatters.ts
// دوال التنسيق الموحدة لمشروع مِخْلاة

/**
 * تنسيق العملة بالريال السعودي
 */
export function formatCurrency(
  amount: number, 
  currency: string = 'SAR',
  locale: string = 'ar-SA'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency === 'SAR' ? 'SAR' : 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * تنسيق المسافة (متر/كيلومتر)
 */
export function formatDistance(distance?: number): string {
  if (!distance) return '';
  if (distance < 1) {
    return `${Math.round(distance * 1000)} م`;
  }
  return `${distance.toFixed(1)} كم`;
}

/**
 * الحصول على الأحرف الأولى من اسم المتجر
 */
export function getStoreInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

/**
 * تنسيق التاريخ والوقت
 */
export function formatDateTime(
  date: Date | string,
  locale: string = 'ar-SA',
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options
  };
  
  return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
}

/**
 * تنسيق الأرقام
 */
export function formatNumber(
  number: number,
  locale: string = 'ar-SA',
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(locale, options).format(number);
}

/**
 * اقتطاع النص مع إضافة نقاط
 */
export function truncateText(text: string, maxLength: number = 100): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
export function isValidSaudiPhone(phone: string): boolean {
  const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * تنسيق رقم الهاتف السعودي
 */
export function formatSaudiPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');

  if (cleaned.startsWith('966')) {
    return `+${cleaned}`;
  } else if (cleaned.startsWith('0')) {
    return `+966${cleaned.substring(1)}`;
  } else if (cleaned.length === 9) {
    return `+966${cleaned}`;
  }

  return phone;
}
