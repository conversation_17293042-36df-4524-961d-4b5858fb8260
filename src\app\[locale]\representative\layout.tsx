'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Loader2,
  ShieldAlert,
  LayoutDashboard,
  Package,
  DollarSign,
  User,
  Settings
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import type { UserDocument, RepresentativeDocument } from '@/types';

export default function RepresentativeLayout({
  children,
}: {
  children: ReactNode;
}) {
  const { user, loading, initialLoadingCompleted } = useAuth();
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    const verifyRepresentativeAccess = async () => {
      if (!initialLoadingCompleted || loading) return;

      // إذا كان المستخدم في صفحة pending-approval، لا نحتاج للتحقق
      if (pathname?.includes('/pending-approval')) {
        setIsVerifying(false);
        return;
      }

      // إذا كان المستخدم في صفحة التسجيل، لا نحتاج للتحقق من المصادقة
      if (pathname?.includes('/signup')) {
        setIsVerifying(false);
        return;
      }

      // للصفحات الأخرى، نحتاج للتحقق من تسجيل الدخول
      if (!user) {
        const currentLocale = pathname?.split('/')[1] || 'ar';
        router.push(`/${currentLocale}/login?redirect=/representative`);
        return;
      }

      try {
        // التحقق من نوع المستخدم
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnap = await getDoc(userDocRef);

        if (userDocSnap.exists()) {
          const userData = userDocSnap.data() as UserDocument;

          if (userData.userType !== 'representative') {
            // توجيه المستخدم حسب نوعه
            const currentLocale = pathname?.split('/')[1] || 'ar';
            if (userData.userType === 'merchant') {
              router.push(`/${currentLocale}/merchant/dashboard`);
            } else {
              router.push(`/${currentLocale}/dashboard`);
            }
            return;
          }

          // التحقق من حالة الموافقة على المندوب
          const representativeDocRef = doc(db, 'representatives', user.uid);
          const representativeDocSnap = await getDoc(representativeDocRef);

          if (representativeDocSnap.exists()) {
            const representativeData = representativeDocSnap.data() as RepresentativeDocument;

            // إذا كان المندوب في انتظار الموافقة أو تم رفضه
            const currentLocale = pathname?.split('/')[1] || 'ar';
            if (representativeData.approvalStatus === 'pending' || representativeData.approvalStatus === 'rejected') {
              router.push(`/${currentLocale}/representative/pending-approval`);
              return;
            }

            // إذا تم قبول المندوب لكن الحساب غير مفعل
            if (representativeData.approvalStatus === 'approved' && !representativeData.isActive) {
              router.push(`/${currentLocale}/representative/pending-approval`);
              return;
            }
          } else {
            // لا يوجد مستند مندوب، توجيه لصفحة الانتظار
            const currentLocale = pathname?.split('/')[1] || 'ar';
            router.push(`/${currentLocale}/representative/pending-approval`);
            return;
          }
        } else {
          const currentLocale = pathname?.split('/')[1] || 'ar';
          router.push(`/${currentLocale}/profile`);
          return;
        }
      } catch (error) {
        console.error('Error verifying representative access:', error);
        const currentLocale = pathname?.split('/')[1] || 'ar';
        router.push(`/${currentLocale}/login`);
        return;
      }

      setIsVerifying(false);
    };

    verifyRepresentativeAccess();
  }, [user, loading, initialLoadingCompleted, router, pathname]);

  // عرض شاشة التحميل أثناء التحقق
  if (loading || !initialLoadingCompleted || isVerifying) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('representative.dashboard.loading')}</p>
      </div>
    );
  }

  // السماح بالوصول لصفحة التسجيل حتى بدون تسجيل دخول
  if (pathname?.includes('/signup')) {
    return (
      <div className="py-8">
        {children}
      </div>
    );
  }

  if (!user) {
    // This case should be handled by useEffect redirect, but as a fallback:
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-8 text-center">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h2 className="text-2xl font-semibold mb-2 text-destructive">{t('accessDenied')}</h2>
        <p className="text-muted-foreground mb-6">{t('representative.dashboard.mustBeLoggedIn')}</p>
        <Button asChild>
          <Link href={`/ar/login?redirect=${encodeURIComponent(`/ar/representative/dashboard`)}`}>
            {t('auth.login')}
          </Link>
        </Button>
      </div>
    );
  }

  // إذا وصل المستخدم هنا، فهو مندوب معتمد ومفعل
  const currentLocale = pathname?.split('/')[1] || 'ar';

  const navigationItems = [
    {
      href: `/${currentLocale}/representative/dashboard`,
      label: t('representative.nav.dashboard'),
      icon: LayoutDashboard,
      active: pathname === `/${currentLocale}/representative/dashboard`
    },
    {
      href: `/${currentLocale}/representative/orders`,
      label: t('representative.nav.orders'),
      icon: Package,
      active: pathname === `/${currentLocale}/representative/orders`
    },
    {
      href: `/${currentLocale}/representative/earnings`,
      label: t('representative.nav.earnings'),
      icon: DollarSign,
      active: pathname === `/${currentLocale}/representative/earnings`
    },
    {
      href: `/${currentLocale}/representative/profile`,
      label: t('representative.nav.profile'),
      icon: User,
      active: pathname === `/${currentLocale}/representative/profile`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        {/* شريط التنقل */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <nav className="flex flex-wrap gap-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors",
                      item.active
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    {item.label}
                  </Link>
                );
              })}
            </nav>
          </CardContent>
        </Card>

        {/* المحتوى */}
        <div className="bg-white rounded-lg shadow-sm">
          {children}
        </div>
      </div>
    </div>
  );
}
