#!/usr/bin/env node

/**
 * سكريبت إصلاح التكرارات في ملفات الترجمة
 * يقوم بإزالة المفاتيح المكررة على مستويات مختلفة من البنية الهرمية
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'en.json');

/**
 * قراءة ملف JSON بشكل آمن
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * كتابة ملف JSON بشكل آمن
 */
function writeJsonFile(filePath, data) {
  try {
    const content = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في كتابة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * إنشاء نسخة احتياطية
 */
function createBackup(filePath) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في إنشاء نسخة احتياطية:`, error.message);
    return false;
  }
}

/**
 * إصلاح التكرارات المعروفة في الملف العربي
 */
function fixArabicDuplicates(data) {
  const fixes = [];
  
  // إصلاح تكرارات representative
  if (data.representative && data.representative.representative) {
    // دمج البيانات
    Object.assign(data.representative, data.representative.representative);
    delete data.representative.representative;
    fixes.push('representative.representative');
  }
  
  // إصلاح تكرارات profile
  if (data.profile && data.representative && data.representative.profile) {
    // الاحتفاظ بـ profile الرئيسي وحذف المكرر
    delete data.representative.profile;
    fixes.push('representative.profile');
  }
  
  // إصلاح تكرارات dashboard
  if (data.dashboard && data.representative && data.representative.dashboard) {
    delete data.representative.dashboard;
    fixes.push('representative.dashboard');
  }
  
  // إصلاح تكرارات orders
  if (data.orders && data.representative && data.representative.orders) {
    delete data.representative.orders;
    fixes.push('representative.orders');
  }
  
  // إصلاح تكرارات earnings
  if (data.earnings && data.representative && data.representative.earnings) {
    delete data.representative.earnings;
    fixes.push('representative.earnings');
  }
  
  // إصلاح تكرارات في مستوى أعمق
  if (data.representative && data.representative.nav) {
    // نقل البيانات من nav إلى المستوى الأعلى
    const navData = data.representative.nav;
    Object.keys(navData).forEach(key => {
      if (!data.representative[key]) {
        data.representative[key] = navData[key];
      }
    });
    delete data.representative.nav;
    fixes.push('representative.nav');
  }
  
  return fixes;
}

/**
 * إصلاح التكرارات المعروفة في الملف الإنجليزي
 */
function fixEnglishDuplicates(data) {
  const fixes = [];

  // إصلاح تكرارات representative
  if (data.representative && data.representative.representative) {
    Object.assign(data.representative, data.representative.representative);
    delete data.representative.representative;
    fixes.push('representative.representative');
  }

  // إصلاح تكرارات profile
  if (data.profile && data.representative && data.representative.profile) {
    delete data.representative.profile;
    fixes.push('representative.profile');
  }

  // إصلاح تكرارات dashboard
  if (data.dashboard && data.representative && data.representative.dashboard) {
    delete data.representative.dashboard;
    fixes.push('representative.dashboard');
  }

  // إصلاح تكرارات orders
  if (data.orders && data.representative && data.representative.orders) {
    delete data.representative.orders;
    fixes.push('representative.orders');
  }

  // إصلاح تكرارات earnings
  if (data.earnings && data.representative && data.representative.earnings) {
    delete data.representative.earnings;
    fixes.push('representative.earnings');
  }

  // إصلاح التكرارات الأخرى
  const duplicateKeys = ['loading', 'active', 'inactive', 'available', 'unavailable',
                        'totalDeliveries', 'monthlyEarnings', 'totalEarnings',
                        'averageRating', 'reviews', 'minutes'];

  duplicateKeys.forEach(key => {
    if (data[key] && data.representative && data.representative[key]) {
      delete data.representative[key];
      fixes.push(`representative.${key}`);
    }
  });

  return fixes;
}

/**
 * معالجة ملف ترجمة واحد
 */
function processTranslationFile(filePath, isArabic = false) {
  console.log(`🔧 معالجة الملف: ${path.basename(filePath)}`);
  
  // إنشاء نسخة احتياطية
  if (!createBackup(filePath)) {
    return { success: false, fixesApplied: 0 };
  }
  
  // قراءة البيانات
  const data = readJsonFile(filePath);
  if (!data) {
    return { success: false, fixesApplied: 0 };
  }
  
  // تطبيق الإصلاحات
  const fixes = isArabic ? fixArabicDuplicates(data) : fixEnglishDuplicates(data);
  
  if (fixes.length > 0) {
    // كتابة البيانات المصلحة
    if (writeJsonFile(filePath, data)) {
      console.log(`   ✅ تم إصلاح ${fixes.length} تكرار`);
      console.log(`   📝 الإصلاحات المطبقة: ${fixes.join(', ')}`);
      return { success: true, fixesApplied: fixes.length, fixes };
    } else {
      return { success: false, fixesApplied: 0 };
    }
  } else {
    console.log(`   ✅ لا توجد تكرارات للإصلاح`);
    return { success: true, fixesApplied: 0 };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء إصلاح تكرارات ملفات الترجمة...\n');
  
  let totalSuccess = 0;
  let totalFixes = 0;
  
  // معالجة الملف العربي
  console.log('📝 معالجة الملف العربي:');
  const arResult = processTranslationFile(AR_TRANSLATIONS_PATH, true);
  if (arResult.success) {
    totalSuccess++;
    totalFixes += arResult.fixesApplied;
  }
  
  console.log('');
  
  // معالجة الملف الإنجليزي
  console.log('📝 معالجة الملف الإنجليزي:');
  const enResult = processTranslationFile(EN_TRANSLATIONS_PATH, false);
  if (enResult.success) {
    totalSuccess++;
    totalFixes += enResult.fixesApplied;
  }
  
  console.log('');
  
  if (totalSuccess === 2) {
    console.log('🎉 تم إصلاح تكرارات الترجمة بنجاح!');
    console.log(`📊 إجمالي الإصلاحات المطبقة: ${totalFixes}`);
    console.log('💡 تم إنشاء نسخ احتياطية من الملفات');
    
    // تشغيل التحقق التلقائي
    console.log('\n🔍 تشغيل التحقق التلقائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في معالجة بعض الملفات');
    console.log(`📊 الملفات المعالجة بنجاح: ${totalSuccess}/2`);
    console.log('🔄 يمكنك استعادة النسخ الاحتياطية إذا لزم الأمر');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  fixArabicDuplicates,
  fixEnglishDuplicates,
  processTranslationFile
};
