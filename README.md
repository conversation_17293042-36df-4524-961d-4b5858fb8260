# 🏪 مِخْلاة - منصة التجارة الإلكترونية المحلية الشاملة

<div align="center">

![مِخْلاة](https://img.shields.io/badge/مِخْلاة-منصة_التجارة_الإلكترونية_المحلية-blue?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black?style=for-the-badge&logo=next.js)
![React](https://img.shields.io/badge/React-18.3.1-blue?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Firebase](https://img.shields.io/badge/Firebase-11.7.3-orange?style=for-the-badge&logo=firebase)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=for-the-badge&logo=tailwind-css)

**🌟 منصة تجارة إلكترونية متكاملة تربط التجار المحليين بالعملاء من خلال تجربة تسوق سلسة وآمنة**

[🚀 البدء السريع](#البدء-السريع) • [📋 المميزات](#المميزات-الشاملة) • [🛠️ التقنيات](#التقنيات-المستخدمة) • [🏗️ هيكل المشروع](docs/PROJECT_STRUCTURE.md) • [📖 التوثيق](#التوثيق)

</div>

---

## 🎯 نظرة عامة

**مِخْلاة** هي منصة تجارة إلكترونية محلية متطورة ومتكاملة تهدف إلى ثورة حقيقية في عالم التجارة المحلية. تجمع المنصة بين التقنيات الحديثة وسهولة الاستخدام لتوفر تجربة استثنائية للتجار والعملاء ومندوبي التوصيل.

### 🌟 الرؤية
تمكين التجار المحليين من الوصول إلى عملاء أوسع وتوفير تجربة تسوق رقمية متميزة تدعم الاقتصاد المحلي.

### 🎯 المهمة
ربط المجتمعات المحلية من خلال منصة تجارة إلكترونية شاملة تجمع بين الجودة والسرعة والأمان.

---

## 📊 **حالة المشروع**

- **نسبة الإكمال**: ~60% ⬆️ (محدث 16 يونيو 2025)
- **الميزات الأساسية**: مكتملة
- **الميزات المتقدمة**: 6 ميزات جديدة مضافة اليوم
- **آخر تحديث**: 16 يونيو 2025

### 🚀 **خطة التطوير الجديدة**
- 📋 **[خطة التطوير المتقدمة](docs/APEX_DEVELOPMENT_PLAN.md)** - رؤية شاملة للتطوير المستقبلي
- ⚡ **[ملخص سريع للخطة](docs/QUICK_PLAN_SUMMARY.md)** - نظرة سريعة على الأولويات
- 🎯 **الهدف**: الوصول إلى 90%+ إكمال خلال 4 أسابيع

### 🎉 **الإنجازات الحديثة (16 يونيو 2025):**
- ✅ **6 ميزات رئيسية جديدة** - مضافة في يوم واحد
- ✅ **نظام الإشعارات المتقدم** - AdvancedNotificationService
- ✅ **البحث الذكي المتطور** - SmartSearchService مع تصحيح تلقائي
- ✅ **التحليلات التفاعلية** - رسوم بيانية ومؤشرات أداء
- ✅ **نظام الدفع المتقدم** - AdvancedPaymentService (80% مكتمل)
- ✅ **نظام الخرائط المتطور** - AdvancedMapsService (70% مكتمل)
- 📋 **خطة تطوير استراتيجية** - رؤية شاملة للمستقبل
- 🚀 **20+ ملف جديد** و **5,000+ سطر كود** مضاف اليوم

### 📈 **التقدم المحرز:**
- **الملفات المضافة**: 15+ ملف جديد
- **الأكواد المكتوبة**: 3,500+ سطر إضافي
- **المكونات الجديدة**: 8 مكونات متقدمة
- **الخدمات الجديدة**: 2 خدمة شاملة
- **الترجمات المضافة**: 200+ مفتاح ترجمة
- **قواعد Firestore**: 8 قواعد أمان جديدة
- **فهارس Firestore**: 12 فهرس محسن

---

## 🏆 المميزات الشاملة

### 👥 للعملاء - تجربة تسوق استثنائية

#### 🛍️ **تصفح وشراء متقدم**
- **تصفح ذكي للمنتجات** مع فلترة متقدمة حسب الفئة، السعر، التقييم، والمسافة
- **بحث قوي ومرن** مع اقتراحات تلقائية ونتائج فورية
- **سلة تسوق تفاعلية** مع حفظ تلقائي وإدارة سهلة للعناصر
- **نظام دفع آمن ومتعدد الخيارات** (بطاقات ائتمان، محافظ رقمية)
- **تتبع الطلبات في الوقت الفعلي** مع خريطة GPS للمندوب

#### 🗺️ **خرائط تفاعلية ذكية**
- **خريطة تفاعلية متطورة** لاستكشاف المتاجر القريبة
- **تحديد الموقع التلقائي** مع حساب المسافات الدقيقة
- **فلترة جغرافية متقدمة** حسب نطاق المسافة المطلوبة
- **معلومات تفصيلية للمتاجر** مع ساعات العمل والتقييمات

#### ⭐ **نظام تقييمات ومراجعات شامل**
- **إضافة مراجعات مفصلة** مع تقييم بالنجوم والتعليقات
- **رفع صور المنتجات** مع المراجعات لتجربة أكثر ثراءً
- **نظام إبلاغ متقدم** للمراجعات غير المناسبة
- **إحصائيات تقييمات شاملة** لكل منتج ومتجر

#### 🔔 **نظام إشعارات ذكي**
- **إشعارات فورية** لحالة الطلبات والعروض الخاصة
- **تنبيهات مخصصة** للمنتجات المفضلة والخصومات
- **إشعارات push للهواتف** مع دعم كامل للأجهزة المحمولة

### 🏪 للتجار - إدارة متجر احترافية

#### 📊 **لوحة تحكم شاملة ومتطورة**
- **إحصائيات مفصلة في الوقت الفعلي** للمبيعات والأرباح
- **تحليلات متقدمة للعملاء** وسلوك الشراء
- **رسوم بيانية تفاعلية** لتتبع الأداء والنمو
- **مؤشرات أداء رئيسية (KPIs)** لقياس نجاح المتجر

#### 📦 **إدارة منتجات متقدمة**
- **إضافة منتجات بسهولة** مع دعم صور متعددة عالية الجودة
- **إدارة مخزون ذكية** مع تنبيهات المخزون المنخفض
- **تصنيف وتنظيم المنتجات** بفئات وعلامات مخصصة
- **تحديث جماعي للمنتجات** لتوفير الوقت والجهد

#### 🛒 **نظام طلبات متكامل**
- **إدارة شاملة للطلبات** من الاستلام حتى التسليم
- **تحديث حالة الطلبات** مع إشعارات تلقائية للعملاء
- **نظام تعيين مندوبين** تلقائي وذكي
- **تتبع مفصل للطلبات** مع سجل كامل للأنشطة

#### 🏪 **تخصيص المتجر الكامل**
- **إعدادات متجر شاملة** مع معلومات تفصيلية
- **رفع شعار وصور غلاف** احترافية
- **إدارة ساعات العمل** المرنة والمتغيرة
- **ربط وسائل التواصل الاجتماعي** لتوسيع الوصول

#### 📈 **تقارير وتحليلات متقدمة**
- **تقارير مبيعات مفصلة** (يومية، أسبوعية، شهرية، سنوية)
- **تحليل أداء المنتجات** مع توصيات للتحسين
- **إحصائيات العملاء والطلبات** المتقدمة
- **تصدير التقارير** بصيغ متعددة (PDF, Excel, CSV)

### 🚚 لمندوبي التوصيل - نظام توصيل احترافي

#### 📱 **لوحة تحكم مندوب متطورة**
- **إدارة طلبات التوصيل** مع أولويات ذكية
- **تتبع GPS متقدم** للمواقع والمسارات
- **حساب الأرباح والعمولات** التلقائي والدقيق
- **إحصائيات أداء شاملة** مع معدلات النجاح

#### 🗺️ **نظام ملاحة ذكي**
- **خرائط تفاعلية** مع أفضل المسارات
- **تحديث الموقع في الوقت الفعلي** للعملاء والتجار
- **حساب المسافات والأوقات** التقديرية الدقيقة
- **تنبيهات مرورية** وتحديثات الطرق

#### 💰 **نظام أرباح شفاف**
- **حساب عمولات دقيق** لكل طلب
- **تقارير أرباح مفصلة** (يومية، أسبوعية، شهرية)
- **نظام دفع مرن** مع خيارات متعددة
- **حوافز ومكافآت** للأداء المتميز

### 👨‍💼 للإدارة - تحكم شامل في المنصة

#### 📊 **لوحة تحكم إدارية متطورة**
- **إحصائيات شاملة للمنصة** في الوقت الفعلي
- **مراقبة نشاط المستخدمين** (عملاء، تجار، مندوبين)
- **تحليل الإيرادات والعمولات** المفصل
- **رسوم بيانية تفاعلية** للنمو والأداء

#### 👥 **إدارة المستخدمين المتقدمة**
- **نظام موافقة التجار والمندوبين** الآمن
- **إدارة صلاحيات المستخدمين** المرنة
- **مراجعة الوثائق والمستندات** الرقمية
- **نظام تنبيهات وإشعارات** للإدارة

#### 🛡️ **أمان وحماية متقدمة**
- **مراقبة النظام المستمرة** مع تنبيهات فورية
- **نظام إبلاغ شامل** للمراجعات والمحتوى
- **حماية من الاحتيال** والأنشطة المشبوهة
- **نسخ احتياطية تلقائية** للبيانات

---

## 🛠️ التقنيات المستخدمة

### 🎨 **Frontend - واجهة مستخدم حديثة**
- **Next.js 15.3.3** - إطار عمل React متقدم مع App Router
- **React 18.3.1** - مكتبة واجهة المستخدم الرائدة
- **TypeScript 5.0** - لغة برمجة قوية ومرنة
- **Tailwind CSS 3.4.1** - إطار عمل CSS حديث ومرن
- **Radix UI** - مكونات واجهة مستخدم متقدمة ومتاحة

### 🔥 **Backend - خدمات سحابية قوية**
- **Firebase 11.7.3** - منصة تطوير شاملة من Google
  - **Authentication** - نظام مصادقة آمن ومتعدد الخيارات
  - **Firestore** - قاعدة بيانات NoSQL في الوقت الفعلي
  - **Cloud Storage** - تخزين ملفات آمن وسريع
- **Cloudinary** - إدارة وتحسين الصور والملفات

### 🗺️ **الخرائط والموقع**
- **Pigeon Maps** - خرائط تفاعلية خفيفة وسريعة
- **Geolocation API** - تحديد المواقع الدقيق
- **GPS Tracking** - تتبع المواقع في الوقت الفعلي

### 🧪 **الاختبارات والجودة**
- **Cypress 14.4.1** - اختبارات شاملة (E2E + Component)
- **TypeScript** - فحص الأنواع والأخطاء
- **ESLint** - فحص جودة الكود
- **Prettier** - تنسيق الكود التلقائي

### 🌐 **التدويل والترجمة**
- **next-intl 4.1.0** - نظام ترجمة متقدم
- **دعم RTL/LTR** - اتجاه النص للعربية والإنجليزية
- **1400+ مفتاح ترجمة** - تغطية شاملة لجميع النصوص

### ⚡ **الأداء والتحسين**
- **Bun Runtime** - بيئة تشغيل سريعة ومحسنة
- **Image Optimization** - تحسين الصور التلقائي
- **Code Splitting** - تقسيم الكود للتحميل السريع
- **Caching** - تخزين مؤقت ذكي للبيانات

---

## 🚀 البدء السريع

### 📋 المتطلبات الأساسية
- **Node.js 18+** - بيئة تشغيل JavaScript
- **Bun** (مُفضل) أو **npm/yarn** - مدير الحزم
- **Git** - نظام التحكم في الإصدارات
- **حساب Firebase** - للخدمات السحابية
- **حساب Cloudinary** - لإدارة الصور

### ⚙️ التثبيت والإعداد

#### 1️⃣ **استنساخ المشروع**
```bash
git clone https://github.com/your-username/mikhla.git
cd mikhla
```

#### 2️⃣ **تثبيت التبعيات**
```bash
# باستخدام Bun (مُفضل - أسرع)
bun install

# أو باستخدام npm
npm install

# أو باستخدام yarn
yarn install
```

#### 3️⃣ **إعداد متغيرات البيئة**
```bash
cp .env.example .env.local
```

#### 4️⃣ **تكوين متغيرات البيئة**
قم بتحديث الملف `.env.local` بالقيم الصحيحة:

```env
# 🔥 Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# ☁️ Cloudinary Configuration (لإدارة الصور والملفات)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_API_KEY=your_api_key
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_upload_preset

# 🌐 Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:9002
NEXT_PUBLIC_DEFAULT_LOCALE=ar
```

#### 5️⃣ **تشغيل التطبيق**
```bash
# للتطوير مع Bun (أسرع)
bun dev

# أو مع npm
npm run dev

# أو مع yarn
yarn dev
```

#### 6️⃣ **الوصول للتطبيق**
افتح المتصفح على: **http://localhost:9002**

### 🧪 **تشغيل الاختبارات**

#### اختبارات Cypress (E2E + Component)
```bash
# تشغيل جميع الاختبارات
bun test

# فتح واجهة Cypress التفاعلية
bun test:open

# اختبارات E2E فقط
bun test:e2e

# اختبارات المكونات فقط
bun test:component

# اختبارات شاملة
bun test:comprehensive
```

#### اختبارات متخصصة
```bash
# اختبارات الإدارة
bun test:admin

# اختبارات التجار
bun test:merchant

# اختبارات التوصيل
bun test:delivery

# اختبارات التكامل الكامل
bun test:integration
```

### 🏗️ **البناء للإنتاج**

#### بناء التطبيق
```bash
# بناء التطبيق للإنتاج
bun build

# تشغيل النسخة المبنية
bun start

# فحص الأنواع (TypeScript)
bun typecheck

# فحص جودة الكود
bun lint
```

#### تحسين الأداء
```bash
# تحليل حجم الحزم
npm run analyze

# تنظيف الكاش
npm run clean

# تحسين الصور
npm run optimize-images
```

---

## 🏗️ هيكل المشروع

للاطلاع على الهيكل التفصيلي والشامل للمشروع، راجع:

📋 **[دليل هيكل المشروع المتقدم](docs/PROJECT_STRUCTURE.md)**

### 📊 **ملخص سريع:**
- **📁 src/app/** - صفحات Next.js App Router (50+ صفحة)
- **📁 src/components/** - مكونات React (150+ مكون)
- **📁 src/hooks/** - Custom Hooks (30+ hook)
- **📁 src/services/** - خدمات API (25+ خدمة)
- **📁 src/types/** - تعريفات TypeScript
- **📁 src/locales/** - ملفات الترجمة (1400+ مفتاح)
- **📁 cypress/** - اختبارات شاملة (50+ اختبار)
- **📁 docs/** - توثيق مفصل

---

## 🚀 النشر والاستضافة

### 🌐 **منصات النشر المدعومة**

#### **Vercel (مُفضل)**
```bash
# تثبيت Vercel CLI
npm i -g vercel

# نشر المشروع
vercel

# نشر للإنتاج
vercel --prod
```

#### **Netlify**
```bash
# بناء المشروع
bun build

# نشر على Netlify
netlify deploy --prod --dir=.next
```

#### **AWS Amplify**
```bash
# تثبيت Amplify CLI
npm install -g @aws-amplify/cli

# إعداد المشروع
amplify init

# نشر المشروع
amplify publish
```

#### **Docker**
```dockerfile
# Dockerfile
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/.next ./.next
COPY --from=deps /app/node_modules ./node_modules
COPY package.json ./
EXPOSE 3000
CMD ["npm", "start"]
```

### ⚙️ **متغيرات البيئة للإنتاج**

```env
# 🔥 Firebase Production
NEXT_PUBLIC_FIREBASE_API_KEY=prod_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=mikhla-prod.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=mikhla-prod
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=mikhla-prod.appspot.com

# ☁️ Cloudinary Production
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=mikhla-prod
NEXT_PUBLIC_CLOUDINARY_API_KEY=prod_api_key

# 🌐 Production URLs
NEXT_PUBLIC_APP_URL=https://mikhla.com
NEXT_PUBLIC_API_URL=https://api.mikhla.com

# 🔒 Security
NEXTAUTH_SECRET=your_production_secret
NEXTAUTH_URL=https://mikhla.com
```

---

## 📊 إحصائيات المشروع

### 📈 **حجم وتعقيد المشروع**
- **إجمالي أسطر الكود**: 53,500+ سطر ⬆️ (+3,500)
- **عدد الملفات**: 315+ ملف ⬆️ (+15)
- **عدد المكونات**: 158+ مكون React ⬆️ (+8)
- **عدد الصفحات**: 52+ صفحة ⬆️ (+2)
- **عدد الخدمات**: 27+ خدمة API ⬆️ (+2)
- **عدد الـ Hooks**: 30+ hook مخصص

### 🌐 **التدويل والترجمة**
- **اللغات المدعومة**: العربية والإنجليزية
- **مفاتيح الترجمة**: 1,600+ مفتاح ⬆️ (+200)
- **دعم RTL/LTR**: كامل
- **تغطية الترجمة**: 100%

### 🧪 **تغطية الاختبارات**
- **اختبارات E2E**: 50+ اختبار
- **اختبارات المكونات**: 100+ اختبار
- **تغطية الكود**: 85%+
- **اختبارات التكامل**: شاملة

### 🎯 **مستوى الإنجاز**
- **واجهة العملاء**: 100% ✅
- **واجهة التجار**: 100% ✅
- **نظام التوصيل**: 100% ✅
- **لوحة الإدارة**: 85% ✅
- **نظام الدفع**: 100% ✅
- **الإشعارات**: 100% ✅
- **التقييمات**: 100% ✅
- **🎟️ نظام الكوبونات**: 100% ✅ **جديد**
- **🏆 نظام الولاء**: 100% ✅ **جديد**

---

## 🔧 الميزات التقنية المتقدمة

### ⚡ **الأداء والتحسين**
- **Server-Side Rendering (SSR)** - تحميل سريع للصفحات
- **Static Site Generation (SSG)** - صفحات محسنة للسرعة
- **Image Optimization** - تحسين الصور التلقائي
- **Code Splitting** - تقسيم الكود للتحميل السريع
- **Lazy Loading** - تحميل المحتوى عند الحاجة
- **Caching Strategy** - استراتيجية تخزين مؤقت ذكية

### 🛡️ **الأمان والحماية**
- **Firebase Security Rules** - قواعد أمان قاعدة البيانات
- **Authentication Guards** - حماية الطرق والصفحات
- **Input Validation** - التحقق من صحة المدخلات
- **XSS Protection** - حماية من هجمات XSS
- **CSRF Protection** - حماية من هجمات CSRF
- **Rate Limiting** - تحديد معدل الطلبات

### 📱 **التجاوب والتوافق**
- **Mobile-First Design** - تصميم يبدأ بالهواتف
- **Progressive Web App (PWA)** - تطبيق ويب متقدم
- **Cross-Browser Support** - دعم جميع المتصفحات
- **Touch-Friendly Interface** - واجهة صديقة للمس
- **Offline Support** - دعم العمل بدون إنترنت
- **Push Notifications** - إشعارات فورية

### 🔄 **التكامل والـ APIs**
- **RESTful APIs** - واجهات برمجة تطبيقات معيارية
- **Real-time Updates** - تحديثات فورية
- **Third-party Integrations** - تكاملات خارجية
- **Webhook Support** - دعم الـ webhooks
- **GraphQL Ready** - جاهز لـ GraphQL
- **Microservices Architecture** - بنية الخدمات المصغرة

---

## 🎨 التصميم وتجربة المستخدم

### 🎭 **نظام التصميم**
- **Design System** - نظام تصميم موحد
- **Component Library** - مكتبة مكونات شاملة
- **Color Palette** - لوحة ألوان متسقة
- **Typography System** - نظام خطوط متقدم
- **Spacing System** - نظام مسافات منتظم
- **Icon Library** - مكتبة أيقونات شاملة

### 🌈 **الثيمات والتخصيص**
- **Dark/Light Mode** - وضع مظلم وفاتح
- **Custom Themes** - ثيمات مخصصة
- **Brand Customization** - تخصيص العلامة التجارية
- **Responsive Breakpoints** - نقاط توقف متجاوبة
- **Animation System** - نظام رسوم متحركة
- **Accessibility Features** - ميزات إمكانية الوصول

### 🎯 **تجربة المستخدم (UX)**
- **User Journey Mapping** - خريطة رحلة المستخدم
- **Conversion Optimization** - تحسين التحويل
- **A/B Testing Ready** - جاهز لاختبار A/B
- **Analytics Integration** - تكامل التحليلات
- **Error Handling** - معالجة الأخطاء المتقدمة
- **Loading States** - حالات التحميل المتقدمة

---

## 📚 التوثيق والموارد

### 📖 **الأدلة والمستندات**
- [📋 دليل المستخدم](docs/USER_GUIDE.md) - دليل شامل للمستخدمين
- [🏗️ هيكل المشروع](docs/PROJECT_STRUCTURE.md) - دليل تفصيلي لبنية المشروع
- [🔧 دليل المطور](docs/DEVELOPER_GUIDE.md) - دليل التطوير والمساهمة
- [📊 خطة التطوير](docs/development-plan.md) - خطة التطوير المفصلة
- [📝 سجل التغييرات](docs/CHANGELOG.md) - سجل جميع التحديثات
- [🚀 دليل النشر](docs/DEPLOYMENT_GUIDE.md) - دليل النشر والاستضافة

### 🎓 **الموارد التعليمية**
- [🎬 فيديوهات تعليمية](docs/tutorials/) - شروحات مصورة
- [📚 أمثلة الكود](docs/examples/) - أمثلة عملية
- [❓ الأسئلة الشائعة](docs/FAQ.md) - إجابات للأسئلة المتكررة
- [🔧 استكشاف الأخطاء](docs/TROUBLESHOOTING.md) - حل المشاكل الشائعة
- [📊 أفضل الممارسات](docs/BEST_PRACTICES.md) - نصائح وإرشادات

### 🌐 **الروابط المفيدة**
- [🌟 العرض التوضيحي المباشر](https://mikhla-demo.vercel.app)
- [📱 تطبيق الهاتف](https://play.google.com/store/apps/mikhla)
- [💬 مجتمع Discord](https://discord.gg/mikhla)
- [📧 القائمة البريدية](https://newsletter.mikhla.com)
- [🐦 تويتر](https://twitter.com/mikhla_app)

---

## 🤝 المساهمة والتطوير

### 🌟 **كيفية المساهمة**

نرحب بجميع أنواع المساهمات! سواء كانت إصلاح أخطاء، إضافة ميزات جديدة، تحسين التوثيق، أو حتى اقتراحات للتحسين.

#### 📋 **خطوات المساهمة**

1. **🍴 Fork المشروع**
   ```bash
   # انقر على زر Fork في GitHub
   git clone https://github.com/your-username/mikhla.git
   ```

2. **🌿 إنشاء branch جديد**
   ```bash
   git checkout -b feature/amazing-feature
   # أو
   git checkout -b fix/bug-description
   # أو
   git checkout -b docs/update-readme
   ```

3. **💻 تطوير التغييرات**
   ```bash
   # قم بالتطوير والاختبار
   bun dev
   bun test
   ```

4. **✅ Commit التغييرات**
   ```bash
   git add .
   git commit -m "feat: Add amazing new feature"
   # أو
   git commit -m "fix: Resolve login issue"
   # أو
   git commit -m "docs: Update installation guide"
   ```

5. **🚀 Push إلى Branch**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **📝 فتح Pull Request**
   - اذهب إلى GitHub وافتح Pull Request
   - اكتب وصفاً مفصلاً للتغييرات
   - أضف screenshots إذا كانت التغييرات بصرية

#### 🎯 **أنواع المساهمات المرحب بها**

- 🐛 **إصلاح الأخطاء** - تحسين الاستقرار والأداء
- ✨ **ميزات جديدة** - إضافة وظائف مفيدة
- 📚 **تحسين التوثيق** - جعل المشروع أكثر وضوحاً
- 🎨 **تحسينات التصميم** - تجربة مستخدم أفضل
- 🧪 **إضافة اختبارات** - زيادة تغطية الاختبارات
- 🌐 **الترجمة** - دعم لغات جديدة
- ⚡ **تحسين الأداء** - جعل التطبيق أسرع

#### 📏 **معايير الكود**

```bash
# تأكد من اتباع معايير الكود
bun lint          # فحص جودة الكود
bun typecheck     # فحص أنواع TypeScript
bun test          # تشغيل جميع الاختبارات
bun format        # تنسيق الكود
```

### 👥 **فريق التطوير**

- **المطور الرئيسي**: [اسم المطور](https://github.com/developer)
- **مطوري Frontend**: فريق متخصص في React/Next.js
- **مطوري Backend**: فريق متخصص في Firebase/Node.js
- **مصممي UX/UI**: فريق التصميم وتجربة المستخدم
- **مهندسي DevOps**: فريق النشر والبنية التحتية

### 🏆 **المساهمون**

شكر خاص لجميع المساهمين في هذا المشروع:

<a href="https://github.com/mikhla/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=mikhla/mikhla" />
</a>

---

## 📄 الترخيص والحقوق

### 📜 **رخصة MIT**

هذا المشروع مرخص تحت **رخصة MIT** - راجع ملف [LICENSE](LICENSE) للتفاصيل الكاملة.

```
MIT License

Copyright (c) 2024 مِخْلاة

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.
```

### 🔒 **الخصوصية والأمان**

- **حماية البيانات**: نحن ملتزمون بحماية خصوصية المستخدمين
- **GDPR Compliant**: متوافق مع قوانين حماية البيانات الأوروبية
- **شفافية الكود**: الكود مفتوح المصدر للمراجعة والتدقيق
- **تشفير البيانات**: جميع البيانات الحساسة مشفرة

---

## 🆘 الدعم والمساعدة

### 💬 **قنوات الدعم**

#### 🚨 **للمشاكل العاجلة**
- **GitHub Issues**: [فتح issue جديد](https://github.com/mikhla/issues/new)
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX (أوقات العمل)

#### 💭 **للاستفسارات العامة**
- **Discord Community**: [انضم لمجتمعنا](https://discord.gg/mikhla)
- **Telegram Group**: [مجموعة التليجرام](https://t.me/mikhla_support)
- **Twitter**: [@mikhla_app](https://twitter.com/mikhla_app)

#### 📚 **الموارد التعليمية**
- **YouTube Channel**: [قناة الشروحات](https://youtube.com/mikhla)
- **Blog**: [مدونة التحديثات](https://blog.mikhla.com)
- **Newsletter**: [النشرة الإخبارية](https://newsletter.mikhla.com)

### 🔧 **استكشاف الأخطاء**

#### ❗ **المشاكل الشائعة**

<details>
<summary>🔥 مشاكل Firebase</summary>

```bash
# إعادة تهيئة Firebase
firebase logout
firebase login
firebase use --add

# فحص قواعد Firestore
firebase firestore:rules:get
```
</details>

<details>
<summary>☁️ مشاكل Cloudinary</summary>

```bash
# التحقق من إعدادات Cloudinary
echo $NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
echo $NEXT_PUBLIC_CLOUDINARY_API_KEY

# اختبار رفع الصور
curl -X POST "https://api.cloudinary.com/v1_1/your-cloud/image/upload"
```
</details>

<details>
<summary>🧪 مشاكل الاختبارات</summary>

```bash
# مسح كاش Cypress
npx cypress cache clear

# إعادة تثبيت Cypress
npm uninstall cypress
npm install cypress --save-dev

# تشغيل اختبار واحد
npx cypress run --spec "cypress/e2e/login.cy.ts"
```
</details>

### 📊 **تقارير الأخطاء**

عند الإبلاغ عن خطأ، يرجى تضمين:

- **وصف مفصل للمشكلة**
- **خطوات إعادة إنتاج الخطأ**
- **لقطات شاشة أو فيديو** (إذا أمكن)
- **معلومات البيئة**:
  ```
  - نظام التشغيل: Windows/Mac/Linux
  - المتصفح: Chrome/Firefox/Safari
  - إصدار Node.js: 18.x
  - إصدار التطبيق: v1.0.0
  ```
- **رسائل الخطأ** من وحدة التحكم

---

## 🎉 الخلاصة والشكر

### 🌟 **إنجازات المشروع**

**مِخْلاة** ليس مجرد منصة تجارة إلكترونية، بل هو **نظام بيئي متكامل** يجمع بين:

- ✅ **تقنيات حديثة ومتطورة** - Next.js 15, React 18, TypeScript
- ✅ **تجربة مستخدم استثنائية** - تصميم متجاوب وسهل الاستخدام
- ✅ **أمان وموثوقية عالية** - Firebase وأفضل ممارسات الأمان
- ✅ **قابلية التوسع** - بنية قابلة للنمو والتطوير
- ✅ **دعم متعدد اللغات** - العربية والإنجليزية مع RTL
- ✅ **اختبارات شاملة** - تغطية 85%+ من الكود
- ✅ **توثيق مفصل** - أدلة شاملة للمطورين والمستخدمين

### 🙏 **شكر خاص**

- **مجتمع المطورين** - للمساهمات والتحسينات المستمرة
- **المستخدمين الأوائل** - للتجريب وتقديم الملاحظات القيمة
- **فريق التطوير** - للعمل الدؤوب والإبداع المستمر
- **الشركاء التقنيون** - Firebase, Cloudinary, Vercel

### 🚀 **المستقبل**

نحن نعمل باستمرار على تطوير وتحسين **مِخْلاة** لتكون:

- 🤖 **أكثر ذكاءً** - تكامل الذكاء الاصطناعي والتعلم الآلي
- 📱 **أكثر تنقلاً** - تطبيقات هاتف محمول أصلية
- 🌍 **أكثر عالمية** - دعم المزيد من اللغات والأسواق
- ⚡ **أكثر سرعة** - تحسينات أداء مستمرة
- 🔒 **أكثر أماناً** - تقنيات أمان متقدمة

---

<div align="center">

### 💝 **شكراً لاستخدامك مِخْلاة!**

**إذا أعجبك المشروع، لا تنس إعطاؤه ⭐ على GitHub!**

[![GitHub stars](https://img.shields.io/github/stars/mikhla/mikhla?style=social)](https://github.com/mikhla/mikhla/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/mikhla/mikhla?style=social)](https://github.com/mikhla/mikhla/network/members)
[![GitHub watchers](https://img.shields.io/github/watchers/mikhla/mikhla?style=social)](https://github.com/mikhla/mikhla/watchers)

**تابعنا على وسائل التواصل الاجتماعي للحصول على آخر التحديثات:**

[![Twitter Follow](https://img.shields.io/twitter/follow/mikhla_app?style=social)](https://twitter.com/mikhla_app)
[![Discord](https://img.shields.io/discord/123456789?style=social&logo=discord)](https://discord.gg/mikhla)
[![YouTube](https://img.shields.io/youtube/channel/subscribers/UC123456789?style=social)](https://youtube.com/mikhla)

---

**صُنع بـ ❤️ في المملكة العربية السعودية**

**© 2024 مِخْلاة. جميع الحقوق محفوظة.**

</div>
