import { Button } from './button'

describe('Button Component', () => {
  it('يعرض النص بشكل صحيح', () => {
    cy.mount(<Button>انقر هنا</Button>)
    cy.contains('انقر هنا').should('be.visible')
  })

  it('يتعامل مع النقرات بشكل صحيح', () => {
    const onClick = cy.stub()
    cy.mount(<Button onClick={onClick}>انقر هنا</Button>)
    
    cy.get('button').click()
    cy.then(() => {
      expect(onClick).to.have.been.called
    })
  })

  it('يطبق الأنماط المختلفة بشكل صحيح', () => {
    cy.mount(
      <div className="space-y-4">
        <Button variant="default">افتراضي</Button>
        <Button variant="destructive">حذف</Button>
        <Button variant="outline">محدد</Button>
        <Button variant="secondary">ثانوي</Button>
        <Button variant="ghost">شفاف</Button>
        <Button variant="link">رابط</Button>
      </div>
    )

    // التحقق من الأنماط المختلفة
    cy.contains('افتراضي').should('have.class', 'bg-primary')
    cy.contains('حذف').should('have.class', 'bg-destructive')
    cy.contains('محدد').should('have.class', 'border')
    cy.contains('ثانوي').should('have.class', 'bg-secondary')
    cy.contains('شفاف').should('have.class', 'hover:bg-accent')
    cy.contains('رابط').should('have.class', 'text-primary')
  })

  it('يطبق الأحجام المختلفة بشكل صحيح', () => {
    cy.mount(
      <div className="space-y-4">
        <Button size="default">حجم افتراضي</Button>
        <Button size="sm">حجم صغير</Button>
        <Button size="lg">حجم كبير</Button>
        <Button size="icon">🔍</Button>
      </div>
    )

    // التحقق من الأحجام المختلفة
    cy.contains('حجم افتراضي').should('have.class', 'h-10')
    cy.contains('حجم صغير').should('have.class', 'h-9')
    cy.contains('حجم كبير').should('have.class', 'h-11')
    cy.contains('🔍').should('have.class', 'h-10', 'w-10')
  })

  it('يتعامل مع حالة التعطيل بشكل صحيح', () => {
    cy.mount(<Button disabled>زر معطل</Button>)
    
    cy.get('button').should('be.disabled')
    cy.get('button').should('have.class', 'disabled:pointer-events-none')
    cy.get('button').should('have.class', 'disabled:opacity-50')
  })

  it('يعمل كرابط عند استخدام asChild', () => {
    cy.mount(
      <Button asChild>
        <a href="/test">رابط</a>
      </Button>
    )
    
    cy.get('a').should('have.attr', 'href', '/test')
    cy.get('a').should('have.class', 'bg-primary') // يجب أن يحتفظ بأنماط الزر
  })

  it('يدعم الأيقونات', () => {
    cy.mount(
      <div className="space-y-4">
        <Button>
          <span className="mr-2">🔍</span>
          بحث
        </Button>
        <Button variant="outline">
          تحميل
          <span className="ml-2">⬇️</span>
        </Button>
      </div>
    )

    cy.contains('بحث').should('be.visible')
    cy.contains('🔍').should('be.visible')
    cy.contains('تحميل').should('be.visible')
    cy.contains('⬇️').should('be.visible')
  })

  it('يتجاوب مع التفاعلات', () => {
    cy.mount(<Button>زر تفاعلي</Button>)
    
    // التحقق من حالة hover
    cy.get('button').trigger('mouseover')
    cy.get('button').should('have.class', 'hover:bg-primary/90')
    
    // التحقق من حالة focus
    cy.get('button').focus()
    cy.get('button').should('have.class', 'focus-visible:ring-2')
  })

  it('يدعم النصوص الطويلة', () => {
    const longText = 'هذا نص طويل جداً للتأكد من أن الزر يتعامل مع النصوص الطويلة بشكل صحيح'
    cy.mount(<Button className="max-w-xs">{longText}</Button>)
    
    cy.contains(longText).should('be.visible')
    cy.get('button').should('have.css', 'word-wrap', 'break-word')
  })

  it('يدعم الفئات المخصصة', () => {
    cy.mount(
      <Button className="custom-class bg-purple-500 text-white">
        زر مخصص
      </Button>
    )
    
    cy.get('button').should('have.class', 'custom-class')
    cy.get('button').should('have.class', 'bg-purple-500')
    cy.get('button').should('have.class', 'text-white')
  })

  it('يعمل مع النماذج', () => {
    const onSubmit = cy.stub()
    
    cy.mount(
      <form onSubmit={onSubmit}>
        <Button type="submit">إرسال</Button>
      </form>
    )
    
    cy.get('button[type="submit"]').click()
    cy.then(() => {
      expect(onSubmit).to.have.been.called
    })
  })
})
