'use client';

import { Loader2, Wifi, WifiOff } from 'lucide-react';
import { useEffect, useState } from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  showNetworkStatus?: boolean;
}

export function LoadingSpinner({ 
  size = 'md', 
  text = 'جاري التحميل...', 
  showNetworkStatus = false 
}: LoadingSpinnerProps) {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsOnline(navigator.onLine);
      
      const handleOnline = () => setIsOnline(true);
      const handleOffline = () => setIsOnline(false);
      
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-3">
      <div className="relative">
        <Loader2 className={`${sizeClasses[size]} animate-spin text-primary`} />
        {showNetworkStatus && (
          <div className="absolute -top-1 -right-1">
            {isOnline ? (
              <Wifi className="h-3 w-3 text-green-500" />
            ) : (
              <WifiOff className="h-3 w-3 text-red-500" />
            )}
          </div>
        )}
      </div>
      
      <div className="text-center space-y-1">
        <p className="text-muted-foreground text-sm font-medium">{text}</p>
        {showNetworkStatus && !isOnline && (
          <p className="text-red-500 text-xs">لا يوجد اتصال بالإنترنت</p>
        )}
      </div>
    </div>
  );
}

interface AuthLoadingProps {
  message?: string;
}

export function AuthLoading({ message = 'التحقق من حالة المصادقة' }: AuthLoadingProps) {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '' : prev + '.');
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="flex flex-col items-center space-y-4">
        <div className="w-10 h-10 border-3 border-primary/30 border-t-primary rounded-full animate-spin"></div>
        <div className="text-center space-y-1">
          <p className="text-muted-foreground text-sm font-medium">
            جاري التحميل{dots}
          </p>
          <p className="text-muted-foreground text-xs">{message}</p>
        </div>
      </div>
    </div>
  );
}

interface PageLoadingProps {
  title?: string;
  description?: string;
}

export function PageLoading({ 
  title = 'جاري تحميل الصفحة', 
  description = 'يرجى الانتظار...' 
}: PageLoadingProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-8">
      <LoadingSpinner size="lg" text={title} showNetworkStatus />
      <p className="text-muted-foreground text-sm mt-4 text-center max-w-md">
        {description}
      </p>
    </div>
  );
}

interface TranslationLoadingProps {
  locale?: string;
}

export function TranslationLoading({ locale = 'ar' }: TranslationLoadingProps) {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="flex items-center space-x-2 rtl:space-x-reverse">
        <Loader2 className="h-4 w-4 animate-spin text-primary" />
        <span className="text-sm text-muted-foreground">
          {locale === 'ar' ? 'جاري تحميل الترجمات...' : 'Loading translations...'}
        </span>
      </div>
    </div>
  );
}

interface NetworkStatusProps {
  className?: string;
}

export function NetworkStatus({ className = '' }: NetworkStatusProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsOnline(navigator.onLine);
      
      const handleOnline = () => {
        setIsOnline(true);
        setShowStatus(true);
        setTimeout(() => setShowStatus(false), 3000);
      };
      
      const handleOffline = () => {
        setIsOnline(false);
        setShowStatus(true);
      };
      
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);

  if (!showStatus && isOnline) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div className={`
        flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium
        ${isOnline 
          ? 'bg-green-100 text-green-800 border border-green-200' 
          : 'bg-red-100 text-red-800 border border-red-200'
        }
      `}>
        {isOnline ? (
          <>
            <Wifi className="h-4 w-4" />
            <span>تم استعادة الاتصال</span>
          </>
        ) : (
          <>
            <WifiOff className="h-4 w-4" />
            <span>لا يوجد اتصال بالإنترنت</span>
          </>
        )}
      </div>
    </div>
  );
}
