const fs = require('fs');

// قراءة ملفات الترجمة
const arTranslations = JSON.parse(fs.readFileSync('src/locales/ar.json', 'utf8'));
const enTranslations = JSON.parse(fs.readFileSync('src/locales/en.json', 'utf8'));

// استخراج جميع المفاتيح
function extractAllKeys(obj, prefix = '') {
  let keys = [];
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys = keys.concat(extractAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}

const arKeys = extractAllKeys(arTranslations);
const enKeys = extractAllKeys(enTranslations);

console.log('📊 إحصائيات الترجمة:');
console.log(`   - المفاتيح العربية: ${arKeys.length}`);
console.log(`   - المفاتيح الإنجليزية: ${enKeys.length}`);
console.log(`   - الفرق: ${Math.abs(arKeys.length - enKeys.length)}`);

// التحقق من المفاتيح المهمة
const importantKeys = [
  'discoverLocalStores',
  'allStores',
  'storesFound',
  'representative.dashboard.welcome',
  'representative.dashboard.subtitle'
];

console.log('\n🔍 التحقق من المفاتيح المهمة:');
importantKeys.forEach(key => {
  const inAr = arKeys.includes(key);
  const inEn = enKeys.includes(key);
  const status = inAr && inEn ? '✅' : '❌';
  console.log(`   ${status} ${key} - عربي: ${inAr}, إنجليزي: ${inEn}`);
});

console.log('\n✅ انتهى التحقق');
