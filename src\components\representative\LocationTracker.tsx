// src/components/representative/LocationTracker.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { useLocationTracking, useCurrentLocation } from '@/hooks/useLocation';
import { useLocale } from '@/context/LocaleContext';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MapPin,
  Navigation,
  Play,
  Square,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';

interface LocationTrackerProps {
  orderId?: string;
  onLocationUpdate?: (location: any) => void;
  className?: string;
}

export default function LocationTracker({ 
  orderId, 
  onLocationUpdate, 
  className 
}: LocationTrackerProps) {
  const { t } = useLocale();
  const { 
    isTracking, 
    currentLocation, 
    error: trackingError, 
    startTracking, 
    stopTracking 
  } = useLocationTracking();
  
  const { 
    location: staticLocation, 
    loading: locationLoading, 
    error: locationError, 
    requestLocation,
    hasPermission 
  } = useCurrentLocation();

  const [trackingDuration, setTrackingDuration] = useState(0);

  // تحديث مدة التتبع
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isTracking) {
      interval = setInterval(() => {
        setTrackingDuration(prev => prev + 1);
      }, 1000);
    } else {
      setTrackingDuration(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTracking]);

  // إشعار الوالد بتحديث الموقع
  useEffect(() => {
    if (currentLocation && onLocationUpdate) {
      onLocationUpdate(currentLocation);
    }
  }, [currentLocation, onLocationUpdate]);

  // تنسيق الوقت
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // تنسيق الإحداثيات
  const formatCoordinates = (lat: number, lng: number): string => {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
  };

  // معالجة بدء التتبع
  const handleStartTracking = async () => {
    const success = await startTracking(orderId);
    if (!success) {
      console.error('Failed to start location tracking');
    }
  };

  // معالجة إيقاف التتبع
  const handleStopTracking = () => {
    stopTracking();
  };

  // الحصول على حالة الموقع
  const getLocationStatus = () => {
    if (!hasPermission) {
      return { status: 'permission_denied', color: 'destructive', icon: AlertCircle };
    }
    
    if (isTracking) {
      return { status: 'tracking', color: 'default', icon: Navigation };
    }
    
    if (currentLocation || staticLocation) {
      return { status: 'available', color: 'secondary', icon: CheckCircle };
    }
    
    return { status: 'unavailable', color: 'secondary', icon: MapPin };
  };

  const locationStatus = getLocationStatus();
  const StatusIcon = locationStatus.icon;
  const activeLocation = currentLocation || staticLocation;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            {t('locationTracking')}
          </div>
          <Badge variant={locationStatus.color as any}>
            <StatusIcon className="w-3 h-3 mr-1" />
            {t(`locationStatus.${locationStatus.status}`)}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* حالة الإذن */}
        {!hasPermission && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {t('locationPermissionRequired')}
            </AlertDescription>
          </Alert>
        )}

        {/* أخطاء التتبع */}
        {(trackingError || locationError) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {trackingError || locationError}
            </AlertDescription>
          </Alert>
        )}

        {/* معلومات الموقع الحالي */}
        {activeLocation && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">{t('latitude')}:</span>
                <p className="font-mono">{activeLocation.latitude.toFixed(6)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">{t('longitude')}:</span>
                <p className="font-mono">{activeLocation.longitude.toFixed(6)}</p>
              </div>
              {activeLocation.accuracy && (
                <div>
                  <span className="text-muted-foreground">{t('accuracy')}:</span>
                  <p>{Math.round(activeLocation.accuracy)} {t('meters')}</p>
                </div>
              )}
              {activeLocation.speed && (
                <div>
                  <span className="text-muted-foreground">{t('speed')}:</span>
                  <p>{Math.round(activeLocation.speed * 3.6)} {t('kmh')}</p>
                </div>
              )}
            </div>

            <div className="text-xs text-muted-foreground">
              {t('lastUpdated')}: {new Date(activeLocation.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )}

        {/* مدة التتبع */}
        {isTracking && (
          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
            <Clock className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium">
              {t('trackingDuration')}: {formatDuration(trackingDuration)}
            </span>
          </div>
        )}

        {/* أزرار التحكم */}
        <div className="flex gap-2">
          {!isTracking ? (
            <>
              <Button
                onClick={handleStartTracking}
                disabled={!hasPermission || locationLoading}
                className="flex-1"
              >
                {locationLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                {t('startTracking')}
              </Button>
              
              <Button
                variant="outline"
                onClick={requestLocation}
                disabled={locationLoading}
              >
                {locationLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Navigation className="w-4 h-4" />
                )}
              </Button>
            </>
          ) : (
            <Button
              variant="destructive"
              onClick={handleStopTracking}
              className="flex-1"
            >
              <Square className="w-4 h-4 mr-2" />
              {t('stopTracking')}
            </Button>
          )}
        </div>

        {/* معلومات إضافية */}
        {orderId && (
          <div className="text-sm text-muted-foreground">
            {t('trackingForOrder')}: <span className="font-mono">#{orderId}</span>
          </div>
        )}

        {/* نصائح الاستخدام */}
        {!isTracking && hasPermission && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {t('locationTrackingTips')}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
