"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ShoppingCart, 
  CreditCard, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  AlertCircle,
  CheckCircle,
  Loader2,
  ArrowLeft,
  Package
} from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import { useAuth } from "@/context/AuthContext";
import { useCart } from "@/components/cart/CartProvider";
import { usePayment, usePayPal, usePaymentFees } from "@/hooks/usePayment";
import { collection, addDoc, serverTimestamp, doc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import type { CheckoutInfo } from "@/types";
import type { PaymentMethod } from "@/services/paymentService";

export default function CheckoutPage() {
  const { t } = useLocale();
  const router = useRouter();
  const { user } = useAuth();
  const { items, totalAmount, totalItems, clearCart } = useCart();
  const { createPayment, processPayment, processing: paymentProcessing } = usePayment();
  const { createOrder: createPayPalOrder, loading: paypalLoading } = usePayPal();
  const { calculateFee, calculateTotal, getSupportedMethods } = usePaymentFees();

  // Form state
  const [customerInfo, setCustomerInfo] = useState({
    name: user?.displayName || "",
    email: user?.email || "",
    phone: ""
  });

  const [shippingAddress, setShippingAddress] = useState({
    street: "",
    city: "",
    postalCode: "",
    notes: ""
  });

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [orderNotes, setOrderNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderNumber, setOrderNumber] = useState("");
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [showCardForm, setShowCardForm] = useState(false);
  const [cardData, setCardData] = useState({
    number: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    holderName: ''
  });

  // Delivery fee calculation
  const deliveryFee = totalAmount > 100 ? 0 : 15; // Free delivery over 100 SAR
  const paymentFee = calculateFee(totalAmount, paymentMethod);
  const subtotal = totalAmount + deliveryFee;
  const finalTotal = calculateTotal(subtotal, paymentMethod);

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0 && !orderSuccess) {
      router.push('/stores');
    }
  }, [items.length, orderSuccess, router]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      router.push('/login');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare order data
      const orderData = {
        customerId: user.uid,
        customerInfo,
        items: items.map(item => ({
          productId: item.productId,
          productName: item.productName,
          productImage: item.productImage,
          price: item.price,
          quantity: item.quantity,
          storeId: item.storeId,
          storeName: item.storeName,
          selectedVariants: item.selectedVariants || {}
        })),
        shippingAddress,
        paymentMethod,
        paymentInfo: {
          method: paymentMethod,
          status: 'pending' as const,
          paymentId: null
        },
        totalAmount,
        deliveryFee,
        paymentFee,
        finalTotal,
        notes: orderNotes,
        status: 'pending' as const,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      // Generate order number first
      const tempOrderRef = doc(collection(db, 'orders'));
      const generatedOrderNumber = `ORD-${tempOrderRef.id.slice(-8).toUpperCase()}`;

      // Save order to Firestore with order number
      const orderRef = await addDoc(collection(db, 'orders'), {
        ...orderData,
        orderNumber: generatedOrderNumber
      });

      setOrderNumber(generatedOrderNumber);
      setOrderSuccess(true);
      clearCart();

    } catch (error) {
      console.error('Error creating order:', error);
      alert(t('orderCreationFailed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Success screen
  if (orderSuccess) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="text-center py-12">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold mb-2">{t('orderSuccessTitle')}</h1>
              <p className="text-muted-foreground mb-4">
                {t('orderSuccessMessage')}
              </p>
              <div className="bg-muted p-4 rounded-lg mb-6">
                <p className="font-semibold">{t('orderNumber')}: {orderNumber}</p>
              </div>
              <div className="flex gap-4 justify-center">
                <Button onClick={() => router.push('/dashboard/orders')}>
                  <Package className="w-4 h-4 mr-2" />
                  {t('viewMyOrders')}
                </Button>
                <Button variant="outline" onClick={() => router.push('/stores')}>
                  {t('continueShopping')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{t('checkout')}</h1>
            <p className="text-muted-foreground">{t('completeYourOrder')}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    {t('customerInformation')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">{t('fullName')}</Label>
                      <Input
                        id="name"
                        value={customerInfo.name}
                        onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">{t('phoneNumber')}</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={customerInfo.phone}
                        onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email">{t('emailAddress')}</Label>
                    <Input
                      id="email"
                      type="email"
                      value={customerInfo.email}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                      required
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Shipping Address */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    {t('shippingAddress')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="street">{t('streetAddress')}</Label>
                    <Input
                      id="street"
                      value={shippingAddress.street}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, street: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="city">{t('city')}</Label>
                      <Input
                        id="city"
                        value={shippingAddress.city}
                        onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="postalCode">{t('postalCode')}</Label>
                      <Input
                        id="postalCode"
                        value={shippingAddress.postalCode}
                        onChange={(e) => setShippingAddress(prev => ({ ...prev, postalCode: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="addressNotes">{t('deliveryNotes')}</Label>
                    <Textarea
                      id="addressNotes"
                      value={shippingAddress.notes}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder={t('deliveryNotesPlaceholder')}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Payment Method */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    {t('paymentMethod')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={paymentMethod} onValueChange={(value: any) => setPaymentMethod(value)}>
                    <div className="flex items-center justify-between space-x-2">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="cash" id="cash" />
                        <Label htmlFor="cash">{t('cashOnDelivery')}</Label>
                      </div>
                      <span className="text-sm text-green-600">{t('free')}</span>
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="card" id="card" />
                        <Label htmlFor="card">{t('creditCard')}</Label>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        +{calculateFee(subtotal, 'card').toFixed(2)} {t('sar')}
                      </span>
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="paypal" id="paypal" />
                        <Label htmlFor="paypal">PayPal</Label>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        +{calculateFee(subtotal, 'paypal').toFixed(2)} {t('sar')}
                      </span>
                    </div>
                  </RadioGroup>

                  {/* Card Form */}
                  {showCardForm && paymentMethod === 'card' && (
                    <div className="mt-4 p-4 border rounded-lg space-y-4">
                      <h4 className="font-medium">{t('cardDetails')}</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="col-span-2">
                          <Label htmlFor="cardNumber">{t('cardNumber')}</Label>
                          <Input
                            id="cardNumber"
                            placeholder="1234 5678 9012 3456"
                            value={cardData.number}
                            onChange={(e) => setCardData(prev => ({ ...prev, number: e.target.value }))}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="expiryMonth">{t('expiryMonth')}</Label>
                          <Input
                            id="expiryMonth"
                            placeholder="MM"
                            value={cardData.expiryMonth}
                            onChange={(e) => setCardData(prev => ({ ...prev, expiryMonth: e.target.value }))}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="expiryYear">{t('expiryYear')}</Label>
                          <Input
                            id="expiryYear"
                            placeholder="YY"
                            value={cardData.expiryYear}
                            onChange={(e) => setCardData(prev => ({ ...prev, expiryYear: e.target.value }))}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvv">CVV</Label>
                          <Input
                            id="cvv"
                            placeholder="123"
                            value={cardData.cvv}
                            onChange={(e) => setCardData(prev => ({ ...prev, cvv: e.target.value }))}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="holderName">{t('cardHolderName')}</Label>
                          <Input
                            id="holderName"
                            placeholder={t('fullName')}
                            value={cardData.holderName}
                            onChange={(e) => setCardData(prev => ({ ...prev, holderName: e.target.value }))}
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Order Notes */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('orderNotes')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    placeholder={t('orderNotesPlaceholder')}
                  />
                </CardContent>
              </Card>
            </form>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5" />
                  {t('orderSummary')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Cart Items */}
                <div className="space-y-3">
                  {items.map((item) => (
                    <div key={`${item.productId}-${JSON.stringify(item.selectedVariants)}`} className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center overflow-hidden">
                        {item.productImage ? (
                          <img
                            src={item.productImage}
                            alt={item.productName}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Package className="w-6 h-6 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">{item.productName}</h4>
                        <p className="text-xs text-muted-foreground">{item.storeName}</p>
                        <p className="text-xs text-muted-foreground">
                          {t('quantity')}: {item.quantity}
                        </p>
                      </div>
                      <div className="text-sm font-medium">
                        {(item.price * item.quantity).toFixed(2)} {t('sar')}
                      </div>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* Order Totals */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{t('subtotal')} ({totalItems} {t('items')})</span>
                    <span>{totalAmount.toFixed(2)} {t('sar')}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>{t('deliveryFee')}</span>
                    <span>
                      {deliveryFee === 0 ? (
                        <span className="text-green-600">{t('free')}</span>
                      ) : (
                        `${deliveryFee.toFixed(2)} ${t('sar')}`
                      )}
                    </span>
                  </div>
                  {paymentFee > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>{t('paymentFee')}</span>
                      <span>{paymentFee.toFixed(2)} {t('sar')}</span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>{t('total')}</span>
                    <span>{finalTotal.toFixed(2)} {t('sar')}</span>
                  </div>
                </div>

                {/* Free Delivery Notice */}
                {deliveryFee > 0 && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {t('freeDeliveryNotice', { amount: (100 - totalAmount).toFixed(2) })}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Place Order Button */}
                <Button
                  type="submit"
                  className="w-full"
                  size="lg"
                  disabled={isSubmitting || items.length === 0}
                  onClick={handleSubmit}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      {t('processingOrder')}
                    </>
                  ) : (
                    <>
                      <CreditCard className="w-4 h-4 mr-2" />
                      {t('placeOrder')} - {finalTotal.toFixed(2)} {t('sar')}
                    </>
                  )}
                </Button>

                {/* Security Notice */}
                <div className="text-xs text-muted-foreground text-center">
                  <p>{t('secureCheckoutNotice')}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
