'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { ExtendedUserDocument } from '@/hooks/useUsersManagement';
import { UserType } from '@/types';
import { 
  Save, 
  X,
  User,
  Mail,
  Phone,
  Shield
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface UserEditDialogProps {
  user: ExtendedUserDocument;
  onClose: () => void;
  onSave: (updatedUser: ExtendedUserDocument) => void;
}

export function UserEditDialog({ user, onClose, onSave }: UserEditDialogProps) {
  const { t } = useLocale();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user.displayName || '',
    email: user.email || '',
    userType: user.userType,
    isActive: user.isActive ?? true,
    phoneNumber: '', // يمكن إضافة هذا للمستخدم
    notes: '' // ملاحظات إدارية
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // محاكاة حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedUser: ExtendedUserDocument = {
        ...user,
        displayName: formData.displayName,
        email: formData.email,
        userType: formData.userType,
        isActive: formData.isActive,
        updatedAt: new Date() as any
      };

      onSave(updatedUser);
    } catch (error) {
      console.error('Error updating user:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('editUser')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* الاسم */}
          <div className="space-y-2">
            <Label htmlFor="displayName">الاسم الكامل</Label>
            <Input
              id="displayName"
              value={formData.displayName}
              onChange={(e) => handleInputChange('displayName', e.target.value)}
              placeholder="أدخل الاسم الكامل"
            />
          </div>

          {/* البريد الإلكتروني */}
          <div className="space-y-2">
            <Label htmlFor="email">البريد الإلكتروني</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="أدخل البريد الإلكتروني"
                className="pl-10"
              />
            </div>
          </div>

          {/* نوع المستخدم */}
          <div className="space-y-2">
            <Label>نوع المستخدم</Label>
            <Select
              value={formData.userType}
              onValueChange={(value) => handleInputChange('userType', value as UserType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="customer">{t('customer')}</SelectItem>
                <SelectItem value="merchant">{t('merchant')}</SelectItem>
                <SelectItem value="representative">مندوب</SelectItem>
                <SelectItem value="admin">مدير</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* رقم الهاتف */}
          <div className="space-y-2">
            <Label htmlFor="phoneNumber">رقم الهاتف</Label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                placeholder="أدخل رقم الهاتف"
                className="pl-10"
              />
            </div>
          </div>

          {/* حالة الحساب */}
          <div className="space-y-2">
            <Label>حالة الحساب</Label>
            <Select
              value={formData.isActive ? 'active' : 'inactive'}
              onValueChange={(value) => handleInputChange('isActive', value === 'active')}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="inactive">غير نشط</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* ملاحظات إدارية */}
          <div className="space-y-2">
            <Label htmlFor="notes">ملاحظات إدارية</Label>
            <textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="أضف ملاحظات إدارية (اختياري)"
              rows={3}
              className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          {/* معلومات إضافية */}
          <div className="p-3 bg-gray-50 rounded-lg space-y-2">
            <h4 className="text-sm font-medium text-gray-900">معلومات الحساب</h4>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div>
                <span className="font-medium">تاريخ التسجيل:</span>
                <br />
                {user.createdAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
              </div>
              <div>
                <span className="font-medium">آخر تحديث:</span>
                <br />
                {user.updatedAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
              </div>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              إلغاء
            </Button>
            
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
