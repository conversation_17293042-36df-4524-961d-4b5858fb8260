describe('تدفق الطلبات والدفع المتكامل', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.mockGeolocation(24.7136, 46.6753) // الرياض
  })

  context('تدفق الطلب الكامل للعميل', () => {
    beforeEach(() => {
      cy.mockLogin('customer')
    })

    it('يجب أن يعمل تدفق الطلب الكامل من البداية للنهاية', () => {
      // 1. تصفح المنتجات
      cy.visitWithLocale('/')
      cy.waitForLoadingToFinish()
      
      // البحث عن منتج
      cy.get('[data-testid="search-input"]').type('هاتف ذكي')
      cy.get('[data-testid="search-button"]').click()
      cy.waitForLoadingToFinish()
      
      // اختيار منتج
      cy.get('[data-testid="product-card"]').first().click()
      cy.waitForLoadingToFinish()
      
      // 2. إضافة للسلة
      cy.get('[data-testid="add-to-cart"]').click()
      cy.get('[data-testid="cart-success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة المنتج للسلة')
      
      // 3. الذهاب للسلة
      cy.get('[data-testid="cart-icon"]').click()
      cy.get('[data-testid="cart-page"]').should('be.visible')
      
      // التحقق من المنتج في السلة
      cy.get('[data-testid="cart-item"]').should('have.length', 1)
      cy.get('[data-testid="cart-total"]').should('be.visible')
      
      // 4. المتابعة للدفع
      cy.get('[data-testid="proceed-to-checkout"]').click()
      cy.get('[data-testid="checkout-page"]').should('be.visible')
      
      // 5. ملء معلومات التوصيل
      cy.get('[data-testid="delivery-address"]').type('الرياض، حي النخيل، شارع الملك فهد')
      cy.get('[data-testid="delivery-phone"]').type('0501234567')
      cy.get('[data-testid="delivery-notes"]').type('الطابق الثاني، شقة رقم 5')
      
      // 6. اختيار طريقة الدفع
      cy.get('[data-testid="payment-method-card"]').click()
      cy.get('[data-testid="card-number"]').type('****************')
      cy.get('[data-testid="card-expiry"]').type('12/25')
      cy.get('[data-testid="card-cvv"]').type('123')
      cy.get('[data-testid="card-name"]').type('أحمد محمد')
      
      // 7. تأكيد الطلب
      cy.get('[data-testid="place-order"]').click()
      cy.get('[data-testid="payment-processing"]').should('be.visible')
      
      // محاكاة نجاح الدفع
      cy.intercept('POST', '**/api/payment/process', {
        statusCode: 200,
        body: { success: true, transactionId: 'TXN-123456' }
      }).as('paymentSuccess')
      
      cy.wait('@paymentSuccess')
      
      // 8. التحقق من نجاح الطلب
      cy.get('[data-testid="order-success"]').should('be.visible')
      cy.shouldContainArabicText('تم تأكيد طلبك بنجاح')
      cy.get('[data-testid="order-number"]').should('be.visible')
      cy.get('[data-testid="estimated-delivery"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة تتبع الطلب', () => {
      // إنشاء طلب أولاً (محاكاة)
      cy.visitWithLocale('/orders')
      cy.waitForLoadingToFinish()
      
      // التحقق من قائمة الطلبات
      cy.get('[data-testid="orders-list"]').should('be.visible')
      cy.get('[data-testid="order-item"]').should('have.length.greaterThan', 0)
      
      // تتبع طلب
      cy.get('[data-testid="track-order-1"]').click()
      cy.get('[data-testid="order-tracking"]').should('be.visible')
      
      // التحقق من مراحل الطلب
      cy.get('[data-testid="order-status-confirmed"]').should('be.visible')
      cy.get('[data-testid="order-status-preparing"]').should('be.visible')
      cy.get('[data-testid="order-status-shipped"]').should('be.visible')
      cy.get('[data-testid="order-status-delivered"]').should('be.visible')
      
      // التحقق من الخريطة
      cy.get('[data-testid="tracking-map"]').should('be.visible')
      cy.get('[data-testid="delivery-location"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة إلغاء الطلب', () => {
      cy.visitWithLocale('/orders')
      cy.waitForLoadingToFinish()
      
      // إلغاء طلب
      cy.get('[data-testid="cancel-order-1"]').click()
      cy.get('[data-testid="cancel-confirmation"]').should('be.visible')
      
      // اختيار سبب الإلغاء
      cy.get('[data-testid="cancel-reason"]').select('changed-mind')
      cy.get('[data-testid="cancel-notes"]').type('تغيير في الخطط')
      
      // تأكيد الإلغاء
      cy.get('[data-testid="confirm-cancel"]').click()
      
      // التحقق من رسالة النجاح
      cy.get('[data-testid="cancel-success"]').should('be.visible')
      cy.shouldContainArabicText('تم إلغاء الطلب بنجاح')
    })

    it('يجب أن تعمل وظيفة إعادة الطلب', () => {
      cy.visitWithLocale('/orders')
      cy.waitForLoadingToFinish()
      
      // إعادة طلب سابق
      cy.get('[data-testid="reorder-1"]').click()
      cy.get('[data-testid="reorder-confirmation"]').should('be.visible')
      
      // تأكيد إعادة الطلب
      cy.get('[data-testid="confirm-reorder"]').click()
      
      // التحقق من إضافة المنتجات للسلة
      cy.get('[data-testid="reorder-success"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة المنتجات للسلة')
      
      // التحقق من الانتقال للسلة
      cy.url().should('include', '/cart')
    })
  })

  context('إدارة الطلبات للتاجر', () => {
    beforeEach(() => {
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/orders')
    })

    it('يجب أن تعرض قائمة الطلبات للتاجر', () => {
      cy.waitForLoadingToFinish()
      
      // التحقق من صفحة الطلبات
      cy.shouldContainArabicText('إدارة الطلبات')
      cy.get('[data-testid="merchant-orders"]').should('be.visible')
      
      // التحقق من تبويبات الطلبات
      cy.get('[data-testid="orders-tabs"]').should('be.visible')
      cy.get('[data-testid="tab-new"]').should('be.visible')
      cy.get('[data-testid="tab-processing"]').should('be.visible')
      cy.get('[data-testid="tab-shipped"]').should('be.visible')
      cy.get('[data-testid="tab-delivered"]').should('be.visible')
      cy.get('[data-testid="tab-cancelled"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة قبول الطلب', () => {
      cy.waitForLoadingToFinish()
      
      // قبول طلب جديد
      cy.get('[data-testid="accept-order-1"]').click()
      cy.get('[data-testid="order-acceptance"]').should('be.visible')
      
      // تحديد وقت التحضير
      cy.get('[data-testid="preparation-time"]').select('30')
      cy.get('[data-testid="preparation-notes"]').type('سيتم تحضير الطلب خلال 30 دقيقة')
      
      // تأكيد القبول
      cy.get('[data-testid="confirm-acceptance"]').click()
      
      // التحقق من رسالة النجاح
      cy.get('[data-testid="acceptance-success"]').should('be.visible')
      cy.shouldContainArabicText('تم قبول الطلب بنجاح')
    })

    it('يجب أن تعمل وظيفة تحديث حالة الطلب', () => {
      cy.waitForLoadingToFinish()
      
      // الانتقال للطلبات قيد التحضير
      cy.get('[data-testid="tab-processing"]').click()
      
      // تحديث حالة طلب
      cy.get('[data-testid="update-status-1"]').click()
      cy.get('[data-testid="status-options"]').should('be.visible')
      
      // اختيار حالة جديدة
      cy.get('[data-testid="status-ready"]').click()
      cy.get('[data-testid="status-notes"]').type('الطلب جاهز للتوصيل')
      
      // تأكيد التحديث
      cy.get('[data-testid="confirm-status-update"]').click()
      
      // التحقق من رسالة النجاح
      cy.get('[data-testid="status-update-success"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث حالة الطلب')
    })

    it('يجب أن تعمل وظيفة رفض الطلب', () => {
      cy.waitForLoadingToFinish()
      
      // رفض طلب
      cy.get('[data-testid="reject-order-1"]').click()
      cy.get('[data-testid="rejection-dialog"]').should('be.visible')
      
      // اختيار سبب الرفض
      cy.get('[data-testid="rejection-reason"]').select('out-of-stock')
      cy.get('[data-testid="rejection-notes"]').type('المنتج غير متوفر حالياً')
      
      // تأكيد الرفض
      cy.get('[data-testid="confirm-rejection"]').click()
      
      // التحقق من رسالة النجاح
      cy.get('[data-testid="rejection-success"]').should('be.visible')
      cy.shouldContainArabicText('تم رفض الطلب')
    })

    it('يجب أن تعمل وظيفة طباعة الطلب', () => {
      cy.waitForLoadingToFinish()
      
      // طباعة طلب
      cy.get('[data-testid="print-order-1"]').click()
      
      // التحقق من فتح نافذة الطباعة
      cy.window().then((win) => {
        cy.stub(win, 'print').as('windowPrint')
      })
      
      cy.get('@windowPrint').should('have.been.called')
    })
  })

  context('نظام الدفع', () => {
    beforeEach(() => {
      cy.mockLogin('customer')
    })

    it('يجب أن تعمل طرق الدفع المختلفة', () => {
      // محاكاة وجود منتجات في السلة
      cy.visitWithLocale('/checkout')
      cy.waitForLoadingToFinish()
      
      // اختبار الدفع بالبطاقة
      cy.get('[data-testid="payment-method-card"]').click()
      cy.get('[data-testid="card-form"]').should('be.visible')
      
      // اختبار الدفع عند الاستلام
      cy.get('[data-testid="payment-method-cod"]').click()
      cy.get('[data-testid="cod-confirmation"]').should('be.visible')
      
      // اختبار الدفع بـ Apple Pay
      cy.get('[data-testid="payment-method-apple-pay"]').click()
      cy.get('[data-testid="apple-pay-button"]').should('be.visible')
      
      // اختبار الدفع بـ STC Pay
      cy.get('[data-testid="payment-method-stc-pay"]').click()
      cy.get('[data-testid="stc-pay-form"]').should('be.visible')
    })

    it('يجب أن تتعامل مع فشل الدفع', () => {
      cy.visitWithLocale('/checkout')
      cy.waitForLoadingToFinish()
      
      // محاكاة فشل الدفع
      cy.intercept('POST', '**/api/payment/process', {
        statusCode: 400,
        body: { error: 'Payment failed', code: 'INSUFFICIENT_FUNDS' }
      }).as('paymentFailure')
      
      // ملء بيانات الدفع
      cy.get('[data-testid="payment-method-card"]').click()
      cy.get('[data-testid="card-number"]').type('****************') // بطاقة فاشلة
      cy.get('[data-testid="card-expiry"]').type('12/25')
      cy.get('[data-testid="card-cvv"]').type('123')
      
      // محاولة الدفع
      cy.get('[data-testid="place-order"]').click()
      cy.wait('@paymentFailure')
      
      // التحقق من رسالة الخطأ
      cy.get('[data-testid="payment-error"]').should('be.visible')
      cy.shouldContainArabicText('فشل في عملية الدفع')
      
      // التحقق من إمكانية إعادة المحاولة
      cy.get('[data-testid="retry-payment"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة حفظ بيانات الدفع', () => {
      cy.visitWithLocale('/checkout')
      cy.waitForLoadingToFinish()
      
      // ملء بيانات البطاقة
      cy.get('[data-testid="payment-method-card"]').click()
      cy.get('[data-testid="card-number"]').type('****************')
      cy.get('[data-testid="card-expiry"]').type('12/25')
      cy.get('[data-testid="card-cvv"]').type('123')
      cy.get('[data-testid="card-name"]').type('أحمد محمد')
      
      // حفظ البطاقة للاستخدام المستقبلي
      cy.get('[data-testid="save-card"]').check()
      
      // إكمال الدفع
      cy.get('[data-testid="place-order"]').click()
      
      // التحقق من حفظ البطاقة
      cy.visitWithLocale('/profile/payment-methods')
      cy.waitForLoadingToFinish()
      cy.get('[data-testid="saved-card"]').should('be.visible')
    })
  })

  it('يجب أن تعمل الإشعارات في الوقت الفعلي', () => {
    // محاكاة إشعارات الطلبات
    cy.mockLogin('merchant')
    cy.visitWithLocale('/merchant/dashboard')
    cy.waitForLoadingToFinish()
    
    // محاكاة وصول طلب جديد
    cy.window().then((win) => {
      win.dispatchEvent(new CustomEvent('newOrder', {
        detail: { orderId: 'ORD-123', customerName: 'أحمد محمد' }
      }))
    })
    
    // التحقق من ظهور الإشعار
    cy.get('[data-testid="new-order-notification"]').should('be.visible')
    cy.shouldContainArabicText('طلب جديد من أحمد محمد')
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في تحميل الطلبات
    cy.intercept('GET', '**/api/orders**', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('ordersError')
    
    cy.mockLogin('customer')
    cy.visitWithLocale('/orders')
    cy.wait('@ordersError')
    
    // التحقق من عرض رسالة الخطأ
    cy.get('[data-testid="orders-error"]').should('be.visible')
    cy.shouldContainArabicText('حدث خطأ في تحميل الطلبات')
    
    // التحقق من وجود زر إعادة المحاولة
    cy.get('[data-testid="retry-orders"]').should('be.visible')
  })

  it('يجب أن تعمل على الأجهزة المحمولة', () => {
    cy.viewport('iphone-x')
    cy.mockLogin('customer')
    cy.visitWithLocale('/checkout')
    cy.waitForLoadingToFinish()
    
    // التحقق من التجاوب
    cy.get('[data-testid="checkout-page"]').should('be.visible')
    
    // اختبار نموذج الدفع على الهاتف المحمول
    cy.get('[data-testid="mobile-payment-form"]').should('be.visible')
    cy.get('[data-testid="mobile-place-order"]').should('be.visible')
  })
})
