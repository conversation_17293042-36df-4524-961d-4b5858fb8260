// src/services/aiApprovalService.ts - المرحلة الأولى المطورة
import { collection, query, where, getDocs, doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { StoreDocument, UserDocument } from '@/types';
import { DocumentAnalysisService } from './documentAnalysisService';
import { AdvancedMatchingService } from './advancedMatchingService';
import { ArabicTextProcessingService } from './arabicTextProcessingService';
import { DynamicMatchingConfigService } from './dynamicMatchingConfigService';

// أنواع البيانات للموافقة الذكية
export interface AIDocumentAnalysis {
  documentType: 'commercial_registration' | 'freelance_document' | 'id_card';
  extractedData: {
    businessName: string;
    ownerName: string;
    registrationNumber: string;
    issueDate: Date;
    expiryDate: Date;
    issuingAuthority: string;
    businessActivity: string;
  };
  confidence: number; // 0-100%
  isValid: boolean;
  issues: string[];
  ocrText: string;
}

export interface VerificationResult {
  nameMatch: boolean;
  nameMatchConfidence: number;
  documentsConsistent: boolean;
  duplicateCheck: boolean;
  duplicateDetails?: {
    existingMerchantId: string;
    duplicateType: 'name' | 'registration' | 'email';
  };
  riskScore: number; // 0-100
  autoApprovalEligible: boolean;
  reasons: string[];
}

export interface AutoApprovalDecision {
  decision: 'approve' | 'reject' | 'manual_review';
  confidence: number;
  reasons: string[];
  extractedData: Record<string, any>;
  riskFactors: string[];
  recommendations: string[];
}

// قواعد الموافقة التلقائية المحسنة للتجار - المرحلة الأولى المطورة
const MERCHANT_AUTO_APPROVAL_RULES = {
  // معايير التطابق المحسنة
  nameMatchThreshold: 85, // تطابق الاسم بنسبة 85%+
  fuzzyMatchThreshold: 80, // تطابق ضبابي 80%+
  soundexMatchThreshold: 75, // تطابق صوتي 75%+
  jaroWinklerThreshold: 0.85, // Jaro-Winkler 85%+

  // معايير المستندات
  documentValidityThreshold: 90, // صحة المستند 90%+
  maxRiskScore: 25, // نقاط المخاطر أقل من 25
  minConfidenceScore: 80, // ثقة عامة 80%+
  requiredDocuments: ['commercial_registration', 'freelance_document'],
  maxDocumentAge: 365 * 5, // أقصى عمر للمستند 5 سنوات
  minBusinessNameLength: 3, // أقل طول لاسم المنشأة
  duplicateCheckEnabled: true, // فحص التكرار مفعل

  // أوزان الحقول الديناميكية
  fieldWeights: {
    ownerName: 0.4, // وزن اسم المالك 40%
    businessName: 0.3, // وزن اسم المنشأة 30%
    registrationNumber: 0.2, // وزن رقم السجل 20%
    address: 0.1 // وزن العنوان 10%
  }
};

export class AIApprovalService {
  /**
   * تحليل المستندات باستخدام الذكاء الاصطناعي
   */
  static async analyzeDocument(
    documentUrl: string, 
    documentType: string
  ): Promise<AIDocumentAnalysis> {
    try {
      // استخدام خدمة تحليل المستندات المحسنة
      const analysis = await DocumentAnalysisService.analyzeDocument(
        documentUrl,
        documentType,
        false // ليس مندوب
      ) as AIDocumentAnalysis;

      // التحقق من صحة البيانات المستخرجة للتجار
      return this.validateMerchantExtractedData(analysis);
    } catch (error) {
      console.error('خطأ في تحليل المستند:', error);
      return {
        documentType: documentType as any,
        extractedData: {
          businessName: '',
          ownerName: '',
          registrationNumber: '',
          issueDate: new Date(),
          expiryDate: new Date(),
          issuingAuthority: '',
          businessActivity: ''
        },
        confidence: 0,
        isValid: false,
        issues: ['فشل في تحليل المستند'],
        ocrText: ''
      };
    }
  }

  /**
   * التحقق من صحة البيانات المستخرجة للتجار
   */
  private static validateMerchantExtractedData(analysis: any): AIDocumentAnalysis {
    const issues: string[] = [];
    
    // التحقق من وجود البيانات الأساسية
    if (!analysis.extractedData.ownerName) {
      issues.push('لم يتم العثور على اسم المالك');
    }
    
    if (!analysis.extractedData.registrationNumber) {
      issues.push('لم يتم العثور على رقم السجل');
    }
    
    // التحقق من تاريخ الانتهاء
    const expiryDate = new Date(analysis.extractedData.expiryDate);
    if (expiryDate < new Date()) {
      issues.push('المستند منتهي الصلاحية');
    }
    
    return {
      ...analysis,
      issues,
      isValid: issues.length === 0 && analysis.confidence > 70
    };
  }

  /**
   * التحقق من التطابق والتكرار
   */
  static async verifyMerchantData(
    userData: UserDocument,
    documentAnalyses: AIDocumentAnalysis[]
  ): Promise<VerificationResult> {
    const result: VerificationResult = {
      nameMatch: false,
      nameMatchConfidence: 0,
      documentsConsistent: false,
      duplicateCheck: true,
      riskScore: 0,
      autoApprovalEligible: false,
      reasons: []
    };

    try {
      // 1. التحقق من تطابق الأسماء
      const nameVerification = this.verifyNameMatch(userData, documentAnalyses);
      result.nameMatch = nameVerification.match;
      result.nameMatchConfidence = nameVerification.confidence;

      // 2. التحقق من تناسق المستندات
      result.documentsConsistent = this.verifyDocumentConsistency(documentAnalyses);

      // 3. فحص التكرار
      const duplicateCheck = await this.checkForDuplicates(userData, documentAnalyses);
      result.duplicateCheck = !duplicateCheck.hasDuplicates;
      result.duplicateDetails = duplicateCheck.details;

      // 4. حساب نقاط المخاطر
      result.riskScore = this.calculateRiskScore(userData, documentAnalyses, duplicateCheck);

      // 5. تحديد الأسباب
      result.reasons = this.generateReasons(result, documentAnalyses);

      // 6. تحديد الأهلية للموافقة التلقائية
      result.autoApprovalEligible = this.isEligibleForAutoApproval(result, documentAnalyses);

      return result;
    } catch (error) {
      console.error('خطأ في التحقق من بيانات التاجر:', error);
      result.reasons.push('خطأ في عملية التحقق');
      result.riskScore = 100;
      return result;
    }
  }

  /**
   * التحقق من تطابق الأسماء - المحسن بالخوارزميات المتقدمة
   */
  private static verifyNameMatch(
    userData: UserDocument,
    documentAnalyses: AIDocumentAnalysis[]
  ): { match: boolean; confidence: number; details?: any } {
    const userName = userData.displayName?.toLowerCase().trim() || '';
    let bestMatch = 0;
    let bestMatchDetails: any = {};

    for (const analysis of documentAnalyses) {
      const docName = analysis.extractedData.ownerName?.toLowerCase().trim() || '';

      // استخدام الخوارزميات المتقدمة
      const advancedResult = AdvancedMatchingService.calculateAdvancedSimilarity(userName, docName);

      // استخدام معالجة النصوص العربية
      const arabicResult = ArabicTextProcessingService.compareTexts(userName, docName);

      // دمج النتائج بالأوزان
      const combinedSimilarity = (
        advancedResult.overallSimilarity * 0.7 +
        arabicResult.similarity * 0.3
      );

      if (combinedSimilarity > bestMatch) {
        bestMatch = combinedSimilarity;
        bestMatchDetails = {
          advanced: advancedResult,
          arabic: arabicResult,
          documentType: analysis.documentType
        };
      }
    }

    // تطبيق العتبة الديناميكية
    const dynamicThreshold = DynamicMatchingConfigService.calculateDynamicThreshold(
      'commercial_registration',
      {
        documentQuality: documentAnalyses[0]?.confidence || 80,
        userHistory: 75, // افتراضي
        timeOfDay: new Date().getHours(),
        systemLoad: 50 // افتراضي
      }
    );

    return {
      match: bestMatch >= dynamicThreshold,
      confidence: bestMatch,
      details: bestMatchDetails
    };
  }

  /**
   * حساب تشابه النصوص - تم استبدالها بالخوارزميات المتقدمة
   * @deprecated استخدم AdvancedMatchingService.calculateAdvancedSimilarity بدلاً من ذلك
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    // تم نقل هذه الوظيفة إلى AdvancedMatchingService للحصول على دقة أفضل
    const result = AdvancedMatchingService.calculateAdvancedSimilarity(str1, str2);
    return result.overallSimilarity;
  }

  /**
   * التحقق من تناسق المستندات - محسن بالخوارزميات المتقدمة
   */
  private static verifyDocumentConsistency(documentAnalyses: AIDocumentAnalysis[]): boolean {
    if (documentAnalyses.length < 2) return true;

    const firstDoc = documentAnalyses[0];
    let consistencyScore = 0;
    let totalComparisons = 0;

    for (let i = 1; i < documentAnalyses.length; i++) {
      const currentDoc = documentAnalyses[i];

      // مقارنة أسماء المالكين
      const nameComparison = ArabicTextProcessingService.compareTexts(
        firstDoc.extractedData.ownerName || '',
        currentDoc.extractedData.ownerName || ''
      );

      // مقارنة أسماء المنشآت (إن وجدت)
      const businessComparison = ArabicTextProcessingService.compareTexts(
        firstDoc.extractedData.businessName || '',
        currentDoc.extractedData.businessName || ''
      );

      // حساب النقاط
      consistencyScore += nameComparison.similarity * 0.7; // وزن أكبر للاسم
      consistencyScore += businessComparison.similarity * 0.3; // وزن أقل لاسم المنشأة
      totalComparisons += 2;
    }

    const averageConsistency = totalComparisons > 0 ? consistencyScore / totalComparisons : 100;
    return averageConsistency >= 80; // عتبة التناسق 80%
  }

  /**
   * فحص التكرار
   */
  private static async checkForDuplicates(
    userData: UserDocument,
    documentAnalyses: AIDocumentAnalysis[]
  ): Promise<{ hasDuplicates: boolean; details?: any }> {
    try {
      // فحص تكرار البريد الإلكتروني
      const emailQuery = query(
        collection(db, 'users'),
        where('email', '==', userData.email),
        where('userType', '==', 'merchant')
      );
      const emailDocs = await getDocs(emailQuery);
      
      if (emailDocs.size > 1) {
        return {
          hasDuplicates: true,
          details: { duplicateType: 'email', existingMerchantId: emailDocs.docs[0].id }
        };
      }

      // فحص تكرار رقم السجل التجاري
      for (const analysis of documentAnalyses) {
        if (analysis.extractedData.registrationNumber) {
          const regQuery = query(
            collection(db, 'stores'),
            where('registrationNumber', '==', analysis.extractedData.registrationNumber)
          );
          const regDocs = await getDocs(regQuery);
          
          if (regDocs.size > 0) {
            return {
              hasDuplicates: true,
              details: { duplicateType: 'registration', existingMerchantId: regDocs.docs[0].id }
            };
          }
        }
      }

      return { hasDuplicates: false };
    } catch (error) {
      console.error('خطأ في فحص التكرار:', error);
      return { hasDuplicates: true }; // في حالة الخطأ، نفترض وجود تكرار للأمان
    }
  }

  /**
   * حساب نقاط المخاطر - محسن بالتقييم الديناميكي
   */
  private static calculateRiskScore(
    userData: UserDocument,
    documentAnalyses: AIDocumentAnalysis[],
    duplicateCheck: any
  ): number {
    let riskScore = 0;
    const riskFactors: string[] = [];

    // تقييم مخاطر كل مستند باستخدام الخدمة الديناميكية
    documentAnalyses.forEach(analysis => {
      const documentRisk = DynamicMatchingConfigService.assessRiskFactors(
        analysis.documentType,
        analysis.extractedData,
        userData
      );

      riskScore += documentRisk.riskScore * 0.4; // وزن 40% لمخاطر المستندات
      riskFactors.push(...documentRisk.factors);

      // مخاطر إضافية للثقة المنخفضة
      if (analysis.confidence < 80) riskScore += 15;
      if (!analysis.isValid) riskScore += 25;
      if (analysis.issues.length > 0) riskScore += analysis.issues.length * 3;
    });

    // مخاطر التكرار - وزن عالي
    if (duplicateCheck.hasDuplicates) riskScore += 40;

    // مخاطر البيانات الناقصة
    if (!userData.displayName) riskScore += 8;
    if (!userData.email) riskScore += 12;

    // تطبيق تعديلات السياق
    const contextualAdjustment = this.calculateContextualRiskAdjustment(userData, documentAnalyses);
    riskScore += contextualAdjustment;

    return Math.min(riskScore, 100);
  }

  /**
   * حساب تعديلات المخاطر السياقية
   */
  private static calculateContextualRiskAdjustment(
    userData: UserDocument,
    documentAnalyses: AIDocumentAnalysis[]
  ): number {
    let adjustment = 0;

    // تعديل حسب وقت التقديم
    const hour = new Date().getHours();
    if (hour < 6 || hour > 22) {
      adjustment += 5; // طلبات في أوقات غير عادية
    }

    // تعديل حسب جودة المستندات الإجمالية
    const avgConfidence = documentAnalyses.reduce((sum, doc) => sum + doc.confidence, 0) / documentAnalyses.length;
    if (avgConfidence < 70) {
      adjustment += 10;
    } else if (avgConfidence > 95) {
      adjustment -= 5; // تقليل المخاطر للمستندات عالية الجودة
    }

    return adjustment;
  }

  /**
   * توليد الأسباب
   */
  private static generateReasons(
    result: VerificationResult,
    documentAnalyses: AIDocumentAnalysis[]
  ): string[] {
    const reasons: string[] = [];

    if (!result.nameMatch) {
      reasons.push(`عدم تطابق الاسم (${result.nameMatchConfidence.toFixed(1)}%)`);
    }

    if (!result.documentsConsistent) {
      reasons.push('عدم تناسق البيانات بين المستندات');
    }

    if (!result.duplicateCheck) {
      reasons.push('وجود حساب مكرر في النظام');
    }

    documentAnalyses.forEach((analysis, index) => {
      if (!analysis.isValid) {
        reasons.push(`مشكلة في المستند ${index + 1}: ${analysis.issues.join(', ')}`);
      }
    });

    return reasons;
  }

  /**
   * تحديد الأهلية للموافقة التلقائية - محسن بالمعايير الديناميكية
   */
  private static isEligibleForAutoApproval(
    result: VerificationResult,
    documentAnalyses: AIDocumentAnalysis[]
  ): boolean {
    // الحصول على الإعدادات الديناميكية
    const primaryDocType = documentAnalyses[0]?.documentType || 'commercial_registration';
    const config = DynamicMatchingConfigService.getDocumentTypeConfig(primaryDocType);

    // حساب العتبة الديناميكية
    const dynamicThreshold = DynamicMatchingConfigService.calculateDynamicThreshold(
      primaryDocType,
      {
        documentQuality: documentAnalyses.reduce((sum, doc) => sum + doc.confidence, 0) / documentAnalyses.length,
        userHistory: 75, // افتراضي - يمكن تحسينه لاحقاً
        timeOfDay: new Date().getHours(),
        systemLoad: 50 // افتراضي
      }
    );

    // فحص المعايير الأساسية
    const basicCriteria = (
      result.nameMatch &&
      result.nameMatchConfidence >= dynamicThreshold &&
      result.documentsConsistent &&
      result.duplicateCheck &&
      documentAnalyses.length >= MERCHANT_AUTO_APPROVAL_RULES.requiredDocuments.length
    );

    // فحص معايير المخاطر المحسنة
    const riskCriteria = result.riskScore <= MERCHANT_AUTO_APPROVAL_RULES.maxRiskScore;

    // فحص جودة المستندات بمعايير ديناميكية
    const documentQualityCriteria = documentAnalyses.every(doc => {
      const docConfig = DynamicMatchingConfigService.getDocumentTypeConfig(doc.documentType);
      return doc.isValid && doc.confidence >= docConfig.minThreshold;
    });

    // فحص معايير إضافية للمرحلة الأولى
    const advancedCriteria = this.checkAdvancedEligibilityCriteria(result, documentAnalyses);

    return basicCriteria && riskCriteria && documentQualityCriteria && advancedCriteria;
  }

  /**
   * فحص معايير الأهلية المتقدمة
   */
  private static checkAdvancedEligibilityCriteria(
    result: VerificationResult,
    documentAnalyses: AIDocumentAnalysis[]
  ): boolean {
    // فحص تناسق البيانات المتقدم
    const dataConsistency = this.checkAdvancedDataConsistency(documentAnalyses);

    // فحص جودة استخراج البيانات
    const extractionQuality = this.checkExtractionQuality(documentAnalyses);

    // فحص عدم وجود علامات تحذيرية
    const noWarningFlags = this.checkForWarningFlags(documentAnalyses);

    return dataConsistency && extractionQuality && noWarningFlags;
  }

  /**
   * فحص تناسق البيانات المتقدم
   */
  private static checkAdvancedDataConsistency(documentAnalyses: AIDocumentAnalysis[]): boolean {
    if (documentAnalyses.length < 2) return true;

    // فحص تناسق التواريخ
    const dates = documentAnalyses.map(doc => ({
      issue: doc.extractedData.issueDate,
      expiry: doc.extractedData.expiryDate
    })).filter(d => d.issue && d.expiry);

    for (const dateSet of dates) {
      if (new Date(dateSet.issue) >= new Date(dateSet.expiry)) {
        return false; // تاريخ إصدار بعد تاريخ انتهاء
      }
    }

    return true;
  }

  /**
   * فحص جودة استخراج البيانات
   */
  private static checkExtractionQuality(documentAnalyses: AIDocumentAnalysis[]): boolean {
    return documentAnalyses.every(doc => {
      // فحص وجود البيانات الأساسية
      const hasEssentialData = doc.extractedData.ownerName &&
                              doc.extractedData.registrationNumber;

      // فحص طول البيانات المعقول
      const reasonableDataLength = doc.extractedData.ownerName?.length >= 3 &&
                                  doc.extractedData.registrationNumber?.length >= 5;

      return hasEssentialData && reasonableDataLength;
    });
  }

  /**
   * فحص عدم وجود علامات تحذيرية
   */
  private static checkForWarningFlags(documentAnalyses: AIDocumentAnalysis[]): boolean {
    const warningKeywords = [
      'مزور', 'مزيف', 'غير صحيح', 'منتهي', 'ملغي', 'موقوف',
      'fake', 'invalid', 'expired', 'cancelled', 'suspended'
    ];

    return documentAnalyses.every(doc => {
      const ocrText = doc.ocrText?.toLowerCase() || '';
      return !warningKeywords.some(keyword => ocrText.includes(keyword));
    });
  }

  /**
   * اتخاذ قرار الموافقة التلقائية
   */
  static async makeAutoApprovalDecision(
    merchantUid: string,
    userData: UserDocument,
    storeData: StoreDocument
  ): Promise<AutoApprovalDecision> {
    try {
      // تحليل المستندات
      const documentAnalyses: AIDocumentAnalysis[] = [];
      
      if (storeData.commercialRegistrationURL) {
        const analysis = await this.analyzeDocument(
          storeData.commercialRegistrationURL,
          'commercial_registration'
        );
        documentAnalyses.push(analysis);
      }

      if (storeData.freelanceDocumentURL) {
        const analysis = await this.analyzeDocument(
          storeData.freelanceDocumentURL,
          'freelance_document'
        );
        documentAnalyses.push(analysis);
      }

      // التحقق من البيانات
      const verification = await this.verifyMerchantData(userData, documentAnalyses);

      // اتخاذ القرار
      let decision: 'approve' | 'reject' | 'manual_review' = 'manual_review';
      let confidence = 0;
      const reasons: string[] = [];
      const riskFactors: string[] = [];
      const recommendations: string[] = [];

      if (verification.autoApprovalEligible) {
        decision = 'approve';
        confidence = Math.min(verification.nameMatchConfidence, 
          Math.min(...documentAnalyses.map(d => d.confidence)));
        reasons.push('جميع المعايير مستوفاة للموافقة التلقائية');
      } else if (verification.riskScore > 70) {
        decision = 'reject';
        confidence = 90;
        reasons.push('نقاط مخاطر عالية');
        reasons.push(...verification.reasons);
      } else {
        decision = 'manual_review';
        confidence = 60;
        reasons.push('يتطلب مراجعة يدوية');
        reasons.push(...verification.reasons);
      }

      // تحديد عوامل المخاطر
      if (verification.riskScore > 30) {
        riskFactors.push(`نقاط مخاطر: ${verification.riskScore}`);
      }
      if (!verification.duplicateCheck) {
        riskFactors.push('حساب مكرر محتمل');
      }

      // التوصيات
      if (decision === 'manual_review') {
        recommendations.push('مراجعة المستندات يدوياً');
        recommendations.push('التحقق من هوية المتقدم');
        if (verification.nameMatchConfidence < 90) {
          recommendations.push('التأكد من تطابق الأسماء');
        }
      }

      return {
        decision,
        confidence,
        reasons,
        extractedData: documentAnalyses.reduce((acc, doc) => ({
          ...acc,
          [doc.documentType]: doc.extractedData
        }), {}),
        riskFactors,
        recommendations
      };

    } catch (error) {
      console.error('خطأ في اتخاذ قرار الموافقة:', error);
      return {
        decision: 'manual_review',
        confidence: 0,
        reasons: ['خطأ في النظام'],
        extractedData: {},
        riskFactors: ['خطأ تقني'],
        recommendations: ['مراجعة يدوية مطلوبة']
      };
    }
  }

  /**
   * تطبيق قرار الموافقة التلقائية
   */
  static async applyAutoApprovalDecision(
    merchantUid: string,
    decision: AutoApprovalDecision
  ): Promise<boolean> {
    try {
      const storeDocRef = doc(db, 'stores', merchantUid);
      
      const updateData: any = {
        aiAnalysis: {
          decision: decision.decision,
          confidence: decision.confidence,
          reasons: decision.reasons,
          extractedData: decision.extractedData,
          riskFactors: decision.riskFactors,
          processedAt: serverTimestamp(),
          version: '1.0'
        },
        updatedAt: serverTimestamp()
      };

      if (decision.decision === 'approve') {
        updateData.approvalStatus = 'approved';
        updateData.isActive = true;
        updateData.approvalDate = serverTimestamp();
        updateData.approvalNotes = `موافقة تلقائية بالذكاء الاصطناعي (ثقة: ${decision.confidence}%)`;
        updateData.reviewedBy = 'ai-system';
      } else if (decision.decision === 'reject') {
        updateData.approvalStatus = 'rejected';
        updateData.isActive = false;
        updateData.approvalDate = serverTimestamp();
        updateData.approvalNotes = `رفض تلقائي: ${decision.reasons.join(', ')}`;
        updateData.reviewedBy = 'ai-system';
      }
      // في حالة manual_review، نترك الحالة pending

      await updateDoc(storeDocRef, updateData);
      return true;
    } catch (error) {
      console.error('خطأ في تطبيق قرار الموافقة:', error);
      return false;
    }
  }
}
