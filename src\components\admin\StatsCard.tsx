// src/components/admin/StatsCard.tsx
"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  LucideIcon 
} from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  loading?: boolean;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
}

export function StatsCard({
  title,
  value,
  icon,
  description,
  trend,
  loading = false,
  className,
  variant = 'default'
}: StatsCardProps) {
  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-20 mb-2" />
          <Skeleton className="h-3 w-32" />
        </CardContent>
      </Card>
    );
  }

  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}م`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}ك`;
      }
      return val.toLocaleString('ar-SA');
    }
    return val;
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    if (trend.value === 0) {
      return <Minus className="h-3 w-3" />;
    }
    
    return trend.isPositive ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    );
  };

  const getTrendColor = () => {
    if (!trend) return '';
    
    if (trend.value === 0) return 'text-muted-foreground';
    return trend.isPositive ? 'text-green-600' : 'text-red-600';
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50/50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50/50';
      case 'destructive':
        return 'border-red-200 bg-red-50/50';
      default:
        return '';
    }
  };

  return (
    <Card className={cn("transition-all hover:shadow-md", getVariantStyles(), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-1">
          {formatValue(value)}
        </div>
        
        <div className="flex items-center justify-between">
          {description && (
            <p className="text-xs text-muted-foreground flex-1">
              {description}
            </p>
          )}
          
          {trend && (
            <Badge 
              variant="outline" 
              className={cn(
                "text-xs flex items-center gap-1 border-0 bg-transparent p-0",
                getTrendColor()
              )}
            >
              {getTrendIcon()}
              <span>
                {Math.abs(trend.value)}% {trend.period}
              </span>
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// مكون مخصص للإحصائيات المالية
interface FinancialStatsCardProps {
  title: string;
  amount: number;
  currency?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  loading?: boolean;
  className?: string;
}

export function FinancialStatsCard({
  title,
  amount,
  currency = 'ريال',
  icon,
  trend,
  loading = false,
  className
}: FinancialStatsCardProps) {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <StatsCard
      title={title}
      value={formatCurrency(amount)}
      icon={icon}
      trend={trend}
      loading={loading}
      className={className}
      variant="success"
    />
  );
}

// مكون مخصص للنسب المئوية
interface PercentageStatsCardProps {
  title: string;
  percentage: number;
  icon: React.ReactNode;
  description?: string;
  target?: number;
  loading?: boolean;
  className?: string;
}

export function PercentageStatsCard({
  title,
  percentage,
  icon,
  description,
  target,
  loading = false,
  className
}: PercentageStatsCardProps) {
  const getVariant = () => {
    if (!target) return 'default';
    
    if (percentage >= target) return 'success';
    if (percentage >= target * 0.8) return 'warning';
    return 'destructive';
  };

  const getDescription = () => {
    if (description) return description;
    if (target) {
      return `الهدف: ${target}%`;
    }
    return undefined;
  };

  return (
    <StatsCard
      title={title}
      value={`${percentage.toFixed(1)}%`}
      icon={icon}
      description={getDescription()}
      loading={loading}
      className={className}
      variant={getVariant()}
    />
  );
}

// مكون مجموعة الإحصائيات
interface StatsGridProps {
  children: React.ReactNode;
  columns?: 2 | 3 | 4;
  className?: string;
}

export function StatsGrid({ 
  children, 
  columns = 4, 
  className 
}: StatsGridProps) {
  const getGridCols = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2';
      case 3:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
    }
  };

  return (
    <div className={cn(
      "grid gap-4",
      getGridCols(),
      className
    )}>
      {children}
    </div>
  );
}

// مكون إحصائيات مقارنة
interface ComparisonStatsCardProps {
  title: string;
  currentValue: number;
  previousValue: number;
  icon: React.ReactNode;
  period: string;
  loading?: boolean;
  className?: string;
}

export function ComparisonStatsCard({
  title,
  currentValue,
  previousValue,
  icon,
  period,
  loading = false,
  className
}: ComparisonStatsCardProps) {
  const change = currentValue - previousValue;
  const changePercentage = previousValue > 0 ? (change / previousValue) * 100 : 0;
  const isPositive = change >= 0;

  const trend = {
    value: Math.abs(changePercentage),
    isPositive,
    period
  };

  return (
    <StatsCard
      title={title}
      value={currentValue}
      icon={icon}
      description={`السابق: ${previousValue.toLocaleString('ar-SA')}`}
      trend={trend}
      loading={loading}
      className={className}
    />
  );
}
