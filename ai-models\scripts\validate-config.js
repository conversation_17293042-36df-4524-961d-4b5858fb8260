#!/usr/bin/env node
// سكريبت التحقق من تكوين الذكاء الاصطناعي

const fs = require('fs');
const path = require('path');

// مسارات المجلدات
const AI_DIR = path.join(__dirname, '..');
const CONFIGS_DIR = path.join(AI_DIR, 'configs');

// الملفات المطلوبة
const REQUIRED_FILES = [
  'configs/ai-config.json',
  'utils/ai-manager.js',
  '.env.example'
];

// دالة فحص وجود الملفات
function checkFileExists(filePath) {
  try {
    const fullPath = path.join(AI_DIR, filePath);
    const stats = fs.statSync(fullPath);
    return {
      exists: true,
      size: stats.size,
      isFile: stats.isFile(),
      lastModified: stats.mtime,
      path: fullPath
    };
  } catch (error) {
    return {
      exists: false,
      size: 0,
      isFile: false,
      error: error.message,
      path: path.join(AI_DIR, filePath)
    };
  }
}

// دالة التحقق من صحة JSON
function validateJSON(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const parsed = JSON.parse(content);
    return {
      valid: true,
      data: parsed,
      size: content.length
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message,
      size: 0
    };
  }
}

// دالة التحقق من التكوين
function validateAIConfig() {
  console.log('🔍 التحقق من تكوين الذكاء الاصطناعي...\n');
  
  const results = {
    configValid: false,
    filesValid: true,
    totalSize: 0,
    fileDetails: {},
    warnings: [],
    errors: [],
    recommendations: []
  };

  // فحص الملفات المطلوبة
  console.log('📁 فحص الملفات المطلوبة:');
  for (const file of REQUIRED_FILES) {
    const fileInfo = checkFileExists(file);
    results.fileDetails[file] = fileInfo;
    results.totalSize += fileInfo.size;
    
    if (fileInfo.exists) {
      const sizeMB = (fileInfo.size / 1024 / 1024).toFixed(3);
      console.log(`  ✅ ${file}: ${sizeMB} MB`);
      
      // التحقق من ملفات JSON
      if (file.endsWith('.json')) {
        const jsonValidation = validateJSON(fileInfo.path);
        if (jsonValidation.valid) {
          console.log(`    📝 JSON صحيح`);
          
          // التحقق من محتوى ai-config.json
          if (file === 'configs/ai-config.json') {
            const config = jsonValidation.data;
            results.configValid = validateConfigContent(config, results);
          }
        } else {
          results.errors.push(`ملف JSON تالف: ${file} - ${jsonValidation.error}`);
          results.filesValid = false;
          console.log(`    ❌ JSON تالف: ${jsonValidation.error}`);
        }
      }
    } else {
      results.errors.push(`ملف مفقود: ${file}`);
      results.filesValid = false;
      console.log(`  ❌ مفقود: ${file}`);
    }
  }
  
  return results;
}

// دالة التحقق من محتوى التكوين
function validateConfigContent(config, results) {
  console.log('\n⚙️  التحقق من محتوى التكوين:');
  
  let isValid = true;
  
  // فحص الإصدار
  if (!config.version) {
    results.errors.push('إصدار التكوين غير محدد');
    isValid = false;
  } else {
    console.log(`  ✅ الإصدار: ${config.version}`);
  }
  
  // فحص المزودين
  if (!config.providers || Object.keys(config.providers).length === 0) {
    results.errors.push('لا توجد مزودين للذكاء الاصطناعي');
    isValid = false;
  } else {
    const enabledProviders = Object.entries(config.providers)
      .filter(([_, provider]) => provider.enabled)
      .map(([name, _]) => name);
    
    if (enabledProviders.length === 0) {
      results.warnings.push('لا توجد مزودين مفعلين');
    } else {
      console.log(`  ✅ المزودين المفعلين: ${enabledProviders.join(', ')}`);
    }
  }
  
  // فحص إعدادات المعالجة
  if (!config.processing) {
    results.errors.push('إعدادات المعالجة غير موجودة');
    isValid = false;
  } else {
    const requiredProcessing = ['textAnalysis', 'ocr', 'validation'];
    for (const proc of requiredProcessing) {
      if (!config.processing[proc]) {
        results.warnings.push(`إعدادات ${proc} غير موجودة`);
      } else {
        console.log(`  ✅ ${proc}: ${config.processing[proc].provider}`);
      }
    }
  }
  
  // فحص قوالب المستندات
  if (!config.documentTemplates) {
    results.warnings.push('قوالب المستندات غير موجودة');
  } else {
    const templateCount = Object.keys(config.documentTemplates).length;
    console.log(`  ✅ قوالب المستندات: ${templateCount} قالب`);
  }
  
  // فحص المفردات العربية
  if (!config.arabicVocabulary) {
    results.warnings.push('المفردات العربية غير موجودة');
  } else {
    console.log(`  ✅ المفردات العربية: موجودة`);
  }
  
  return isValid;
}

// دالة فحص التوافق مع Netlify
function checkNetlifyCompatibility(totalSize) {
  console.log('\n🌐 فحص التوافق مع Netlify:');
  
  const results = {
    isCompatible: true,
    warnings: [],
    recommendations: []
  };

  const sizeMB = totalSize / 1024 / 1024;
  console.log(`📊 الحجم الإجمالي: ${sizeMB.toFixed(3)} MB`);
  
  // بدون نماذج محلية، الحجم سيكون صغير جداً
  if (sizeMB < 1) {
    console.log(`✅ حجم مثالي لـ Netlify (${sizeMB.toFixed(3)} MB)`);
    results.recommendations.push('النظام محسن للنشر السحابي');
  } else if (sizeMB < 10) {
    console.log(`✅ حجم جيد لـ Netlify (${sizeMB.toFixed(3)} MB)`);
  } else {
    results.warnings.push(`حجم كبير نسبياً: ${sizeMB.toFixed(3)} MB`);
  }
  
  // فحص هيكل المجلدات
  const requiredDirs = ['configs', 'utils', 'scripts'];
  for (const dir of requiredDirs) {
    const dirPath = path.join(AI_DIR, dir);
    if (fs.existsSync(dirPath)) {
      console.log(`✅ مجلد موجود: ${dir}`);
    } else {
      results.warnings.push(`مجلد مفقود: ${dir}`);
    }
  }
  
  return results;
}

// دالة إنشاء التقرير النهائي
function generateReport(configResults, netlifyResults) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      configValid: configResults.configValid,
      filesValid: configResults.filesValid,
      netlifyCompatible: netlifyResults.isCompatible,
      totalSize: configResults.totalSize,
      totalSizeMB: (configResults.totalSize / 1024 / 1024).toFixed(3)
    },
    files: configResults.fileDetails,
    allWarnings: [
      ...configResults.warnings,
      ...netlifyResults.warnings
    ],
    allErrors: configResults.errors,
    recommendations: netlifyResults.recommendations
  };
  
  return report;
}

// دالة طباعة التقرير النهائي
function printFinalReport(report) {
  console.log('\n' + '='.repeat(60));
  console.log('📋 تقرير التحقق النهائي');
  console.log('='.repeat(60));
  
  // الملخص
  console.log('\n📊 الملخص:');
  console.log(`- التكوين صحيح: ${report.summary.configValid ? '✅ نعم' : '❌ لا'}`);
  console.log(`- الملفات موجودة: ${report.summary.filesValid ? '✅ نعم' : '❌ لا'}`);
  console.log(`- الحجم الإجمالي: ${report.summary.totalSizeMB} MB`);
  console.log(`- متوافق مع Netlify: ${report.summary.netlifyCompatible ? '✅ نعم' : '❌ لا'}`);
  
  // التحذيرات
  if (report.allWarnings.length > 0) {
    console.log('\n⚠️  التحذيرات:');
    report.allWarnings.forEach(warning => {
      console.log(`  - ${warning}`);
    });
  }
  
  // الأخطاء
  if (report.allErrors.length > 0) {
    console.log('\n❌ الأخطاء:');
    report.allErrors.forEach(error => {
      console.log(`  - ${error}`);
    });
  }
  
  // التوصيات
  if (report.recommendations.length > 0) {
    console.log('\n💡 التوصيات:');
    report.recommendations.forEach(rec => {
      console.log(`  - ${rec}`);
    });
  }
  
  // النتيجة النهائية
  const isOverallValid = report.summary.configValid && 
                        report.summary.filesValid && 
                        report.summary.netlifyCompatible;
  
  console.log('\n' + '='.repeat(60));
  if (isOverallValid) {
    console.log('🎉 النظام جاهز للنشر على Netlify!');
    console.log('🚀 لا توجد نماذج محلية - سرعة فائقة في البناء!');
  } else {
    console.log('⚠️  يتطلب إصلاحات قبل النشر');
  }
  console.log('='.repeat(60));
  
  return isOverallValid;
}

// دالة حفظ التقرير
function saveReport(report) {
  try {
    const reportPath = path.join(AI_DIR, 'validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 تم حفظ التقرير في: ${reportPath}`);
  } catch (error) {
    console.error('❌ فشل حفظ التقرير:', error.message);
  }
}

// الدالة الرئيسية
function main() {
  console.log('🚀 بدء التحقق من نظام الذكاء الاصطناعي الجديد\n');
  
  try {
    // التحقق من التكوين
    const configResults = validateAIConfig();
    
    // فحص التوافق مع Netlify
    const netlifyResults = checkNetlifyCompatibility(configResults.totalSize);
    
    // إنشاء التقرير
    const report = generateReport(configResults, netlifyResults);
    
    // طباعة التقرير النهائي
    const isValid = printFinalReport(report);
    
    // حفظ التقرير
    saveReport(report);
    
    // إنهاء البرنامج مع الكود المناسب
    process.exit(isValid ? 0 : 1);
    
  } catch (error) {
    console.error('\n❌ خطأ في التحقق:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { main, validateAIConfig };
