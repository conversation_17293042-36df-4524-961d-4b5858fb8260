'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Flag, AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useReviews } from '@/hooks/useReviews';
import type { ReviewReport } from '@/types';

interface ReportReviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  reviewId: string | null;
}

export function ReportReviewDialog({ 
  open, 
  onOpenChange, 
  reviewId 
}: ReportReviewDialogProps) {
  const t = useTranslations();
  const { reportReview } = useReviews({ autoFetch: false });
  
  const [reason, setReason] = useState<ReviewReport['reason']>('inappropriate');
  const [description, setDescription] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const reportReasons: { value: ReviewReport['reason']; label: string; description: string }[] = [
    {
      value: 'spam',
      label: t('reviews.reportReasons.spam'),
      description: t('reviews.reportReasons.spamDescription')
    },
    {
      value: 'inappropriate',
      label: t('reviews.reportReasons.inappropriate'),
      description: t('reviews.reportReasons.inappropriateDescription')
    },
    {
      value: 'fake',
      label: t('reviews.reportReasons.fake'),
      description: t('reviews.reportReasons.fakeDescription')
    },
    {
      value: 'offensive',
      label: t('reviews.reportReasons.offensive'),
      description: t('reviews.reportReasons.offensiveDescription')
    },
    {
      value: 'other',
      label: t('reviews.reportReasons.other'),
      description: t('reviews.reportReasons.otherDescription')
    }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reviewId) return;

    setSubmitting(true);
    
    try {
      const success = await reportReview(
        reviewId, 
        reason, 
        description.trim() || undefined
      );
      
      if (success) {
        onOpenChange(false);
        setReason('inappropriate');
        setDescription('');
      }
    } catch (error) {
      console.error('Error submitting report:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!submitting) {
      onOpenChange(false);
      setReason('inappropriate');
      setDescription('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 rtl:space-x-reverse">
            <Flag className="h-5 w-5 text-red-500" />
            <span>{t('reviews.reportReview')}</span>
          </DialogTitle>
          <DialogDescription>
            {t('reviews.reportReviewDescription')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <Label className="text-base font-medium">
              {t('reviews.reportReason')} *
            </Label>
            
            <RadioGroup
              value={reason}
              onValueChange={(value) => setReason(value as ReviewReport['reason'])}
              className="space-y-3"
            >
              {reportReasons.map((reasonOption) => (
                <div key={reasonOption.value} className="space-y-2">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <RadioGroupItem 
                      value={reasonOption.value} 
                      id={reasonOption.value}
                    />
                    <Label 
                      htmlFor={reasonOption.value}
                      className="font-medium cursor-pointer"
                    >
                      {reasonOption.label}
                    </Label>
                  </div>
                  <p className="text-sm text-gray-600 mr-6 rtl:ml-6">
                    {reasonOption.description}
                  </p>
                </div>
              ))}
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-base font-medium">
              {t('reviews.additionalDetails')} ({t('reviews.optional')})
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t('reviews.reportDescriptionPlaceholder')}
              className="min-h-[100px] resize-none"
              maxLength={500}
            />
            <div className="text-xs text-gray-500 text-right rtl:text-left">
              {description.length}/500
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">
                  {t('reviews.reportWarningTitle')}
                </p>
                <p>
                  {t('reviews.reportWarningDescription')}
                </p>
              </div>
            </div>
          </div>

          <DialogFooter className="flex space-x-3 rtl:space-x-reverse">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={submitting}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              variant="destructive"
              disabled={submitting}
            >
              {submitting ? t('reviews.submittingReport') : t('reviews.submitReport')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
