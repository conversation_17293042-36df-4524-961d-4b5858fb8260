"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle } from "lucide-react";
import type { SubscriptionPlan } from '@/types';
import { useLocale } from '@/hooks/use-locale';
import { Badge } from "@/components/ui/badge";

interface SubscriptionCardProps {
  plan: SubscriptionPlan;
}

export default function SubscriptionCard({ plan }: SubscriptionCardProps) {
  const { t } = useLocale();

  const formatPrice = () => {
    if (plan.priceDisplayKey === 'free') {
      return t('free');
    }
    if (plan.priceValue !== undefined && plan.currencyKey && plan.periodKey) {
      return t(plan.priceDisplayKey, {
        price: plan.priceValue,
        currency: t(plan.currencyKey),
        period: t(plan.periodKey)
      });
    }
    return '';
  };

  return (
    <Card className={`flex flex-col ${plan.isPopular ? 'border-primary border-2 shadow-primary/30 shadow-lg' : 'shadow-md'} hover:shadow-xl transition-shadow duration-300`}>
      {plan.isPopular && (
        <Badge variant="default" className="absolute -top-3 start-1/2 -translate-x-1/2 bg-primary text-primary-foreground">
          {t('popular')}
        </Badge>
      )}
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-bold text-center text-primary">{t(plan.nameKey)}</CardTitle>
        <CardDescription className="text-4xl font-extrabold text-center text-foreground pt-2">
          {formatPrice()}
        </CardDescription>
        {plan.commission !== undefined && plan.commissionKey && (
           <p className="text-sm text-muted-foreground text-center pt-1">
             {t(plan.commissionKey, { value: plan.commission })}
           </p>
        )}
      </CardHeader>
      <CardContent className="flex-grow">
        <ul className="space-y-3">
          {plan.features.map((feature) => (
            <li key={feature.nameKey} className="flex items-center justify-center space-x-2 rtl:space-x-reverse text-center">
              {feature.available ? (
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
              )}
              <span className={`text-sm ${feature.available ? 'text-foreground' : 'text-muted-foreground line-through'}`}>
                {t(feature.nameKey)}
              </span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button className={`w-full ${plan.isPopular ? 'bg-primary hover:bg-primary/90 text-primary-foreground' : 'bg-accent hover:bg-accent/90 text-accent-foreground'}`}>
          {t(plan.ctaKey)}
        </Button>
      </CardFooter>
    </Card>
  );
}
