/// <reference types="cypress" />

/**
 * اختبارات شاملة للوحة تحكم التاجر
 * تختبر جميع وظائف إدارة المتجر والمنتجات والطلبات بشكل متقدم
 */

describe('🏪 لوحة تحكم التاجر - الاختبارات الشاملة', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('merchant')
    cy.mockFirebaseAuth()
    cy.mockCoupons()
    cy.mockCRMCustomers()
    cy.mockLoyaltyProgram()
    
    // زيارة لوحة تحكم التاجر
    cy.visitWithLocale('/merchant/dashboard')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📊 لوحة التحكم المتقدمة', () => {
    it('يجب أن تعرض تحليلات متقدمة للأداء', () => {
      // التحقق من وجود مؤشرات الأداء الرئيسية
      cy.get('[data-testid="kpi-dashboard"]').should('be.visible')
      cy.get('[data-testid="revenue-growth"]').should('be.visible')
      cy.get('[data-testid="customer-acquisition"]').should('be.visible')
      cy.get('[data-testid="conversion-rate"]').should('be.visible')
      cy.get('[data-testid="average-order-value"]').should('be.visible')
      
      // التحقق من الرسوم البيانية التفاعلية
      cy.get('[data-testid="interactive-sales-chart"]').should('be.visible')
      cy.get('[data-testid="product-performance-chart"]').should('be.visible')
      cy.get('[data-testid="customer-behavior-chart"]').should('be.visible')
    })

    it('يجب أن تعرض تنبيهات ذكية', () => {
      // التحقق من التنبيهات الذكية
      cy.get('[data-testid="smart-alerts"]').should('be.visible')
      cy.shouldContainArabicText('التنبيهات الذكية')
      
      // تنبيهات المخزون
      cy.get('[data-testid="inventory-alerts"]').should('be.visible')
      cy.shouldContainArabicText('منتجات تحتاج إعادة تخزين')
      
      // تنبيهات الطلبات
      cy.get('[data-testid="order-alerts"]').should('be.visible')
      cy.shouldContainArabicText('طلبات تحتاج معالجة')
      
      // تنبيهات العملاء
      cy.get('[data-testid="customer-alerts"]').should('be.visible')
      cy.shouldContainArabicText('عملاء يحتاجون متابعة')
    })

    it('يجب أن تعرض توقعات المبيعات', () => {
      // التحقق من قسم التوقعات
      cy.get('[data-testid="sales-forecast"]').should('be.visible')
      cy.shouldContainArabicText('توقعات المبيعات')
      
      // توقعات الأسبوع القادم
      cy.get('[data-testid="weekly-forecast"]').should('be.visible')
      cy.get('[data-testid="forecast-amount"]').should('be.visible')
      cy.get('[data-testid="forecast-confidence"]').should('be.visible')
      
      // توقعات الشهر القادم
      cy.get('[data-testid="monthly-forecast"]').should('be.visible')
    })

    it('يجب أن تعرض مقارنات الأداء', () => {
      // مقارنة مع الفترة السابقة
      cy.get('[data-testid="performance-comparison"]').should('be.visible')
      cy.shouldContainArabicText('مقارنة الأداء')
      
      // نسب النمو
      cy.get('[data-testid="growth-rates"]').should('be.visible')
      cy.get('[data-testid="revenue-growth-rate"]').should('contain.text', '%')
      cy.get('[data-testid="orders-growth-rate"]').should('contain.text', '%')
      cy.get('[data-testid="customers-growth-rate"]').should('contain.text', '%')
    })
  })

  describe('📦 إدارة المنتجات المتقدمة', () => {
    beforeEach(() => {
      cy.visitWithLocale('/merchant/products')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن يدير المنتجات بالجملة', () => {
      // تحديد منتجات متعددة
      cy.get('[data-testid="select-all-products"]').check()
      
      // إجراءات الجملة
      cy.get('[data-testid="bulk-actions"]').should('be.visible')
      cy.get('[data-testid="bulk-edit-price"]').should('be.visible')
      cy.get('[data-testid="bulk-update-stock"]').should('be.visible')
      cy.get('[data-testid="bulk-change-status"]').should('be.visible')
      
      // تحديث أسعار بالجملة
      cy.get('[data-testid="bulk-edit-price"]').click()
      cy.get('[data-testid="price-adjustment"]').select('increase')
      cy.get('[data-testid="adjustment-value"]').type('10')
      cy.get('[data-testid="adjustment-type"]').select('percentage')
      cy.get('[data-testid="apply-bulk-price"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث الأسعار بنجاح')
    })

    it('يجب أن يستورد منتجات من ملف CSV', () => {
      // فتح نموذج الاستيراد
      cy.get('[data-testid="import-products"]').click()
      
      // رفع ملف CSV
      cy.get('[data-testid="csv-file-input"]').selectFile('cypress/fixtures/products.csv')
      
      // معاينة البيانات
      cy.get('[data-testid="preview-import"]').click()
      cy.get('[data-testid="import-preview"]').should('be.visible')
      
      // تأكيد الاستيراد
      cy.get('[data-testid="confirm-import"]').click()
      
      // التحقق من نجاح الاستيراد
      cy.get('[data-testid="import-progress"]').should('be.visible')
      cy.get('[data-testid="import-success"]', { timeout: 30000 }).should('be.visible')
      cy.shouldContainArabicText('تم استيراد المنتجات بنجاح')
    })

    it('يجب أن يصدر منتجات إلى ملف CSV', () => {
      // تصدير المنتجات
      cy.get('[data-testid="export-products"]').click()
      
      // اختيار خيارات التصدير
      cy.get('[data-testid="export-format"]').select('csv')
      cy.get('[data-testid="include-images"]').check()
      cy.get('[data-testid="include-inventory"]').check()
      
      // بدء التصدير
      cy.get('[data-testid="start-export"]').click()
      
      // التحقق من بدء التصدير
      cy.get('[data-testid="export-progress"]').should('be.visible')
    })

    it('يجب أن يدير متغيرات المنتج', () => {
      // فتح منتج يحتوي على متغيرات
      cy.get('[data-testid="product-with-variants"]').first().click()
      
      // إضافة متغير جديد
      cy.get('[data-testid="add-variant"]').click()
      cy.fillForm({
        'variant-name': 'اللون الأزرق',
        'variant-price': '2600',
        'variant-stock': '25',
        'variant-sku': 'PHONE-001-BLUE'
      })
      
      // حفظ المتغير
      cy.get('[data-testid="save-variant"]').click()
      
      // التحقق من إضافة المتغير
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة المتغير بنجاح')
    })

    it('يجب أن يدير صور المنتجات', () => {
      // فتح معرض الصور
      cy.get('[data-testid="product-row"]').first().within(() => {
        cy.get('[data-testid="manage-images"]').click()
      })
      
      // رفع صور جديدة
      cy.get('[data-testid="upload-images"]').selectFile([
        'cypress/fixtures/product-image-3.jpg',
        'cypress/fixtures/product-image-4.jpg'
      ])
      
      // ترتيب الصور
      cy.get('[data-testid="image-item"]').first()
        .drag('[data-testid="image-item"]').eq(1)
      
      // تعيين صورة رئيسية
      cy.get('[data-testid="image-item"]').first().within(() => {
        cy.get('[data-testid="set-main-image"]').click()
      })
      
      // حفظ التغييرات
      cy.get('[data-testid="save-images"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
    })
  })

  describe('📋 إدارة الطلبات المتقدمة', () => {
    beforeEach(() => {
      cy.visitWithLocale('/merchant/orders')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن يدير الطلبات بالجملة', () => {
      // تحديد طلبات متعددة
      cy.get('[data-testid="select-multiple-orders"]').check()
      
      // إجراءات الجملة
      cy.get('[data-testid="bulk-order-actions"]').should('be.visible')
      cy.get('[data-testid="bulk-update-status"]').should('be.visible')
      cy.get('[data-testid="bulk-print-invoices"]').should('be.visible')
      cy.get('[data-testid="bulk-export-orders"]').should('be.visible')
      
      // تحديث حالة بالجملة
      cy.get('[data-testid="bulk-update-status"]').click()
      cy.get('[data-testid="new-bulk-status"]').select('processing')
      cy.get('[data-testid="bulk-status-note"]').type('تم بدء تجهيز الطلبات')
      cy.get('[data-testid="apply-bulk-status"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث حالة الطلبات بنجاح')
    })

    it('يجب أن يطبع فواتير متعددة', () => {
      // تحديد طلبات للطباعة
      cy.get('[data-testid="order-checkbox"]').eq(0).check()
      cy.get('[data-testid="order-checkbox"]').eq(1).check()
      
      // طباعة الفواتير
      cy.get('[data-testid="bulk-print-invoices"]').click()
      
      // التحقق من فتح نافذة الطباعة
      cy.window().then((win) => {
        cy.stub(win, 'print').as('windowPrint')
      })
      
      cy.get('@windowPrint').should('have.been.called')
    })

    it('يجب أن يتتبع الشحنات', () => {
      // فتح تفاصيل طلب مشحون
      cy.get('[data-testid="shipped-order"]').first().click()
      
      // التحقق من معلومات الشحن
      cy.get('[data-testid="shipping-info"]').should('be.visible')
      cy.get('[data-testid="tracking-number"]').should('be.visible')
      cy.get('[data-testid="shipping-company"]').should('be.visible')
      cy.get('[data-testid="estimated-delivery"]').should('be.visible')
      
      // تحديث معلومات الشحن
      cy.get('[data-testid="update-shipping"]').click()
      cy.fillForm({
        'tracking-number': 'TRK123456789',
        'shipping-notes': 'الطلب في الطريق للعميل'
      })
      
      // حفظ التحديث
      cy.get('[data-testid="save-shipping-update"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
    })

    it('يجب أن يدير المرتجعات والاستبدالات', () => {
      // الانتقال إلى قسم المرتجعات
      cy.get('[data-testid="returns-tab"]').click()
      
      // عرض طلبات الإرجاع
      cy.get('[data-testid="return-requests"]').should('be.visible')
      
      // معالجة طلب إرجاع
      cy.get('[data-testid="return-request"]').first().within(() => {
        cy.get('[data-testid="process-return"]').click()
      })
      
      // اتخاذ قرار بشأن الإرجاع
      cy.get('[data-testid="return-decision"]').select('approve')
      cy.get('[data-testid="return-reason"]').type('المنتج معيب')
      cy.get('[data-testid="refund-amount"]').type('250')
      
      // تأكيد الإرجاع
      cy.get('[data-testid="confirm-return"]').click()
      
      // التحقق من نجاح المعالجة
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم معالجة طلب الإرجاع بنجاح')
    })
  })

  describe('📊 التقارير والتحليلات المتقدمة', () => {
    beforeEach(() => {
      cy.visitWithLocale('/merchant/reports')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن ينشئ تقارير مخصصة', () => {
      // إنشاء تقرير مخصص
      cy.get('[data-testid="create-custom-report"]').click()
      
      // اختيار نوع التقرير
      cy.get('[data-testid="report-type"]').select('sales_analysis')
      
      // تحديد المعايير
      cy.get('[data-testid="date-range"]').select('custom')
      cy.get('[data-testid="start-date"]').type('2024-01-01')
      cy.get('[data-testid="end-date"]').type('2024-12-31')
      
      // اختيار المقاييس
      cy.get('[data-testid="include-revenue"]').check()
      cy.get('[data-testid="include-orders"]').check()
      cy.get('[data-testid="include-customers"]').check()
      cy.get('[data-testid="include-products"]').check()
      
      // إنشاء التقرير
      cy.get('[data-testid="generate-report"]').click()
      
      // التحقق من إنشاء التقرير
      cy.get('[data-testid="report-results"]').should('be.visible')
      cy.get('[data-testid="report-charts"]').should('be.visible')
    })

    it('يجب أن يحلل أداء المنتجات', () => {
      // الانتقال إلى تحليل المنتجات
      cy.get('[data-testid="product-analytics"]').click()
      
      // عرض أفضل المنتجات أداءً
      cy.get('[data-testid="top-performers"]').should('be.visible')
      cy.get('[data-testid="product-ranking"]').should('be.visible')
      
      // عرض المنتجات الأقل أداءً
      cy.get('[data-testid="underperformers"]').should('be.visible')
      
      // تحليل الاتجاهات
      cy.get('[data-testid="product-trends"]').should('be.visible')
      cy.get('[data-testid="seasonal-analysis"]').should('be.visible')
    })

    it('يجب أن يحلل سلوك العملاء', () => {
      // الانتقال إلى تحليل العملاء
      cy.get('[data-testid="customer-analytics"]').click()
      
      // تحليل دورة حياة العميل
      cy.get('[data-testid="customer-lifecycle"]').should('be.visible')
      cy.get('[data-testid="acquisition-funnel"]').should('be.visible')
      cy.get('[data-testid="retention-analysis"]').should('be.visible')
      
      // تحليل القيمة الدائمة للعميل
      cy.get('[data-testid="customer-ltv"]').should('be.visible')
      cy.get('[data-testid="ltv-segments"]').should('be.visible')
      
      // تحليل أنماط الشراء
      cy.get('[data-testid="purchase-patterns"]').should('be.visible')
      cy.get('[data-testid="frequency-analysis"]').should('be.visible')
    })

    it('يجب أن يحلل الأداء المالي', () => {
      // الانتقال إلى التحليل المالي
      cy.get('[data-testid="financial-analytics"]').click()
      
      // تحليل الربحية
      cy.get('[data-testid="profitability-analysis"]').should('be.visible')
      cy.get('[data-testid="profit-margins"]').should('be.visible')
      cy.get('[data-testid="cost-analysis"]').should('be.visible')
      
      // تحليل التدفق النقدي
      cy.get('[data-testid="cash-flow"]').should('be.visible')
      cy.get('[data-testid="revenue-trends"]').should('be.visible')
      
      // مقارنة الأداء
      cy.get('[data-testid="performance-benchmarks"]').should('be.visible')
    })
  })

  describe('🔧 الأتمتة والتكامل', () => {
    it('يجب أن يدير قواعد الأتمتة', () => {
      // الانتقال إلى إعدادات الأتمتة
      cy.visitWithLocale('/merchant/automation')
      cy.waitForLoadingToFinish()
      
      // إنشاء قاعدة أتمتة جديدة
      cy.get('[data-testid="create-automation-rule"]').click()
      
      // تحديد المحفز
      cy.get('[data-testid="trigger-type"]').select('low_stock')
      cy.get('[data-testid="trigger-threshold"]').type('5')
      
      // تحديد الإجراء
      cy.get('[data-testid="action-type"]').select('send_notification')
      cy.get('[data-testid="notification-message"]').type('تنبيه: مخزون منخفض')
      
      // حفظ القاعدة
      cy.get('[data-testid="save-automation-rule"]').click()
      
      // التحقق من إنشاء القاعدة
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء قاعدة الأتمتة بنجاح')
    })

    it('يجب أن يدير التكاملات الخارجية', () => {
      // الانتقال إلى صفحة التكاملات
      cy.visitWithLocale('/merchant/integrations')
      cy.waitForLoadingToFinish()
      
      // التحقق من التكاملات المتاحة
      cy.get('[data-testid="available-integrations"]').should('be.visible')
      cy.get('[data-testid="erp-integrations"]').should('be.visible')
      cy.get('[data-testid="pos-integrations"]').should('be.visible')
      cy.get('[data-testid="payment-integrations"]').should('be.visible')
      
      // تفعيل تكامل جديد
      cy.get('[data-testid="activate-integration"]').first().click()
      
      // ملء معلومات التكامل
      cy.fillForm({
        'integration-name': 'تكامل نظام المحاسبة',
        'api-endpoint': 'https://api.accounting-system.com',
        'api-key': 'test-api-key-12345'
      })
      
      // اختبار الاتصال
      cy.get('[data-testid="test-connection"]').click()
      cy.get('[data-testid="connection-success"]').should('be.visible')
      
      // حفظ التكامل
      cy.get('[data-testid="save-integration"]').click()
      
      // التحقق من نجاح التفعيل
      cy.get('[data-testid="success-message"]').should('be.visible')
    })
  })

  describe('📱 التطبيق المحمول والإشعارات', () => {
    it('يجب أن يدير إعدادات الإشعارات', () => {
      // الانتقال إلى إعدادات الإشعارات
      cy.visitWithLocale('/merchant/notifications')
      cy.waitForLoadingToFinish()
      
      // تخصيص إعدادات الإشعارات
      cy.get('[data-testid="email-notifications"]').check()
      cy.get('[data-testid="sms-notifications"]').check()
      cy.get('[data-testid="push-notifications"]').check()
      
      // تحديد أنواع الإشعارات
      cy.get('[data-testid="new-order-notification"]').check()
      cy.get('[data-testid="low-stock-notification"]').check()
      cy.get('[data-testid="payment-notification"]').check()
      
      // حفظ الإعدادات
      cy.get('[data-testid="save-notification-settings"]').click()
      
      // التحقق من نجاح الحفظ
      cy.get('[data-testid="success-message"]').should('be.visible')
    })

    it('يجب أن يعمل على الأجهزة المحمولة', () => {
      // اختبار التجاوب على الهاتف
      cy.viewport('iphone-x')
      
      // التحقق من التجاوب
      cy.get('[data-testid="merchant-dashboard"]').should('be.visible')
      cy.get('[data-testid="mobile-navigation"]').should('be.visible')
      
      // اختبار القائمة المحمولة
      cy.get('[data-testid="mobile-menu-toggle"]').click()
      cy.get('[data-testid="mobile-menu"]').should('be.visible')
      
      // اختبار الإحصائيات على الهاتف
      cy.get('[data-testid="mobile-stats"]').should('be.visible')
      cy.get('[data-testid="mobile-charts"]').should('be.visible')
    })
  })
})
