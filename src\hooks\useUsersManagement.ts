'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  collection, 
  query, 
  orderBy, 
  limit, 
  startAfter, 
  getDocs, 
  where,
  doc,
  updateDoc,
  deleteDoc,
  getCountFromServer
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserDocument, UserType } from '@/types';

export interface UserFilters {
  userType?: UserType | 'all';
  status?: 'active' | 'inactive' | 'all';
  registrationDateFrom?: Date;
  registrationDateTo?: Date;
  verificationStatus?: 'verified' | 'unverified' | 'all';
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  totalCustomers: number;
  totalMerchants: number;
  totalRepresentatives: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
}

export interface ExtendedUserDocument extends UserDocument {
  id: string;
  lastLoginAt?: Date;
  isActive?: boolean;
  totalOrders?: number;
  totalSpent?: number;
  averageRating?: number;
  loyaltyPoints?: number;
  complaintsCount?: number;
  reportsCount?: number;
}

export function useUsersManagement() {
  const [users, setUsers] = useState<ExtendedUserDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [filters, setFilters] = useState<UserFilters>({ userType: 'all', status: 'all' });
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState<any>(null);

  // جلب إحصائيات المستخدمين
  const fetchUserStats = useCallback(async () => {
    try {
      const usersRef = collection(db, 'users');
      
      // إجمالي المستخدمين
      const totalUsersSnapshot = await getCountFromServer(usersRef);
      const totalUsers = totalUsersSnapshot.data().count;

      // العملاء
      const customersQuery = query(usersRef, where('userType', '==', 'customer'));
      const customersSnapshot = await getCountFromServer(customersQuery);
      const totalCustomers = customersSnapshot.data().count;

      // التجار
      const merchantsQuery = query(usersRef, where('userType', '==', 'merchant'));
      const merchantsSnapshot = await getCountFromServer(merchantsQuery);
      const totalMerchants = merchantsSnapshot.data().count;

      // المندوبين
      const representativesQuery = query(usersRef, where('userType', '==', 'representative'));
      const representativesSnapshot = await getCountFromServer(representativesQuery);
      const totalRepresentatives = representativesSnapshot.data().count;

      // المستخدمين الجدد (اليوم)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const newUsersTodayQuery = query(
        usersRef, 
        where('createdAt', '>=', today)
      );
      const newUsersTodaySnapshot = await getCountFromServer(newUsersTodayQuery);
      const newUsersToday = newUsersTodaySnapshot.data().count;

      // المستخدمين الجدد (هذا الأسبوع)
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      const newUsersWeekQuery = query(
        usersRef, 
        where('createdAt', '>=', weekAgo)
      );
      const newUsersWeekSnapshot = await getCountFromServer(newUsersWeekQuery);
      const newUsersThisWeek = newUsersWeekSnapshot.data().count;

      // المستخدمين الجدد (هذا الشهر)
      const monthAgo = new Date();
      monthAgo.setDate(monthAgo.getDate() - 30);
      const newUsersMonthQuery = query(
        usersRef, 
        where('createdAt', '>=', monthAgo)
      );
      const newUsersMonthSnapshot = await getCountFromServer(newUsersMonthQuery);
      const newUsersThisMonth = newUsersMonthSnapshot.data().count;

      setStats({
        totalUsers,
        activeUsers: totalUsers, // يمكن تحسين هذا لاحقاً
        totalCustomers,
        totalMerchants,
        totalRepresentatives,
        newUsersToday,
        newUsersThisWeek,
        newUsersThisMonth
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  }, []);

  // جلب المستخدمين
  const fetchUsers = useCallback(async (reset = false) => {
    try {
      setLoading(true);
      setError(null);

      const usersRef = collection(db, 'users');
      let q = query(usersRef, orderBy('createdAt', 'desc'));

      // تطبيق المرشحات
      if (filters.userType && filters.userType !== 'all') {
        q = query(q, where('userType', '==', filters.userType));
      }

      // تطبيق البحث
      if (searchQuery.trim()) {
        // البحث في الاسم أو البريد الإلكتروني
        // ملاحظة: Firestore لا يدعم البحث النصي المتقدم، لذا نحتاج لحل بديل
        // يمكن استخدام Algolia أو تنفيذ البحث في الواجهة الأمامية
      }

      // التصفح التدريجي
      if (!reset && lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      q = query(q, limit(20));

      const snapshot = await getDocs(q);
      const newUsers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        // إضافة بيانات إضافية يمكن جلبها من مجموعات أخرى
        isActive: true, // يمكن تحديد هذا بناءً على آخر نشاط
        totalOrders: 0, // يمكن جلبه من مجموعة الطلبات
        totalSpent: 0, // يمكن حسابه من الطلبات
        averageRating: 0, // يمكن جلبه من التقييمات
        loyaltyPoints: 0, // يمكن جلبه من نظام النقاط
        complaintsCount: 0, // يمكن جلبه من الشكاوى
        reportsCount: 0 // يمكن جلبه من التقارير
      })) as ExtendedUserDocument[];

      if (reset) {
        setUsers(newUsers);
      } else {
        setUsers(prev => [...prev, ...newUsers]);
      }

      setHasMore(snapshot.docs.length === 20);
      setLastDoc(snapshot.docs[snapshot.docs.length - 1]);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('حدث خطأ أثناء جلب بيانات المستخدمين');
    } finally {
      setLoading(false);
    }
  }, [filters, searchQuery, lastDoc]);

  // تحديث المستخدم
  const updateUser = useCallback(async (userId: string, updates: Partial<UserDocument>) => {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        ...updates,
        updatedAt: new Date()
      });
      
      // تحديث القائمة المحلية
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, ...updates } : user
      ));
      
      return true;
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  }, []);

  // حذف المستخدم
  const deleteUser = useCallback(async (userId: string) => {
    try {
      const userRef = doc(db, 'users', userId);
      await deleteDoc(userRef);
      
      // إزالة من القائمة المحلية
      setUsers(prev => prev.filter(user => user.id !== userId));
      
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }, []);

  // تصدير المستخدمين
  const exportUsers = useCallback(async () => {
    try {
      // تحويل البيانات إلى CSV
      const csvData = users.map(user => ({
        'الاسم': user.displayName || 'غير محدد',
        'البريد الإلكتروني': user.email || 'غير محدد',
        'نوع المستخدم': user.userType,
        'تاريخ التسجيل': user.createdAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد',
        'الحالة': user.isActive ? 'نشط' : 'غير نشط',
        'إجمالي الطلبات': user.totalOrders || 0,
        'إجمالي المبلغ المنفق': user.totalSpent || 0
      }));

      // تحويل إلى CSV وتنزيل
      const csvContent = convertToCSV(csvData);
      downloadCSV(csvContent, 'users-export.csv');
    } catch (error) {
      console.error('Error exporting users:', error);
    }
  }, [users]);

  // تحديث البيانات
  const refreshUsers = useCallback(() => {
    setLastDoc(null);
    fetchUsers(true);
    fetchUserStats();
  }, [fetchUsers, fetchUserStats]);

  // تحميل البيانات الأولية
  useEffect(() => {
    fetchUsers(true);
    fetchUserStats();
  }, []);

  // إعادة تحميل عند تغيير المرشحات أو البحث
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setLastDoc(null);
      fetchUsers(true);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters, searchQuery]);

  return {
    users,
    loading,
    error,
    stats,
    filters,
    selectedUsers,
    searchQuery,
    hasMore,
    setSearchQuery,
    setFilters,
    setSelectedUsers,
    updateUser,
    deleteUser,
    exportUsers,
    refreshUsers,
    loadMore: () => fetchUsers(false)
  };
}

// دوال مساعدة
function convertToCSV(data: any[]): string {
  if (data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
  ];
  
  return csvRows.join('\n');
}

function downloadCSV(content: string, filename: string): void {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
