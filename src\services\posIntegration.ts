import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  POSIntegration, 
  IntegrationLog, 
  SyncResult, 
  ProductDocument,
  OrderDocument 
} from '@/types';

/**
 * خدمة تكامل أنظمة POS
 * تدير الاتصال والمزامنة مع أنظمة نقاط البيع المختلفة
 */
export class POSIntegrationService {
  private readonly integrationsCollection = collection(db, 'posIntegrations');
  private readonly logsCollection = collection(db, 'integrationLogs');

  /**
   * إنشاء تكامل POS جديد
   */
  async createIntegration(integrationData: Omit<POSIntegration, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      // التحقق من صحة البيانات
      await this.validateConfiguration(integrationData.configuration, integrationData.systemType);

      const newIntegration: Omit<POSIntegration, 'id'> = {
        ...integrationData,
        status: 'disconnected',
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      };

      const docRef = await addDoc(this.integrationsCollection, newIntegration);
      
      // تسجيل العملية
      await this.logOperation(
        integrationData.merchantId,
        'pos',
        docRef.id,
        'create',
        'integration',
        undefined,
        'success',
        'تم إنشاء تكامل POS جديد'
      );

      return docRef.id;
    } catch (error) {
      console.error('Error creating POS integration:', error);
      throw new Error('فشل في إنشاء تكامل POS');
    }
  }

  /**
   * اختبار الاتصال مع نظام POS
   */
  async testConnection(integrationId: string): Promise<boolean> {
    try {
      const integration = await this.getIntegration(integrationId);
      if (!integration) {
        throw new Error('التكامل غير موجود');
      }

      // محاولة الاتصال حسب نوع النظام
      const isConnected = await this.performConnectionTest(integration);
      
      // تحديث حالة التكامل
      await this.updateIntegrationStatus(
        integrationId, 
        isConnected ? 'connected' : 'error',
        isConnected ? undefined : 'فشل في الاتصال'
      );

      // تسجيل العملية
      await this.logOperation(
        integration.merchantId,
        'pos',
        integrationId,
        'sync',
        'integration',
        undefined,
        isConnected ? 'success' : 'error',
        isConnected ? 'تم الاتصال بنجاح' : 'فشل في الاتصال'
      );

      return isConnected;
    } catch (error) {
      console.error('Error testing POS connection:', error);
      throw new Error('فشل في اختبار الاتصال');
    }
  }

  /**
   * مزامنة البيانات مع نظام POS
   */
  async syncData(integrationId: string, entityType: 'products' | 'sales' | 'customers' | 'payments'): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      const integration = await this.getIntegration(integrationId);
      if (!integration) {
        throw new Error('التكامل غير موجود');
      }

      if (!integration.isActive || integration.status !== 'connected') {
        throw new Error('التكامل غير نشط أو غير متصل');
      }

      // تحديث حالة التكامل إلى "جاري المزامنة"
      await this.updateIntegrationStatus(integrationId, 'syncing');

      let result: SyncResult;

      // تنفيذ المزامنة حسب نوع البيانات
      switch (entityType) {
        case 'products':
          result = await this.syncProducts(integration);
          break;
        case 'sales':
          result = await this.syncSales(integration);
          break;
        case 'customers':
          result = await this.syncCustomers(integration);
          break;
        case 'payments':
          result = await this.syncPayments(integration);
          break;
        default:
          throw new Error('نوع البيانات غير مدعوم');
      }

      // تحديث وقت آخر مزامنة
      await this.updateLastSyncTime(integrationId);
      
      // تحديث حالة التكامل
      await this.updateIntegrationStatus(
        integrationId, 
        result.success ? 'connected' : 'error',
        result.success ? undefined : 'فشل في المزامنة'
      );

      // تسجيل العملية
      await this.logOperation(
        integration.merchantId,
        'pos',
        integrationId,
        'sync',
        entityType as any,
        undefined,
        result.success ? 'success' : 'error',
        `مزامنة ${entityType}: ${result.successfulRecords}/${result.totalRecords} نجحت`,
        result,
        Date.now() - startTime
      );

      return result;
    } catch (error) {
      console.error(`Error syncing ${entityType}:`, error);
      
      // تحديث حالة التكامل إلى خطأ
      await this.updateIntegrationStatus(integrationId, 'error', error.message);
      
      const failedResult: SyncResult = {
        success: false,
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        errors: [{ record: null, error: error.message }],
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };

      return failedResult;
    }
  }

  /**
   * الحصول على تكامل POS
   */
  async getIntegration(integrationId: string): Promise<POSIntegration | null> {
    try {
      const docRef = doc(this.integrationsCollection, integrationId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as POSIntegration;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting POS integration:', error);
      throw new Error('فشل في جلب بيانات التكامل');
    }
  }

  /**
   * الحصول على جميع تكاملات التاجر
   */
  async getMerchantIntegrations(merchantId: string): Promise<POSIntegration[]> {
    try {
      const q = query(
        this.integrationsCollection,
        where('merchantId', '==', merchantId),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as POSIntegration[];
    } catch (error) {
      console.error('Error getting merchant integrations:', error);
      throw new Error('فشل في جلب تكاملات التاجر');
    }
  }

  /**
   * تحديث تكامل POS
   */
  async updateIntegration(integrationId: string, updates: Partial<POSIntegration>): Promise<void> {
    try {
      const docRef = doc(this.integrationsCollection, integrationId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating POS integration:', error);
      throw new Error('فشل في تحديث التكامل');
    }
  }

  /**
   * حذف تكامل POS
   */
  async deleteIntegration(integrationId: string): Promise<void> {
    try {
      const integration = await this.getIntegration(integrationId);
      if (!integration) {
        throw new Error('التكامل غير موجود');
      }

      await deleteDoc(doc(this.integrationsCollection, integrationId));
      
      // تسجيل العملية
      await this.logOperation(
        integration.merchantId,
        'pos',
        integrationId,
        'delete',
        'integration',
        undefined,
        'success',
        'تم حذف تكامل POS'
      );
    } catch (error) {
      console.error('Error deleting POS integration:', error);
      throw new Error('فشل في حذف التكامل');
    }
  }

  /**
   * الحصول على سجلات التكامل
   */
  async getIntegrationLogs(
    merchantId: string, 
    integrationId?: string, 
    limitCount: number = 50
  ): Promise<IntegrationLog[]> {
    try {
      let q = query(
        this.logsCollection,
        where('merchantId', '==', merchantId),
        where('integrationType', '==', 'pos')
      );

      if (integrationId) {
        q = query(q, where('integrationId', '==', integrationId));
      }

      q = query(q, orderBy('createdAt', 'desc'), limit(limitCount));
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as IntegrationLog[];
    } catch (error) {
      console.error('Error getting integration logs:', error);
      throw new Error('فشل في جلب سجلات التكامل');
    }
  }

  // الدوال المساعدة الخاصة

  /**
   * التحقق من صحة إعدادات التكامل
   */
  private async validateConfiguration(
    config: POSIntegration['configuration'], 
    systemType: POSIntegration['systemType']
  ): Promise<void> {
    if (!config.apiUrl || !config.apiKey) {
      throw new Error('رابط API ومفتاح API مطلوبان');
    }

    // التحقق من صحة الرابط
    try {
      new URL(config.apiUrl);
    } catch {
      throw new Error('رابط API غير صحيح');
    }

    // تحققات إضافية حسب نوع النظام
    switch (systemType) {
      case 'square':
        if (!config.locationId) {
          throw new Error('معرف الموقع مطلوب لنظام Square');
        }
        break;
      case 'shopify_pos':
        if (!config.storeId) {
          throw new Error('معرف المتجر مطلوب لنظام Shopify POS');
        }
        break;
      // يمكن إضافة المزيد من التحققات هنا
    }
  }

  /**
   * اختبار الاتصال مع النظام
   */
  private async performConnectionTest(integration: POSIntegration): Promise<boolean> {
    try {
      // محاولة استدعاء API بسيط للتحقق من الاتصال
      const endpoint = this.getHealthCheckEndpoint(integration.systemType);
      const response = await fetch(`${integration.configuration.apiUrl}${endpoint}`, {
        method: 'GET',
        headers: this.buildHeaders(integration)
      });

      return response.ok;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  /**
   * بناء headers للطلبات
   */
  private buildHeaders(integration: POSIntegration): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // إضافة headers حسب نوع النظام
    switch (integration.systemType) {
      case 'square':
        headers['Authorization'] = `Bearer ${integration.configuration.accessToken}`;
        headers['Square-Version'] = '2023-10-18';
        break;
      case 'shopify_pos':
        headers['X-Shopify-Access-Token'] = integration.configuration.accessToken || '';
        break;
      default:
        headers['Authorization'] = `Bearer ${integration.configuration.apiKey}`;
    }

    return headers;
  }

  /**
   * الحصول على endpoint للتحقق من الصحة
   */
  private getHealthCheckEndpoint(systemType: POSIntegration['systemType']): string {
    switch (systemType) {
      case 'square':
        return '/v2/locations';
      case 'shopify_pos':
        return '/admin/api/2023-10/shop.json';
      case 'lightspeed':
        return '/API/Account.json';
      default:
        return '/health';
    }
  }

  // دوال المزامنة المتخصصة

  /**
   * مزامنة المنتجات مع نظام POS
   */
  private async syncProducts(integration: POSIntegration): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: Array<{ record: any; error: string }> = [];
    let successfulRecords = 0;
    let totalRecords = 0;

    try {
      if (!integration.syncSettings.syncProducts) {
        throw new Error('مزامنة المنتجات غير مفعلة');
      }

      // جلب المنتجات من نظام POS
      const posProducts = await this.fetchPOSProducts(integration);
      totalRecords = posProducts.length;

      // معالجة كل منتج
      for (const posProduct of posProducts) {
        try {
          // تحويل بيانات المنتج من POS إلى تنسيق النظام
          const productData = this.mapPOSProductToSystem(posProduct, integration);

          // البحث عن المنتج الموجود أو إنشاء جديد
          await this.upsertProduct(productData, integration.merchantId);

          successfulRecords++;
        } catch (error) {
          errors.push({
            record: posProduct,
            error: error.message
          });
        }
      }

      return {
        success: errors.length === 0,
        totalRecords,
        successfulRecords,
        failedRecords: errors.length,
        errors,
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    } catch (error) {
      return {
        success: false,
        totalRecords,
        successfulRecords,
        failedRecords: totalRecords - successfulRecords,
        errors: [{ record: null, error: error.message }],
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    }
  }

  /**
   * مزامنة المبيعات من نظام POS
   */
  private async syncSales(integration: POSIntegration): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: Array<{ record: any; error: string }> = [];
    let successfulRecords = 0;
    let totalRecords = 0;

    try {
      if (!integration.syncSettings.syncSales) {
        throw new Error('مزامنة المبيعات غير مفعلة');
      }

      // جلب المبيعات من نظام POS
      const posSales = await this.fetchPOSSales(integration);
      totalRecords = posSales.length;

      // معالجة كل عملية بيع
      for (const posSale of posSales) {
        try {
          // تحويل بيانات البيع من POS إلى تنسيق النظام
          const orderData = this.mapPOSSaleToSystem(posSale, integration);

          // إنشاء طلب في النظام
          await this.createOrderFromPOSSale(orderData, integration.merchantId);

          successfulRecords++;
        } catch (error) {
          errors.push({
            record: posSale,
            error: error.message
          });
        }
      }

      return {
        success: errors.length === 0,
        totalRecords,
        successfulRecords,
        failedRecords: errors.length,
        errors,
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    } catch (error) {
      return {
        success: false,
        totalRecords,
        successfulRecords,
        failedRecords: totalRecords - successfulRecords,
        errors: [{ record: null, error: error.message }],
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    }
  }

  /**
   * مزامنة العملاء مع نظام POS
   */
  private async syncCustomers(integration: POSIntegration): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: Array<{ record: any; error: string }> = [];
    let successfulRecords = 0;
    let totalRecords = 0;

    try {
      if (!integration.syncSettings.syncCustomers) {
        throw new Error('مزامنة العملاء غير مفعلة');
      }

      // جلب العملاء من نظام POS
      const posCustomers = await this.fetchPOSCustomers(integration);
      totalRecords = posCustomers.length;

      // معالجة كل عميل
      for (const posCustomer of posCustomers) {
        try {
          // تحويل بيانات العميل من POS إلى تنسيق النظام
          const customerData = this.mapPOSCustomerToSystem(posCustomer, integration);

          // البحث عن العميل الموجود أو إنشاء جديد
          await this.upsertCustomer(customerData, integration.merchantId);

          successfulRecords++;
        } catch (error) {
          errors.push({
            record: posCustomer,
            error: error.message
          });
        }
      }

      return {
        success: errors.length === 0,
        totalRecords,
        successfulRecords,
        failedRecords: errors.length,
        errors,
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    } catch (error) {
      return {
        success: false,
        totalRecords,
        successfulRecords,
        failedRecords: totalRecords - successfulRecords,
        errors: [{ record: null, error: error.message }],
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    }
  }

  /**
   * مزامنة المدفوعات من نظام POS
   */
  private async syncPayments(integration: POSIntegration): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: Array<{ record: any; error: string }> = [];
    let successfulRecords = 0;
    let totalRecords = 0;

    try {
      if (!integration.syncSettings.syncPayments) {
        throw new Error('مزامنة المدفوعات غير مفعلة');
      }

      // جلب المدفوعات من نظام POS
      const posPayments = await this.fetchPOSPayments(integration);
      totalRecords = posPayments.length;

      // معالجة كل دفعة
      for (const posPayment of posPayments) {
        try {
          // تحديث حالة الدفع في النظام
          await this.updatePaymentStatus(posPayment, integration.merchantId);

          successfulRecords++;
        } catch (error) {
          errors.push({
            record: posPayment,
            error: error.message
          });
        }
      }

      return {
        success: errors.length === 0,
        totalRecords,
        successfulRecords,
        failedRecords: errors.length,
        errors,
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    } catch (error) {
      return {
        success: false,
        totalRecords,
        successfulRecords,
        failedRecords: totalRecords - successfulRecords,
        errors: [{ record: null, error: error.message }],
        duration: Date.now() - startTime,
        timestamp: serverTimestamp() as Timestamp
      };
    }
  }

  // دوال مساعدة لتكامل POS

  /**
   * جلب المنتجات من نظام POS
   */
  private async fetchPOSProducts(integration: POSIntegration): Promise<any[]> {
    try {
      const endpoint = this.getProductsEndpoint(integration.systemType);
      const response = await fetch(`${integration.configuration.apiUrl}${endpoint}`, {
        method: 'GET',
        headers: this.buildHeaders(integration)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.extractProductsFromResponse(data, integration.systemType);
    } catch (error) {
      throw new Error(`فشل في جلب المنتجات من POS: ${error.message}`);
    }
  }

  /**
   * جلب المبيعات من نظام POS
   */
  private async fetchPOSSales(integration: POSIntegration): Promise<any[]> {
    try {
      const endpoint = this.getSalesEndpoint(integration.systemType);
      const response = await fetch(`${integration.configuration.apiUrl}${endpoint}`, {
        method: 'GET',
        headers: this.buildHeaders(integration)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.extractSalesFromResponse(data, integration.systemType);
    } catch (error) {
      throw new Error(`فشل في جلب المبيعات من POS: ${error.message}`);
    }
  }

  /**
   * جلب العملاء من نظام POS
   */
  private async fetchPOSCustomers(integration: POSIntegration): Promise<any[]> {
    try {
      const endpoint = this.getCustomersEndpoint(integration.systemType);
      const response = await fetch(`${integration.configuration.apiUrl}${endpoint}`, {
        method: 'GET',
        headers: this.buildHeaders(integration)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.extractCustomersFromResponse(data, integration.systemType);
    } catch (error) {
      throw new Error(`فشل في جلب العملاء من POS: ${error.message}`);
    }
  }

  /**
   * جلب المدفوعات من نظام POS
   */
  private async fetchPOSPayments(integration: POSIntegration): Promise<any[]> {
    try {
      const endpoint = this.getPaymentsEndpoint(integration.systemType);
      const response = await fetch(`${integration.configuration.apiUrl}${endpoint}`, {
        method: 'GET',
        headers: this.buildHeaders(integration)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.extractPaymentsFromResponse(data, integration.systemType);
    } catch (error) {
      throw new Error(`فشل في جلب المدفوعات من POS: ${error.message}`);
    }
  }

  /**
   * الحصول على endpoint للمنتجات
   */
  private getProductsEndpoint(systemType: POSIntegration['systemType']): string {
    switch (systemType) {
      case 'square':
        return '/v2/catalog/list';
      case 'shopify_pos':
        return '/admin/api/2023-10/products.json';
      case 'lightspeed':
        return '/API/Item.json';
      default:
        return '/products';
    }
  }

  /**
   * الحصول على endpoint للمبيعات
   */
  private getSalesEndpoint(systemType: POSIntegration['systemType']): string {
    switch (systemType) {
      case 'square':
        return '/v2/orders/search';
      case 'shopify_pos':
        return '/admin/api/2023-10/orders.json';
      case 'lightspeed':
        return '/API/Sale.json';
      default:
        return '/sales';
    }
  }

  /**
   * الحصول على endpoint للعملاء
   */
  private getCustomersEndpoint(systemType: POSIntegration['systemType']): string {
    switch (systemType) {
      case 'square':
        return '/v2/customers/search';
      case 'shopify_pos':
        return '/admin/api/2023-10/customers.json';
      case 'lightspeed':
        return '/API/Customer.json';
      default:
        return '/customers';
    }
  }

  /**
   * الحصول على endpoint للمدفوعات
   */
  private getPaymentsEndpoint(systemType: POSIntegration['systemType']): string {
    switch (systemType) {
      case 'square':
        return '/v2/payments';
      case 'shopify_pos':
        return '/admin/api/2023-10/transactions.json';
      case 'lightspeed':
        return '/API/PaymentType.json';
      default:
        return '/payments';
    }
  }

  /**
   * استخراج المنتجات من الاستجابة
   */
  private extractProductsFromResponse(data: any, systemType: POSIntegration['systemType']): any[] {
    switch (systemType) {
      case 'square':
        return data.objects?.filter((obj: any) => obj.type === 'ITEM') || [];
      case 'shopify_pos':
        return data.products || [];
      case 'lightspeed':
        return data.Item || [];
      default:
        return data.products || data.data || data;
    }
  }

  /**
   * استخراج المبيعات من الاستجابة
   */
  private extractSalesFromResponse(data: any, systemType: POSIntegration['systemType']): any[] {
    switch (systemType) {
      case 'square':
        return data.orders || [];
      case 'shopify_pos':
        return data.orders || [];
      case 'lightspeed':
        return data.Sale || [];
      default:
        return data.sales || data.data || data;
    }
  }

  /**
   * استخراج العملاء من الاستجابة
   */
  private extractCustomersFromResponse(data: any, systemType: POSIntegration['systemType']): any[] {
    switch (systemType) {
      case 'square':
        return data.customers || [];
      case 'shopify_pos':
        return data.customers || [];
      case 'lightspeed':
        return data.Customer || [];
      default:
        return data.customers || data.data || data;
    }
  }

  /**
   * استخراج المدفوعات من الاستجابة
   */
  private extractPaymentsFromResponse(data: any, systemType: POSIntegration['systemType']): any[] {
    switch (systemType) {
      case 'square':
        return data.payments || [];
      case 'shopify_pos':
        return data.transactions || [];
      case 'lightspeed':
        return data.PaymentType || [];
      default:
        return data.payments || data.data || data;
    }
  }

  /**
   * تحويل بيانات المنتج من POS إلى تنسيق النظام
   */
  private mapPOSProductToSystem(posProduct: any, integration: POSIntegration): Partial<ProductDocument> {
    const mapping = integration.fieldMapping.productFields;

    // تحويل حسب نوع النظام
    switch (integration.systemType) {
      case 'square':
        return this.mapSquareProductToSystem(posProduct, mapping);
      case 'shopify_pos':
        return this.mapShopifyProductToSystem(posProduct, mapping);
      default:
        return this.mapGenericProductToSystem(posProduct, mapping);
    }
  }

  /**
   * تحويل منتج Square إلى تنسيق النظام
   */
  private mapSquareProductToSystem(squareProduct: any, mapping: Record<string, string>): Partial<ProductDocument> {
    const itemData = squareProduct.item_data || {};
    const variations = itemData.variations || [];
    const firstVariation = variations[0]?.item_variation_data || {};

    return {
      name: itemData.name || '',
      description: itemData.description || '',
      price: firstVariation.price_money?.amount ? firstVariation.price_money.amount / 100 : 0,
      sku: firstVariation.sku || squareProduct.id,
      category: itemData.category_id || 'عام',
      currency: 'SAR',
      isActive: true,
      stockQuantity: 0, // Square لا يوفر معلومات المخزون في API المنتجات
      imageUrls: [],
      updatedAt: serverTimestamp() as Timestamp
    };
  }

  /**
   * تحويل منتج Shopify إلى تنسيق النظام
   */
  private mapShopifyProductToSystem(shopifyProduct: any, mapping: Record<string, string>): Partial<ProductDocument> {
    const firstVariant = shopifyProduct.variants?.[0] || {};

    return {
      name: shopifyProduct.title || '',
      description: shopifyProduct.body_html || '',
      price: parseFloat(firstVariant.price || 0),
      sku: firstVariant.sku || shopifyProduct.id.toString(),
      category: shopifyProduct.product_type || 'عام',
      currency: 'SAR',
      isActive: shopifyProduct.status === 'active',
      stockQuantity: parseInt(firstVariant.inventory_quantity || 0),
      imageUrls: shopifyProduct.images?.map((img: any) => img.src) || [],
      updatedAt: serverTimestamp() as Timestamp
    };
  }

  /**
   * تحويل منتج عام إلى تنسيق النظام
   */
  private mapGenericProductToSystem(posProduct: any, mapping: Record<string, string>): Partial<ProductDocument> {
    return {
      name: posProduct[mapping.name || 'name'] || posProduct.name,
      description: posProduct[mapping.description || 'description'] || posProduct.description,
      price: parseFloat(posProduct[mapping.price || 'price'] || posProduct.price || 0),
      sku: posProduct[mapping.sku || 'sku'] || posProduct.sku,
      stockQuantity: parseInt(posProduct[mapping.stockQuantity || 'stock'] || posProduct.stock || 0),
      category: posProduct[mapping.category || 'category'] || posProduct.category || 'عام',
      currency: 'SAR',
      isActive: true,
      imageUrls: posProduct[mapping.images || 'images'] || posProduct.images || [],
      updatedAt: serverTimestamp() as Timestamp
    };
  }

  // دوال مساعدة أخرى (مبسطة للمثال)
  private mapPOSSaleToSystem(posSale: any, integration: POSIntegration): Partial<OrderDocument> {
    // تحويل مبسط - يحتاج تطوير أكثر تفصيلاً
    return {
      orderNumber: posSale.id || Date.now().toString(),
      totalAmount: parseFloat(posSale.total || 0),
      status: 'delivered' as any,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp
    };
  }

  private mapPOSCustomerToSystem(posCustomer: any, integration: POSIntegration): any {
    // تحويل مبسط - يحتاج تطوير أكثر تفصيلاً
    return {
      name: posCustomer.name || posCustomer.first_name + ' ' + posCustomer.last_name,
      email: posCustomer.email,
      phone: posCustomer.phone
    };
  }

  // دوال العمليات (مبسطة للمثال)
  private async upsertProduct(productData: Partial<ProductDocument>, merchantId: string): Promise<void> {
    console.log('Upserting POS product:', productData);
  }

  private async upsertCustomer(customerData: any, merchantId: string): Promise<void> {
    console.log('Upserting POS customer:', customerData);
  }

  private async createOrderFromPOSSale(orderData: Partial<OrderDocument>, merchantId: string): Promise<void> {
    console.log('Creating order from POS sale:', orderData);
  }

  private async updatePaymentStatus(posPayment: any, merchantId: string): Promise<void> {
    console.log('Updating payment status:', posPayment);
  }

  private async updateIntegrationStatus(
    integrationId: string,
    status: POSIntegration['status'],
    error?: string
  ): Promise<void> {
    const updates: any = {
      status,
      updatedAt: serverTimestamp()
    };

    if (error) {
      updates.lastError = error;
    } else if (status === 'connected') {
      updates.lastError = null;
    }

    await updateDoc(doc(this.integrationsCollection, integrationId), updates);
  }

  private async updateLastSyncTime(integrationId: string): Promise<void> {
    await updateDoc(doc(this.integrationsCollection, integrationId), {
      'syncSettings.lastSync': serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  }

  private async logOperation(
    merchantId: string,
    integrationType: 'erp' | 'pos',
    integrationId: string,
    operation: IntegrationLog['operation'],
    entityType: IntegrationLog['entityType'],
    entityId?: string,
    status: IntegrationLog['status'] = 'success',
    message: string = '',
    details?: any,
    duration?: number
  ): Promise<void> {
    try {
      const logEntry: Omit<IntegrationLog, 'id'> = {
        merchantId,
        integrationType,
        integrationId,
        operation,
        entityType,
        entityId,
        status,
        message,
        details,
        duration,
        createdAt: serverTimestamp() as Timestamp
      };

      await addDoc(this.logsCollection, logEntry);
    } catch (error) {
      console.error('Error logging operation:', error);
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const posIntegrationService = new POSIntegrationService();
