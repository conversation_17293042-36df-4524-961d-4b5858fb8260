describe('إدارة المستخدمين - صفحة الإدارة', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.mockLogin('admin')
    cy.visitWithLocale('/admin/users')
  })

  it('يجب أن تعرض صفحة إدارة المستخدمين بنجاح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من العنوان الرئيسي
    cy.shouldContainArabicText('إدارة المستخدمين')
    cy.get('[data-testid="users-management-page"]').should('be.visible')
    
    // التحقق من وجود إحصائيات المستخدمين
    cy.get('[data-testid="user-stats-cards"]').should('be.visible')
    cy.get('[data-testid="total-users-stat"]').should('be.visible')
    cy.get('[data-testid="active-users-stat"]').should('be.visible')
    cy.get('[data-testid="new-users-stat"]').should('be.visible')
  })

  it('يجب أن تعرض إحصائيات المستخدمين', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من بطاقات الإحصائيات
    cy.get('[data-testid="user-stats-cards"]').within(() => {
      cy.shouldContainArabicText('إجمالي المستخدمين')
      cy.shouldContainArabicText('المستخدمين النشطين')
      cy.shouldContainArabicText('مستخدمين جدد')
      cy.shouldContainArabicText('التجار')
      cy.shouldContainArabicText('المندوبين')
    })
    
    // التحقق من وجود أرقام الإحصائيات
    cy.get('[data-testid="total-users-count"]').should('contain.text', /\d+/)
    cy.get('[data-testid="active-users-count"]').should('contain.text', /\d+/)
  })

  it('يجب أن تعمل وظيفة البحث عن المستخدمين', () => {
    cy.waitForLoadingToFinish()
    
    // البحث عن مستخدم
    cy.get('[data-testid="users-search-input"]').type('أحمد')
    cy.get('[data-testid="search-button"]').click()
    
    // التحقق من تحديث النتائج
    cy.get('[data-testid="users-list"]').should('be.visible')
    cy.get('[data-testid="search-results-count"]').should('be.visible')
  })

  it('يجب أن تعمل مرشحات المستخدمين', () => {
    cy.waitForLoadingToFinish()
    
    // فتح مرشحات البحث
    cy.get('[data-testid="filters-toggle-button"]').click()
    cy.get('[data-testid="user-filters-panel"]').should('be.visible')
    
    // تطبيق مرشح نوع المستخدم
    cy.get('[data-testid="user-type-filter"]').click()
    cy.get('[data-testid="filter-merchant"]').click()
    
    // تطبيق مرشح الحالة
    cy.get('[data-testid="user-status-filter"]').click()
    cy.get('[data-testid="filter-active"]').click()
    
    // تطبيق المرشحات
    cy.get('[data-testid="apply-filters-button"]').click()
    
    // التحقق من تحديث النتائج
    cy.get('[data-testid="filtered-users-list"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة تحديد المستخدمين', () => {
    cy.waitForLoadingToFinish()
    
    // تحديد مستخدم واحد
    cy.get('[data-testid="user-checkbox-1"]').check()
    cy.get('[data-testid="selected-users-count"]').should('contain.text', '1')
    
    // تحديد جميع المستخدمين
    cy.get('[data-testid="select-all-users"]').check()
    cy.get('[data-testid="bulk-actions-bar"]').should('be.visible')
    
    // إلغاء التحديد
    cy.get('[data-testid="select-all-users"]').uncheck()
    cy.get('[data-testid="bulk-actions-bar"]').should('not.exist')
  })

  it('يجب أن تعمل الإجراءات الجماعية', () => {
    cy.waitForLoadingToFinish()
    
    // تحديد عدة مستخدمين
    cy.get('[data-testid="user-checkbox-1"]').check()
    cy.get('[data-testid="user-checkbox-2"]').check()
    
    // التحقق من ظهور شريط الإجراءات الجماعية
    cy.get('[data-testid="bulk-actions-bar"]').should('be.visible')
    
    // اختبار إجراء التفعيل
    cy.get('[data-testid="bulk-activate-button"]').click()
    cy.get('[data-testid="confirm-bulk-action"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="success-message"]').should('be.visible')
    cy.shouldContainArabicText('تم تفعيل المستخدمين بنجاح')
  })

  it('يجب أن تعمل وظيفة إرسال الإشعارات', () => {
    cy.waitForLoadingToFinish()
    
    // تحديد مستخدمين
    cy.get('[data-testid="user-checkbox-1"]').check()
    cy.get('[data-testid="user-checkbox-2"]').check()
    
    // فتح محرر الإشعارات
    cy.get('[data-testid="send-notification-button"]').click()
    cy.get('[data-testid="notification-composer"]').should('be.visible')
    
    // ملء نموذج الإشعار
    cy.get('[data-testid="notification-title"]').type('إشعار تجريبي')
    cy.get('[data-testid="notification-message"]').type('هذا إشعار تجريبي للمستخدمين المحددين')
    cy.get('[data-testid="notification-type"]').select('info')
    
    // إرسال الإشعار
    cy.get('[data-testid="send-notification-submit"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="notification-success"]').should('be.visible')
    cy.shouldContainArabicText('تم إرسال الإشعار بنجاح')
  })

  it('يجب أن تعمل وظيفة عرض تفاصيل المستخدم', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على مستخدم لعرض التفاصيل
    cy.get('[data-testid="user-row-1"]').click()
    cy.get('[data-testid="user-details-dialog"]').should('be.visible')
    
    // التحقق من محتوى التفاصيل
    cy.get('[data-testid="user-basic-info"]').should('be.visible')
    cy.get('[data-testid="user-activity-history"]').should('be.visible')
    cy.get('[data-testid="user-orders-summary"]').should('be.visible')
    
    // إغلاق النافذة
    cy.get('[data-testid="close-user-details"]').click()
    cy.get('[data-testid="user-details-dialog"]').should('not.exist')
  })

  it('يجب أن تعمل وظيفة تعديل المستخدم', () => {
    cy.waitForLoadingToFinish()
    
    // فتح نافذة التعديل
    cy.get('[data-testid="edit-user-1"]').click()
    cy.get('[data-testid="user-edit-dialog"]').should('be.visible')
    
    // تعديل بيانات المستخدم
    cy.get('[data-testid="edit-user-name"]').clear().type('اسم محدث')
    cy.get('[data-testid="edit-user-email"]').clear().type('<EMAIL>')
    cy.get('[data-testid="edit-user-status"]').select('active')
    
    // حفظ التغييرات
    cy.get('[data-testid="save-user-changes"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="user-update-success"]').should('be.visible')
    cy.shouldContainArabicText('تم تحديث بيانات المستخدم بنجاح')
  })

  it('يجب أن تعمل وظيفة تصدير بيانات المستخدمين', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على زر التصدير
    cy.get('[data-testid="export-users-button"]').click()
    cy.get('[data-testid="export-options-menu"]').should('be.visible')
    
    // اختيار تصدير CSV
    cy.get('[data-testid="export-csv"]').click()
    
    // التحقق من بدء التحميل
    cy.get('[data-testid="export-progress"]').should('be.visible')
    cy.shouldContainArabicText('جاري تصدير البيانات')
  })

  it('يجب أن تعمل وظيفة تحديث البيانات', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على زر التحديث
    cy.get('[data-testid="refresh-users-button"]').click()
    
    // التحقق من إعادة تحميل البيانات
    cy.get('[data-testid="loading-indicator"]').should('be.visible')
    cy.get('[data-testid="loading-indicator"]').should('not.exist')
    
    // التحقق من تحديث الإحصائيات
    cy.get('[data-testid="user-stats-cards"]').should('be.visible')
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في تحميل البيانات
    cy.intercept('GET', '**/api/admin/users**', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('usersError')
    
    cy.visitWithLocale('/admin/users')
    cy.wait('@usersError')
    
    // التحقق من عرض رسالة الخطأ
    cy.get('[data-testid="error-message"]').should('be.visible')
    cy.shouldContainArabicText('حدث خطأ في تحميل البيانات')
    
    // التحقق من وجود زر إعادة المحاولة
    cy.get('[data-testid="retry-button"]').should('be.visible')
  })

  it('يجب أن تعمل على الأجهزة المحمولة', () => {
    cy.viewport('iphone-x')
    cy.waitForLoadingToFinish()
    
    // التحقق من التجاوب
    cy.get('[data-testid="mobile-menu-toggle"]').should('be.visible')
    cy.get('[data-testid="users-management-page"]').should('be.visible')
    
    // اختبار القائمة المحمولة
    cy.get('[data-testid="mobile-menu-toggle"]').click()
    cy.get('[data-testid="mobile-navigation"]').should('be.visible')
  })
})
