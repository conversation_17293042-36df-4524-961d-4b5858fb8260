// src/services/locationService.ts
"use client";

import { doc, updateDoc, serverTimestamp, collection, addDoc, query, where, orderBy, limit, getDocs, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// واجهة بيانات الموقع
export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: Date;
  speed?: number;
  heading?: number;
}

// واجهة بيانات تتبع المندوب
export interface RepresentativeLocation {
  id?: string;
  representativeUid: string;
  location: LocationData;
  isActive: boolean;
  lastUpdated: any;
  orderId?: string; // الطلب الحالي الذي يتم توصيله
  status: 'available' | 'busy' | 'offline';
}

// واجهة بيانات مسار التوصيل
export interface DeliveryRoute {
  id?: string;
  orderId: string;
  representativeUid: string;
  startLocation: LocationData;
  endLocation: LocationData;
  currentLocation: LocationData;
  waypoints: LocationData[];
  estimatedDuration: number; // بالدقائق
  actualDuration?: number;
  distance: number; // بالكيلومتر
  status: 'started' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: any;
  completedAt?: any;
}

class LocationService {
  private watchId: number | null = null;
  private isTracking = false;

  // طلب إذن الموقع
  async requestLocationPermission(): Promise<boolean> {
    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser');
      }

      const permission = await navigator.permissions.query({ name: 'geolocation' });
      
      if (permission.state === 'granted') {
        return true;
      } else if (permission.state === 'prompt') {
        // سيتم طلب الإذن عند أول استخدام
        return new Promise((resolve) => {
          navigator.geolocation.getCurrentPosition(
            () => resolve(true),
            () => resolve(false),
            { timeout: 10000 }
          );
        });
      }
      
      return false;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  }

  // الحصول على الموقع الحالي
  async getCurrentLocation(): Promise<LocationData | null> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve(null);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date(position.timestamp),
            speed: position.coords.speed || undefined,
            heading: position.coords.heading || undefined
          });
        },
        (error) => {
          console.error('Error getting current location:', error);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000 // 1 minute
        }
      );
    });
  }

  // بدء تتبع موقع المندوب
  async startLocationTracking(representativeUid: string, orderId?: string): Promise<boolean> {
    try {
      const hasPermission = await this.requestLocationPermission();
      if (!hasPermission) {
        throw new Error('Location permission denied');
      }

      if (this.isTracking) {
        this.stopLocationTracking();
      }

      this.isTracking = true;

      this.watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date(position.timestamp),
            speed: position.coords.speed || undefined,
            heading: position.coords.heading || undefined
          };

          await this.updateRepresentativeLocation(representativeUid, locationData, orderId);
        },
        (error) => {
          console.error('Location tracking error:', error);
        },
        {
          enableHighAccuracy: true,
          timeout: 30000,
          maximumAge: 30000 // 30 seconds
        }
      );

      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      return false;
    }
  }

  // إيقاف تتبع الموقع
  stopLocationTracking() {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
    this.isTracking = false;
  }

  // تحديث موقع المندوب في قاعدة البيانات
  private async updateRepresentativeLocation(
    representativeUid: string, 
    location: LocationData, 
    orderId?: string
  ) {
    try {
      const locationDoc: RepresentativeLocation = {
        representativeUid,
        location,
        isActive: true,
        lastUpdated: serverTimestamp(),
        orderId,
        status: orderId ? 'busy' : 'available'
      };

      // تحديث الموقع الحالي
      const locationRef = doc(db, 'representativeLocations', representativeUid);
      await updateDoc(locationRef, locationDoc);

      // إضافة نقطة في مسار التتبع إذا كان هناك طلب نشط
      if (orderId) {
        await this.addRouteWaypoint(orderId, representativeUid, location);
      }
    } catch (error) {
      console.error('Error updating representative location:', error);
    }
  }

  // إضافة نقطة في مسار التوصيل
  private async addRouteWaypoint(orderId: string, representativeUid: string, location: LocationData) {
    try {
      await addDoc(collection(db, 'deliveryRoutes', orderId, 'waypoints'), {
        representativeUid,
        location,
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('Error adding route waypoint:', error);
    }
  }

  // بدء مسار توصيل جديد
  async startDeliveryRoute(
    orderId: string, 
    representativeUid: string, 
    startLocation: LocationData,
    endLocation: LocationData
  ): Promise<string | null> {
    try {
      const routeData: Omit<DeliveryRoute, 'id'> = {
        orderId,
        representativeUid,
        startLocation,
        endLocation,
        currentLocation: startLocation,
        waypoints: [startLocation],
        estimatedDuration: await this.calculateEstimatedDuration(startLocation, endLocation),
        distance: this.calculateDistance(startLocation, endLocation),
        status: 'started',
        createdAt: serverTimestamp()
      };

      const routeRef = await addDoc(collection(db, 'deliveryRoutes'), routeData);
      
      // بدء تتبع الموقع
      await this.startLocationTracking(representativeUid, orderId);
      
      return routeRef.id;
    } catch (error) {
      console.error('Error starting delivery route:', error);
      return null;
    }
  }

  // إنهاء مسار التوصيل
  async completeDeliveryRoute(routeId: string, finalLocation: LocationData) {
    try {
      const routeRef = doc(db, 'deliveryRoutes', routeId);
      await updateDoc(routeRef, {
        currentLocation: finalLocation,
        status: 'completed',
        completedAt: serverTimestamp()
      });

      // إيقاف تتبع الموقع
      this.stopLocationTracking();
    } catch (error) {
      console.error('Error completing delivery route:', error);
    }
  }

  // حساب المسافة بين نقطتين (بالكيلومتر)
  calculateDistance(point1: LocationData, point2: LocationData): number {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100; // تقريب لرقمين عشريين
  }

  // تحويل الدرجات إلى راديان
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // حساب الوقت المتوقع للوصول (تقدير بسيط)
  private async calculateEstimatedDuration(start: LocationData, end: LocationData): Promise<number> {
    const distance = this.calculateDistance(start, end);
    const averageSpeed = 30; // متوسط السرعة 30 كم/ساعة في المدينة
    const estimatedHours = distance / averageSpeed;
    return Math.ceil(estimatedHours * 60); // تحويل إلى دقائق
  }

  // جلب موقع المندوب الحالي
  async getRepresentativeLocation(representativeUid: string): Promise<RepresentativeLocation | null> {
    try {
      const locationRef = doc(db, 'representativeLocations', representativeUid);
      const locationDoc = await locationRef.get();
      
      if (locationDoc.exists()) {
        return {
          id: locationDoc.id,
          ...locationDoc.data()
        } as RepresentativeLocation;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting representative location:', error);
      return null;
    }
  }

  // جلب المندوبين القريبين من موقع معين
  async getNearbyRepresentatives(
    location: LocationData, 
    radiusKm: number = 10
  ): Promise<RepresentativeLocation[]> {
    try {
      // هذا تنفيذ مبسط - في الواقع نحتاج لاستخدام Geohash أو خدمة خارجية
      const q = query(
        collection(db, 'representativeLocations'),
        where('isActive', '==', true),
        where('status', '==', 'available')
      );

      const querySnapshot = await getDocs(q);
      const nearbyReps: RepresentativeLocation[] = [];

      querySnapshot.forEach((doc) => {
        const repLocation = {
          id: doc.id,
          ...doc.data()
        } as RepresentativeLocation;

        const distance = this.calculateDistance(location, repLocation.location);
        if (distance <= radiusKm) {
          nearbyReps.push(repLocation);
        }
      });

      // ترتيب حسب المسافة
      return nearbyReps.sort((a, b) => {
        const distanceA = this.calculateDistance(location, a.location);
        const distanceB = this.calculateDistance(location, b.location);
        return distanceA - distanceB;
      });
    } catch (error) {
      console.error('Error getting nearby representatives:', error);
      return [];
    }
  }

  // الاستماع لتحديثات موقع المندوب في الوقت الفعلي
  subscribeToRepresentativeLocation(
    representativeUid: string, 
    callback: (location: RepresentativeLocation | null) => void
  ): () => void {
    const locationRef = doc(db, 'representativeLocations', representativeUid);
    
    return onSnapshot(locationRef, (doc) => {
      if (doc.exists()) {
        callback({
          id: doc.id,
          ...doc.data()
        } as RepresentativeLocation);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('Error listening to representative location:', error);
      callback(null);
    });
  }

  // تحديث حالة المندوب
  async updateRepresentativeStatus(
    representativeUid: string, 
    status: 'available' | 'busy' | 'offline'
  ) {
    try {
      const locationRef = doc(db, 'representativeLocations', representativeUid);
      await updateDoc(locationRef, {
        status,
        lastUpdated: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating representative status:', error);
    }
  }
}

export const locationService = new LocationService();
export default locationService;
