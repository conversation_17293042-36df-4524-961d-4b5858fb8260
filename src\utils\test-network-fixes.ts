// src/utils/test-network-fixes.ts
// ملف اختبار للتأكد من عمل إصلاحات الشبكة

import { 
  getDocumentWithRetry, 
  setDocumentWithRetry,
  testFirestoreConnection,
  clearFirestoreCache 
} from '@/lib/firestore-utils';
import { 
  handleFirestoreError, 
  checkNetworkStatus 
} from '@/lib/firebase';

// اختبار الاتصال بـ Firestore
export const testFirestoreConnectivity = async (): Promise<boolean> => {
  console.log('🧪 Testing Firestore connectivity...');
  
  try {
    const isConnected = await testFirestoreConnection();
    console.log(`✅ Firestore connection test: ${isConnected ? 'SUCCESS' : 'FAILED'}`);
    return isConnected;
  } catch (error) {
    console.error('❌ Firestore connection test failed:', error);
    return false;
  }
};

// اختبار جلب مستند مع إعادة المحاولة
export const testDocumentRetrieval = async (docPath: string): Promise<boolean> => {
  console.log(`🧪 Testing document retrieval: ${docPath}`);
  
  try {
    const result = await getDocumentWithRetry(docPath, {
      retries: 2,
      timeout: 5000,
      enableOffline: true
    });
    
    if (result.error) {
      console.log(`⚠️ Document retrieval completed with error: ${result.error}`);
      return false;
    }
    
    console.log(`✅ Document retrieval test: ${result.exists ? 'FOUND' : 'NOT_FOUND'}`);
    return true;
  } catch (error) {
    console.error('❌ Document retrieval test failed:', error);
    return false;
  }
};

// اختبار معالجة الأخطاء
export const testErrorHandling = (): void => {
  console.log('🧪 Testing error handling...');
  
  // محاكاة أخطاء مختلفة
  const errors = [
    { code: 'unavailable', message: 'Service unavailable' },
    { code: 'permission-denied', message: 'Permission denied' },
    { code: 'not-found', message: 'Document not found' },
    { code: 'deadline-exceeded', message: 'Deadline exceeded' }
  ];
  
  errors.forEach(error => {
    const errorInfo = handleFirestoreError(error, 'test');
    console.log(`📋 Error type: ${error.code} -> ${errorInfo.type} (retry: ${errorInfo.retry})`);
  });
  
  console.log('✅ Error handling test completed');
};

// اختبار حالة الشبكة
export const testNetworkStatus = (): void => {
  console.log('🧪 Testing network status...');
  
  const isOnline = checkNetworkStatus();
  console.log(`📶 Network status: ${isOnline ? 'ONLINE' : 'OFFLINE'}`);
  
  // إضافة مستمعي أحداث الشبكة للاختبار
  if (typeof window !== 'undefined') {
    const handleOnline = () => console.log('🟢 Network: ONLINE');
    const handleOffline = () => console.log('🔴 Network: OFFLINE');
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // إزالة المستمعين بعد 30 ثانية
    setTimeout(() => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      console.log('🧪 Network status test completed');
    }, 30000);
  }
};

// اختبار مسح الـ cache
export const testCacheClear = async (): Promise<void> => {
  console.log('🧪 Testing cache clear...');
  
  try {
    await clearFirestoreCache();
    console.log('✅ Cache clear test: SUCCESS');
  } catch (error) {
    console.error('❌ Cache clear test failed:', error);
  }
};

// تشغيل جميع الاختبارات
export const runAllNetworkTests = async (): Promise<void> => {
  console.log('🚀 Starting comprehensive network tests...');
  console.log('=' .repeat(50));
  
  // اختبار حالة الشبكة
  testNetworkStatus();
  
  // اختبار معالجة الأخطاء
  testErrorHandling();
  
  // اختبار مسح الـ cache
  await testCacheClear();
  
  // اختبار الاتصال بـ Firestore
  const connectivityTest = await testFirestoreConnectivity();
  
  // اختبار جلب مستند (إذا كان الاتصال يعمل)
  if (connectivityTest) {
    await testDocumentRetrieval('test/connection');
  }
  
  console.log('=' .repeat(50));
  console.log('🏁 Network tests completed');
};

// دالة لمحاكاة أخطاء الشبكة (للاختبار)
export const simulateNetworkError = (errorType: 'timeout' | 'offline' | 'permission'): Error => {
  switch (errorType) {
    case 'timeout':
      return new Error('Request timeout');
    case 'offline':
      const offlineError = new Error('Network request failed');
      (offlineError as any).code = 'unavailable';
      return offlineError;
    case 'permission':
      const permissionError = new Error('Permission denied');
      (permissionError as any).code = 'permission-denied';
      return permissionError;
    default:
      return new Error('Unknown error');
  }
};

// دالة لاختبار الأداء
export const testPerformance = async (iterations: number = 5): Promise<void> => {
  console.log(`🧪 Testing performance with ${iterations} iterations...`);
  
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = performance.now();
    
    try {
      await getDocumentWithRetry('test/performance', {
        retries: 1,
        timeout: 3000
      });
    } catch (error) {
      // تجاهل الأخطاء في اختبار الأداء
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    times.push(duration);
    
    console.log(`📊 Iteration ${i + 1}: ${duration.toFixed(2)}ms`);
  }
  
  const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  
  console.log('📈 Performance Results:');
  console.log(`   Average: ${avgTime.toFixed(2)}ms`);
  console.log(`   Min: ${minTime.toFixed(2)}ms`);
  console.log(`   Max: ${maxTime.toFixed(2)}ms`);
};

// تصدير دالة سريعة للاختبار من وحدة التحكم
export const quickTest = async (): Promise<void> => {
  console.log('⚡ Quick network test...');
  
  const isOnline = checkNetworkStatus();
  console.log(`📶 Online: ${isOnline}`);
  
  if (isOnline) {
    const connected = await testFirestoreConnectivity();
    console.log(`🔥 Firestore: ${connected ? 'Connected' : 'Disconnected'}`);
  }
  
  console.log('⚡ Quick test completed');
};

// للاستخدام في وحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).networkTests = {
    runAll: runAllNetworkTests,
    quick: quickTest,
    performance: testPerformance,
    connectivity: testFirestoreConnectivity,
    errorHandling: testErrorHandling,
    networkStatus: testNetworkStatus,
    cacheClear: testCacheClear
  };
  
  console.log('🔧 Network test utilities loaded. Use window.networkTests to access them.');
}
