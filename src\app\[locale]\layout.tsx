import type { ReactNode } from 'react';
import type { Locale } from '@/lib/i18n';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { AuthProvider } from '@/context/AuthContext';
import { getTranslations } from '@/context/locale-context';
import { Toaster } from "@/components/ui/toaster";
import LocaleProvider from '@/components/providers/LocaleProvider';
import NetworkErrorBoundary from '@/components/error/NetworkErrorBoundary';
import { NetworkStatus } from '@/components/ui/loading-states';
import { CartProvider } from '@/components/cart/CartProvider';
import NavigationLogger from '@/components/debug/NavigationLogger';

export async function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'ar' }];
}

export async function generateMetadata({
  params,
}: {
  params: { locale: Locale };
}) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale);

  return {
    title: t('appName'),
    description: t('tagline'),
  };
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: { locale: Locale };
}) {
  // انتظار params قبل استخدام خصائصها
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale); // t is available if needed for layout strings

  return (
    <LocaleProvider locale={locale}>
      <NetworkErrorBoundary>
        <AuthProvider>
          <CartProvider>
            <div className="flex flex-col min-h-screen">
              <Header locale={locale} />
              <main className="flex-grow container mx-auto px-4 py-8">
                {children}
              </main>
              <Footer locale={locale} />
            </div>
            <NetworkStatus />
            <Toaster />
            {process.env.NODE_ENV === 'development' && <NavigationLogger />}
          </CartProvider>
        </AuthProvider>
      </NetworkErrorBoundary>
    </LocaleProvider>
  );
}
