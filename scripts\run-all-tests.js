#!/usr/bin/env node

/**
 * سكريبت لتشغيل جميع اختبارات الترجمات
 */

const { spawn } = require('child_process');
const path = require('path');

/**
 * تشغيل أمر وإرجاع النتيجة
 */
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 تشغيل: ${command} ${args.join(' ')}`);
    
    const process = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ نجح: ${command}`);
        resolve(true);
      } else {
        console.log(`❌ فشل: ${command} (رمز الخطأ: ${code})`);
        resolve(false);
      }
    });
    
    process.on('error', (error) => {
      console.log(`❌ خطأ في تشغيل: ${command}`, error.message);
      resolve(false);
    });
  });
}

/**
 * تشغيل اختبار محدد
 */
async function runTest(testName, command, args = []) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🧪 بدء اختبار: ${testName}`);
  console.log(`${'='.repeat(60)}`);
  
  const success = await runCommand(command, args);
  
  console.log(`\n📊 نتيجة اختبار ${testName}: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  return success;
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🚀 بدء تشغيل جميع اختبارات الترجمات...\n');
  
  const startTime = Date.now();
  
  // قائمة الاختبارات
  const tests = [
    {
      name: 'اختبار صحة ملفات JSON والترجمات الجديدة',
      command: 'node',
      args: ['scripts/test-translations.js']
    },
    {
      name: 'استخراج الترجمات المفقودة',
      command: 'node', 
      args: ['scripts/extract-missing-translations.js']
    },
    {
      name: 'اختبار التطبيق المباشر',
      command: 'node',
      args: ['scripts/simple-app-test.js']
    }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  const results = [];
  
  // تشغيل كل اختبار
  for (const test of tests) {
    try {
      const success = await runTest(test.name, test.command, test.args);
      results.push({ name: test.name, success });
      
      if (success) {
        passedTests++;
      }
    } catch (error) {
      console.log(`❌ خطأ في تشغيل اختبار ${test.name}:`, error.message);
      results.push({ name: test.name, success: false });
    }
  }
  
  // اختبار Cypress (اختياري)
  console.log(`\n${'='.repeat(60)}`);
  console.log('🧪 محاولة تشغيل اختبارات Cypress...');
  console.log(`${'='.repeat(60)}`);
  
  try {
    const cypressSuccess = await runCommand('npx', ['cypress', 'run', '--spec', 'cypress/e2e/translation-validation.cy.ts'], {
      timeout: 60000
    });
    
    if (cypressSuccess) {
      console.log('✅ اختبارات Cypress نجحت');
      results.push({ name: 'اختبارات Cypress', success: true });
      passedTests++;
      totalTests++;
    } else {
      console.log('⚠️  اختبارات Cypress فشلت أو غير متوفرة');
      results.push({ name: 'اختبارات Cypress', success: false });
      totalTests++;
    }
  } catch (error) {
    console.log('⚠️  Cypress غير متوفر أو لم يكتمل التثبيت');
    console.log('💡 يمكن تشغيل: npx cypress install');
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(1);
  
  // النتائج النهائية
  console.log(`\n${'='.repeat(80)}`);
  console.log('📋 ملخص نتائج جميع الاختبارات');
  console.log(`${'='.repeat(80)}`);
  
  console.log('\n📊 تفاصيل النتائج:');
  results.forEach((result, index) => {
    const status = result.success ? '✅ نجح' : '❌ فشل';
    console.log(`   ${index + 1}. ${result.name}: ${status}`);
  });
  
  console.log(`\n📈 الإحصائيات:`);
  console.log(`   🧪 إجمالي الاختبارات: ${totalTests}`);
  console.log(`   ✅ الاختبارات الناجحة: ${passedTests}`);
  console.log(`   ❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
  console.log(`   📊 معدل النجاح: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  console.log(`   ⏱️  الوقت المستغرق: ${duration} ثانية`);
  
  // التوصيات
  console.log(`\n💡 التوصيات:`);
  
  if (passedTests === totalTests) {
    console.log('   🎉 جميع الاختبارات نجحت! الترجمات تعمل بشكل ممتاز.');
    console.log('   🚀 التطبيق جاهز للاستخدام مع الترجمات المحدثة.');
    console.log('   📝 يمكن الآن إضافة الترجمات المتبقية تدريجياً.');
  } else if (passedTests > totalTests / 2) {
    console.log('   ⚠️  معظم الاختبارات نجحت، لكن هناك مشاكل بسيطة.');
    console.log('   🔧 راجع الاختبارات الفاشلة وأصلح المشاكل.');
    console.log('   ✨ الترجمات الأساسية تعمل بشكل جيد.');
  } else {
    console.log('   ❌ هناك مشاكل كبيرة تحتاج إصلاح.');
    console.log('   🔧 راجع جميع الاختبارات الفاشلة.');
    console.log('   📞 قد تحتاج مساعدة إضافية لحل المشاكل.');
  }
  
  console.log(`\n📁 الملفات المفيدة:`);
  console.log('   📄 docs/testing-results-report.md - تقرير مفصل');
  console.log('   📄 docs/missing-translations-keys.json - الترجمات المفقودة');
  console.log('   📄 docs/translation-fix-summary.md - ملخص الإصلاحات');
  
  console.log(`\n🔗 الأوامر المفيدة:`);
  console.log('   node scripts/test-translations.js - اختبار الترجمات');
  console.log('   node scripts/extract-missing-translations.js - استخراج المفقود');
  console.log('   npx cypress open - فتح Cypress للاختبار التفاعلي');
  
  // رمز الخروج
  process.exit(passedTests === totalTests ? 0 : 1);
}

// تشغيل السكريبت
if (require.main === module) {
  main().catch(error => {
    console.error('❌ خطأ عام في تشغيل الاختبارات:', error);
    process.exit(1);
  });
}

module.exports = { runTest, runCommand };
