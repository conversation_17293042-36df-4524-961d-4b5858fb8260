// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// إعدادات عامة للاختبارات
beforeEach(() => {
  // تجاهل الأخطاء غير المهمة
  cy.on('uncaught:exception', (err, runnable) => {
    // منع فشل الاختبار بسبب أخطاء Firebase في بيئة التطوير
    if (err.message.includes('Firebase')) {
      return false
    }
    if (err.message.includes('ResizeObserver')) {
      return false
    }
    if (err.message.includes('Non-Error promise rejection')) {
      return false
    }
    return true
  })
})

// إعدادات خاصة بالمشروع
Cypress.on('window:before:load', (win) => {
  // Mock Firebase في بيئة الاختبار
  win.indexedDB.deleteDatabase('firebaseLocalStorageDb')
  
  // Mock geolocation
  cy.stub(win.navigator.geolocation, 'getCurrentPosition').callsFake((success) => {
    return success({
      coords: {
        latitude: 24.7136,
        longitude: 46.6753,
        accuracy: 100
      }
    })
  })
})
