// src/components/auth/CacheCleaner.tsx
"use client";

import { useEffect } from 'react';

interface CacheCleanerProps {
  clearOnMount?: boolean;
  clearFirebaseOnly?: boolean;
}

export default function CacheCleaner({ 
  clearOnMount = false, 
  clearFirebaseOnly = true 
}: CacheCleanerProps) {
  
  const clearFirebaseCache = () => {
    if (typeof window === 'undefined') return;
    
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🧹 Clearing Firebase cache...');
      }
      
      // مسح مفاتيح Firebase من localStorage
      const firebaseKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('firebase:') || 
        key.includes('authUser') || 
        key.includes('firebase') ||
        key.includes('firebaseui') ||
        key.includes('firebase-heartbeat')
      );
      
      firebaseKeys.forEach(key => {
        localStorage.removeItem(key);
        if (process.env.NODE_ENV === 'development') {
          console.log(`🗑️ Removed: ${key}`);
        }
      });
      
      // مسح sessionStorage
      sessionStorage.clear();
      
      // مسح IndexedDB للـ Firebase (إن وجد)
      if ('indexedDB' in window) {
        try {
          indexedDB.deleteDatabase('firebaseLocalStorageDb');
        } catch (e) {
          console.log('Could not clear IndexedDB:', e);
        }
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Firebase cache cleared successfully');
      }
    } catch (error) {
      console.error('Error clearing Firebase cache:', error);
    }
  };

  const clearAllCache = () => {
    if (typeof window === 'undefined') return;
    
    try {
      console.log('Clearing all cache...');
      
      // مسح جميع البيانات المحلية
      localStorage.clear();
      sessionStorage.clear();
      
      // مسح cookies
      document.cookie.split(";").forEach((c) => {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      
      // مسح Service Worker cache إن وجد
      if ('serviceWorker' in navigator && 'caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
      }
      
      console.log('All cache cleared successfully');
    } catch (error) {
      console.error('Error clearing all cache:', error);
    }
  };

  useEffect(() => {
    if (clearOnMount) {
      if (clearFirebaseOnly) {
        clearFirebaseCache();
      } else {
        clearAllCache();
      }
    }
  }, [clearOnMount, clearFirebaseOnly]);

  // هذا المكون لا يعرض أي شيء
  return null;
}

// وظائف مساعدة يمكن استخدامها في أي مكان
export const clearFirebaseCacheManually = () => {
  if (typeof window === 'undefined') return;
  
  const firebaseKeys = Object.keys(localStorage).filter(key => 
    key.startsWith('firebase:') || 
    key.includes('authUser') || 
    key.includes('firebase')
  );
  
  firebaseKeys.forEach(key => localStorage.removeItem(key));
  sessionStorage.clear();
  
  console.log('Firebase cache cleared manually');
};

export const clearAllCacheManually = () => {
  if (typeof window === 'undefined') return;
  
  localStorage.clear();
  sessionStorage.clear();
  
  document.cookie.split(";").forEach((c) => {
    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
  });
  
  console.log('All cache cleared manually');
};

export const forceReloadWithCacheClear = () => {
  clearAllCacheManually();
  window.location.reload();
};
