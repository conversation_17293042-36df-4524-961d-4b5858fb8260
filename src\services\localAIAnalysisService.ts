// خدمة التحليل المحلي للذكاء الاصطناعي - مِخْلاة
import { modelLoader } from '../../ai-models/utils/model-loader';
import type { AIDocumentAnalysis } from './aiApprovalService';

// أنواع البيانات المحلية
interface LocalAnalysisResult {
  documentType: string;
  extractedData: any;
  confidence: number;
  isValid: boolean;
  issues: string[];
  ocrText: string;
  securityLevel: 'high';
  processingMethod: 'local_ai';
  analysisTime: number;
  modelStats: any;
}

interface DocumentFeatures {
  hasBusinessName: boolean;
  hasOwnerName: boolean;
  hasRegistrationNumber: boolean;
  hasDates: boolean;
  textQuality: number;
  documentStructure: string;
}

// خدمة التحليل المحلي
export class LocalAIAnalysisService {
  private static isInitialized = false;

  /**
   * تهيئة الخدمة
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🤖 تهيئة خدمة التحليل المحلي...');
      await modelLoader.initialize();
      this.isInitialized = true;
      console.log('✅ تم تهيئة خدمة التحليل المحلي');
    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة التحليل المحلي:', error);
      throw error;
    }
  }

  /**
   * تحليل المستند محلياً
   */
  static async analyzeDocument(
    documentUrl: string,
    documentType: string,
    isRepresentative: boolean = false
  ): Promise<LocalAnalysisResult> {
    const startTime = performance.now();

    try {
      console.log(`🔍 بدء التحليل المحلي للمستند: ${documentType}`);

      // تهيئة الخدمة إذا لم تكن مهيأة
      await this.initialize();

      // 1. استخراج النص من المستند
      const ocrText = await this.extractTextFromDocument(documentUrl);
      
      // 2. تصنيف نوع المستند
      const classification = await this.classifyDocument(ocrText);
      
      // 3. استخراج البيانات المنظمة
      const extractedData = await this.extractStructuredData(ocrText, documentType);
      
      // 4. التحقق من صحة البيانات
      const validation = await this.validateExtractedData(extractedData, documentType);
      
      // 5. حساب مستوى الثقة
      const confidence = this.calculateConfidence(extractedData, validation, classification);
      
      // 6. تحديد المشاكل
      const issues = this.identifyIssues(extractedData, validation);

      const analysisTime = performance.now() - startTime;

      const result: LocalAnalysisResult = {
        documentType: classification.documentType || documentType,
        extractedData,
        confidence,
        isValid: validation.isValid,
        issues,
        ocrText,
        securityLevel: 'high',
        processingMethod: 'local_ai',
        analysisTime,
        modelStats: modelLoader.getLoadedModelsStats()
      };

      console.log(`✅ تم التحليل المحلي في ${analysisTime.toFixed(2)}ms`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في التحليل المحلي:', error);
      
      // إرجاع نتيجة افتراضية في حالة الخطأ
      return this.getFallbackAnalysis(documentUrl, documentType, performance.now() - startTime);
    }
  }

  /**
   * استخراج النص من المستند باستخدام OCR حقيقي
   */
  private static async extractTextFromDocument(documentUrl: string): Promise<string> {
    try {
      console.log('📄 استخراج النص من المستند باستخدام OCR...');

      // استخدام OCR حقيقي من النماذج المحلية
      const ocrResult = await modelLoader.performOCR(documentUrl);

      if (ocrResult && ocrResult.text) {
        console.log('✅ تم استخراج النص بنجاح');
        return ocrResult.text;
      }

      // في حالة فشل OCR المحلي، استخدام Tesseract.js كبديل
      return await this.fallbackOCR(documentUrl);

    } catch (error) {
      console.error('❌ خطأ في استخراج النص:', error);

      // محاولة أخيرة باستخدام OCR البديل
      try {
        return await this.fallbackOCR(documentUrl);
      } catch (fallbackError) {
        console.error('❌ فشل في جميع طرق استخراج النص:', fallbackError);
        throw new Error('فشل في استخراج النص من المستند');
      }
    }
  }

  /**
   * OCR بديل باستخدام Tesseract.js
   */
  private static async fallbackOCR(documentUrl: string): Promise<string> {
    try {
      console.log('🔄 استخدام OCR البديل...');

      // تحميل Tesseract.js ديناميكياً
      const { createWorker } = await import('tesseract.js');

      const worker = await createWorker('ara+eng', 1, {
        logger: m => console.log('OCR:', m.status, m.progress)
      });

      // معالجة الصورة
      const { data: { text } } = await worker.recognize(documentUrl);

      // تنظيف العامل
      await worker.terminate();

      console.log('✅ تم استخراج النص باستخدام OCR البديل');
      return text;

    } catch (error) {
      console.error('❌ خطأ في OCR البديل:', error);
      throw new Error('فشل في استخراج النص باستخدام جميع طرق OCR');
    }
  }

  /**
   * تصنيف نوع المستند
   */
  private static async classifyDocument(text: string): Promise<any> {
    try {
      console.log('🏷️ تصنيف نوع المستند...');
      
      // استخدام النموذج المحلي للتصنيف
      const classification = await modelLoader.classifyDocument(text);
      
      return classification;
    } catch (error) {
      console.error('❌ خطأ في تصنيف المستند:', error);
      
      // تصنيف بسيط كبديل
      if (text.includes('سجل تجاري')) {
        return { documentType: 'commercial_registration', confidence: 0.9 };
      } else if (text.includes('عمل حر')) {
        return { documentType: 'freelance_document', confidence: 0.85 };
      } else if (text.includes('رخصة قيادة')) {
        return { documentType: 'driving_license', confidence: 0.88 };
      }
      
      return { documentType: 'unknown', confidence: 0.5 };
    }
  }

  /**
   * استخراج البيانات المنظمة
   */
  private static async extractStructuredData(text: string, documentType: string): Promise<any> {
    try {
      console.log('📊 استخراج البيانات المنظمة...');
      
      // استخدام النموذج المحلي لاستخراج الكيانات
      const entities = await modelLoader.extractArabicEntities(text);
      
      // استخراج البيانات حسب نوع المستند
      const extractedData = this.extractDataByType(text, documentType, entities);
      
      return extractedData;
    } catch (error) {
      console.error('❌ خطأ في استخراج البيانات:', error);
      return this.extractDataWithRegex(text, documentType);
    }
  }

  /**
   * استخراج البيانات حسب النوع
   */
  private static extractDataByType(text: string, documentType: string, entities: any): any {
    const baseData = this.extractDataWithRegex(text, documentType);
    
    // تحسين البيانات باستخدام الكيانات المستخرجة
    if (entities.entities) {
      entities.entities.forEach((entity: any) => {
        if (entity.label === 'PERSON' && !baseData.ownerName) {
          baseData.ownerName = entity.text;
        } else if (entity.label === 'ORG' && !baseData.businessName) {
          baseData.businessName = entity.text;
        }
      });
    }
    
    return baseData;
  }

  /**
   * استخراج البيانات باستخدام Regex
   */
  private static extractDataWithRegex(text: string, documentType: string): any {
    const patterns = this.getExtractionPatterns(documentType);
    const extractedData: any = {};
    
    for (const [field, pattern] of Object.entries(patterns)) {
      const match = text.match(pattern);
      extractedData[field] = match?.[1]?.trim() || '';
    }
    
    return extractedData;
  }

  /**
   * الحصول على أنماط الاستخراج
   */
  private static getExtractionPatterns(documentType: string): Record<string, RegExp> {
    const patterns: Record<string, Record<string, RegExp>> = {
      commercial_registration: {
        businessName: /اسم المنشأة[:\s]*([^\n]+)/i,
        ownerName: /اسم التاجر[:\s]*([^\n]+)/i,
        registrationNumber: /رقم السجل[:\s]*([^\n]+)/i,
        issueDate: /تاريخ الإصدار[:\s]*([^\n]+)/i,
        expiryDate: /تاريخ الانتهاء[:\s]*([^\n]+)/i,
        businessActivity: /النشاط[:\s]*([^\n]+)/i,
        issuingAuthority: /الجهة المصدرة[:\s]*([^\n]+)/i
      },
      freelance_document: {
        ownerName: /اسم صاحب الوثيقة[:\s]*([^\n]+)/i,
        documentNumber: /رقم الوثيقة[:\s]*([^\n]+)/i,
        issueDate: /تاريخ الإصدار[:\s]*([^\n]+)/i,
        expiryDate: /تاريخ الانتهاء[:\s]*([^\n]+)/i,
        activityType: /نوع النشاط[:\s]*([^\n]+)/i,
        issuingAuthority: /الجهة المصدرة[:\s]*([^\n]+)/i
      },
      driving_license: {
        holderName: /اسم حامل الرخصة[:\s]*([^\n]+)/i,
        licenseNumber: /رقم الرخصة[:\s]*([^\n]+)/i,
        issueDate: /تاريخ الإصدار[:\s]*([^\n]+)/i,
        expiryDate: /تاريخ الانتهاء[:\s]*([^\n]+)/i,
        licenseClass: /فئة الرخصة[:\s]*([^\n]+)/i
      }
    };
    
    return patterns[documentType] || patterns.commercial_registration;
  }

  /**
   * التحقق من صحة البيانات المستخرجة
   */
  private static async validateExtractedData(data: any, documentType: string): Promise<any> {
    const validation = {
      isValid: true,
      issues: [] as string[],
      fieldValidation: {} as Record<string, boolean>
    };

    // التحقق من الحقول المطلوبة
    const requiredFields = this.getRequiredFields(documentType);
    
    for (const field of requiredFields) {
      const isFieldValid = data[field] && data[field].trim() !== '';
      validation.fieldValidation[field] = isFieldValid;
      
      if (!isFieldValid) {
        validation.isValid = false;
        validation.issues.push(`الحقل مفقود: ${field}`);
      }
    }

    // التحقق من صحة التواريخ
    if (data.issueDate && data.expiryDate) {
      const issueDate = new Date(data.issueDate);
      const expiryDate = new Date(data.expiryDate);
      
      if (expiryDate <= issueDate) {
        validation.isValid = false;
        validation.issues.push('تاريخ الانتهاء يجب أن يكون بعد تاريخ الإصدار');
      }
      
      if (expiryDate < new Date()) {
        validation.issues.push('المستند منتهي الصلاحية');
      }
    }

    return validation;
  }

  /**
   * الحصول على الحقول المطلوبة
   */
  private static getRequiredFields(documentType: string): string[] {
    const requiredFields: Record<string, string[]> = {
      commercial_registration: ['businessName', 'ownerName', 'registrationNumber'],
      freelance_document: ['ownerName', 'documentNumber'],
      driving_license: ['holderName', 'licenseNumber']
    };
    
    return requiredFields[documentType] || requiredFields.commercial_registration;
  }

  /**
   * حساب مستوى الثقة
   */
  private static calculateConfidence(extractedData: any, validation: any, classification: any): number {
    let confidence = 0;
    
    // ثقة التصنيف (30%)
    confidence += (classification.confidence || 0.5) * 0.3;
    
    // اكتمال البيانات (40%)
    const totalFields = Object.keys(extractedData).length;
    const filledFields = Object.values(extractedData).filter(value => 
      value && String(value).trim() !== ''
    ).length;
    confidence += (filledFields / Math.max(totalFields, 1)) * 0.4;
    
    // صحة البيانات (30%)
    confidence += (validation.isValid ? 1 : 0.5) * 0.3;
    
    return Math.round(confidence * 100);
  }

  /**
   * تحديد المشاكل
   */
  private static identifyIssues(extractedData: any, validation: any): string[] {
    const issues = [...validation.issues];
    
    // فحص جودة البيانات
    Object.entries(extractedData).forEach(([key, value]) => {
      if (!value || String(value).trim() === '') {
        issues.push(`بيانات ناقصة: ${key}`);
      } else if (String(value).length < 2) {
        issues.push(`بيانات قصيرة جداً: ${key}`);
      }
    });
    
    return issues;
  }

  /**
   * نتيجة احتياطية في حالة الخطأ
   */
  private static getFallbackAnalysis(
    documentUrl: string, 
    documentType: string, 
    analysisTime: number
  ): LocalAnalysisResult {
    return {
      documentType,
      extractedData: {
        businessName: 'فشل في الاستخراج',
        ownerName: 'فشل في الاستخراج',
        registrationNumber: 'غير متاح'
      },
      confidence: 30,
      isValid: false,
      issues: ['فشل في التحليل المحلي', 'يتطلب مراجعة يدوية'],
      ocrText: 'فشل في استخراج النص',
      securityLevel: 'high',
      processingMethod: 'local_ai',
      analysisTime,
      modelStats: { totalModels: 0, models: [] }
    };
  }

  /**
   * الحصول على إحصائيات الخدمة
   */
  static getServiceStats(): any {
    return {
      isInitialized: this.isInitialized,
      modelStats: modelLoader.getLoadedModelsStats(),
      securityLevel: 'high',
      processingMethod: 'local_ai'
    };
  }

  /**
   * تنظيف الموارد
   */
  static async cleanup(): Promise<void> {
    await modelLoader.cleanup();
    this.isInitialized = false;
    console.log('🧹 تم تنظيف خدمة التحليل المحلي');
  }
}
