#!/usr/bin/env node

/**
 * سكريبت لإضافة كمية كبيرة من الترجمات المفقودة
 * يركز على الترجمات الأكثر أهمية أولاً
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  fs.copyFileSync(filePath, backupPath);
  console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
  return backupPath;
}

/**
 * إضافة الترجمات المفقودة للإنجليزية
 */
function addMissingEnglishTranslations() {
  console.log('\n🔧 إضافة الترجمات المفقودة للإنجليزية...');
  
  const missingEnglishTranslations = {
    // الترجمات الأساسية
    "representative": "Representative",
    "sar": "SAR",
    "status": "Status",
    "processing": "Processing",
    "cancel": "Cancel",
    "loading": "Loading...",
    "close": "Close",
    "actions": "Actions",
    "category": "Category",
    "price": "Price",
    "stock": "Stock",
    "active": "Active",
    "inactive": "Inactive",
    "delete": "Delete",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "search": "Search",
    "filter": "Filter",
    "store": "Store",
    "available": "Available",
    "outOfStock": "Out of Stock",
    "address": "Address",
    "businessHours": "Business Hours",
    "totalOrders": "Total Orders",
    "productNotFound": "Product Not Found",
    "storeNotFound": "Store Not Found",
    "description": "Description",
    "moderationNotes": "Moderation Notes",
    
    // ترجمات التقييمات
    "reviews.cannotReview": "Cannot Review",
    "reviews.cannotReviewDescription": "You cannot review this item",
    "reviews.addStoreReview": "Add Store Review",
    "reviews.addProductReview": "Add Product Review",
    "reviews.ratingRequired": "Rating is required",
    "reviews.comment": "Comment",
    "reviews.commentRequired": "Comment is required",
    "reviews.commentTooShort": "Comment is too short",
    "reviews.commentPlaceholder": "Share your experience...",
    "reviews.images": "Images",
    "reviews.addImages": "Add Images",
    "reviews.maxImages": "Maximum {{count}} images",
    "reviews.submitReview": "Submit Review",
    "reviews.editReview": "Edit Review",
    "reviews.deleteReview": "Delete Review",
    "reviews.reviewSubmitted": "Review Submitted",
    "reviews.reviewUpdated": "Review Updated",
    "reviews.reviewDeleted": "Review Deleted",
    "reviews.loadingReviews": "Loading Reviews...",
    "reviews.noReviewsYet": "No reviews yet",
    "reviews.writeFirstReview": "Write the first review",
    
    // ترجمات الخرائط والموقع
    "map.loading": "Loading Map...",
    "map.error": "Map Error",
    "map.noLocation": "No Location",
    "map.enableLocation": "Enable Location",
    "map.locationDenied": "Location Access Denied",
    "map.locationUnavailable": "Location Unavailable",
    "map.findStores": "Find Stores",
    "map.nearbyStores": "Nearby Stores",
    "map.storeDetails": "Store Details",
    "map.getDirections": "Get Directions",
    "map.distance": "Distance",
    "map.estimatedTime": "Estimated Time",
    
    // ترجمات المتاجر
    "stores.featured": "Featured Stores",
    "stores.popular": "Popular Stores",
    "stores.newest": "Newest Stores",
    "stores.topRated": "Top Rated Stores",
    "stores.openNow": "Open Now",
    "stores.closedNow": "Closed Now",
    "stores.deliveryAvailable": "Delivery Available",
    "stores.pickupOnly": "Pickup Only",
    "stores.freeDelivery": "Free Delivery",
    "stores.fastDelivery": "Fast Delivery",
    "stores.viewMenu": "View Menu",
    "stores.orderOnline": "Order Online",
    "stores.callStore": "Call Store",
    "stores.storeInfo": "Store Information",
    "stores.workingHours": "Working Hours",
    "stores.contactInfo": "Contact Information",
    "stores.socialMedia": "Social Media",
    
    // ترجمات المنتجات
    "products.featured": "Featured Products",
    "products.bestsellers": "Bestsellers",
    "products.newArrivals": "New Arrivals",
    "products.onSale": "On Sale",
    "products.recommended": "Recommended",
    "products.relatedProducts": "Related Products",
    "products.productDetails": "Product Details",
    "products.specifications": "Specifications",
    "products.ingredients": "Ingredients",
    "products.nutritionFacts": "Nutrition Facts",
    "products.allergenInfo": "Allergen Information",
    "products.storageInstructions": "Storage Instructions",
    "products.usageInstructions": "Usage Instructions",
    "products.warranty": "Warranty",
    "products.returnPolicy": "Return Policy",
    
    // ترجمات الطلبات
    "orders.orderHistory": "Order History",
    "orders.currentOrders": "Current Orders",
    "orders.pastOrders": "Past Orders",
    "orders.orderDetails": "Order Details",
    "orders.orderSummary": "Order Summary",
    "orders.orderItems": "Order Items",
    "orders.orderTotal": "Order Total",
    "orders.orderStatus": "Order Status",
    "orders.trackOrder": "Track Order",
    "orders.cancelOrder": "Cancel Order",
    "orders.reorder": "Reorder",
    "orders.orderConfirmation": "Order Confirmation",
    "orders.estimatedDelivery": "Estimated Delivery",
    "orders.deliveryAddress": "Delivery Address",
    "orders.paymentMethod": "Payment Method",
    "orders.orderNotes": "Order Notes",
    
    // ترجمات الدفع
    "payment.paymentMethods": "Payment Methods",
    "payment.creditCard": "Credit Card",
    "payment.debitCard": "Debit Card",
    "payment.cashOnDelivery": "Cash on Delivery",
    "payment.digitalWallet": "Digital Wallet",
    "payment.bankTransfer": "Bank Transfer",
    "payment.paymentProcessing": "Processing Payment...",
    "payment.paymentSuccess": "Payment Successful",
    "payment.paymentFailed": "Payment Failed",
    "payment.paymentCancelled": "Payment Cancelled",
    "payment.refundProcessing": "Processing Refund...",
    "payment.refundCompleted": "Refund Completed",
    "payment.billingAddress": "Billing Address",
    "payment.securePayment": "Secure Payment",
    
    // ترجمات التوصيل
    "delivery.deliveryOptions": "Delivery Options",
    "delivery.standardDelivery": "Standard Delivery",
    "delivery.expressDelivery": "Express Delivery",
    "delivery.sameDay": "Same Day Delivery",
    "delivery.nextDay": "Next Day Delivery",
    "delivery.scheduled": "Scheduled Delivery",
    "delivery.pickup": "Pickup",
    "delivery.deliveryFee": "Delivery Fee",
    "delivery.freeDelivery": "Free Delivery",
    "delivery.deliveryTime": "Delivery Time",
    "delivery.deliveryAddress": "Delivery Address",
    "delivery.deliveryInstructions": "Delivery Instructions",
    "delivery.contactlessDelivery": "Contactless Delivery",
    "delivery.trackDelivery": "Track Delivery",
    "delivery.deliveryStatus": "Delivery Status",
    "delivery.outForDelivery": "Out for Delivery",
    "delivery.delivered": "Delivered",
    "delivery.deliveryConfirmation": "Delivery Confirmation"
  };
  
  try {
    createBackup(EN_TRANSLATIONS_PATH);
    const enContent = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const enTranslations = JSON.parse(enContent);
    
    let addedCount = 0;
    Object.entries(missingEnglishTranslations).forEach(([key, value]) => {
      if (!enTranslations[key]) {
        enTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للإنجليزية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(EN_TRANSLATIONS_PATH, JSON.stringify(enTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة إنجليزية جديدة`);
    } else {
      console.log('ℹ️ جميع الترجمات الإنجليزية موجودة مسبقاً');
    }
    
    return addedCount;
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات الإنجليزية:', error.message);
    return 0;
  }
}

/**
 * إضافة الترجمات المفقودة للعربية
 */
function addMissingArabicTranslations() {
  console.log('\n🔧 إضافة الترجمات المفقودة للعربية...');

  const missingArabicTranslations = {
    // ترجمات التقييمات
    "reviews.cannotReview": "لا يمكن التقييم",
    "reviews.cannotReviewDescription": "لا يمكنك تقييم هذا العنصر",
    "reviews.addStoreReview": "إضافة تقييم للمتجر",
    "reviews.addProductReview": "إضافة تقييم للمنتج",
    "reviews.ratingRequired": "التقييم مطلوب",
    "reviews.comment": "تعليق",
    "reviews.commentRequired": "التعليق مطلوب",
    "reviews.commentTooShort": "التعليق قصير جداً",
    "reviews.commentPlaceholder": "شارك تجربتك...",
    "reviews.images": "الصور",
    "reviews.addImages": "إضافة صور",
    "reviews.maxImages": "حد أقصى {{count}} صور",
    "reviews.submitReview": "إرسال التقييم",
    "reviews.editReview": "تعديل التقييم",
    "reviews.deleteReview": "حذف التقييم",
    "reviews.reviewSubmitted": "تم إرسال التقييم",
    "reviews.reviewUpdated": "تم تحديث التقييم",
    "reviews.reviewDeleted": "تم حذف التقييم",
    "reviews.loadingReviews": "جاري تحميل التقييمات...",
    "reviews.noReviewsYet": "لا توجد تقييمات بعد",
    "reviews.writeFirstReview": "اكتب أول تقييم",

    // ترجمات الخرائط والموقع
    "map.loading": "جاري تحميل الخريطة...",
    "map.error": "خطأ في الخريطة",
    "map.noLocation": "لا يوجد موقع",
    "map.enableLocation": "تمكين الموقع",
    "map.locationDenied": "تم رفض الوصول للموقع",
    "map.locationUnavailable": "الموقع غير متاح",
    "map.findStores": "البحث عن المتاجر",
    "map.nearbyStores": "المتاجر القريبة",
    "map.storeDetails": "تفاصيل المتجر",
    "map.getDirections": "الحصول على الاتجاهات",
    "map.distance": "المسافة",
    "map.estimatedTime": "الوقت المقدر",

    // ترجمات المتاجر
    "stores.featured": "المتاجر المميزة",
    "stores.popular": "المتاجر الشائعة",
    "stores.newest": "أحدث المتاجر",
    "stores.topRated": "الأعلى تقييماً",
    "stores.openNow": "مفتوح الآن",
    "stores.closedNow": "مغلق الآن",
    "stores.deliveryAvailable": "التوصيل متاح",
    "stores.pickupOnly": "الاستلام فقط",
    "stores.freeDelivery": "توصيل مجاني",
    "stores.fastDelivery": "توصيل سريع",
    "stores.viewMenu": "عرض القائمة",
    "stores.orderOnline": "اطلب أونلاين",
    "stores.callStore": "اتصل بالمتجر",
    "stores.storeInfo": "معلومات المتجر",
    "stores.workingHours": "ساعات العمل",
    "stores.contactInfo": "معلومات التواصل",
    "stores.socialMedia": "وسائل التواصل الاجتماعي",

    // ترجمات المنتجات
    "products.featured": "المنتجات المميزة",
    "products.bestsellers": "الأكثر مبيعاً",
    "products.newArrivals": "وصل حديثاً",
    "products.onSale": "في التخفيضات",
    "products.recommended": "موصى به",
    "products.relatedProducts": "منتجات ذات صلة",
    "products.productDetails": "تفاصيل المنتج",
    "products.specifications": "المواصفات",
    "products.ingredients": "المكونات",
    "products.nutritionFacts": "القيم الغذائية",
    "products.allergenInfo": "معلومات المواد المسببة للحساسية",
    "products.storageInstructions": "تعليمات التخزين",
    "products.usageInstructions": "تعليمات الاستخدام",
    "products.warranty": "الضمان",
    "products.returnPolicy": "سياسة الإرجاع",

    // ترجمات الطلبات
    "orders.orderHistory": "تاريخ الطلبات",
    "orders.currentOrders": "الطلبات الحالية",
    "orders.pastOrders": "الطلبات السابقة",
    "orders.orderDetails": "تفاصيل الطلب",
    "orders.orderSummary": "ملخص الطلب",
    "orders.orderItems": "عناصر الطلب",
    "orders.orderTotal": "إجمالي الطلب",
    "orders.orderStatus": "حالة الطلب",
    "orders.trackOrder": "تتبع الطلب",
    "orders.cancelOrder": "إلغاء الطلب",
    "orders.reorder": "إعادة الطلب",
    "orders.orderConfirmation": "تأكيد الطلب",
    "orders.estimatedDelivery": "التوصيل المقدر",
    "orders.deliveryAddress": "عنوان التوصيل",
    "orders.paymentMethod": "طريقة الدفع",
    "orders.orderNotes": "ملاحظات الطلب",

    // ترجمات الدفع
    "payment.paymentMethods": "طرق الدفع",
    "payment.creditCard": "بطاقة ائتمان",
    "payment.debitCard": "بطاقة خصم",
    "payment.cashOnDelivery": "الدفع عند التسليم",
    "payment.digitalWallet": "المحفظة الرقمية",
    "payment.bankTransfer": "تحويل بنكي",
    "payment.paymentProcessing": "جاري معالجة الدفع...",
    "payment.paymentSuccess": "تم الدفع بنجاح",
    "payment.paymentFailed": "فشل الدفع",
    "payment.paymentCancelled": "تم إلغاء الدفع",
    "payment.refundProcessing": "جاري معالجة الاسترداد...",
    "payment.refundCompleted": "تم الاسترداد",
    "payment.billingAddress": "عنوان الفوترة",
    "payment.securePayment": "دفع آمن",

    // ترجمات التوصيل
    "delivery.deliveryOptions": "خيارات التوصيل",
    "delivery.standardDelivery": "توصيل عادي",
    "delivery.expressDelivery": "توصيل سريع",
    "delivery.sameDay": "توصيل في نفس اليوم",
    "delivery.nextDay": "توصيل في اليوم التالي",
    "delivery.scheduled": "توصيل مجدول",
    "delivery.pickup": "الاستلام",
    "delivery.deliveryFee": "رسوم التوصيل",
    "delivery.freeDelivery": "توصيل مجاني",
    "delivery.deliveryTime": "وقت التوصيل",
    "delivery.deliveryAddress": "عنوان التوصيل",
    "delivery.deliveryInstructions": "تعليمات التوصيل",
    "delivery.contactlessDelivery": "توصيل بدون تلامس",
    "delivery.trackDelivery": "تتبع التوصيل",
    "delivery.deliveryStatus": "حالة التوصيل",
    "delivery.outForDelivery": "في الطريق للتوصيل",
    "delivery.delivered": "تم التوصيل",
    "delivery.deliveryConfirmation": "تأكيد التوصيل"
  };

  try {
    createBackup(AR_TRANSLATIONS_PATH);
    const arContent = fs.readFileSync(AR_TRANSLATIONS_PATH, 'utf8');
    const arTranslations = JSON.parse(arContent);

    let addedCount = 0;
    Object.entries(missingArabicTranslations).forEach(([key, value]) => {
      if (!arTranslations[key]) {
        arTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للعربية: "${key}"`);
      }
    });

    if (addedCount > 0) {
      fs.writeFileSync(AR_TRANSLATIONS_PATH, JSON.stringify(arTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة عربية جديدة`);
    } else {
      console.log('ℹ️ جميع الترجمات العربية موجودة مسبقاً');
    }

    return addedCount;

  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات العربية:', error.message);
    return 0;
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء إضافة الترجمات المفقودة بكميات كبيرة...\n');

  try {
    // إضافة الترجمات المفقودة للإنجليزية
    const addedEnglish = addMissingEnglishTranslations();

    // إضافة الترجمات المفقودة للعربية
    const addedArabic = addMissingArabicTranslations();

    console.log('\n📋 ملخص الإضافات:');
    console.log(`✅ تم إضافة ${addedEnglish} ترجمة إنجليزية جديدة`);
    console.log(`✅ تم إضافة ${addedArabic} ترجمة عربية جديدة`);
    console.log(`✅ إجمالي الترجمات المضافة: ${addedEnglish + addedArabic}`);

    if (addedEnglish > 0 || addedArabic > 0) {
      console.log('\n🎉 تم الانتهاء من إضافة الترجمات بنجاح!');
      console.log('💡 يُنصح بتشغيل سكريبت التحقق للتأكد من النتائج:');
      console.log('   node scripts/validate-translations.js');
    } else {
      console.log('\n✨ لا توجد ترجمات جديدة لإضافتها');
    }

  } catch (error) {
    console.error('\n❌ فشل في إضافة الترجمات:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  addMissingEnglishTranslations,
  addMissingArabicTranslations,
  createBackup
};
