'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Star, Upload, X, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useReviews, useCanReview } from '@/hooks/useReviews';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

interface AddReviewFormProps {
  targetId: string;
  type: 'store' | 'product';
  targetName?: string;
  onSuccess?: () => void;
  className?: string;
}

export function AddReviewForm({ 
  targetId, 
  type, 
  targetName,
  onSuccess,
  className 
}: AddReviewFormProps) {
  const t = useTranslations();
  const { user } = useAuth();
  const { addReview, submitting } = useReviews({ targetId, type, autoFetch: false });
  const { canReview, hasExistingReview, loading: checkingEligibility } = useCanReview(targetId, type);

  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [comment, setComment] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [uploadingImages, setUploadingImages] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast.error(t('auth.loginRequired'));
      return;
    }

    if (rating === 0) {
      toast.error(t('reviews.ratingRequired'));
      return;
    }

    if (comment.trim().length < 10) {
      toast.error(t('reviews.commentTooShort'));
      return;
    }

    const success = await addReview({
      rating,
      comment: comment.trim(),
      images
    });

    if (success) {
      setRating(0);
      setComment('');
      setImages([]);
      onSuccess?.();
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    if (images.length + files.length > 5) {
      toast.error(t('reviews.maxImagesExceeded'));
      return;
    }

    setUploadingImages(true);
    
    try {
      // هنا يجب إضافة منطق رفع الصور إلى Cloudinary أو خدمة أخرى
      // للآن، سنستخدم URL.createObjectURL للمعاينة المحلية
      const newImageUrls = files.map(file => URL.createObjectURL(file));
      setImages(prev => [...prev, ...newImageUrls]);
      
      toast.success(t('reviews.imagesUploaded'));
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.error(t('reviews.imageUploadFailed'));
    } finally {
      setUploadingImages(false);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, i) => {
      const starValue = i + 1;
      return (
        <button
          key={i}
          type="button"
          className="focus:outline-none"
          onMouseEnter={() => setHoverRating(starValue)}
          onMouseLeave={() => setHoverRating(0)}
          onClick={() => setRating(starValue)}
        >
          <Star
            className={`h-8 w-8 transition-colors ${
              starValue <= (hoverRating || rating)
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300 hover:text-yellow-200'
            }`}
          />
        </button>
      );
    });
  };

  if (checkingEligibility) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/3" />
            <div className="h-20 bg-gray-200 rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!user) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('reviews.loginToReview')}
          </h3>
          <p className="text-gray-500 mb-4">
            {t('reviews.loginToReviewDescription')}
          </p>
          <Button variant="outline">
            {t('auth.login')}
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (hasExistingReview) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Star className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('reviews.alreadyReviewed')}
          </h3>
          <p className="text-gray-500">
            {t('reviews.alreadyReviewedDescription')}
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!canReview) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('reviews.cannotReview')}
          </h3>
          <p className="text-gray-500">
            {t('reviews.cannotReviewDescription')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <Star className="h-5 w-5 text-yellow-500" />
          <span>
            {type === 'store' 
              ? t('reviews.addStoreReview') 
              : t('reviews.addProductReview')
            }
          </span>
        </CardTitle>
        {targetName && (
          <p className="text-sm text-gray-600">
            {targetName}
          </p>
        )}
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* تقييم النجوم */}
          <div className="space-y-2">
            <Label className="text-base font-medium">
              {t('reviews.rating')} *
            </Label>
            <div className="flex items-center space-x-1 rtl:space-x-reverse">
              {renderStars()}
            </div>
            {rating > 0 && (
              <p className="text-sm text-gray-600">
                {t(`reviews.ratingLabels.${rating}`)}
              </p>
            )}
          </div>

          {/* التعليق */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-base font-medium">
              {t('reviews.comment')} *
            </Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={t('reviews.commentPlaceholder')}
              className="min-h-[120px] resize-none"
              maxLength={1000}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{t('reviews.minCharacters', { count: 10 })}</span>
              <span>{comment.length}/1000</span>
            </div>
          </div>

          {/* رفع الصور */}
          <div className="space-y-2">
            <Label className="text-base font-medium">
              {t('reviews.images')} ({t('reviews.optional')})
            </Label>
            
            {images.length > 0 && (
              <div className="grid grid-cols-3 gap-2 mb-3">
                {images.map((image, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={image}
                      alt={`Review image ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            {images.length < 5 && (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  id="images"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  disabled={uploadingImages}
                />
                <label
                  htmlFor="images"
                  className="cursor-pointer flex flex-col items-center space-y-2"
                >
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {uploadingImages 
                      ? t('reviews.uploadingImages')
                      : t('reviews.uploadImages')
                    }
                  </span>
                  <span className="text-xs text-gray-500">
                    {t('reviews.maxImages', { count: 5 - images.length })}
                  </span>
                </label>
              </div>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex justify-end space-x-3 rtl:space-x-reverse pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setRating(0);
                setComment('');
                setImages([]);
              }}
              disabled={submitting}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={submitting || rating === 0 || comment.trim().length < 10}
            >
              {submitting ? t('reviews.submitting') : t('reviews.submit')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
