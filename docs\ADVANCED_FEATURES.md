# الميزات المتقدمة - Advanced Features

هذا المستند يوثق الميزات المتقدمة الجديدة في مشروع مِخْلاة.

## 🌍 نظام التعرف التلقائي على اللغة

### نظرة عامة
نظام ذكي يكتشف لغة المستخدم المفضلة تلقائياً بناءً على إعدادات المتصفح والجهاز.

### الميزات الرئيسية

#### 🔍 اكتشاف متعدد المصادر
```typescript
// مصادر اكتشاف اللغة
1. اللغة المحفوظة في localStorage
2. لغة المتصفح الأساسية (navigator.language)
3. جميع اللغات المفضلة (navigator.languages)
4. المنطقة الزمنية للجهاز
5. إعدادات تنسيق الأرقام والتاريخ
```

#### 💾 حفظ تلقائي للتفضيلات
- حفظ في `localStorage` للجلسات المحلية
- حفظ في `cookies` للتزامن مع الخادم
- طابع زمني لتتبع آخر تحديث

#### 🎯 دقة عالية في الاكتشاف
- **المناطق الزمنية العربية**: اكتشاف تلقائي للبلدان العربية
- **تحليل اللغات المفضلة**: فحص شامل لقائمة اللغات
- **نظام نقاط الثقة**: حساب مستوى الثقة في الاكتشاف

### المكونات

#### `AutoLanguageDetector`
```tsx
<AutoLanguageDetector 
  currentLocale="en" 
  onLanguageChange={(locale) => console.log(locale)}
/>
```

#### `LanguageSettings`
```tsx
<LanguageSettings 
  currentLocale="ar"
  onLanguageChange={handleLanguageChange}
/>
```

### الاستخدام

```typescript
import { detectUserLanguage, saveUserLanguagePreference } from '@/hooks/use-locale';

// اكتشاف اللغة
const detectedLanguage = detectUserLanguage();

// حفظ اللغة المفضلة
saveUserLanguagePreference('ar');
```

---

## 🔍 نظام الفلاتر المتقدم

### نظرة عامة
نظام فلترة شامل ومتقدم لصفحة المتاجر مع خيارات متعددة وواجهة سهلة الاستخدام.

### الفلاتر المتاحة

#### 📂 فلاتر الفئات
- **فلترة متعددة الفئات**: اختيار عدة فئات في نفس الوقت
- **عداد المتاجر**: عرض عدد المتاجر لكل فئة
- **بحث في الفئات**: إمكانية البحث داخل الفئات

#### 💰 فلاتر السعر
- **نطاق سعر متقدم**: slider قابل للتخصيص
- **حدود مرنة**: تحديد حد أدنى وأعلى منفصلين
- **عرض فوري**: تحديث النتائج أثناء التحريك

#### ⭐ فلاتر التقييم
- **مستويات متعددة**: من 1 إلى 5 نجوم
- **فلترة تراكمية**: "3 نجوم فأكثر"
- **عرض بصري**: نجوم ملونة للوضوح

#### 📍 فلاتر الموقع والمسافة
- **نطاق مسافة قابل للتخصيص**: من 1 إلى 100 كم
- **اكتشاف موقع تلقائي**: استخدام GPS
- **حساب دقيق للمسافة**: خوارزمية Haversine

#### 🚚 فلاتر التوفر والتسليم
- **مفتوح الآن**: المتاجر المفتوحة حالياً
- **يوفر التوصيل**: المتاجر التي تقدم خدمة التوصيل
- **توصيل سريع**: توصيل خلال ساعة

#### 🏪 فلاتر خصائص المتجر
- **متاجر موثقة فقط**: المتاجر المعتمدة
- **متاجر مميزة**: المتاجر المدفوعة الإعلان

#### ⚡ فلاتر متقدمة
- **يحتوي على عروض**: متاجر بها تخفيضات
- **وصل حديثاً**: متاجر جديدة (آخر 30 يوم)
- **الأكثر رواجاً**: بناءً على الزيارات
- **مميز**: متاجر مختارة بعناية

### المكونات

#### `AdvancedFilterSidebar`
```tsx
<AdvancedFilterSidebar
  filters={filters}
  onFiltersChange={setFilters}
  onClearFilters={clearFilters}
  availableCategories={categories}
  storeStats={stats}
  isMobile={false}
/>
```

#### `ActiveFilters`
```tsx
<ActiveFilters
  filters={filters}
  onFiltersChange={setFilters}
  onClearFilters={clearFilters}
  availableCategories={categories}
/>
```

#### `SortingControls`
```tsx
<SortingControls
  filters={filters}
  onFiltersChange={setFilters}
  totalResults={results.length}
  showViewMode={true}
/>
```

### خيارات الترتيب

#### 🎯 ترتيب ذكي
- **الأكثر صلة**: خوارزمية تجمع عدة عوامل
- **الأعلى تقييماً**: حسب متوسط التقييمات
- **الأقرب مسافة**: حسب المسافة الجغرافية
- **الأحدث**: حسب تاريخ الإضافة
- **الأكثر شعبية**: حسب عدد الزيارات
- **الأكثر عروضاً**: المتاجر التي تحتوي على تخفيضات

#### 📊 خوارزمية الصلة
```typescript
const relevanceScore = 
  (rating * 0.4) +           // وزن التقييم
  (views * 0.0001) +         // وزن الشعبية  
  (isFeatured ? 10 : 0) -    // مكافأة المميز
  (distance * 0.1);          // خصم المسافة
```

### أوضاع العرض

#### 🔲 وضع الشبكة (Grid)
- عرض بطاقات في شبكة
- مناسب للشاشات الكبيرة
- عرض صور وتفاصيل أساسية

#### 📋 وضع القائمة (List)
- عرض في قائمة عمودية
- تفاصيل أكثر لكل متجر
- مناسب للمقارنة السريعة

#### 🗺️ وضع الخريطة (Map)
- عرض المتاجر على خريطة
- تفاعلي مع إمكانية التكبير
- مفيد لاختيار حسب الموقع

---

## 🎨 تحسينات واجهة المستخدم

### التصميم المتجاوب
- **شاشات كبيرة**: فلاتر جانبية ثابتة
- **شاشات متوسطة**: فلاتر قابلة للطي
- **شاشات صغيرة**: فلاتر في نافذة منبثقة

### التفاعلات السلسة
- **رسوم متحركة**: انتقالات سلسة بين الحالات
- **تحديث فوري**: النتائج تتحدث أثناء الفلترة
- **مؤشرات التحميل**: feedback بصري للمستخدم

### إمكانية الوصول
- **دعم لوحة المفاتيح**: تنقل كامل بالكيبورد
- **قارئ الشاشة**: labels وصفية شاملة
- **تباين عالي**: ألوان واضحة ومقروءة

---

## 🔧 التكامل والاستخدام

### إعداد المشروع

```bash
# تثبيت التبعيات
npm install

# تشغيل المشروع
npm run dev
```

### استيراد المكونات

```typescript
// مكونات اللغة
import AutoLanguageDetector from '@/components/layout/AutoLanguageDetector';
import LanguageSettings from '@/components/layout/LanguageSettings';

// مكونات الفلترة
import AdvancedFilterSidebar from '@/components/customer/AdvancedFilterSidebar';
import ActiveFilters from '@/components/customer/ActiveFilters';
import SortingControls from '@/components/customer/SortingControls';

// hooks
import { detectUserLanguage, useLocale } from '@/hooks/use-locale';
```

### مثال شامل

```tsx
function StoresPage() {
  const [filters, setFilters] = useState<SearchFilter>({});
  const [stores, setStores] = useState([]);
  
  return (
    <div>
      <AutoLanguageDetector currentLocale="en" />
      
      <div className="flex gap-8">
        <AdvancedFilterSidebar
          filters={filters}
          onFiltersChange={setFilters}
          onClearFilters={() => setFilters({})}
        />
        
        <div className="flex-1">
          <SortingControls
            filters={filters}
            onFiltersChange={setFilters}
            totalResults={stores.length}
          />
          
          <ActiveFilters
            filters={filters}
            onFiltersChange={setFilters}
            onClearFilters={() => setFilters({})}
          />
          
          {/* عرض النتائج */}
        </div>
      </div>
    </div>
  );
}
```

---

## 📈 الأداء والتحسين

### تحسينات الأداء
- **Lazy Loading**: تحميل المكونات عند الحاجة
- **Memoization**: تخزين مؤقت للحسابات المعقدة
- **Debouncing**: تأخير طلبات البحث المتكررة

### إدارة الذاكرة
- **Cleanup**: تنظيف المستمعين عند إلغاء التحميل
- **Caching**: تخزين مؤقت ذكي للبيانات
- **Optimization**: تحسين عمليات الفلترة

---

*آخر تحديث: 25 يونيو 2025*
