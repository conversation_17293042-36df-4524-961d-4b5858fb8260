'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useLocale } from '@/hooks/use-locale';
import { GeneralSettings } from './settings/GeneralSettings';
import { CommissionSettings } from './settings/CommissionSettings';
import { NotificationSettings } from './settings/NotificationSettings';
import { PaymentSettings } from './settings/PaymentSettings';
import { SecuritySettings } from './settings/SecuritySettings';
import { BackupSettings } from './settings/BackupSettings';
import { 
  Settings, 
  Palette, 
  Percent,
  Bell,
  CreditCard,
  Shield,
  Database,
  Save,
  RotateCcw
} from 'lucide-react';

export function SystemSettings() {
  const { t } = useLocale();
  const [activeSection, setActiveSection] = useState('general');
  const [hasChanges, setHasChanges] = useState(false);

  const settingSections = [
    {
      id: 'general',
      label: t('generalSettings'),
      icon: Settings,
      description: 'إعدادات عامة للمنصة'
    },
    {
      id: 'commission',
      label: t('commissionSettings'),
      icon: Percent,
      description: 'إدارة العمولات والرسوم'
    },
    {
      id: 'notifications',
      label: t('notificationSettings'),
      icon: Bell,
      description: 'إعدادات الإشعارات'
    },
    {
      id: 'payment',
      label: t('paymentSettings'),
      icon: CreditCard,
      description: 'إعدادات طرق الدفع'
    },
    {
      id: 'security',
      label: t('securitySettings'),
      icon: Shield,
      description: 'إعدادات الأمان'
    },
    {
      id: 'backup',
      label: t('backupSettings'),
      icon: Database,
      description: 'النسخ الاحتياطية'
    }
  ];

  const handleSaveChanges = async () => {
    try {
      // محاكاة حفظ الإعدادات
      console.log('Saving settings for section:', activeSection);
      
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      
      // يمكن إضافة إشعار نجاح هنا
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const handleResetSection = () => {
    // إعادة تعيين الإعدادات للقيم الافتراضية
    setHasChanges(false);
  };

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'general':
        return <GeneralSettings onSettingsChange={() => setHasChanges(true)} />;
      case 'commission':
        return <CommissionSettings onSettingsChange={() => setHasChanges(true)} />;
      case 'notifications':
        return <NotificationSettings onSettingsChange={() => setHasChanges(true)} />;
      case 'payment':
        return <PaymentSettings onSettingsChange={() => setHasChanges(true)} />;
      case 'security':
        return <SecuritySettings onSettingsChange={() => setHasChanges(true)} />;
      case 'backup':
        return <BackupSettings onSettingsChange={() => setHasChanges(true)} />;
      default:
        return <GeneralSettings onSettingsChange={() => setHasChanges(true)} />;
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* قائمة الإعدادات */}
      <div className="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">أقسام الإعدادات</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <nav className="space-y-1">
              {settingSections.map((section) => {
                const IconComponent = section.icon;
                const isActive = activeSection === section.id;
                
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full text-left p-4 flex items-start gap-3 transition-colors ${
                      isActive 
                        ? 'bg-blue-50 border-r-2 border-blue-500 text-blue-700' 
                        : 'hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    <IconComponent className={`h-5 w-5 mt-0.5 ${
                      isActive ? 'text-blue-600' : 'text-gray-400'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm">
                        {section.label}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {section.description}
                      </div>
                    </div>
                  </button>
                );
              })}
            </nav>
          </CardContent>
        </Card>

        {/* أزرار الحفظ والإعادة تعيين */}
        {hasChanges && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="text-sm text-orange-600 bg-orange-50 p-3 rounded-lg">
                  ⚠️ لديك تغييرات غير محفوظة
                </div>
                
                <div className="space-y-2">
                  <Button 
                    onClick={handleSaveChanges}
                    className="w-full flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    حفظ التغييرات
                  </Button>
                  
                  <Button 
                    variant="outline"
                    onClick={handleResetSection}
                    className="w-full flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    إعادة تعيين
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* المحتوى الرئيسي */}
      <div className="lg:col-span-3">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {(() => {
                  const activeSection_data = settingSections.find(s => s.id === activeSection);
                  const IconComponent = activeSection_data?.icon || Settings;
                  return (
                    <>
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <IconComponent className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <CardTitle>{activeSection_data?.label}</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">
                          {activeSection_data?.description}
                        </p>
                      </div>
                    </>
                  );
                })()}
              </div>
              
              {hasChanges && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-orange-600">غير محفوظ</span>
                </div>
              )}
            </div>
          </CardHeader>
          
          <CardContent>
            {renderActiveSection()}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
