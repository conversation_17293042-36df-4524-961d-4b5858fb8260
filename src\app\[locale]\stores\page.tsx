"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Store, MapPin, Grid3X3, List, AlertCircle, Loader2, Target, Navigation } from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGeolocation } from "@/hooks/useGeolocation";
import SearchBar from "@/components/customer/SearchBar";
import FilterSidebar from "@/components/customer/FilterSidebar";
import AdvancedFilterSidebar from "@/components/customer/AdvancedFilterSidebar";
import <PERSON>Filters, { ActiveFiltersBar } from "@/components/customer/ActiveFilters";
import SortingControls from "@/components/customer/SortingControls";
import StoreCard from "@/components/customer/StoreCard";
import LocationPermission from "@/components/map/LocationPermission";
import AutoLanguageDetector from "@/components/layout/AutoLanguageDetector";
import type { StoreWithDistance, SearchFilter } from "@/types";
import { collection, query, where, orderBy, limit, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";

export default function StoresPage() {
  const { t } = useLocale();
  const isMobile = useIsMobile();
  const [stores, setStores] = useState<StoreWithDistance[]>([]);
  const [filteredStores, setFilteredStores] = useState<StoreWithDistance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalStores, setTotalStores] = useState(0);
  const storesPerPage = 12;
  const [showLocationPermission, setShowLocationPermission] = useState(false);
  const [locationRequested, setLocationRequested] = useState(false);
  const [permissionAsked, setPermissionAsked] = useState(false);
  const [useAdvancedFilters, setUseAdvancedFilters] = useState(false);
  const [availableCategories, setAvailableCategories] = useState<Array<{ id: string; name: string; count: number }>>([]);
  const [storeStats, setStoreStats] = useState({
    totalStores: 0,
    openStores: 0,
    verifiedStores: 0,
    averageRating: 0
  });

  // استخدام hook الموقع
  const {
    latitude,
    longitude,
    accuracy,
    error: locationError,
    loading: locationLoading,
    permissionStatus,
    getCurrentPosition
  } = useGeolocation();

  const [filters, setFilters] = useState<SearchFilter>({
    sortBy: 'distance', // تغيير الترتيب الافتراضي للمسافة
    isActive: true,
    location: latitude && longitude ? { latitude, longitude } : undefined
  });

  // تحقق من حالة الإذن المحفوظة عند تحميل الصفحة
  useEffect(() => {
    const savedPermissionStatus = localStorage.getItem('locationPermissionStatus');
    const savedLocationRequested = localStorage.getItem('locationRequested');

    if (savedLocationRequested === 'true') {
      setLocationRequested(true);
      setPermissionAsked(true);
    }

    // إذا كان الإذن محفوظ كـ "granted" أو "denied"، لا نطلب مرة أخرى
    if (savedPermissionStatus === 'granted') {
      setLocationRequested(true);
      setPermissionAsked(true);
      // محاولة الحصول على الموقع مباشرة
      getCurrentPosition();
    } else if (savedPermissionStatus === 'denied') {
      setLocationRequested(true);
      setPermissionAsked(true);
    } else if (!permissionAsked && permissionStatus === 'prompt') {
      // فقط إذا لم نسأل من قبل ولم يكن هناك إذن محفوظ
      setShowLocationPermission(true);
    }
  }, [permissionStatus, permissionAsked, getCurrentPosition]);

  // تحديث الفلاتر عند تغيير الموقع
  useEffect(() => {
    if (latitude && longitude) {
      setFilters(prev => ({
        ...prev,
        location: { latitude, longitude }
      }));
      setShowLocationPermission(false);
      // حفظ حالة الإذن كـ "granted"
      localStorage.setItem('locationPermissionStatus', 'granted');
      localStorage.setItem('locationRequested', 'true');
    }
  }, [latitude, longitude]);

  // تحديث حالة الإذن عند تغيير permissionStatus
  useEffect(() => {
    if (permissionStatus !== 'prompt') {
      localStorage.setItem('locationPermissionStatus', permissionStatus);
      localStorage.setItem('locationRequested', 'true');
    }
  }, [permissionStatus]);

  // حساب المسافة بين نقطتين (Haversine formula)
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // تحميل الفئات المتاحة والإحصائيات
  useEffect(() => {
    const loadCategoriesAndStats = async () => {
      try {
        const storesRef = collection(db, 'stores');
        const storeQuery = query(
          storesRef,
          where('isActive', '==', true),
          where('approvalStatus', '==', 'approved')
        );

        const snapshot = await getDocs(storeQuery);
        const allStores = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // حساب الفئات
        const categoryCount: Record<string, number> = {};
        allStores.forEach(store => {
          if (store.categories && Array.isArray(store.categories)) {
            store.categories.forEach((category: string) => {
              categoryCount[category] = (categoryCount[category] || 0) + 1;
            });
          }
        });

        const categories = Object.entries(categoryCount).map(([id, count]) => ({
          id,
          name: id, // يمكن تحسين هذا بترجمة الفئات
          count
        }));

        setAvailableCategories(categories);

        // حساب الإحصائيات
        const now = new Date();
        const openStores = allStores.filter(store => {
          // منطق تحديد ما إذا كان المتجر مفتوح (يمكن تحسينه)
          return store.isActive;
        }).length;

        const verifiedStores = allStores.filter(store => store.isVerified).length;
        const totalRating = allStores.reduce((sum, store) => sum + (store.stats?.averageRating || 0), 0);
        const averageRating = allStores.length > 0 ? totalRating / allStores.length : 0;

        setStoreStats({
          totalStores: allStores.length,
          openStores,
          verifiedStores,
          averageRating: Math.round(averageRating * 10) / 10
        });

      } catch (error) {
        console.error('خطأ في تحميل الفئات والإحصائيات:', error);
      }
    };

    loadCategoriesAndStats();
  }, []);

  // Fetch stores from Firestore
  useEffect(() => {
    const fetchStores = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const storesRef = collection(db, 'stores');
        let storeQuery = query(
          storesRef,
          where('isActive', '==', true),
          where('approvalStatus', '==', 'approved')
        );

        // Add ordering based on sortBy (except distance which is calculated later)
        switch (filters.sortBy) {
          case 'rating':
            storeQuery = query(storeQuery, orderBy('stats.averageRating', 'desc'));
            break;
          case 'newest':
            storeQuery = query(storeQuery, orderBy('createdAt', 'desc'));
            break;
          default:
            storeQuery = query(storeQuery, orderBy('createdAt', 'desc'));
        }

        storeQuery = query(storeQuery, limit(100)); // Limit for performance

        const snapshot = await getDocs(storeQuery);
        const storesData: StoreWithDistance[] = [];

        snapshot.forEach((doc) => {
          const storeData = { ...doc.data(), id: doc.id } as StoreWithDistance;

          // Mock business hours check (you can implement real logic)
          const now = new Date();
          const currentHour = now.getHours();
          storeData.isOpen = currentHour >= 8 && currentHour <= 22; // Assume 8 AM to 10 PM

          // حساب المسافة إذا كان الموقع متاحاً
          if (latitude && longitude && storeData.location?.latitude && storeData.location?.longitude) {
            storeData.distance = calculateDistance(
              latitude,
              longitude,
              storeData.location.latitude,
              storeData.location.longitude
            );
            // حساب الوقت المقدر (افتراض 30 كم/ساعة)
            storeData.estimatedTime = Math.round((storeData.distance / 30) * 60);
          } else {
            storeData.distance = 0;
            storeData.estimatedTime = 0;
          }

          storesData.push(storeData);
        });

        setStores(storesData);
        setTotalStores(storesData.length);
      } catch (err) {
        console.error('Error fetching stores:', err);
        setError(t('errorFetchingStores'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchStores();
  }, [filters.sortBy, latitude, longitude, t, calculateDistance]);

  // Apply advanced filters
  useEffect(() => {
    let filtered = [...stores];

    // Category filters (multiple categories)
    if (filters.categories?.length) {
      filtered = filtered.filter(store =>
        store.categories?.some(cat =>
          filters.categories!.some(filterCat =>
            cat.toLowerCase().includes(filterCat.toLowerCase())
          )
        )
      );
    }

    // Legacy single category filter
    if (filters.category) {
      filtered = filtered.filter(store =>
        store.categories?.some(cat =>
          cat.toLowerCase().includes(filters.category!.toLowerCase())
        )
      );
    }

    // Search query filter
    if (filters.query) {
      const query = filters.query.toLowerCase();
      filtered = filtered.filter(store =>
        store.storeName.toLowerCase().includes(query) ||
        store.storeDescription?.toLowerCase().includes(query) ||
        store.categories?.some(cat => cat.toLowerCase().includes(query))
      );
    }

    // Rating filters
    if (filters.minRating) {
      filtered = filtered.filter(store =>
        (store.stats?.averageRating || 0) >= filters.minRating!
      );
    }

    // Legacy rating filter
    if (filters.rating) {
      filtered = filtered.filter(store =>
        (store.stats?.averageRating || 0) >= filters.rating!
      );
    }

    // Distance filter
    if (filters.maxDistance && latitude && longitude) {
      filtered = filtered.filter(store =>
        (store.distance || 0) <= filters.maxDistance!
      );
    }

    // Verified stores only
    if (filters.verifiedOnly) {
      filtered = filtered.filter(store => store.isVerified);
    }

    // Open now filter
    if (filters.openNow) {
      filtered = filtered.filter(store => {
        // منطق تحديد ما إذا كان المتجر مفتوح (يمكن تحسينه)
        return store.isActive;
      });
    }

    // Has delivery filter
    if (filters.hasDelivery) {
      filtered = filtered.filter(store => store.deliveryOptions?.available);
    }

    // Fast delivery filter
    if (filters.fastDelivery) {
      filtered = filtered.filter(store =>
        store.deliveryOptions?.available &&
        store.deliveryOptions?.estimatedTime <= 60 // أقل من ساعة
      );
    }

    // Has offers filter
    if (filters.hasOffers) {
      filtered = filtered.filter(store =>
        store.hasActiveOffers ||
        (store.stats?.activeOffers || 0) > 0
      );
    }

    // New arrivals filter
    if (filters.newArrivals) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      filtered = filtered.filter(store =>
        store.createdAt && store.createdAt.toDate() >= thirtyDaysAgo
      );
    }

    // Trending filter
    if (filters.trending) {
      filtered = filtered.filter(store =>
        (store.stats?.monthlyViews || 0) > 100 // معيار بسيط للرواج
      );
    }

    // Featured filter
    if (filters.featured) {
      filtered = filtered.filter(store => store.isFeatured);
    }

    // Sort filtered results with advanced options
    switch (filters.sortBy) {
      case 'rating':
        filtered.sort((a, b) => (b.stats?.averageRating || 0) - (a.stats?.averageRating || 0));
        break;
      case 'distance':
        filtered.sort((a, b) => (a.distance || 0) - (b.distance || 0));
        break;
      case 'newest':
        filtered.sort((a, b) => b.createdAt.seconds - a.createdAt.seconds);
        break;
      case 'popular':
        filtered.sort((a, b) => (b.stats?.monthlyViews || 0) - (a.stats?.monthlyViews || 0));
        break;
      case 'offers':
        filtered.sort((a, b) => {
          const aOffers = a.stats?.activeOffers || 0;
          const bOffers = b.stats?.activeOffers || 0;
          if (aOffers !== bOffers) return bOffers - aOffers;
          // ثانوياً: ترتيب حسب التقييم
          return (b.stats?.averageRating || 0) - (a.stats?.averageRating || 0);
        });
        break;
      case 'relevance':
      default:
        // ترتيب حسب الصلة: مزيج من التقييم والشعبية والمسافة
        filtered.sort((a, b) => {
          const aScore = (a.stats?.averageRating || 0) * 0.4 +
                        (a.stats?.monthlyViews || 0) * 0.0001 +
                        (a.isFeatured ? 10 : 0) -
                        (a.distance || 0) * 0.1;
          const bScore = (b.stats?.averageRating || 0) * 0.4 +
                        (b.stats?.monthlyViews || 0) * 0.0001 +
                        (b.isFeatured ? 10 : 0) -
                        (b.distance || 0) * 0.1;
          return bScore - aScore;
        });
        break;
    }

    setFilteredStores(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [stores, filters]);

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, query }));
  };

  const handleFiltersChange = (newFilters: SearchFilter) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      sortBy: 'distance',
      isActive: true,
      location: latitude && longitude ? { latitude, longitude } : undefined
    });
  };

  // معالجة طلب الموقع
  const handleLocationAllow = () => {
    setLocationRequested(true);
    setPermissionAsked(true);
    setShowLocationPermission(false);
    // حفظ الموافقة في localStorage
    localStorage.setItem('locationPermissionStatus', 'granted');
    localStorage.setItem('locationRequested', 'true');
    getCurrentPosition();
  };

  const handleLocationDeny = () => {
    setLocationRequested(true);
    setPermissionAsked(true);
    setShowLocationPermission(false);
    // حفظ الرفض في localStorage
    localStorage.setItem('locationPermissionStatus', 'denied');
    localStorage.setItem('locationRequested', 'true');
    // تغيير الترتيب الافتراضي إذا تم رفض الموقع
    setFilters(prev => ({
      ...prev,
      sortBy: 'newest',
      location: undefined
    }));
  };

  const requestLocationManually = () => {
    // إعادة تعيين حالة الإذن للسماح بطلب جديد
    localStorage.removeItem('locationPermissionStatus');
    localStorage.removeItem('locationRequested');
    setLocationRequested(false);
    setPermissionAsked(false);
    setShowLocationPermission(true);
  };

  // Pagination
  const totalPages = Math.ceil(filteredStores.length / storesPerPage);
  const startIndex = (currentPage - 1) * storesPerPage;
  const endIndex = startIndex + storesPerPage;
  const currentStores = filteredStores.slice(startIndex, endIndex);

  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Skeleton className="h-14 w-14 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />
              <div className="flex gap-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      {/* Location Permission Modal */}
      {showLocationPermission && (
        <LocationPermission
          onAllow={handleLocationAllow}
          onDeny={handleLocationDeny}
          loading={locationLoading}
          error={locationError}
        />
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Store className="w-8 h-8 text-primary mr-3" />
              <div>
                <h1 className="text-3xl font-bold">{t('stores')}</h1>
                <p className="text-muted-foreground">
                  {latitude && longitude
                    ? t('findNearbyStores')
                    : t('discoverLocalStores')
                  }
                </p>
              </div>
            </div>

            {/* Location Status */}
            <div className="text-sm">
              {locationLoading ? (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{t('detectingLocation')}</span>
                </div>
              ) : latitude && longitude ? (
                <div className="flex items-center gap-2 text-green-600">
                  <Navigation className="w-4 h-4" />
                  <span>{t('locationDetected')}</span>
                  {accuracy && (
                    <Badge variant="secondary" className="text-xs">
                      ±{Math.round(accuracy)}m
                    </Badge>
                  )}
                </div>
              ) : locationError ? (
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-red-500 text-xs">{t('locationError')}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={requestLocationManually}
                    className="text-xs"
                  >
                    <Target className="w-3 h-3 mr-1" />
                    {t('enableLocation')}
                  </Button>
                </div>
              ) : permissionAsked && localStorage.getItem('locationPermissionStatus') === 'denied' ? (
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                  <span className="text-orange-500 text-xs">{t('locationPermissionDeniedPreviously')}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={requestLocationManually}
                    className="text-xs"
                  >
                    <Target className="w-3 h-3 mr-1" />
                    {t('retryLocationPermission')}
                  </Button>
                </div>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={requestLocationManually}
                >
                  <Target className="w-4 h-4 mr-2" />
                  {t('enableLocation')}
                </Button>
              )}
            </div>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl">
            <SearchBar
              placeholder={t('searchStores')}
              onSearch={handleSearch}
              variant="default"
            />
          </div>
        </div>

        {/* Auto Language Detector */}
        <AutoLanguageDetector currentLocale={t('locale') as any} />

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Advanced Filters Sidebar */}
          <div className="lg:w-80 flex-shrink-0">
            {useAdvancedFilters ? (
              <AdvancedFilterSidebar
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onClearFilters={handleClearFilters}
                isMobile={isMobile}
                className={isMobile ? "" : "sticky top-4"}
                availableCategories={availableCategories}
                storeStats={storeStats}
              />
            ) : (
              <FilterSidebar
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onClearFilters={handleClearFilters}
                isMobile={isMobile}
                className={isMobile ? "" : "sticky top-4"}
              />
            )}

            {/* Toggle Advanced Filters */}
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setUseAdvancedFilters(!useAdvancedFilters)}
                className="w-full"
              >
                {useAdvancedFilters ? 'فلاتر بسيطة' : 'فلاتر متقدمة'}
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 space-y-6">
            {/* Sorting Controls */}
            <SortingControls
              filters={filters}
              onFiltersChange={handleFiltersChange}
              totalResults={filteredStores.length}
              showViewMode={true}
            />

            {/* Active Filters Bar */}
            <ActiveFiltersBar
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={handleClearFilters}
              availableCategories={availableCategories}
            />
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <span className="text-sm text-muted-foreground">
                  {isLoading ? (
                    <div className="flex items-center">
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      {t('loading')}
                    </div>
                  ) : (
                    t('storesFound', { count: filteredStores.length })
                  )}
                </span>
                {filters.query && (
                  <Badge variant="secondary">
                    {t('searchResults')}: "{filters.query}"
                  </Badge>
                )}
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Stores Grid/List */}
            {isLoading ? (
              <LoadingSkeleton />
            ) : filteredStores.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Store className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{t('noStoresFound')}</h3>
                  <p className="text-muted-foreground mb-4">
                    {filters.query || filters.category
                      ? t('tryDifferentFilters')
                      : t('noStoresAvailable')
                    }
                  </p>
                  {(filters.query || filters.category) && (
                    <Button onClick={handleClearFilters}>
                      {t('clearFilters')}
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <>
                <div className={
                  viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                    : "space-y-4"
                }>
                  {currentStores.map((store) => (
                    <StoreCard
                      key={store.merchantUid}
                      store={store}
                      showDistance={!!filters.location}
                      variant={viewMode === 'list' ? 'compact' : 'default'}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse mt-8">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      {t('previous')}
                    </Button>

                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                        const pageNum = i + 1;
                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      {t('next')}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
