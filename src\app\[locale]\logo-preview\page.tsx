'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, AnimatedLogo } from '@/components/Logo';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export default function LogoPreviewPage() {
  const [size, setSize] = useState<'small' | 'default' | 'large'>('default');
  const [showText, setShowText] = useState(true);
  const [interactive, setInteractive] = useState(true);
  const [animated, setAnimated] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  
  const handleReplay = () => {
    setAnimated(false);
    setTimeout(() => setAnimated(true), 100);
  };

  return (
    <div className={`min-h-screen p-8 ${darkMode ? 'dark' : ''}`}>
      <div className="container mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center text-primary-foreground">معاينة شعار مِخْلاة</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="bg-card">
            <CardHeader>
              <CardTitle>الشعار المتحرك</CardTitle>
              <CardDescription>معاينة الشعار المتحرك مع خيارات التخصيص</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center justify-center p-10 min-h-[400px] bg-background rounded-md">
              <div className={`flex items-center justify-center transition-all duration-500 ${size === 'small' ? 'scale-75' : size === 'large' ? 'scale-125' : 'scale-100'}`}>
                <BrandLogo 
                  size={size} 
                  showText={showText} 
                  interactive={interactive} 
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-center gap-4">
              <Button onClick={handleReplay} variant="outline">إعادة تشغيل الرسوم المتحركة</Button>
              <Button onClick={() => setDarkMode(!darkMode)} variant="outline">
                {darkMode ? 'الوضع الفاتح' : 'الوضع الداكن'}
              </Button>
            </CardFooter>
          </Card>
          
          <Card className="bg-card">
            <CardHeader>
              <CardTitle>خيارات التخصيص</CardTitle>
              <CardDescription>تعديل مظهر الشعار</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">الحجم</h3>
                <Tabs defaultValue="default" onValueChange={(value) => setSize(value as any)}>
                  <TabsList className="grid grid-cols-3">
                    <TabsTrigger value="small">صغير</TabsTrigger>
                    <TabsTrigger value="default">متوسط</TabsTrigger>
                    <TabsTrigger value="large">كبير</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-text">إظهار النص</Label>
                <Switch 
                  id="show-text" 
                  checked={showText} 
                  onCheckedChange={setShowText} 
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="interactive">تفاعلي</Label>
                <Switch 
                  id="interactive" 
                  checked={interactive} 
                  onCheckedChange={setInteractive} 
                />
              </div>
            </CardContent>
            <CardFooter>
              <div className="w-full space-y-4">
                <h3 className="text-sm font-medium">أمثلة على استخدام الشعار</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex flex-col items-center gap-2">
                    <div className="p-4 bg-primary/10 rounded-md flex items-center justify-center">
                      <AnimatedLogo variant="small" />
                    </div>
                    <span className="text-xs">الشعار فقط</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <div className="p-4 bg-primary/10 rounded-md flex items-center justify-center">
                      <BrandLogo size="small" showText={false} />
                    </div>
                    <span className="text-xs">تفاعلي</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <div className="p-4 bg-primary/10 rounded-md flex items-center justify-center">
                      <BrandLogo size="small" />
                    </div>
                    <span className="text-xs">مع النص</span>
                  </div>
                </div>
              </div>
            </CardFooter>
          </Card>
        </div>
        
        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold mb-4">شعار مِخْلاة</h2>
          <p className="max-w-2xl mx-auto text-muted-foreground">
            تم تصميم شعار مِخْلاة ليعكس الهوية البصرية للمشروع بطريقة متحركة وجذابة. 
            يتميز الشعار بتصميم متحرك يجمع بين الحداثة والأصالة، مع تأثيرات حركية سلسة تضفي حيوية على الشعار.
          </p>
        </div>
      </div>
    </div>
  );
}
