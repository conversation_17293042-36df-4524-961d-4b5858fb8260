"use client";

import { useLocale } from '@/hooks/use-locale';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { X, RotateCcw, Filter } from 'lucide-react';
import type { SearchFilter } from '@/types';
import { cn } from '@/lib/utils';

interface ActiveFiltersProps {
  filters: SearchFilter;
  onFiltersChange: (filters: SearchFilter) => void;
  onClearFilters: () => void;
  className?: string;
  availableCategories?: Array<{ id: string; name: string }>;
}

interface ActiveFilter {
  id: string;
  label: string;
  value: string;
  type: 'category' | 'price' | 'rating' | 'distance' | 'switch' | 'other';
  onRemove: () => void;
}

export default function ActiveFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  className = "",
  availableCategories = []
}: ActiveFiltersProps) {
  const { t } = useLocale();

  const getActiveFilters = (): ActiveFilter[] => {
    const activeFilters: ActiveFilter[] = [];

    // فلاتر الفئات
    if (filters.categories?.length) {
      filters.categories.forEach(categoryId => {
        const category = availableCategories.find(cat => cat.id === categoryId);
        if (category) {
          activeFilters.push({
            id: `category-${categoryId}`,
            label: 'الفئة',
            value: category.name,
            type: 'category',
            onRemove: () => {
              const newCategories = filters.categories?.filter(id => id !== categoryId) || [];
              onFiltersChange({ ...filters, categories: newCategories });
            }
          });
        }
      });
    }

    // فلتر السعر
    if (filters.minPrice || filters.maxPrice) {
      const minPrice = filters.minPrice || 0;
      const maxPrice = filters.maxPrice || 1000;
      activeFilters.push({
        id: 'price-range',
        label: 'السعر',
        value: `${minPrice} - ${maxPrice} ريال`,
        type: 'price',
        onRemove: () => {
          onFiltersChange({ 
            ...filters, 
            minPrice: undefined, 
            maxPrice: undefined 
          });
        }
      });
    }

    // فلتر التقييم
    if (filters.minRating) {
      activeFilters.push({
        id: 'rating',
        label: 'التقييم',
        value: `${filters.minRating} نجوم فأكثر`,
        type: 'rating',
        onRemove: () => {
          onFiltersChange({ ...filters, minRating: undefined });
        }
      });
    }

    // فلتر المسافة
    if (filters.maxDistance && filters.maxDistance < 50) {
      activeFilters.push({
        id: 'distance',
        label: 'المسافة',
        value: `حتى ${filters.maxDistance} كم`,
        type: 'distance',
        onRemove: () => {
          onFiltersChange({ ...filters, maxDistance: undefined });
        }
      });
    }

    // فلاتر التبديل (Switch)
    const switchFilters = [
      { key: 'verifiedOnly', label: 'موثق فقط' },
      { key: 'openNow', label: 'مفتوح الآن' },
      { key: 'hasDelivery', label: 'يوفر التوصيل' },
      { key: 'fastDelivery', label: 'توصيل سريع' },
      { key: 'hasOffers', label: 'يحتوي على عروض' },
      { key: 'newArrivals', label: 'وصل حديثاً' },
      { key: 'trending', label: 'الأكثر رواجاً' },
      { key: 'featured', label: 'مميز' }
    ];

    switchFilters.forEach(({ key, label }) => {
      if (filters[key as keyof SearchFilter]) {
        activeFilters.push({
          id: key,
          label: 'خاصية',
          value: label,
          type: 'switch',
          onRemove: () => {
            onFiltersChange({ ...filters, [key]: false });
          }
        });
      }
    });

    return activeFilters;
  };

  const activeFilters = getActiveFilters();

  if (activeFilters.length === 0) {
    return null;
  }

  const getFilterColor = (type: ActiveFilter['type']): string => {
    switch (type) {
      case 'category':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'price':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'rating':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'distance':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'switch':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              الفلاتر النشطة ({activeFilters.length})
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            مسح الكل
          </Button>
        </div>

        <div className="flex flex-wrap gap-2">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.id}
              variant="secondary"
              className={cn(
                "flex items-center gap-1 px-3 py-1 text-xs font-medium transition-colors cursor-pointer",
                getFilterColor(filter.type)
              )}
              onClick={filter.onRemove}
            >
              <span className="text-xs opacity-75">{filter.label}:</span>
              <span>{filter.value}</span>
              <X className="w-3 h-3 ml-1 hover:bg-black/10 rounded-full p-0.5" />
            </Badge>
          ))}
        </div>

        {/* معلومات إضافية */}
        <div className="mt-3 pt-3 border-t border-border/50">
          <p className="text-xs text-muted-foreground">
            انقر على أي فلتر لإزالته، أو استخدم "مسح الكل" لإزالة جميع الفلاتر
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * مكون مبسط لعرض الفلاتر النشطة في شريط أفقي
 */
export function ActiveFiltersBar({
  filters,
  onFiltersChange,
  onClearFilters,
  className = "",
  availableCategories = []
}: ActiveFiltersProps) {
  const { t } = useLocale();

  const getActiveFilters = (): ActiveFilter[] => {
    const activeFilters: ActiveFilter[] = [];

    // فلاتر الفئات
    if (filters.categories?.length) {
      filters.categories.forEach(categoryId => {
        const category = availableCategories.find(cat => cat.id === categoryId);
        if (category) {
          activeFilters.push({
            id: `category-${categoryId}`,
            label: category.name,
            value: '',
            type: 'category',
            onRemove: () => {
              const newCategories = filters.categories?.filter(id => id !== categoryId) || [];
              onFiltersChange({ ...filters, categories: newCategories });
            }
          });
        }
      });
    }

    // فلتر السعر
    if (filters.minPrice || filters.maxPrice) {
      const minPrice = filters.minPrice || 0;
      const maxPrice = filters.maxPrice || 1000;
      activeFilters.push({
        id: 'price-range',
        label: `${minPrice} - ${maxPrice} ريال`,
        value: '',
        type: 'price',
        onRemove: () => {
          onFiltersChange({ 
            ...filters, 
            minPrice: undefined, 
            maxPrice: undefined 
          });
        }
      });
    }

    // فلتر التقييم
    if (filters.minRating) {
      activeFilters.push({
        id: 'rating',
        label: `${filters.minRating}+ نجوم`,
        value: '',
        type: 'rating',
        onRemove: () => {
          onFiltersChange({ ...filters, minRating: undefined });
        }
      });
    }

    // فلاتر أخرى
    const otherFilters = [
      { key: 'verifiedOnly', label: 'موثق' },
      { key: 'openNow', label: 'مفتوح' },
      { key: 'hasDelivery', label: 'توصيل' },
      { key: 'fastDelivery', label: 'سريع' },
      { key: 'hasOffers', label: 'عروض' },
      { key: 'newArrivals', label: 'جديد' },
      { key: 'trending', label: 'رائج' },
      { key: 'featured', label: 'مميز' }
    ];

    otherFilters.forEach(({ key, label }) => {
      if (filters[key as keyof SearchFilter]) {
        activeFilters.push({
          id: key,
          label,
          value: '',
          type: 'switch',
          onRemove: () => {
            onFiltersChange({ ...filters, [key]: false });
          }
        });
      }
    });

    return activeFilters;
  };

  const activeFilters = getActiveFilters();

  if (activeFilters.length === 0) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-2 py-2", className)}>
      <span className="text-sm text-muted-foreground whitespace-nowrap">
        الفلاتر:
      </span>
      
      <div className="flex items-center gap-2 flex-wrap">
        {activeFilters.map((filter) => (
          <Badge
            key={filter.id}
            variant="secondary"
            className="flex items-center gap-1 px-2 py-1 text-xs cursor-pointer hover:bg-secondary/80"
            onClick={filter.onRemove}
          >
            {filter.label}
            <X className="w-3 h-3" />
          </Badge>
        ))}
        
        {activeFilters.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-xs h-6 px-2 text-muted-foreground hover:text-foreground"
          >
            مسح الكل
          </Button>
        )}
      </div>
    </div>
  );
}
