"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Plus, Minus, Trash2, Eye, AlertTriangle } from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import { useCart } from "./CartProvider";
import Link from "next/link";
import type { CartItem as CartItemType } from "@/types";

interface CartItemProps {
  item: CartItemType;
  variant?: "default" | "compact" | "checkout";
  showStore?: boolean;
  className?: string;
}

export default function CartItem({
  item,
  variant = "default",
  showStore = true,
  className = ""
}: CartItemProps) {
  const { t, locale } = useLocale();
  const { updateItem, removeItem } = useCart();
  const [isUpdating, setIsUpdating] = useState(false);
  const [imageError, setImageError] = useState(false);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const getStoreInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1 || newQuantity > item.maxQuantity || isUpdating) return;
    
    setIsUpdating(true);
    try {
      await updateItem(item.id, newQuantity);
    } catch (error) {
      console.error('Error updating item quantity:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = async () => {
    setIsUpdating(true);
    try {
      await removeItem(item.id);
    } catch (error) {
      console.error('Error removing item:', error);
      setIsUpdating(false);
    }
  };

  const totalPrice = item.price * item.quantity;
  const isOutOfStock = item.quantity > item.maxQuantity;

  if (variant === "compact") {
    return (
      <div className={`flex items-center space-x-3 rtl:space-x-reverse p-3 border-b last:border-b-0 ${className}`}>
        {/* Product Image */}
        <div className="relative w-12 h-12 flex-shrink-0">
          <Link href={`/${locale}/products/${item.productId}`}>
            {!imageError && item.productImage ? (
              <img 
                src={item.productImage}
                alt={item.productName}
                className="w-full h-full object-cover rounded-lg"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center">
                <Eye className="w-4 h-4 text-muted-foreground" />
              </div>
            )}
          </Link>
        </div>

        {/* Product Info */}
        <div className="flex-1 min-w-0">
          <Link href={`/${locale}/products/${item.productId}`}>
            <h4 className="font-medium text-sm truncate hover:text-primary transition-colors">
              {item.productName}
            </h4>
          </Link>
          <div className="flex items-center justify-between mt-1">
            <span className="text-sm font-semibold text-primary">
              {formatPrice(totalPrice)}
            </span>
            <div className="flex items-center space-x-1 rtl:space-x-reverse">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleQuantityChange(item.quantity - 1)}
                disabled={item.quantity <= 1 || isUpdating}
                className="h-6 w-6 p-0"
              >
                <Minus className="w-3 h-3" />
              </Button>
              <span className="text-sm w-8 text-center">{item.quantity}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleQuantityChange(item.quantity + 1)}
                disabled={item.quantity >= item.maxQuantity || isUpdating}
                className="h-6 w-6 p-0"
              >
                <Plus className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === "checkout") {
    return (
      <div className={`flex items-center space-x-4 rtl:space-x-reverse p-4 border rounded-lg ${className}`}>
        {/* Product Image */}
        <div className="relative w-16 h-16 flex-shrink-0">
          {!imageError && item.productImage ? (
            <img 
              src={item.productImage}
              alt={item.productName}
              className="w-full h-full object-cover rounded-lg"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center">
              <Eye className="w-6 h-6 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="flex-1">
          <h4 className="font-semibold">{item.productName}</h4>
          {showStore && (
            <p className="text-sm text-muted-foreground">{item.storeName}</p>
          )}
          
          {/* Variants */}
          {item.selectedVariants && Object.keys(item.selectedVariants).length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {Object.entries(item.selectedVariants).map(([key, value]) => (
                <Badge key={key} variant="outline" className="text-xs">
                  {key}: {value}
                </Badge>
              ))}
            </div>
          )}

          <div className="flex items-center justify-between mt-2">
            <div className="text-sm text-muted-foreground">
              {formatPrice(item.price)} × {item.quantity}
            </div>
            <div className="font-semibold text-primary">
              {formatPrice(totalPrice)}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex space-x-4 rtl:space-x-reverse">
          {/* Product Image */}
          <div className="relative w-20 h-20 flex-shrink-0">
            <Link href={`/${locale}/products/${item.productId}`}>
              {!imageError && item.productImage ? (
                <img 
                  src={item.productImage}
                  alt={item.productName}
                  className="w-full h-full object-cover rounded-lg"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center">
                  <Eye className="w-8 h-8 text-muted-foreground" />
                </div>
              )}
            </Link>
            
            {isOutOfStock && (
              <div className="absolute -top-1 -right-1">
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  {t('outOfStock')}
                </Badge>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="flex-1 space-y-2">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link href={`/${locale}/products/${item.productId}`}>
                  <h4 className="font-semibold hover:text-primary transition-colors">
                    {item.productName}
                  </h4>
                </Link>
                
                {showStore && (
                  <div className="flex items-center mt-1">
                    <Avatar className="h-4 w-4 mr-2">
                      <AvatarImage src="" />
                      <AvatarFallback className="text-xs">
                        {getStoreInitials(item.storeName)}
                      </AvatarFallback>
                    </Avatar>
                    <Link href={`/${locale}/stores/${item.storeId}`}>
                      <span className="text-sm text-muted-foreground hover:text-primary transition-colors">
                        {item.storeName}
                      </span>
                    </Link>
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemove}
                disabled={isUpdating}
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            {/* Variants */}
            {item.selectedVariants && Object.keys(item.selectedVariants).length > 0 && (
              <div className="flex flex-wrap gap-1">
                {Object.entries(item.selectedVariants).map(([key, value]) => (
                  <Badge key={key} variant="outline" className="text-xs">
                    {key}: {value}
                  </Badge>
                ))}
              </div>
            )}

            {/* Price and Quantity */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <span className="text-sm text-muted-foreground">{t('price')}:</span>
                <span className="font-semibold">{formatPrice(item.price)}</span>
              </div>
              
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <span className="text-sm text-muted-foreground">{t('quantity')}:</span>
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(item.quantity - 1)}
                    disabled={item.quantity <= 1 || isUpdating}
                    className="h-8 w-8 p-0"
                  >
                    <Minus className="w-3 h-3" />
                  </Button>
                  <Input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => {
                      const newQuantity = parseInt(e.target.value) || 1;
                      handleQuantityChange(newQuantity);
                    }}
                    className="w-12 h-8 text-center border-0 focus-visible:ring-0"
                    min={1}
                    max={item.maxQuantity}
                    disabled={isUpdating}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(item.quantity + 1)}
                    disabled={item.quantity >= item.maxQuantity || isUpdating}
                    className="h-8 w-8 p-0"
                  >
                    <Plus className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Total Price */}
            <div className="flex items-center justify-between pt-2 border-t">
              <span className="text-sm text-muted-foreground">{t('total')}:</span>
              <span className="text-lg font-bold text-primary">
                {formatPrice(totalPrice)}
              </span>
            </div>

            {/* Stock Warning */}
            {isOutOfStock && (
              <div className="flex items-center text-sm text-destructive">
                <AlertTriangle className="w-4 h-4 mr-2" />
                {t('quantityExceedsStock')}
              </div>
            )}

            {/* Low Stock Warning */}
            {!isOutOfStock && item.maxQuantity <= 5 && (
              <div className="text-sm text-orange-600">
                {t('onlyXLeft', { count: item.maxQuantity })}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
