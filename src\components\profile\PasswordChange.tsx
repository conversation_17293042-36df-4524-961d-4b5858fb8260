"use client";

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLocale } from '@/hooks/use-locale';
import { auth } from '@/lib/firebase';
import { updatePassword, reauthenticateWithCredential, EmailAuthProvider } from 'firebase/auth';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Lock } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// زود سكيما للتحقق من صحة كلمات المرور
const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, {
    message: "يجب إدخال كلمة المرور الحالية",
  }),
  newPassword: z.string().min(8, {
    message: "يجب أن تتكون كلمة المرور من 8 أحرف على الأقل",
  }),
  confirmPassword: z.string().min(1, {
    message: "يجب تأكيد كلمة المرور الجديدة",
  }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "كلمات المرور غير متطابقة",
  path: ["confirmPassword"],
});

type PasswordChangeFormValues = z.infer<typeof passwordChangeSchema>;

export default function PasswordChange() {
  const { t } = useLocale();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // إعداد نموذج react-hook-form مع التحقق من زود
  const form = useForm<PasswordChangeFormValues>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (values: PasswordChangeFormValues) => {
    if (!auth.currentUser || !auth.currentUser.email) {
      toast({ 
        title: t('errorTitle'), 
        description: t('notLoggedIn'), 
        variant: 'destructive' 
      });
      return;
    }

    setIsLoading(true);
    try {
      // إعادة المصادقة قبل تغيير كلمة المرور
      const credential = EmailAuthProvider.credential(
        auth.currentUser.email,
        values.currentPassword
      );
      
      await reauthenticateWithCredential(auth.currentUser, credential);
      
      // تحديث كلمة المرور
      await updatePassword(auth.currentUser, values.newPassword);
      
      toast({ 
        title: t('passwordChangeSuccessTitle'), 
        description: t('passwordChangeSuccessMessage') 
      });
      
      // إعادة تعيين النموذج
      form.reset();
    } catch (error: any) {
      console.error("Password change error:", error);
      
      // رسائل خطأ مخصصة حسب نوع الخطأ
      if (error.code === 'auth/wrong-password') {
        toast({
          title: t('errorTitle'),
          description: t('wrongCurrentPassword'),
          variant: 'destructive',
        });
      } else if (error.code === 'auth/weak-password') {
        toast({
          title: t('errorTitle'),
          description: t('weakPassword'),
          variant: 'destructive',
        });
      } else if (error.code === 'auth/requires-recent-login') {
        toast({
          title: t('errorTitle'),
          description: t('recentLoginRequired'),
          variant: 'destructive',
        });
      } else {
        toast({
          title: t('errorTitle'),
          description: error.message || t('passwordChangeFailed'),
          variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center">
          <Lock className="me-2 h-5 w-5" />
          {t('changePassword')}
        </CardTitle>
        <CardDescription>{t('changePasswordDescription')}</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('currentPassword')}</FormLabel>
                  <FormControl>
                    <Input 
                      type="password" 
                      placeholder={t('enterCurrentPassword')} 
                      disabled={isLoading}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('newPassword')}</FormLabel>
                  <FormControl>
                    <Input 
                      type="password" 
                      placeholder={t('enterNewPassword')} 
                      disabled={isLoading}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('confirmPassword')}</FormLabel>
                  <FormControl>
                    <Input 
                      type="password" 
                      placeholder={t('confirmNewPassword')} 
                      disabled={isLoading}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-center">
              <Button type="submit" className="w-full md:w-1/2" disabled={isLoading}>
                {isLoading && <Loader2 className="me-2 h-4 w-4 animate-spin" />}
                <Lock className={`me-2 h-4 w-4 ${isLoading ? 'hidden' : ''}`} />
                {t('updatePassword')}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
