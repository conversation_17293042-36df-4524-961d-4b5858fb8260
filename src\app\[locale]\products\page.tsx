// src/app/[locale]/products/page.tsx
"use client";

import { useEffect, useState } from 'react';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import type { ProductDocument } from '@/types';
import { useLocale } from '@/hooks/use-locale';
import ProductCard from '@/components/common/ProductCard';
import { Loader2, PackageSearch } from 'lucide-react';
import { Button } from '@/components/ui/button'; // For potential future "Load More"

export default function ProductsPage() {
  const { t, locale, isLoading: translationsLoading } = useLocale();
  const [products, setProducts] = useState<ProductDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // const [lastVisible, setLastVisible] = useState<QueryDocumentSnapshot | null>(null); // For pagination
  // const [hasMore, setHasMore] = useState(true); // For pagination

  const PRODUCTS_PER_PAGE = 12;

  // Debug log to check translations (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('ProductsPage - Current locale:', locale);
    console.log('ProductsPage - Translations loading:', translationsLoading);
    console.log('ProductsPage - Sample translations:', {
      loadingProducts: t('loadingProducts'),
      browseAllProductsTitle: t('browseAllProductsTitle'),
      noProductsAvailableTitle: t('noProductsAvailableTitle')
    });
  }

  useEffect(() => {
    // Don't fetch products until translations are loaded
    if (translationsLoading) {
      if (process.env.NODE_ENV === 'development') {
        console.log('ProductsPage - Waiting for translations to load...');
      }
      return;
    }

    const fetchProducts = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('ProductsPage - Starting to fetch products...');
      }
      setIsLoading(true);
      setError(null);
      try {
        const productsRef = collection(db, 'products');
        const q = query(
          productsRef,
          where('isActive', '==', true),
          orderBy('createdAt', 'desc'),
          limit(PRODUCTS_PER_PAGE)
        );
        const querySnapshot = await getDocs(q);
        const fetchedProducts = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as ProductDocument));
        setProducts(fetchedProducts);
        if (process.env.NODE_ENV === 'development') {
          console.log(`ProductsPage - Fetched ${fetchedProducts.length} products`);
        }
        // setLastVisible(querySnapshot.docs[querySnapshot.docs.length - 1]);
        // setHasMore(fetchedProducts.length === PRODUCTS_PER_PAGE);
      } catch (err) {
        console.error("Error fetching products:", err);
        setError(t('errorFetchingProductsGeneral'));
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, [t, translationsLoading]); // Wait for translations to be ready

  // TODO: Implement loadMoreProducts function for pagination

  if (isLoading || translationsLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">
          {translationsLoading ? 'Loading...' : t('loadingProducts')}
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <PackageSearch className="h-16 w-16 text-destructive mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-destructive mb-2">{t('errorTitle')}</h2>
        <p className="text-muted-foreground">{error}</p>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-20">
        <PackageSearch className="h-24 w-24 text-muted-foreground mx-auto mb-6" />
        <h2 className="text-3xl font-semibold mb-3">{t('noProductsAvailableTitle')}</h2>
        <p className="text-muted-foreground text-lg">{t('noProductsAvailableDesc')}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl md:text-4xl font-bold text-primary mb-8 text-center">
        {t('browseAllProductsTitle')}
      </h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
      {/* TODO: Add Load More Button if hasMore */}
      {/* {hasMore && (
        <div className="mt-12 text-center">
          <Button onClick={loadMoreProducts} disabled={isLoadingMore}>
            {isLoadingMore ? <Loader2 className="me-2 h-4 w-4 animate-spin" /> : null}
            {t('loadMoreProducts')}
          </Button>
        </div>
      )} */}
    </div>
  );
}
