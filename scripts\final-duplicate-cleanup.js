#!/usr/bin/env node

/**
 * سكريبت التنظيف النهائي للتكرارات في ملفات الترجمة
 * يقوم بإزالة جميع التكرارات المتبقية بطريقة ذكية
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'en.json');

/**
 * قراءة ملف JSON بشكل آمن
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * كتابة ملف JSON بشكل آمن
 */
function writeJsonFile(filePath, data) {
  try {
    const content = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في كتابة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * إنشاء نسخة احتياطية
 */
function createBackup(filePath) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في إنشاء نسخة احتياطية:`, error.message);
    return false;
  }
}

/**
 * إصلاح التكرارات في الملف العربي
 */
function fixArabicFinalDuplicates(data) {
  const fixes = [];
  
  // إصلاح تكرار profile
  if (data.profile && data.representative && data.representative.profile) {
    // دمج البيانات من representative.profile إلى profile الرئيسي
    Object.assign(data.profile, data.representative.profile);
    delete data.representative.profile;
    fixes.push('representative.profile merged into main profile');
  }
  
  // إصلاح تكرار dashboard
  if (data.dashboard && data.representative && data.representative.dashboard) {
    delete data.representative.dashboard;
    fixes.push('removed duplicate representative.dashboard');
  }
  
  // إصلاح تكرار orders
  if (data.orders && data.representative && data.representative.orders) {
    delete data.representative.orders;
    fixes.push('removed duplicate representative.orders');
  }
  
  // إصلاح تكرار earnings
  if (data.earnings && data.representative && data.representative.earnings) {
    delete data.representative.earnings;
    fixes.push('removed duplicate representative.earnings');
  }
  
  return fixes;
}

/**
 * إصلاح التكرارات في الملف الإنجليزي
 */
function fixEnglishFinalDuplicates(data) {
  const fixes = [];
  
  // إصلاح تكرار profile
  if (data.profile && data.representative && data.representative.profile) {
    Object.assign(data.profile, data.representative.profile);
    delete data.representative.profile;
    fixes.push('representative.profile merged into main profile');
  }
  
  // إصلاح تكرار representative
  if (data.representative && data.representative.representative) {
    Object.assign(data.representative, data.representative.representative);
    delete data.representative.representative;
    fixes.push('merged nested representative');
  }
  
  // إصلاح تكرار dashboard
  if (data.dashboard && data.representative && data.representative.dashboard) {
    delete data.representative.dashboard;
    fixes.push('removed duplicate representative.dashboard');
  }
  
  // إصلاح تكرار orders
  if (data.orders && data.representative && data.representative.orders) {
    delete data.representative.orders;
    fixes.push('removed duplicate representative.orders');
  }
  
  // إصلاح تكرار earnings
  if (data.earnings && data.representative && data.representative.earnings) {
    delete data.representative.earnings;
    fixes.push('removed duplicate representative.earnings');
  }
  
  // إصلاح تكرارات أخرى في representative
  const duplicateKeys = ['loading', 'active', 'inactive', 'available', 'unavailable', 
                        'totalDeliveries', 'monthlyEarnings', 'totalEarnings', 
                        'averageRating', 'reviews', 'minutes'];
  
  duplicateKeys.forEach(key => {
    if (data[key] && data.representative && data.representative[key]) {
      delete data.representative[key];
      fixes.push(`removed duplicate representative.${key}`);
    }
  });
  
  return fixes;
}

/**
 * معالجة ملف ترجمة واحد
 */
function processTranslationFile(filePath, isArabic = false) {
  console.log(`🔧 معالجة الملف: ${path.basename(filePath)}`);
  
  // إنشاء نسخة احتياطية
  if (!createBackup(filePath)) {
    return { success: false, fixesApplied: 0 };
  }
  
  // قراءة البيانات
  const data = readJsonFile(filePath);
  if (!data) {
    return { success: false, fixesApplied: 0 };
  }
  
  // تطبيق الإصلاحات
  const fixes = isArabic ? fixArabicFinalDuplicates(data) : fixEnglishFinalDuplicates(data);
  
  if (fixes.length > 0) {
    // كتابة البيانات المصلحة
    if (writeJsonFile(filePath, data)) {
      console.log(`   ✅ تم إصلاح ${fixes.length} تكرار`);
      fixes.forEach(fix => console.log(`   📝 ${fix}`));
      return { success: true, fixesApplied: fixes.length, fixes };
    } else {
      return { success: false, fixesApplied: 0 };
    }
  } else {
    console.log(`   ✅ لا توجد تكرارات للإصلاح`);
    return { success: true, fixesApplied: 0 };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧹 بدء التنظيف النهائي لتكرارات ملفات الترجمة...\n');
  
  let totalSuccess = 0;
  let totalFixes = 0;
  
  // معالجة الملف العربي
  console.log('📝 معالجة الملف العربي:');
  const arResult = processTranslationFile(AR_TRANSLATIONS_PATH, true);
  if (arResult.success) {
    totalSuccess++;
    totalFixes += arResult.fixesApplied;
  }
  
  console.log('');
  
  // معالجة الملف الإنجليزي
  console.log('📝 معالجة الملف الإنجليزي:');
  const enResult = processTranslationFile(EN_TRANSLATIONS_PATH, false);
  if (enResult.success) {
    totalSuccess++;
    totalFixes += enResult.fixesApplied;
  }
  
  console.log('');
  
  if (totalSuccess === 2) {
    console.log('🎉 تم التنظيف النهائي لتكرارات الترجمة بنجاح!');
    console.log(`📊 إجمالي الإصلاحات المطبقة: ${totalFixes}`);
    console.log('💡 تم إنشاء نسخ احتياطية من الملفات');
    
    // تشغيل التحقق النهائي
    console.log('\n🔍 تشغيل التحقق النهائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في معالجة بعض الملفات');
    console.log(`📊 الملفات المعالجة بنجاح: ${totalSuccess}/2`);
    console.log('🔄 يمكنك استعادة النسخ الاحتياطية إذا لزم الأمر');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  fixArabicFinalDuplicates,
  fixEnglishFinalDuplicates,
  processTranslationFile
};
