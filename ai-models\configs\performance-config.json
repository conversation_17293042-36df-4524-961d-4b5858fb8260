{"version": "2.0.0", "description": "إعدادات الأداء المحسنة للنماذج المحلية", "memory": {"maxTotalMemory": "1024MB", "maxModelMemory": "512MB", "memoryThreshold": 0.85, "autoCleanup": true, "cleanupInterval": 300000, "garbageCollection": {"enabled": true, "interval": 60000, "aggressive": false}}, "processing": {"maxConcurrentAnalysis": 3, "analysisTimeout": 30000, "queueSize": 10, "priorityQueue": true, "batchProcessing": {"enabled": true, "batchSize": 5, "batchTimeout": 10000}}, "cache": {"enabled": true, "maxSize": "256MB", "ttl": 3600000, "strategy": "LRU", "compression": true, "persistence": false}, "workers": {"enabled": true, "maxWorkers": 4, "workerTimeout": 30000, "dedicatedWorkers": {"ocr": 2, "nlp": 1, "validation": 1}}, "optimization": {"modelQuantization": true, "modelPruning": true, "tensorOptimization": true, "webglAcceleration": true, "wasmOptimization": true}, "monitoring": {"enabled": true, "metricsCollection": true, "performanceLogging": true, "alertThresholds": {"memoryUsage": 0.9, "processingTime": 15000, "errorRate": 0.05}}, "fallback": {"enabled": true, "fallbackModels": {"ocr": ["tesseract_arabic", "easyocr_arabic"], "classification": ["document_classifier"], "validation": ["format_validator"]}, "degradedMode": {"enabled": true, "reducedAccuracy": true, "fasterProcessing": true}}}