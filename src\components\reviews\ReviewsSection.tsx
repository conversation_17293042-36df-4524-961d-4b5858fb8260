'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Star, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ReviewStats, ReviewsList } from '@/components/reviews';
import { useReviews } from '@/hooks/useReviews';

interface ReviewsSectionProps {
  targetId: string;
  type: 'store' | 'product';
  targetName?: string;
  showAddReview?: boolean;
  maxReviews?: number;
  className?: string;
}

export function ReviewsSection({ 
  targetId, 
  type, 
  targetName,
  showAddReview = false,
  maxReviews = 3,
  className 
}: ReviewsSectionProps) {
  const t = useTranslations();
  const { stats } = useReviews({ targetId, type, autoFetch: true });
  const [showAllReviews, setShowAllReviews] = useState(false);

  const reviewsPageUrl = type === 'product' 
    ? `/products/${targetId}/reviews`
    : `/stores/${targetId}/reviews`;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* عنوان القسم */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">
          {t('reviews.ratingsAndReviews')}
        </h2>
        
        {stats && stats.totalReviews > maxReviews && (
          <Link href={reviewsPageUrl}>
            <Button variant="outline" size="sm">
              {t('reviews.viewAllReviews')}
              <ChevronRight className="h-4 w-4 ms-1" />
            </Button>
          </Link>
        )}
      </div>

      {/* إحصائيات سريعة */}
      {stats && stats.totalReviews > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {stats.averageRating.toFixed(1)}
              </div>
              <div className="flex items-center justify-center mb-2">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(stats.averageRating) 
                        ? 'fill-yellow-400 text-yellow-400' 
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <div className="text-sm text-gray-600">
                {t('reviews.totalReviews', { count: stats.totalReviews })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-3xl font-bold text-green-600 mb-1">
                {Math.round(((stats.ratingDistribution[4] + stats.ratingDistribution[5]) / stats.totalReviews) * 100)}%
              </div>
              <div className="text-sm text-gray-600">
                {t('reviews.positiveReviews')}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                4-5 {t('reviews.stars')}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-1">
                {stats.verifiedReviews}
              </div>
              <div className="text-sm text-gray-600">
                {t('reviews.verifiedReviews', { count: stats.verifiedReviews })}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {Math.round((stats.verifiedReviews / stats.totalReviews) * 100)}% {t('reviews.verified')}
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('reviews.noReviews')}
            </h3>
            <p className="text-gray-500 mb-4">
              {t('reviews.beFirstToReview')}
            </p>
            {showAddReview && (
              <Link href={reviewsPageUrl}>
                <Button>
                  <Star className="h-4 w-4 me-2" />
                  {type === 'product' 
                    ? t('reviews.addProductReview')
                    : t('reviews.addStoreReview')
                  }
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      )}

      {/* عرض المراجعات */}
      {stats && stats.totalReviews > 0 && (
        <>
          <Separator />
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {t('reviews.recentReviews')}
              </h3>
              
              {showAddReview && (
                <Link href={reviewsPageUrl}>
                  <Button variant="outline" size="sm">
                    <Star className="h-4 w-4 me-2" />
                    {type === 'product' 
                      ? t('reviews.addProductReview')
                      : t('reviews.addStoreReview')
                    }
                  </Button>
                </Link>
              )}
            </div>

            <ReviewsList
              targetId={targetId}
              type={type}
              className="space-y-3"
            />

            {stats.totalReviews > maxReviews && (
              <div className="text-center pt-4">
                <Link href={reviewsPageUrl}>
                  <Button variant="outline">
                    {t('reviews.viewAllReviews')} ({stats.totalReviews})
                    <ChevronRight className="h-4 w-4 ms-1" />
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </>
      )}

      {/* دعوة لإضافة مراجعة */}
      {showAddReview && stats && stats.totalReviews > 0 && (
        <>
          <Separator />
          
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-6 text-center">
              <Star className="h-10 w-10 text-blue-500 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('reviews.shareYourExperience')}
              </h3>
              <p className="text-gray-600 mb-4">
                {type === 'product'
                  ? t('reviews.helpOthersWithYourReview')
                  : t('reviews.helpOthersWithYourStoreReview')
                }
              </p>
              <Link href={reviewsPageUrl}>
                <Button>
                  <Star className="h-4 w-4 me-2" />
                  {type === 'product' 
                    ? t('reviews.addProductReview')
                    : t('reviews.addStoreReview')
                  }
                </Button>
              </Link>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
