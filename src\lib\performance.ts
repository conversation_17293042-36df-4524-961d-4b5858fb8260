// src/lib/performance.ts
// مكتبة تحسين الأداء العام للتطبيق

/**
 * تحسين تحميل الترجمات وتجنب التحميل المتكرر
 */
export class TranslationPerformanceManager {
  private static loadingPromises = new Map<string, Promise<any>>();
  
  /**
   * تحميل الترجمات مع تجنب التحميل المتكرر
   */
  static async loadTranslationOnce<T>(
    key: string, 
    loader: () => Promise<T>
  ): Promise<T> {
    // إذا كان هناك طلب تحميل جاري، انتظر نتيجته
    if (this.loadingPromises.has(key)) {
      return this.loadingPromises.get(key)!;
    }
    
    // إنشاء طلب تحميل جديد
    const loadingPromise = loader();
    this.loadingPromises.set(key, loadingPromise);
    
    try {
      const result = await loadingPromise;
      return result;
    } finally {
      // إزالة الطلب من الذاكرة بعد الانتهاء
      this.loadingPromises.delete(key);
    }
  }
  
  /**
   * مسح جميع طلبات التحميل المعلقة
   */
  static clearLoadingPromises() {
    this.loadingPromises.clear();
  }
}

/**
 * تحسين console logs في بيئة الإنتاج
 */
export class LogManager {
  private static isDevelopment = process.env.NODE_ENV === 'development';
  
  static log(message: string, ...args: any[]) {
    if (this.isDevelopment) {
      console.log(message, ...args);
    }
  }
  
  static warn(message: string, ...args: any[]) {
    if (this.isDevelopment) {
      console.warn(message, ...args);
    }
  }
  
  static error(message: string, ...args: any[]) {
    // الأخطاء تظهر دائماً
    console.error(message, ...args);
  }
  
  static info(message: string, ...args: any[]) {
    if (this.isDevelopment) {
      console.info(message, ...args);
    }
  }
}

/**
 * تحسين تحميل المكونات
 */
export class ComponentLoadingManager {
  private static loadingStates = new Map<string, boolean>();
  
  static setLoading(componentId: string, isLoading: boolean) {
    this.loadingStates.set(componentId, isLoading);
  }
  
  static isLoading(componentId: string): boolean {
    return this.loadingStates.get(componentId) || false;
  }
  
  static clearLoadingState(componentId: string) {
    this.loadingStates.delete(componentId);
  }
  
  static clearAllLoadingStates() {
    this.loadingStates.clear();
  }
}

/**
 * تحسين إدارة الذاكرة
 */
export class MemoryManager {
  /**
   * مسح البيانات غير المستخدمة من الذاكرة
   */
  static cleanup() {
    TranslationPerformanceManager.clearLoadingPromises();
    ComponentLoadingManager.clearAllLoadingStates();
    
    // تشغيل garbage collection إذا كان متاحاً
    if (typeof window !== 'undefined' && 'gc' in window) {
      try {
        (window as any).gc();
      } catch (e) {
        // تجاهل الأخطاء
      }
    }
  }
  
  /**
   * مراقبة استخدام الذاكرة (في بيئة التطوير فقط)
   */
  static monitorMemoryUsage() {
    if (process.env.NODE_ENV === 'development' && 'performance' in window && 'memory' in (performance as any)) {
      const memory = (performance as any).memory;
      LogManager.info('Memory Usage:', {
        used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`,
        total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)} MB`,
        limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`
      });
    }
  }
}

/**
 * تحسين الأداء العام
 */
export class PerformanceOptimizer {
  /**
   * تهيئة تحسينات الأداء
   */
  static initialize() {
    if (typeof window !== 'undefined') {
      // تنظيف الذاكرة كل 5 دقائق
      setInterval(() => {
        MemoryManager.cleanup();
      }, 5 * 60 * 1000);
      
      // مراقبة الذاكرة كل دقيقة في بيئة التطوير
      if (process.env.NODE_ENV === 'development') {
        setInterval(() => {
          MemoryManager.monitorMemoryUsage();
        }, 60 * 1000);
      }
    }
  }
  
  /**
   * تحسين تحميل الصفحات
   */
  static optimizePageLoad() {
    if (typeof window !== 'undefined') {
      // تحسين تحميل الخطوط
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = 'https://fonts.googleapis.com';
      document.head.appendChild(link);
      
      // تحسين تحميل الصور
      const imageLinks = document.querySelectorAll('img[data-src]');
      imageLinks.forEach((img) => {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const image = entry.target as HTMLImageElement;
              image.src = image.dataset.src || '';
              observer.unobserve(image);
            }
          });
        });
        observer.observe(img);
      });
    }
  }
}
