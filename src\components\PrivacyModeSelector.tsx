'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Cloud, Download, CheckCircle, AlertTriangle, Info } from 'lucide-react';

interface PrivacyMode {
  id: string;
  name: string;
  description: string;
  privacy: 'high' | 'medium' | 'low';
  accuracy: 'high' | 'medium' | 'low';
  speed: 'fast' | 'medium' | 'slow';
  cost: 'free' | 'low' | 'medium';
  dataProcessing: string;
  pros: string[];
  cons: string[];
  recommended: boolean;
}

const privacyModes: PrivacyMode[] = [
  {
    id: 'cloud',
    name: 'النظام السحابي',
    description: 'استخدام Google Gemini 2.0 Flash للحصول على أعلى دقة',
    privacy: 'medium',
    accuracy: 'high',
    speed: 'fast',
    cost: 'low',
    dataProcessing: 'معالجة خارجية مشفرة',
    pros: [
      'دقة عالية جداً (98%+)',
      'سرعة فائقة (< 2 ثانية)',
      'أحدث تقنيات الذكاء الاصطناعي',
      'لا يتطلب تحميل إضافي'
    ],
    cons: [
      'إرسال البيانات لخوادم Google',
      'يتطلب اتصال إنترنت',
      'تكلفة حسب الاستخدام'
    ],
    recommended: true
  },
  {
    id: 'local',
    name: 'النظام المحلي',
    description: 'معالجة محلية بالكامل - خصوصية 100%',
    privacy: 'high',
    accuracy: 'medium',
    speed: 'fast',
    cost: 'free',
    dataProcessing: 'معالجة محلية في المتصفح',
    pros: [
      'خصوصية كاملة 100%',
      'لا إرسال بيانات خارجياً',
      'يعمل بدون إنترنت',
      'مجاني بالكامل',
      'امتثال كامل لقوانين حماية البيانات'
    ],
    cons: [
      'دقة أقل (85-90%)',
      'يتطلب تحميل مكتبات (~3.5MB)',
      'استهلاك ذاكرة أعلى'
    ],
    recommended: false
  }
];

export default function PrivacyModeSelector() {
  const [selectedMode, setSelectedMode] = useState<string>('cloud');
  const [localModeLoading, setLocalModeLoading] = useState(false);
  const [localModeLoaded, setLocalModeLoaded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // تحقق من الوضع المحفوظ
    const savedMode = localStorage.getItem('ai_processing_mode');
    if (savedMode) {
      setSelectedMode(savedMode);
    }

    // تحقق من حالة النظام المحلي
    checkLocalModeStatus();
  }, []);

  const checkLocalModeStatus = () => {
    if (typeof window !== 'undefined' && window.privacyAILoader) {
      const status = window.privacyAILoader.getLoadingStatus();
      setLocalModeLoaded(status.loaded);
    }
  };

  const handleModeChange = async (modeId: string) => {
    setSelectedMode(modeId);
    localStorage.setItem('ai_processing_mode', modeId);

    if (modeId === 'local' && !localModeLoaded) {
      await enableLocalMode();
    }
  };

  const enableLocalMode = async () => {
    setLocalModeLoading(true);
    
    try {
      if (typeof window !== 'undefined' && window.enablePrivacyMode) {
        const success = await window.enablePrivacyMode();
        if (success) {
          setLocalModeLoaded(true);
          localStorage.setItem('privacy_mode_enabled', 'true');
        }
      }
    } catch (error) {
      console.error('فشل في تفعيل النظام المحلي:', error);
    } finally {
      setLocalModeLoading(false);
    }
  };

  const getPrivacyBadge = (level: string) => {
    const colors = {
      high: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-red-100 text-red-800'
    };
    
    const labels = {
      high: 'عالية',
      medium: 'متوسطة', 
      low: 'منخفضة'
    };

    return (
      <Badge className={colors[level as keyof typeof colors]}>
        {labels[level as keyof typeof labels]}
      </Badge>
    );
  };

  const getAccuracyBadge = (level: string) => {
    const colors = {
      high: 'bg-blue-100 text-blue-800',
      medium: 'bg-purple-100 text-purple-800',
      low: 'bg-gray-100 text-gray-800'
    };
    
    const labels = {
      high: 'عالية',
      medium: 'متوسطة',
      low: 'منخفضة'
    };

    return (
      <Badge className={colors[level as keyof typeof colors]}>
        {labels[level as keyof typeof labels]}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">اختر نظام معالجة المستندات</h2>
        <p className="text-gray-600">
          اختر النظام المناسب حسب احتياجاتك للخصوصية والدقة
        </p>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Info className="h-4 w-4 text-blue-500" />
          <span className="text-sm text-gray-600">عرض التفاصيل المتقدمة</span>
        </div>
        <Switch
          checked={showDetails}
          onCheckedChange={setShowDetails}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {privacyModes.map((mode) => (
          <Card 
            key={mode.id}
            className={`cursor-pointer transition-all ${
              selectedMode === mode.id 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:shadow-md'
            }`}
            onClick={() => handleModeChange(mode.id)}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {mode.id === 'cloud' ? (
                    <Cloud className="h-6 w-6 text-blue-500" />
                  ) : (
                    <Shield className="h-6 w-6 text-green-500" />
                  )}
                  <div>
                    <CardTitle className="text-lg">{mode.name}</CardTitle>
                    {mode.recommended && (
                      <Badge className="mt-1 bg-orange-100 text-orange-800">
                        موصى به
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex items-center">
                  {selectedMode === mode.id && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                </div>
              </div>
              <CardDescription>{mode.description}</CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">الخصوصية:</span>
                  <div className="mt-1">{getPrivacyBadge(mode.privacy)}</div>
                </div>
                <div>
                  <span className="font-medium">الدقة:</span>
                  <div className="mt-1">{getAccuracyBadge(mode.accuracy)}</div>
                </div>
              </div>

              <div className="text-sm">
                <span className="font-medium">نوع المعالجة:</span>
                <p className="text-gray-600 mt-1">{mode.dataProcessing}</p>
              </div>

              {showDetails && (
                <div className="space-y-3 pt-3 border-t">
                  <div>
                    <h4 className="font-medium text-green-700 mb-2">المميزات:</h4>
                    <ul className="text-sm space-y-1">
                      {mode.pros.map((pro, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{pro}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-orange-700 mb-2">الاعتبارات:</h4>
                    <ul className="text-sm space-y-1">
                      {mode.cons.map((con, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <AlertTriangle className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />
                          <span>{con}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {mode.id === 'local' && selectedMode === 'local' && (
                <div className="pt-3 border-t">
                  {!localModeLoaded ? (
                    <Button
                      onClick={enableLocalMode}
                      disabled={localModeLoading}
                      className="w-full"
                      variant="outline"
                    >
                      {localModeLoading ? (
                        <>
                          <Download className="h-4 w-4 mr-2 animate-spin" />
                          جاري تحميل المكتبات...
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          تحميل النظام المحلي (~3.5MB)
                        </>
                      )}
                    </Button>
                  ) : (
                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertDescription>
                        ✅ النظام المحلي جاهز - خصوصية 100% مضمونة
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>ملاحظة:</strong> يمكنك تغيير النظام في أي وقت من إعدادات الحساب.
          النظام السحابي مفعل افتراضياً للحصول على أفضل تجربة.
        </AlertDescription>
      </Alert>
    </div>
  );
}

// إضافة types للـ window
declare global {
  interface Window {
    privacyAILoader: any;
    enablePrivacyMode: () => Promise<boolean>;
  }
}
