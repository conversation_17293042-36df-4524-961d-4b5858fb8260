// src/components/admin/TopPerformers.tsx
"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTopPerformers } from '@/hooks/useAdminStats';
import { useLocale } from '@/hooks/use-locale';
import { 
  Store, 
  Package, 
  Truck, 
  TrendingUp, 
  TrendingDown,
  Star,
  DollarSign,
  ShoppingCart,
  Award,
  Crown,
  Medal,
  ExternalLink
} from 'lucide-react';

interface TopPerformersProps {
  className?: string;
  showTabs?: boolean;
  defaultTab?: 'merchants' | 'products' | 'representatives';
}

export function TopPerformers({
  className,
  showTabs = true,
  defaultTab = 'merchants'
}: TopPerformersProps) {
  const { t } = useLocale();
  const { topMerchants, topProducts, topRepresentatives, loading } = useTopPerformers();
  const [activeTab, setActiveTab] = useState(defaultTab);

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 1:
        return <Medal className="h-4 w-4 text-gray-400" />;
      case 2:
        return <Award className="h-4 w-4 text-amber-600" />;
      default:
        return <span className="text-sm font-bold text-muted-foreground">#{index + 1}</span>;
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatRating = (rating: number): string => {
    return rating.toFixed(1);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderMerchants = () => (
    <div className="space-y-3">
      {topMerchants.map((merchant: any, index) => (
        <div
          key={merchant.id}
          className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
        >
          {/* ترتيب */}
          <div className="flex-shrink-0 w-8 flex justify-center">
            {getRankIcon(index)}
          </div>

          {/* صورة المتجر */}
          <Avatar className="h-10 w-10">
            <AvatarImage src="" alt={merchant.name} />
            <AvatarFallback>
              <Store className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>

          {/* معلومات المتجر */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium truncate">{merchant.name}</h4>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                <ShoppingCart className="h-3 w-3 mr-1" />
                {merchant.orders} طلب
              </Badge>
              <span className="text-xs text-muted-foreground">
                {formatCurrency(merchant.revenue)}
              </span>
            </div>
          </div>

          {/* زر عرض التفاصيل */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );

  const renderProducts = () => (
    <div className="space-y-3">
      {topProducts.map((product: any, index) => (
        <div
          key={product.id}
          className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
        >
          {/* ترتيب */}
          <div className="flex-shrink-0 w-8 flex justify-center">
            {getRankIcon(index)}
          </div>

          {/* صورة المنتج */}
          <Avatar className="h-10 w-10">
            <AvatarImage src="" alt={product.name} />
            <AvatarFallback>
              <Package className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>

          {/* معلومات المنتج */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium truncate">{product.name}</h4>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                {product.sales} مبيعة
              </Badge>
              <span className="text-xs text-muted-foreground">
                {formatCurrency(product.revenue)}
              </span>
            </div>
          </div>

          {/* زر عرض التفاصيل */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );

  const renderRepresentatives = () => (
    <div className="space-y-3">
      {topRepresentatives.map((rep: any, index) => (
        <div
          key={rep.id}
          className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
        >
          {/* ترتيب */}
          <div className="flex-shrink-0 w-8 flex justify-center">
            {getRankIcon(index)}
          </div>

          {/* صورة المندوب */}
          <Avatar className="h-10 w-10">
            <AvatarImage src="" alt={rep.name} />
            <AvatarFallback>
              {rep.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>

          {/* معلومات المندوب */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium truncate">{rep.name}</h4>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                <Truck className="h-3 w-3 mr-1" />
                {rep.deliveries} توصيل
              </Badge>
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500" />
                <span className="text-xs text-muted-foreground">
                  {formatRating(rep.rating)}
                </span>
              </div>
            </div>
          </div>

          {/* زر عرض التفاصيل */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );

  if (!showTabs) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Award className="h-5 w-5" />
            {t('topPerformers')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {activeTab === 'merchants' && renderMerchants()}
          {activeTab === 'products' && renderProducts()}
          {activeTab === 'representatives' && renderRepresentatives()}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Award className="h-5 w-5" />
          {t('topPerformers')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="merchants" className="text-xs">
              <Store className="h-4 w-4 mr-1" />
              {t('topMerchants')}
            </TabsTrigger>
            <TabsTrigger value="products" className="text-xs">
              <Package className="h-4 w-4 mr-1" />
              {t('topProducts')}
            </TabsTrigger>
            <TabsTrigger value="representatives" className="text-xs">
              <Truck className="h-4 w-4 mr-1" />
              {t('topRepresentatives')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="merchants" className="mt-4">
            {renderMerchants()}
          </TabsContent>

          <TabsContent value="products" className="mt-4">
            {renderProducts()}
          </TabsContent>

          <TabsContent value="representatives" className="mt-4">
            {renderRepresentatives()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

// مكون مبسط لعرض أفضل مؤد واحد
interface TopPerformerCardProps {
  type: 'merchant' | 'product' | 'representative';
  data: any;
  className?: string;
}

export function TopPerformerCard({
  type,
  data,
  className
}: TopPerformerCardProps) {
  const { t } = useLocale();

  const getIcon = () => {
    switch (type) {
      case 'merchant':
        return <Store className="h-5 w-5" />;
      case 'product':
        return <Package className="h-5 w-5" />;
      case 'representative':
        return <Truck className="h-5 w-5" />;
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'merchant':
        return 'أفضل تاجر';
      case 'product':
        return 'أفضل منتج';
      case 'representative':
        return 'أفضل مندوب';
    }
  };

  const getMetric = () => {
    switch (type) {
      case 'merchant':
        return `${data.orders} طلب`;
      case 'product':
        return `${data.sales} مبيعة`;
      case 'representative':
        return `${data.deliveries} توصيل`;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          {getIcon()}
          {getTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src="" alt={data.name} />
            <AvatarFallback>
              {type === 'representative' 
                ? data.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()
                : getIcon()
              }
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <h4 className="font-medium truncate">{data.name}</h4>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                <Crown className="h-3 w-3 mr-1 text-yellow-500" />
                #1
              </Badge>
              <span className="text-sm text-muted-foreground">
                {getMetric()}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
