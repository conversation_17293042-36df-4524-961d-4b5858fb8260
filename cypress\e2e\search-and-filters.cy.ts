describe('البحث والفلاتر', () => {
  beforeEach(() => {
    cy.mockGeolocation()
    cy.visitWithLocale('/search')
  })

  it('يجب أن تعرض صفحة البحث بشكل صحيح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود العناصر الأساسية
    cy.get('[data-testid="search-page"]').should('be.visible')
    cy.get('[data-testid="search-input"]').should('be.visible')
    cy.get('[data-testid="search-filters"]').should('be.visible')
    cy.get('[data-testid="search-results"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة البحث النصي', () => {
    cy.waitForLoadingToFinish()
    
    // كتابة نص البحث
    cy.get('[data-testid="search-input"]').type('مطعم')
    cy.get('[data-testid="search-button"]').click()
    
    // انتظار النتائج
    cy.get('[data-testid="search-results"]').should('be.visible')
    
    // التحقق من وجود نتائج
    cy.get('[data-testid="store-card"]').should('have.length.at.least', 1)
    
    // التحقق من أن النتائج تحتوي على كلمة البحث
    cy.get('[data-testid="store-card"]').first().should('contain.text', 'مطعم')
  })

  it('يجب أن تعمل فلاتر الفئات', () => {
    cy.waitForLoadingToFinish()
    
    // فتح فلتر الفئات
    cy.get('[data-testid="category-filter"]').click()
    
    // اختيار فئة معينة
    cy.get('[data-testid="category-restaurants"]').click()
    
    // تطبيق الفلتر
    cy.get('[data-testid="apply-filters"]').click()
    
    // التحقق من تطبيق الفلتر
    cy.url().should('include', 'category=restaurants')
    
    // التحقق من النتائج
    cy.get('[data-testid="store-card"]').each(($card) => {
      cy.wrap($card).should('contain.text', 'مطعم')
    })
  })

  it('يجب أن تعمل فلاتر المسافة', () => {
    cy.waitForLoadingToFinish()
    
    // فتح فلتر المسافة
    cy.get('[data-testid="distance-filter"]').click()
    
    // اختيار مسافة معينة
    cy.get('[data-testid="distance-5km"]').click()
    
    // تطبيق الفلتر
    cy.get('[data-testid="apply-filters"]').click()
    
    // التحقق من تطبيق الفلتر
    cy.url().should('include', 'distance=5')
    
    // التحقق من أن جميع النتائج ضمن المسافة المحددة
    cy.get('[data-testid="store-distance"]').each(($distance) => {
      const distanceText = $distance.text()
      const distanceValue = parseFloat(distanceText.replace(/[^\d.]/g, ''))
      expect(distanceValue).to.be.lessThan(5)
    })
  })

  it('يجب أن تعمل فلاتر التقييم', () => {
    cy.waitForLoadingToFinish()
    
    // فتح فلتر التقييم
    cy.get('[data-testid="rating-filter"]').click()
    
    // اختيار تقييم 4 نجوم فأكثر
    cy.get('[data-testid="rating-4-stars"]').click()
    
    // تطبيق الفلتر
    cy.get('[data-testid="apply-filters"]').click()
    
    // التحقق من تطبيق الفلتر
    cy.url().should('include', 'rating=4')
    
    // التحقق من النتائج
    cy.get('[data-testid="store-rating"]').each(($rating) => {
      const ratingValue = parseFloat($rating.text())
      expect(ratingValue).to.be.at.least(4)
    })
  })

  it('يجب أن تعمل فلاتر السعر', () => {
    cy.waitForLoadingToFinish()
    
    // فتح فلتر السعر
    cy.get('[data-testid="price-filter"]').click()
    
    // تحديد نطاق سعري
    cy.get('[data-testid="price-min"]').clear().type('10')
    cy.get('[data-testid="price-max"]').clear().type('100')
    
    // تطبيق الفلتر
    cy.get('[data-testid="apply-filters"]').click()
    
    // التحقق من تطبيق الفلتر
    cy.url().should('include', 'priceMin=10')
    cy.url().should('include', 'priceMax=100')
  })

  it('يجب أن تعمل وظيفة الترتيب', () => {
    cy.waitForLoadingToFinish()
    
    // فتح قائمة الترتيب
    cy.get('[data-testid="sort-dropdown"]').click()
    
    // اختيار الترتيب حسب التقييم
    cy.get('[data-testid="sort-by-rating"]').click()
    
    // التحقق من تطبيق الترتيب
    cy.url().should('include', 'sortBy=rating')
    
    // التحقق من ترتيب النتائج
    let previousRating = 5
    cy.get('[data-testid="store-rating"]').each(($rating) => {
      const currentRating = parseFloat($rating.text())
      expect(currentRating).to.be.at.most(previousRating)
      previousRating = currentRating
    })
  })

  it('يجب أن تعمل وظيفة مسح الفلاتر', () => {
    cy.waitForLoadingToFinish()
    
    // تطبيق عدة فلاتر
    cy.get('[data-testid="category-filter"]').click()
    cy.get('[data-testid="category-restaurants"]').click()
    cy.get('[data-testid="apply-filters"]').click()
    
    cy.get('[data-testid="distance-filter"]').click()
    cy.get('[data-testid="distance-5km"]').click()
    cy.get('[data-testid="apply-filters"]').click()
    
    // مسح جميع الفلاتر
    cy.get('[data-testid="clear-filters"]').click()
    
    // التحقق من مسح الفلاتر
    cy.url().should('not.include', 'category=')
    cy.url().should('not.include', 'distance=')
    
    // التحقق من عرض جميع النتائج
    cy.get('[data-testid="store-card"]').should('have.length.at.least', 5)
  })

  it('يجب أن تعمل وظيفة البحث المتقدم', () => {
    cy.waitForLoadingToFinish()
    
    // فتح البحث المتقدم
    cy.get('[data-testid="advanced-search-toggle"]').click()
    
    // ملء حقول البحث المتقدم
    cy.get('[data-testid="store-name-input"]').type('مطعم الأصالة')
    cy.get('[data-testid="location-input"]').type('الرياض')
    
    // تطبيق البحث المتقدم
    cy.get('[data-testid="advanced-search-submit"]').click()
    
    // التحقق من النتائج
    cy.get('[data-testid="search-results"]').should('be.visible')
    cy.get('[data-testid="store-card"]').should('contain.text', 'الأصالة')
  })

  it('يجب أن تعمل وظيفة البحث بالخريطة', () => {
    cy.waitForLoadingToFinish()
    
    // التبديل لعرض الخريطة
    cy.get('[data-testid="map-view-toggle"]').click()
    
    // التحقق من ظهور الخريطة
    cy.get('[data-testid="search-map"]').should('be.visible')
    
    // التحقق من وجود علامات المتاجر على الخريطة
    cy.get('[data-testid="store-marker"]').should('have.length.at.least', 1)
    
    // النقر على علامة متجر
    cy.get('[data-testid="store-marker"]').first().click()
    
    // التحقق من ظهور معلومات المتجر
    cy.get('[data-testid="store-popup"]').should('be.visible')
  })

  it('يجب أن تحفظ تفضيلات البحث', () => {
    cy.waitForLoadingToFinish()
    
    // تطبيق فلاتر معينة
    cy.get('[data-testid="category-filter"]').click()
    cy.get('[data-testid="category-restaurants"]').click()
    cy.get('[data-testid="apply-filters"]').click()
    
    // إعادة تحميل الصفحة
    cy.reload()
    cy.waitForLoadingToFinish()
    
    // التحقق من حفظ الفلاتر
    cy.url().should('include', 'category=restaurants')
    cy.get('[data-testid="category-restaurants"]').should('be.checked')
  })
})
