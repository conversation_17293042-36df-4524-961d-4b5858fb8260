// src/components/admin/AlertsPanel.tsx
"use client";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSystemAlerts } from '@/hooks/useAdminStats';
import { useLocale } from '@/hooks/use-locale';
import type { SystemAlert } from '@/services/adminDashboardService';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  X, 
  Eye, 
  RefreshCw,
  CheckCircle,
  Shield,
  Server,
  Database,
  Wifi,
  HardDrive
} from 'lucide-react';

interface AlertsPanelProps {
  className?: string;
  maxHeight?: number;
  showActions?: boolean;
}

export function AlertsPanel({
  className,
  maxHeight = 400,
  showActions = true
}: AlertsPanelProps) {
  const { t } = useLocale();
  const { alerts, loading, markAsRead, dismissAlert, refetch } = useSystemAlerts();

  const getAlertIcon = (type: SystemAlert['type']) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getAlertVariant = (type: SystemAlert['type']) => {
    switch (type) {
      case 'critical':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getAlertBgColor = (type: SystemAlert['type']) => {
    switch (type) {
      case 'critical':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'الآن';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `منذ ${days} يوم`;
    }
  };

  const unreadAlertsCount = alerts.filter(alert => !alert.isRead).length;
  const criticalAlertsCount = alerts.filter(alert => alert.type === 'critical').length;

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <div className="flex items-start gap-3">
                  <Skeleton className="h-4 w-4 mt-1" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold">
            {t('systemAlerts')}
          </CardTitle>
          <div className="flex items-center gap-2 mt-1">
            {unreadAlertsCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadAlertsCount} غير مقروء
              </Badge>
            )}
            {criticalAlertsCount > 0 && (
              <Badge variant="outline" className="text-xs text-red-600">
                {criticalAlertsCount} حرج
              </Badge>
            )}
            {alerts.length === 0 && (
              <Badge variant="outline" className="text-xs text-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                كل شيء طبيعي
              </Badge>
            )}
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={refetch}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea style={{ height: `${maxHeight}px` }}>
          <div className="p-6 pt-0">
            {alerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {t('noAlertsTitle')}
                </h3>
                <p className="text-muted-foreground text-sm">
                  {t('noAlertsDescription')}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`p-4 rounded-lg border transition-all ${
                      alert.isRead ? 'opacity-75' : ''
                    } ${getAlertBgColor(alert.type)}`}
                  >
                    <div className="flex items-start gap-3">
                      {/* أيقونة التنبيه */}
                      <div className="flex-shrink-0 mt-0.5">
                        {getAlertIcon(alert.type)}
                      </div>

                      {/* محتوى التنبيه */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-foreground">
                              {alert.title}
                            </h4>
                            <p className="text-sm text-muted-foreground mt-1">
                              {alert.message}
                            </p>
                            
                            {/* معلومات إضافية */}
                            <div className="flex items-center gap-2 mt-2">
                              <Badge 
                                variant={getAlertVariant(alert.type)}
                                className="text-xs"
                              >
                                {alert.type === 'critical' && t('criticalAlert')}
                                {alert.type === 'warning' && t('warningAlert')}
                                {alert.type === 'info' && t('infoAlert')}
                              </Badge>
                              
                              <span className="text-xs text-muted-foreground">
                                {formatTimeAgo(alert.timestamp)}
                              </span>
                              
                              {alert.actionRequired && (
                                <Badge variant="outline" className="text-xs text-orange-600">
                                  يتطلب إجراء
                                </Badge>
                              )}
                            </div>
                          </div>

                          {/* أزرار الإجراءات */}
                          {showActions && (
                            <div className="flex items-center gap-1">
                              {!alert.isRead && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => markAsRead(alert.id)}
                                  className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                                  title={t('markAsRead')}
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              )}
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => dismissAlert(alert.id)}
                                className="h-6 w-6 p-0 text-muted-foreground hover:text-red-600"
                                title={t('dismissAlert')}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// مكون مبسط لعرض التنبيهات في الشريط العلوي
interface AlertsSummaryProps {
  alerts: SystemAlert[];
  loading?: boolean;
  limit?: number;
}

export function AlertsSummary({
  alerts,
  loading = false,
  limit = 3
}: AlertsSummaryProps) {
  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-20" />
      </div>
    );
  }

  const criticalAlerts = alerts.filter(alert => alert.type === 'critical');
  const unreadAlerts = alerts.filter(alert => !alert.isRead);

  if (alerts.length === 0) {
    return (
      <div className="flex items-center gap-2 text-green-600">
        <CheckCircle className="h-4 w-4" />
        <span className="text-sm">كل شيء طبيعي</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {criticalAlerts.length > 0 && (
        <Badge variant="destructive" className="text-xs">
          <AlertTriangle className="h-3 w-3 mr-1" />
          {criticalAlerts.length} حرج
        </Badge>
      )}
      
      {unreadAlerts.length > 0 && (
        <Badge variant="secondary" className="text-xs">
          {unreadAlerts.length} جديد
        </Badge>
      )}
    </div>
  );
}

// مكون حالة النظام
export function SystemStatus() {
  const { t } = useLocale();

  const systemComponents = [
    { name: 'الخادم الرئيسي', status: 'online', icon: Server },
    { name: 'قاعدة البيانات', status: 'online', icon: Database },
    { name: 'الشبكة', status: 'online', icon: Wifi },
    { name: 'التخزين', status: 'warning', icon: HardDrive },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'offline':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'online':
        return <Badge variant="outline" className="text-green-600 border-green-600">متصل</Badge>;
      case 'warning':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">تحذير</Badge>;
      case 'offline':
        return <Badge variant="outline" className="text-red-600 border-red-600">غير متصل</Badge>;
      default:
        return <Badge variant="outline">غير معروف</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t('systemHealth')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {systemComponents.map((component) => {
            const IconComponent = component.icon;
            return (
              <div key={component.name} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <IconComponent className={`h-4 w-4 ${getStatusColor(component.status)}`} />
                  <span className="text-sm font-medium">{component.name}</span>
                </div>
                {getStatusBadge(component.status)}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
