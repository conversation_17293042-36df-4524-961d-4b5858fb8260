// src/hooks/useAdminStats.ts
"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  adminDashboardService, 
  type AdminStats, 
  type ActivityItem, 
  type SystemAlert 
} from '@/services/adminDashboardService';

export interface UseAdminStatsReturn {
  stats: AdminStats | null;
  recentActivity: ActivityItem[];
  systemAlerts: SystemAlert[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useAdminStats(): UseAdminStatsReturn {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // جلب البيانات بشكل متوازي
      const [statsData, activityData, alertsData] = await Promise.all([
        adminDashboardService.getAdminStats(),
        adminDashboardService.getRecentActivity(10),
        adminDashboardService.getSystemAlerts(),
      ]);

      setStats(statsData);
      setRecentActivity(activityData);
      setSystemAlerts(alertsData);
    } catch (err) {
      console.error('Error fetching admin data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch admin data');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    stats,
    recentActivity,
    systemAlerts,
    loading,
    error,
    refetch: fetchData,
  };
}

// Hook للبيانات المباشرة (Real-time)
export function useRealtimeData() {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    // محاكاة الاتصال المباشر
    const interval = setInterval(() => {
      setIsConnected(true);
      setLastUpdate(new Date());
    }, 30000); // تحديث كل 30 ثانية

    // تعيين الحالة الأولية
    setIsConnected(true);
    setLastUpdate(new Date());

    return () => clearInterval(interval);
  }, []);

  return {
    isConnected,
    lastUpdate,
  };
}

// Hook لتنبيهات النظام
export function useSystemAlerts() {
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchAlerts = useCallback(async () => {
    try {
      setLoading(true);
      const alertsData = await adminDashboardService.getSystemAlerts();
      setAlerts(alertsData);
    } catch (error) {
      console.error('Error fetching system alerts:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const markAsRead = useCallback((alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId 
          ? { ...alert, isRead: true }
          : alert
      )
    );
  }, []);

  const dismissAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  useEffect(() => {
    fetchAlerts();
    
    // تحديث التنبيهات كل دقيقة
    const interval = setInterval(fetchAlerts, 60000);
    
    return () => clearInterval(interval);
  }, [fetchAlerts]);

  return {
    alerts,
    loading,
    markAsRead,
    dismissAlert,
    refetch: fetchAlerts,
  };
}

// Hook للمؤدين الأفضل
export function useTopPerformers() {
  const [topMerchants, setTopMerchants] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [topRepresentatives, setTopRepresentatives] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // محاكاة بيانات المؤدين الأفضل
    const fetchTopPerformers = async () => {
      try {
        setLoading(true);
        
        // في التطبيق الحقيقي، هذه ستأتي من قاعدة البيانات
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setTopMerchants([
          { id: '1', name: 'متجر الأزياء العصرية', revenue: 15000, orders: 45 },
          { id: '2', name: 'متجر الإلكترونيات', revenue: 12000, orders: 38 },
          { id: '3', name: 'متجر المنزل والحديقة', revenue: 9500, orders: 32 },
        ]);

        setTopProducts([
          { id: '1', name: 'هاتف ذكي', sales: 120, revenue: 8000 },
          { id: '2', name: 'لابتوب', sales: 85, revenue: 6500 },
          { id: '3', name: 'ساعة ذكية', sales: 95, revenue: 4500 },
        ]);

        setTopRepresentatives([
          { id: '1', name: 'أحمد محمد', deliveries: 156, rating: 4.9 },
          { id: '2', name: 'فاطمة علي', deliveries: 142, rating: 4.8 },
          { id: '3', name: 'محمد سالم', deliveries: 138, rating: 4.7 },
        ]);
      } catch (error) {
        console.error('Error fetching top performers:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTopPerformers();
  }, []);

  return {
    topMerchants,
    topProducts,
    topRepresentatives,
    loading,
  };
}

// Hook لبيانات الرسوم البيانية
export function useChartData(period: '7d' | '30d' | '3m' | '1y' = '30d') {
  const [salesData, setSalesData] = useState([]);
  const [ordersData, setOrdersData] = useState([]);
  const [usersData, setUsersData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChartData = async () => {
      try {
        setLoading(true);
        
        // محاكاة بيانات الرسوم البيانية
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '3m' ? 90 : 365;
        const data = [];
        
        for (let i = days - 1; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          
          data.push({
            date: date.toISOString().split('T')[0],
            sales: Math.floor(Math.random() * 10000) + 5000,
            orders: Math.floor(Math.random() * 50) + 20,
            users: Math.floor(Math.random() * 20) + 5,
          });
        }
        
        setSalesData(data.map(d => ({ date: d.date, value: d.sales })));
        setOrdersData(data.map(d => ({ date: d.date, value: d.orders })));
        setUsersData(data.map(d => ({ date: d.date, value: d.users })));
      } catch (error) {
        console.error('Error fetching chart data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, [period]);

  return {
    salesData,
    ordersData,
    usersData,
    loading,
  };
}

// Hook للتوزيع الجغرافي
export function useGeographicData() {
  const [geographicData, setGeographicData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGeographicData = async () => {
      try {
        setLoading(true);
        
        // محاكاة البيانات الجغرافية
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setGeographicData([
          { region: 'الرياض', city: 'الرياض', orders: 450, revenue: 125000, users: 1200 },
          { region: 'مكة المكرمة', city: 'جدة', orders: 380, revenue: 98000, users: 950 },
          { region: 'مكة المكرمة', city: 'مكة المكرمة', orders: 220, revenue: 65000, users: 600 },
          { region: 'المنطقة الشرقية', city: 'الدمام', orders: 310, revenue: 85000, users: 750 },
          { region: 'المدينة المنورة', city: 'المدينة المنورة', orders: 180, revenue: 52000, users: 480 },
        ]);
      } catch (error) {
        console.error('Error fetching geographic data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchGeographicData();
  }, []);

  return {
    geographicData,
    loading,
  };
}
