describe('الصفحة الرئيسية', () => {
  beforeEach(() => {
    cy.mockGeolocation()
    cy.visitWithLocale('/')
  })

  it('يجب أن تحمل الصفحة الرئيسية بنجاح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود العناصر الأساسية
    cy.get('header').should('be.visible')
    cy.get('main').should('be.visible')
    cy.get('footer').should('be.visible')
  })

  it('يجب أن تعرض العنوان الرئيسي', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود عنوان الصفحة
    cy.get('h1').should('be.visible')
    cy.shouldContainArabicText('مخلة')
  })

  it('يجب أن تعرض أزرار التنقل في الهيدر', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود روابط التنقل
    cy.get('header').within(() => {
      cy.get('nav').should('be.visible')
      cy.get('a').should('have.length.at.least', 3)
    })
  })

  it('يجب أن تعمل أزرار تغيير اللغة', () => {
    cy.waitForLoadingToFinish()
    
    // البحث عن زر تغيير اللغة
    cy.get('[data-testid="language-switcher"]').should('be.visible')
    
    // النقر على زر تغيير اللغة
    cy.get('[data-testid="language-switcher"]').click()
    
    // التحقق من تغيير URL
    cy.url().should('include', '/en')
  })

  it('يجب أن تعرض قسم المتاجر المميزة', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود قسم المتاجر
    cy.get('[data-testid="featured-stores"]').should('be.visible')
    
    // التحقق من وجود بطاقات المتاجر
    cy.get('[data-testid="store-card"]').should('have.length.at.least', 1)
  })

  it('يجب أن تعمل وظيفة البحث', () => {
    cy.waitForLoadingToFinish()
    
    // البحث عن شريط البحث
    cy.get('[data-testid="search-input"]').should('be.visible')
    
    // كتابة نص البحث
    cy.get('[data-testid="search-input"]').type('مطعم')
    
    // النقر على زر البحث أو الضغط على Enter
    cy.get('[data-testid="search-button"]').click()
    
    // التحقق من الانتقال لصفحة البحث
    cy.url().should('include', '/search')
  })

  it('يجب أن تعرض الفوتر مع الروابط المهمة', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود الفوتر
    cy.get('footer').should('be.visible')
    
    // التحقق من وجود روابط مهمة في الفوتر
    cy.get('footer').within(() => {
      cy.shouldContainArabicText('حول مخلة')
      cy.shouldContainArabicText('اتصل بنا')
      cy.shouldContainArabicText('الشروط والأحكام')
    })
  })

  it('يجب أن تكون الصفحة متجاوبة على الهاتف المحمول', () => {
    // تغيير حجم الشاشة للهاتف المحمول
    cy.viewport('iphone-x')
    cy.waitForLoadingToFinish()
    
    // التحقق من أن العناصر لا تزال مرئية
    cy.get('header').should('be.visible')
    cy.get('main').should('be.visible')
    
    // التحقق من أن القائمة المحمولة تعمل
    cy.get('[data-testid="mobile-menu-button"]').should('be.visible')
    cy.get('[data-testid="mobile-menu-button"]').click()
    cy.get('[data-testid="mobile-menu"]').should('be.visible')
  })

  it('يجب أن تعمل الروابط الخارجية بشكل صحيح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من رابط صفحة الأسعار
    cy.get('a[href*="/pricing"]').should('be.visible')
    cy.get('a[href*="/pricing"]').click()
    cy.url().should('include', '/pricing')
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في الشبكة
    cy.intercept('GET', '**/api/**', { forceNetworkError: true }).as('networkError')
    
    cy.visitWithLocale('/')
    
    // التحقق من عدم تعطل الصفحة
    cy.get('body').should('be.visible')
    
    // التحقق من وجود رسالة خطأ أو حالة fallback
    cy.get('[data-testid="error-message"]').should('not.exist')
  })
})
