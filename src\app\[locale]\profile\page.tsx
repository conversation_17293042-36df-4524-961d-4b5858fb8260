// src/app/[locale]/profile/page.tsx
"use client"; 

import ProfileTabs from '@/components/profile/ProfileTabs';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { UserDocument } from '@/types';

export default function ProfilePage() {
  const { t, locale } = useLocale();
  const { user, loading, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const [shouldRedirect, setShouldRedirect] = useState(false);

  useEffect(() => {
    if (initialLoadingCompleted && !user) {
      router.push(`/${locale}/login`);
    } else if (initialLoadingCompleted && user) {
      // Check if user should be redirected to their appropriate dashboard
      const checkUserTypeAndRedirect = async () => {
        try {
          const userDocRef = doc(db, "users", user.uid);
          const userDocSnap = await getDoc(userDocRef);

          if (userDocSnap.exists()) {
            const userData = userDocSnap.data() as UserDocument;
            // Only redirect if user came from login/signup, not if they explicitly navigated to profile
            const referrer = document.referrer;
            const isFromAuthPage = referrer.includes('/login') || referrer.includes('/signup');

            if (isFromAuthPage) {
              setShouldRedirect(true);
              if (userData.userType === 'merchant') {
                router.replace(`/${locale}/merchant/dashboard`);
              } else if (userData.userType === 'customer') {
                router.replace(`/${locale}/dashboard`);
              }
            }
          }
        } catch (error) {
          console.error("Error fetching user document:", error);
        }
      };

      checkUserTypeAndRedirect();
    }
  }, [user, initialLoadingCompleted, router, locale]);

  if (loading || !initialLoadingCompleted || !user || shouldRedirect) {
    return (
      <div className="container mx-auto px-4 py-12 flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">
          {shouldRedirect ? t('redirectingToYourDashboard') : t('loadingProfile')}
        </p>
      </div>
    );
  }

  const userProfileData = {
    name: user.displayName || '',
    email: user.email || '',
    avatarUrl: user.photoURL || '',
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-center text-primary mb-2">{t('profileSettings')}</h1>
          <p className="text-center text-muted-foreground">{t('manageYourAccountSettings')}</p>
        </div>

        <ProfileTabs user={userProfileData} />
      </div>
    </div>
  );
}
