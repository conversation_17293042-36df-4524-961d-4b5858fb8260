#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'en.json');

// مسار مكون السلة
const CART_COMPONENT_PATH = path.join(__dirname, '..', 'src', 'components', 'cart', 'CartSidebar.tsx');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * قراءة ملف نصي
 */
function readTextFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * استخراج مفاتيح الترجمة من مكون السلة
 */
function extractTranslationKeysFromComponent(componentContent) {
  const translationKeyRegex = /t\(['"`]([^'"`]+)['"`]\)/g;
  const keys = [];
  let match;
  
  while ((match = translationKeyRegex.exec(componentContent)) !== null) {
    keys.push(match[1]);
  }
  
  return [...new Set(keys)]; // إزالة المفاتيح المكررة
}

/**
 * التحقق من وجود المفاتيح في الترجمات
 */
function checkKeysInTranslations(translations, keys, language) {
  const missingKeys = [];
  const existingKeys = [];
  
  keys.forEach(key => {
    if (translations[key]) {
      existingKeys.push(key);
    } else {
      missingKeys.push(key);
    }
  });
  
  return { missingKeys, existingKeys };
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧪 اختبار مكون السلة والترجمات\n');
  console.log('=' .repeat(60));
  
  // قراءة مكون السلة
  console.log('📄 قراءة مكون السلة...');
  const componentContent = readTextFile(CART_COMPONENT_PATH);
  if (!componentContent) {
    console.error('❌ فشل في قراءة مكون السلة');
    process.exit(1);
  }
  
  // استخراج مفاتيح الترجمة من المكون
  const usedKeys = extractTranslationKeysFromComponent(componentContent);
  console.log(`✅ تم استخراج ${usedKeys.length} مفتاح ترجمة من المكون`);
  
  // عرض المفاتيح المستخدمة
  console.log('\n🔑 المفاتيح المستخدمة في مكون السلة:');
  usedKeys.forEach(key => console.log(`   - ${key}`));
  
  // قراءة ملفات الترجمة
  console.log('\n📚 قراءة ملفات الترجمة...');
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    process.exit(1);
  }
  
  // التحقق من الترجمات العربية
  console.log('\n🔍 التحقق من الترجمات العربية:');
  const arResult = checkKeysInTranslations(arTranslations, usedKeys, 'العربي');
  
  if (arResult.missingKeys.length > 0) {
    console.log('❌ مفاتيح مفقودة في الترجمة العربية:');
    arResult.missingKeys.forEach(key => console.log(`   - ${key}`));
  } else {
    console.log('✅ جميع المفاتيح موجودة في الترجمة العربية');
  }
  
  // التحقق من الترجمات الإنجليزية
  console.log('\n🔍 التحقق من الترجمات الإنجليزية:');
  const enResult = checkKeysInTranslations(enTranslations, usedKeys, 'الإنجليزي');
  
  if (enResult.missingKeys.length > 0) {
    console.log('❌ مفاتيح مفقودة في الترجمة الإنجليزية:');
    enResult.missingKeys.forEach(key => console.log(`   - ${key}`));
  } else {
    console.log('✅ جميع المفاتيح موجودة في الترجمة الإنجليزية');
  }
  
  // ملخص النتائج
  console.log('\n' + '=' .repeat(60));
  console.log('📊 ملخص النتائج:');
  console.log(`   - المفاتيح المستخدمة في المكون: ${usedKeys.length}`);
  console.log(`   - موجودة في العربي: ${arResult.existingKeys.length}`);
  console.log(`   - مفقودة في العربي: ${arResult.missingKeys.length}`);
  console.log(`   - موجودة في الإنجليزي: ${enResult.existingKeys.length}`);
  console.log(`   - مفقودة في الإنجليزي: ${enResult.missingKeys.length}`);
  
  // تحديد حالة النجاح
  const allKeysPresent = arResult.missingKeys.length === 0 && enResult.missingKeys.length === 0;
  
  if (allKeysPresent) {
    console.log('\n✅ جميع مفاتيح الترجمة المطلوبة لمكون السلة موجودة!');
    console.log('🎉 مكون السلة جاهز للعمل بشكل صحيح');
    process.exit(0);
  } else {
    console.log('\n❌ هناك مفاتيح مفقودة تحتاج إلى إضافة');
    process.exit(1);
  }
}

// تشغيل السكريبت
main();
