// src/components/admin/RealtimeChart.tsx
"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { 
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { useChartData } from '@/hooks/useAdminStats';
import { useLocale } from '@/hooks/use-locale';
import { 
  TrendingUp, 
  BarChart3, 
  LineChart as LineChartIcon,
  PieChart as PieChartIcon,
  RefreshCw
} from 'lucide-react';

type ChartType = 'line' | 'area' | 'bar';
type TimePeriod = '7d' | '30d' | '3m' | '1y';

interface RealtimeChartProps {
  title: string;
  dataKey: 'sales' | 'orders' | 'users';
  type?: ChartType;
  height?: number;
  showControls?: boolean;
  className?: string;
}

export function RealtimeChart({
  title,
  dataKey,
  type = 'area',
  height = 300,
  showControls = true,
  className
}: RealtimeChartProps) {
  const { t } = useLocale();
  const [chartType, setChartType] = useState<ChartType>(type);
  const [period, setPeriod] = useState<TimePeriod>('30d');
  
  const { salesData, ordersData, usersData, loading } = useChartData(period);

  const getData = () => {
    switch (dataKey) {
      case 'sales':
        return salesData;
      case 'orders':
        return ordersData;
      case 'users':
        return usersData;
      default:
        return [];
    }
  };

  const data = getData();

  const getColor = () => {
    switch (dataKey) {
      case 'sales':
        return '#10b981'; // green
      case 'orders':
        return '#3b82f6'; // blue
      case 'users':
        return '#8b5cf6'; // purple
      default:
        return '#6b7280'; // gray
    }
  };

  const formatValue = (value: number) => {
    if (dataKey === 'sales') {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
      }).format(value);
    }
    return value.toLocaleString('ar-SA');
  };

  const renderChart = () => {
    const color = getColor();
    
    if (loading) {
      return <Skeleton className="w-full h-full" />;
    }

    const chartConfig = {
      value: {
        label: title,
        color: color,
      },
    };

    switch (chartType) {
      case 'line':
        return (
          <ChartContainer config={chartConfig}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' })}
              />
              <YAxis tickFormatter={formatValue} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke={color}
                strokeWidth={2}
                dot={{ fill: color, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6 }}
              />
            </LineChart>
          </ChartContainer>
        );

      case 'area':
        return (
          <ChartContainer config={chartConfig}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' })}
              />
              <YAxis tickFormatter={formatValue} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke={color}
                fill={color}
                fillOpacity={0.3}
              />
            </AreaChart>
          </ChartContainer>
        );

      case 'bar':
        return (
          <ChartContainer config={chartConfig}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' })}
              />
              <YAxis tickFormatter={formatValue} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="value" fill={color} />
            </BarChart>
          </ChartContainer>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="text-xs">
              {t('realTimeData')}
            </Badge>
            {!loading && (
              <span className="text-xs text-muted-foreground">
                {data.length} نقطة بيانات
              </span>
            )}
          </div>
        </div>

        {showControls && (
          <div className="flex items-center gap-2">
            {/* أزرار نوع الرسم البياني */}
            <div className="flex items-center gap-1">
              <Button
                variant={chartType === 'line' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setChartType('line')}
              >
                <LineChartIcon className="h-4 w-4" />
              </Button>
              <Button
                variant={chartType === 'area' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setChartType('area')}
              >
                <TrendingUp className="h-4 w-4" />
              </Button>
              <Button
                variant={chartType === 'bar' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setChartType('bar')}
              >
                <BarChart3 className="h-4 w-4" />
              </Button>
            </div>

            {/* أزرار الفترة الزمنية */}
            <div className="flex items-center gap-1">
              {(['7d', '30d', '3m', '1y'] as TimePeriod[]).map((p) => (
                <Button
                  key={p}
                  variant={period === p ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPeriod(p)}
                >
                  {p === '7d' && t('last7Days')}
                  {p === '30d' && t('last30Days')}
                  {p === '3m' && t('last3Months')}
                  {p === '1y' && t('lastYear')}
                </Button>
              ))}
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div style={{ height: `${height}px` }}>
          {renderChart()}
        </div>
      </CardContent>
    </Card>
  );
}

// مكون رسم بياني دائري للتوزيع
interface DistributionChartProps {
  title: string;
  data: Array<{
    name: string;
    value: number;
    color?: string;
  }>;
  loading?: boolean;
  height?: number;
  className?: string;
}

export function DistributionChart({
  title,
  data,
  loading = false,
  height = 300,
  className
}: DistributionChartProps) {
  const { t } = useLocale();

  const COLORS = [
    '#10b981', // green
    '#3b82f6', // blue
    '#8b5cf6', // purple
    '#f59e0b', // amber
    '#ef4444', // red
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#f97316', // orange
  ];

  const chartData = data.map((item, index) => ({
    ...item,
    color: item.color || COLORS[index % COLORS.length],
  }));

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full h-64" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ height: `${height}px` }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip 
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload;
                    return (
                      <div className="bg-background border rounded-lg p-2 shadow-md">
                        <p className="font-medium">{data.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {data.value.toLocaleString('ar-SA')}
                        </p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
