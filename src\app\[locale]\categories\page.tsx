"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Grid3X3,
  List,
  Package,
  AlertCircle,
  Filter,
  SlidersHorizontal,
  Tag
} from "lucide-react";
import { CategoryColoredIcon } from "@/components/ui/colored-icons";
import { useLocale } from "@/hooks/use-locale";
import ProductCard from "@/components/common/ProductCard";
import FilterSidebar from "@/components/customer/FilterSidebar";
import type { ProductDocument, SearchFilter } from "@/types";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";

// Categories with colored icons
const CATEGORIES = [
  { id: 'food', name: 'food' },
  { id: 'groceries', name: 'groceries' },
  { id: 'fashion', name: 'fashion' },
  { id: 'electronics', name: 'electronics' },
  { id: 'home', name: 'homeAndGarden' },
  { id: 'beauty', name: 'beautyAndHealth' },
  { id: 'sports', name: 'sportsAndFitness' },
  { id: 'automotive', name: 'automotive' },
  { id: 'books', name: 'booksAndMedia' },
  { id: 'arts', name: 'artsAndCrafts' },
  { id: 'handicrafts', name: 'handicrafts' },
  { id: 'toys', name: 'toys' },
  { id: 'pets', name: 'pets' },
  { id: 'babyKids', name: 'babyKids' },
  { id: 'jewelry', name: 'jewelry' },
  { id: 'services', name: 'services' },
  { id: 'plants', name: 'plants' },
  { id: 'appliances', name: 'appliances' },
  { id: 'waterSports', name: 'waterSports' },
  { id: 'perfumes', name: 'perfumes' },
  { id: 'tools', name: 'tools' },
  { id: 'other', name: 'other' }
];

export default function CategoriesPage() {
  const searchParams = useSearchParams();
  const { t } = useLocale();
  
  const [selectedCategory, setSelectedCategory] = useState<string>(searchParams?.get('category') || '');
  const [products, setProducts] = useState<ProductDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('newest');
  const [filters, setFilters] = useState<SearchFilter>({
    categories: [],
    priceRange: [0, 1000],
    rating: 0,
    location: '',
    inStock: false
  });
  const [showFilters, setShowFilters] = useState(false);

  // Fetch products by category
  const fetchProductsByCategory = async (category: string, searchFilters: SearchFilter) => {
    setIsLoading(true);
    setError(null);

    try {
      const productsRef = collection(db, 'products');
      let productsQuery = query(
        productsRef,
        where('isActive', '==', true)
      );

      // Add category filter
      if (category) {
        productsQuery = query(
          productsRef,
          where('category', '==', category),
          where('isActive', '==', true)
        );
      }

      // Apply additional filters
      if (searchFilters.inStock) {
        productsQuery = query(
          productsRef,
          where('stock', '>', 0),
          where('isActive', '==', true),
          ...(category ? [where('category', '==', category)] : [])
        );
      }

      // Add sorting
      if (sortBy === 'price_low') {
        productsQuery = query(productsQuery, orderBy('price', 'asc'));
      } else if (sortBy === 'price_high') {
        productsQuery = query(productsQuery, orderBy('price', 'desc'));
      } else if (sortBy === 'rating') {
        productsQuery = query(productsQuery, orderBy('averageRating', 'desc'));
      } else if (sortBy === 'newest') {
        productsQuery = query(productsQuery, orderBy('createdAt', 'desc'));
      } else if (sortBy === 'popular') {
        productsQuery = query(productsQuery, orderBy('totalSales', 'desc'));
      }

      const snapshot = await getDocs(productsQuery);
      const productsData: ProductDocument[] = [];

      snapshot.forEach((doc) => {
        const product = { ...doc.data(), id: doc.id } as ProductDocument;
        
        // Apply price filter
        if (product.price >= searchFilters.priceRange[0] && 
            product.price <= searchFilters.priceRange[1]) {
          
          // Apply rating filter
          if (searchFilters.rating === 0 || 
              (product.averageRating && product.averageRating >= searchFilters.rating)) {
            productsData.push(product);
          }
        }
      });

      setProducts(productsData);

    } catch (err) {
      console.error('Error fetching products:', err);
      setError(t('errorFetchingProducts'));
    } finally {
      setIsLoading(false);
    }
  };

  // Effect for initial load and category changes
  useEffect(() => {
    fetchProductsByCategory(selectedCategory, filters);
  }, [selectedCategory, sortBy]);

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    // Update URL
    const url = new URL(window.location.href);
    if (categoryId) {
      url.searchParams.set('category', categoryId);
    } else {
      url.searchParams.delete('category');
    }
    window.history.pushState({}, '', url.toString());
  };

  // Handle filter change
  const handleFilterChange = (newFilters: SearchFilter) => {
    setFilters(newFilters);
    fetchProductsByCategory(selectedCategory, newFilters);
  };

  // Get category info
  const getCategoryInfo = (categoryId: string) => {
    return CATEGORIES.find(cat => cat.id === categoryId);
  };

  const selectedCategoryInfo = selectedCategory ? getCategoryInfo(selectedCategory) : null;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">{t('browseByCategories')}</h1>
        <p className="text-muted-foreground">
          {t('discoverProductsInDifferentCategories')}
        </p>
      </div>

      {/* Categories Grid */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">{t('categories')}</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6 mb-8" data-testid="categories-grid">
          {/* All Categories */}
          <Card
            className={`category-card cursor-pointer ${
              !selectedCategory ? 'selected' : ''
            }`}
            onClick={() => handleCategorySelect('')}
          >
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <CategoryColoredIcon category="all" size={48} />
              </div>
              <h3 className="font-medium text-sm text-center">{t('allCategories')}</h3>
            </CardContent>
          </Card>

          {/* Individual Categories */}
          {CATEGORIES.map((category) => {
            const isSelected = selectedCategory === category.id;

            return (
              <Card
                key={category.id}
                className={`category-card cursor-pointer ${
                  isSelected ? 'selected' : ''
                }`}
                onClick={() => handleCategorySelect(category.id)}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <CategoryColoredIcon
                      category={category.id as any}
                      size={48}
                    />
                  </div>
                  <h3 className="font-medium text-sm text-center">{t(category.name)}</h3>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Selected Category Header */}
      {selectedCategory && selectedCategoryInfo && (
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 flex items-center justify-center">
              <CategoryColoredIcon category={selectedCategory as any} size={56} />
            </div>
            <div>
              <h2 className="text-2xl font-bold">{t(selectedCategoryInfo.name)}</h2>
              <p className="text-muted-foreground">
                {products.length} {t('productsFound')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div className="text-sm text-muted-foreground">
          {selectedCategory ? (
            <>
              {products.length} {t('productsInCategory')} {t(selectedCategoryInfo?.name || '')}
            </>
          ) : (
            <>
              {products.length} {t('totalProducts')}
            </>
          )}
        </div>
        
        <div className="flex items-center gap-4">
          {/* Filters Toggle */}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal className="w-4 h-4 mr-2" />
            {t('filters')}
          </Button>

          {/* Sort */}
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">{t('newest')}</SelectItem>
              <SelectItem value="popular">{t('mostPopular')}</SelectItem>
              <SelectItem value="price_low">{t('priceLowToHigh')}</SelectItem>
              <SelectItem value="price_high">{t('priceHighToLow')}</SelectItem>
              <SelectItem value="rating">{t('highestRated')}</SelectItem>
            </SelectContent>
          </Select>

          {/* View Mode */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex gap-8">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="w-80 flex-shrink-0">
            <FilterSidebar
              filters={filters}
              onFiltersChange={handleFilterChange}
              className="sticky top-4"
            />
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1">
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            // Loading state
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i}>
                  <Skeleton className="h-40 w-full" />
                  <CardContent className="p-4 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-8 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : products.length === 0 ? (
            // No products
            <Card>
              <CardContent className="text-center py-16">
                <Package className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">
                  {selectedCategory ? t('noProductsInThisCategory') : t('noProductsFound')}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {selectedCategory ? t('trySelectingDifferentCategory') : t('checkBackLaterForNewProducts')}
                </p>
                {selectedCategory && (
                  <Button variant="outline" onClick={() => handleCategorySelect('')}>
                    {t('browseAllCategories')}
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            // Products grid
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1'
            }`}>
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  variant={viewMode === 'list' ? 'compact' : 'default'}
                  showStore={true}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
