'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useLocale } from '@/hooks/use-locale';
import { ExtendedUserDocument } from '@/hooks/useUsersManagement';
import { 
  X,
  User,
  Mail,
  Phone,
  Calendar,
  Star,
  ShoppingBag,
  TrendingUp,
  Award,
  AlertTriangle,
  Clock
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface UserDetailsDialogProps {
  user: ExtendedUserDocument;
  onClose: () => void;
}

export function UserDetailsDialog({ user, onClose }: UserDetailsDialogProps) {
  const { t } = useLocale();

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'customer': return t('customer');
      case 'merchant': return t('merchant');
      case 'representative': return 'مندوب';
      case 'admin': return 'مدير';
      default: return userType;
    }
  };

  const getUserTypeBadgeColor = (userType: string) => {
    switch (userType) {
      case 'customer': return 'bg-blue-100 text-blue-800';
      case 'merchant': return 'bg-green-100 text-green-800';
      case 'representative': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            تفاصيل المستخدم
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* معلومات أساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={user.photoURL || undefined} />
                  <AvatarFallback className="text-lg">
                    {user.displayName?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 space-y-3">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {user.displayName || 'بدون اسم'}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getUserTypeBadgeColor(user.userType)}>
                        {getUserTypeLabel(user.userType)}
                      </Badge>
                      {user.isActive ? (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          نشط
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-red-600 border-red-600">
                          غير نشط
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span>{user.email || 'غير محدد'}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>
                        انضم في {user.createdAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>
                        آخر تحديث: {user.updatedAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* إحصائيات المستخدم */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <ShoppingBag className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                    <p className="text-xl font-bold">{user.totalOrders || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إجمالي الإنفاق</p>
                    <p className="text-xl font-bold">
                      {(user.totalSpent || 0).toLocaleString('ar-SA')} ر.س
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Star className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">متوسط التقييم</p>
                    <p className="text-xl font-bold">
                      {user.averageRating ? user.averageRating.toFixed(1) : 'لا يوجد'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Award className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">نقاط الولاء</p>
                    <p className="text-xl font-bold">{user.loyaltyPoints || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* نشاط المستخدم */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">النشاط الحديث</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">تسجيل الدخول الأخير</p>
                    <p className="text-xs text-gray-500">
                      {user.lastLoginAt ? user.lastLoginAt.toLocaleDateString('ar-SA') : 'غير محدد'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <ShoppingBag className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">آخر طلب</p>
                    <p className="text-xs text-gray-500">لا توجد طلبات حديثة</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Star className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">آخر تقييم</p>
                    <p className="text-xs text-gray-500">لا توجد تقييمات حديثة</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* تقارير وشكاوى */}
          {(user.complaintsCount || user.reportsCount) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  التقارير والشكاوى
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p className="text-sm font-medium text-orange-800">الشكاوى</p>
                    <p className="text-2xl font-bold text-orange-900">
                      {user.complaintsCount || 0}
                    </p>
                  </div>
                  
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm font-medium text-red-800">التقارير</p>
                    <p className="text-2xl font-bold text-red-900">
                      {user.reportsCount || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              إغلاق
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
