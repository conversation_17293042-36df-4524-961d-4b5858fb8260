import { NextRequest, NextResponse } from 'next/server';
import { i18nConfig } from '@/lib/i18n-config';
import { match } from '@formatjs/intl-localematcher';
import Negotiator from 'negotiator';

// ===== APEX SECURITY: تحليل التهديدات في الـ Middleware =====
interface SecurityAnalysis {
  riskScore: number;
  indicators: string[];
  shouldBlock: boolean;
  threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * تحليل أمني سريع للطلبات
 */
function analyzeRequestSecurity(request: NextRequest): SecurityAnalysis {
  const indicators: string[] = [];
  let riskScore = 0;

  // فحص User Agent المشبوه
  const userAgent = request.headers.get('user-agent') || '';
  if (!userAgent || userAgent.length < 10) {
    indicators.push('missing_or_suspicious_user_agent');
    riskScore += 30;
  }

  // فحص أنماط الهجمات الشائعة في URL
  const url = request.url.toLowerCase();
  const suspiciousPatterns = [
    'admin', 'wp-admin', 'phpmyadmin', '.env', 'config',
    'sql', 'union', 'select', 'drop', 'delete',
    '../', '..\\', 'etc/passwd', 'cmd.exe'
  ];

  for (const pattern of suspiciousPatterns) {
    if (url.includes(pattern)) {
      indicators.push(`suspicious_url_pattern: ${pattern}`);
      riskScore += 20;
    }
  }

  // فحص Headers المشبوهة
  const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip'];
  for (const header of suspiciousHeaders) {
    const value = request.headers.get(header);
    if (value && (value.includes('127.0.0.1') || value.includes('localhost'))) {
      indicators.push(`suspicious_header: ${header}`);
      riskScore += 15;
    }
  }

  // فحص معدل الطلبات (مبسط)
  const referer = request.headers.get('referer');
  if (!referer && request.method === 'POST') {
    indicators.push('missing_referer_on_post');
    riskScore += 10;
  }

  // تحديد مستوى التهديد
  let threatLevel: SecurityAnalysis['threatLevel'] = 'LOW';
  if (riskScore >= 70) threatLevel = 'CRITICAL';
  else if (riskScore >= 50) threatLevel = 'HIGH';
  else if (riskScore >= 30) threatLevel = 'MEDIUM';

  return {
    riskScore,
    indicators,
    shouldBlock: riskScore >= 70,
    threatLevel
  };
}

// Get the preferred locale from the request headers
function getLocale(request: NextRequest): string {
  // Get the accepted languages from the request headers
  const acceptLanguage = request.headers.get('accept-language') || 'en';
  
  // Create a negotiator instance
  const headers = { 'accept-language': acceptLanguage };
  const languages = new Negotiator({ headers }).languages();
  
  // Match the preferred language with our supported locales
  return match(languages, i18nConfig.locales, i18nConfig.defaultLocale);
}

export function middleware(request: NextRequest) {
  try {
    const { pathname } = request.nextUrl;

    // ===== APEX SECURITY: تحليل التهديدات الأمنية =====
    const securityAnalysis = analyzeRequestSecurity(request);

    // حظر الطلبات عالية الخطورة
    if (securityAnalysis.shouldBlock) {
      console.warn('🚨 [APEX SECURITY] طلب محظور - مستوى تهديد عالي:', {
        pathname,
        threatLevel: securityAnalysis.threatLevel,
        riskScore: securityAnalysis.riskScore,
        indicators: securityAnalysis.indicators,
        ip: request.ip || 'unknown',
        userAgent: request.headers.get('user-agent')?.substring(0, 100)
      });

      return new NextResponse('Access Denied - Security Violation', {
        status: 403,
        headers: {
          'X-Security-Block': 'true',
          'X-Threat-Level': securityAnalysis.threatLevel,
          'X-Risk-Score': securityAnalysis.riskScore.toString()
        }
      });
    }

    // تسجيل التهديدات المتوسطة والعالية
    if (securityAnalysis.threatLevel !== 'LOW') {
      console.warn('⚠️ [APEX SECURITY] تهديد محتمل:', {
        pathname,
        threatLevel: securityAnalysis.threatLevel,
        riskScore: securityAnalysis.riskScore,
        indicators: securityAnalysis.indicators
      });
    }

    // حماية من الحلقة اللا نهائية - فحص الـ referer
    const referer = request.headers.get('referer');
    const isLanguageSwitchLoop = referer &&
      (referer.includes('/ar/user-type-selection') || referer.includes('/en/user-type-selection')) &&
      (pathname.includes('/ar/user-type-selection') || pathname.includes('/en/user-type-selection'));

    if (isLanguageSwitchLoop) {
      console.warn('⚠️ [Middleware] تم اكتشاف حلقة تبديل لغة محتملة، تم تجاهل الطلب');
      return NextResponse.next();
    }

    console.log('🛣️ [Middleware] معالجة طلب:', {
      pathname,
      url: request.url,
      method: request.method,
      userAgent: request.headers.get('user-agent')?.substring(0, 50) + '...',
      referer: referer?.substring(0, 100) + '...'
    });

    const pathLocale = i18nConfig.locales.find(
      (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
    );

    console.log('🛣️ [Middleware] فحص اللغة:', {
      pathLocale,
      supportedLocales: i18nConfig.locales,
      hasLocaleInPath: !!pathLocale
    });

    // إنشاء nonce عشوائي لـ CSP (متوافق مع Edge Runtime)
    const nonce = btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(16))));

    // التحقق من المسارات المحمية
    const protectedPaths = {
      admin: ['/admin'],
      merchant: ['/merchant'],
      representative: ['/representative'],
      customer: ['/dashboard', '/profile', '/orders']
    };

    // التحقق من المسارات المحمية
    for (const [userType, paths] of Object.entries(protectedPaths)) {
      for (const path of paths) {
        if (pathname.includes(path) && !pathname.includes('/pending-approval')) {
          // يمكن إضافة منطق التحقق من المصادقة هنا لاحقاً
          // حالياً نتركه للمكونات للتعامل مع التحقق
          break;
        }
      }
    }

  // إعداد Content Security Policy محسن
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'nonce-${nonce}' 'strict-dynamic' https://apis.google.com https://accounts.google.com https://www.gstatic.com https://ssl.gstatic.com https://www.google.com https://maps.googleapis.com https://unpkg.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com;
    img-src 'self' blob: data: https://res.cloudinary.com https://lh3.googleusercontent.com https://www.google.com https://maps.gstatic.com https://maps.googleapis.com https://unpkg.com;
    font-src 'self' https://fonts.gstatic.com https://unpkg.com;
    connect-src 'self' https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://firestore.googleapis.com https://firebase.googleapis.com https://accounts.google.com https://api.cloudinary.com https://res.cloudinary.com https://maps.googleapis.com;
    frame-src 'self' https://accounts.google.com https://www.google.com;
    worker-src 'self' blob:;
    child-src 'self' blob:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
  `.replace(/\s{2,}/g, ' ').trim();

    // If the path already has a supported locale, continue
    if (pathLocale) {
      console.log('✅ [Middleware] المسار يحتوي على لغة صحيحة:', pathLocale);

      // Set the response headers for language, direction, and security
      const response = NextResponse.next();
      response.headers.set('x-locale', pathLocale);
      response.headers.set('x-direction', pathLocale === 'ar' ? 'rtl' : 'ltr');

      console.log('✅ [Middleware] تم تعيين headers وإرجاع response');

      // إضافة headers الأمان
      response.headers.set('Content-Security-Policy', cspHeader);
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
      response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(self)');
      response.headers.set('X-Nonce', nonce);

      // ===== APEX SECURITY: headers أمنية إضافية =====
      response.headers.set('X-Security-Analysis', JSON.stringify({
        threatLevel: securityAnalysis.threatLevel,
        riskScore: securityAnalysis.riskScore,
        timestamp: new Date().toISOString()
      }));
      response.headers.set('X-Powered-By', 'Mikhla-Security-Engine');
      response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      response.headers.set('X-DNS-Prefetch-Control', 'off');

      return response;
    }

  // Redirect to the preferred locale if no locale is in the path
  console.log('🔄 [Middleware] إعادة توجيه مطلوبة - لا توجد لغة في المسار');

  const locale = getLocale(request);
  const newUrl = new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`, request.url);

  // Preserve query parameters
  if (request.nextUrl.search) {
    newUrl.search = request.nextUrl.search;
  }

  console.log('🔄 [Middleware] إعادة التوجيه:', {
    detectedLocale: locale,
    originalPath: pathname,
    newUrl: newUrl.toString(),
    queryParams: request.nextUrl.search
  });

  const redirectResponse = NextResponse.redirect(newUrl);

  // إضافة headers الأمان للـ redirect أيضاً
  redirectResponse.headers.set('Content-Security-Policy', cspHeader);
  redirectResponse.headers.set('X-Content-Type-Options', 'nosniff');
  redirectResponse.headers.set('X-Frame-Options', 'DENY');
  redirectResponse.headers.set('X-XSS-Protection', '1; mode=block');
  redirectResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  redirectResponse.headers.set('X-Nonce', nonce);

    return redirectResponse;
  } catch (error) {
    // معالجة أخطاء middleware
    console.error('❌ [Middleware] خطأ في middleware:', {
      error: error.message,
      stack: error.stack,
      pathname: request.nextUrl.pathname,
      url: request.url
    });

    // إرجاع response بسيط في حالة الخطأ
    return NextResponse.next();
  }
}

export const config = {
  // Matcher ignoring `/_next/`, `/api/`, and static files
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|images|.*\..*).*)',
  ],
};
