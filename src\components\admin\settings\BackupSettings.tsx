'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { Database, Download, Upload, Clock, AlertCircle } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface BackupSettingsProps {
  onSettingsChange: () => void;
}

export function BackupSettings({ onSettingsChange }: BackupSettingsProps) {
  const { t } = useLocale();
  const [settings, setSettings] = useState({
    automaticBackup: {
      enabled: true,
      frequency: 'daily',
      time: '02:00',
      retention: 30
    },
    backupLocation: {
      type: 'cloud',
      cloudProvider: 'aws',
      localPath: '/backups',
      encryption: true
    },
    backupTypes: {
      database: true,
      files: true,
      configurations: true,
      logs: false
    }
  });

  const [backupHistory] = useState([
    {
      id: '1',
      date: '2024-01-28 02:00:00',
      type: 'automatic',
      size: '2.5 GB',
      status: 'completed',
      duration: '15 دقيقة'
    },
    {
      id: '2',
      date: '2024-01-27 02:00:00',
      type: 'automatic',
      size: '2.4 GB',
      status: 'completed',
      duration: '14 دقيقة'
    },
    {
      id: '3',
      date: '2024-01-26 14:30:00',
      type: 'manual',
      size: '2.4 GB',
      status: 'completed',
      duration: '12 دقيقة'
    }
  ]);

  const handleSettingChange = (category: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [field]: value
      }
    }));
    onSettingsChange();
  };

  const handleManualBackup = async () => {
    // محاكاة إنشاء نسخة احتياطية يدوية
    console.log('Starting manual backup...');
  };

  const handleRestoreBackup = async (backupId: string) => {
    // محاكاة استعادة نسخة احتياطية
    console.log('Restoring backup:', backupId);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">مكتملة</span>;
      case 'failed':
        return <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">فشلت</span>;
      case 'running':
        return <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">قيد التشغيل</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">{status}</span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* النسخ الاحتياطية التلقائية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            النسخ الاحتياطية التلقائية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>تفعيل النسخ الاحتياطية التلقائية</Label>
            <Switch
              checked={settings.automaticBackup.enabled}
              onCheckedChange={(checked) => handleSettingChange('automaticBackup', 'enabled', checked)}
            />
          </div>

          {settings.automaticBackup.enabled && (
            <div className="space-y-4 pl-4 border-l-2 border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>تكرار النسخ</Label>
                  <Select
                    value={settings.automaticBackup.frequency}
                    onValueChange={(value) => handleSettingChange('automaticBackup', 'frequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">كل ساعة</SelectItem>
                      <SelectItem value="daily">يومياً</SelectItem>
                      <SelectItem value="weekly">أسبوعياً</SelectItem>
                      <SelectItem value="monthly">شهرياً</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>وقت النسخ</Label>
                  <Input
                    type="time"
                    value={settings.automaticBackup.time}
                    onChange={(e) => handleSettingChange('automaticBackup', 'time', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>فترة الاحتفاظ (أيام)</Label>
                  <Input
                    type="number"
                    value={settings.automaticBackup.retention}
                    onChange={(e) => handleSettingChange('automaticBackup', 'retention', parseInt(e.target.value))}
                    min="1"
                    max="365"
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* موقع النسخ الاحتياطية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            موقع النسخ الاحتياطية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>نوع التخزين</Label>
            <Select
              value={settings.backupLocation.type}
              onValueChange={(value) => handleSettingChange('backupLocation', 'type', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cloud">التخزين السحابي</SelectItem>
                <SelectItem value="local">التخزين المحلي</SelectItem>
                <SelectItem value="both">كلاهما</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {settings.backupLocation.type === 'cloud' && (
            <div className="space-y-2">
              <Label>مزود الخدمة السحابية</Label>
              <Select
                value={settings.backupLocation.cloudProvider}
                onValueChange={(value) => handleSettingChange('backupLocation', 'cloudProvider', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aws">Amazon S3</SelectItem>
                  <SelectItem value="google">Google Cloud Storage</SelectItem>
                  <SelectItem value="azure">Azure Blob Storage</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {(settings.backupLocation.type === 'local' || settings.backupLocation.type === 'both') && (
            <div className="space-y-2">
              <Label>المسار المحلي</Label>
              <Input
                value={settings.backupLocation.localPath}
                onChange={(e) => handleSettingChange('backupLocation', 'localPath', e.target.value)}
                placeholder="/path/to/backups"
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <Label>تشفير النسخ الاحتياطية</Label>
            <Switch
              checked={settings.backupLocation.encryption}
              onCheckedChange={(checked) => handleSettingChange('backupLocation', 'encryption', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* أنواع البيانات */}
      <Card>
        <CardHeader>
          <CardTitle>أنواع البيانات المشمولة</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>قاعدة البيانات</Label>
              <Switch
                checked={settings.backupTypes.database}
                onCheckedChange={(checked) => handleSettingChange('backupTypes', 'database', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>الملفات والصور</Label>
              <Switch
                checked={settings.backupTypes.files}
                onCheckedChange={(checked) => handleSettingChange('backupTypes', 'files', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>إعدادات النظام</Label>
              <Switch
                checked={settings.backupTypes.configurations}
                onCheckedChange={(checked) => handleSettingChange('backupTypes', 'configurations', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>سجلات النظام</Label>
              <Switch
                checked={settings.backupTypes.logs}
                onCheckedChange={(checked) => handleSettingChange('backupTypes', 'logs', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إجراءات النسخ الاحتياطية */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات النسخ الاحتياطية</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={handleManualBackup} className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              إنشاء نسخة احتياطية الآن
            </Button>

            <Button variant="outline" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              رفع نسخة احتياطية
            </Button>
          </div>

          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">تنبيه مهم:</p>
                <p>تأكد من اختبار النسخ الاحتياطية بانتظام للتأكد من إمكانية استعادتها بنجاح.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* سجل النسخ الاحتياطية */}
      <Card>
        <CardHeader>
          <CardTitle>سجل النسخ الاحتياطية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {backupHistory.map((backup) => (
              <div key={backup.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{backup.date}</span>
                    {getStatusBadge(backup.status)}
                    <span className="text-sm text-gray-500">
                      {backup.type === 'automatic' ? 'تلقائية' : 'يدوية'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    الحجم: {backup.size} • المدة: {backup.duration}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRestoreBackup(backup.id)}
                    className="flex items-center gap-1"
                  >
                    <Upload className="h-3 w-3" />
                    استعادة
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Download className="h-3 w-3" />
                    تحميل
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
