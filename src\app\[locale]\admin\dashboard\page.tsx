// src/app/[locale]/admin/dashboard/page.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useRouter } from 'next/navigation';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { 
  Shield, 
  ShieldCheck, 
  AlertTriangle, 
  Loader2,
  ArrowLeft,
  Settings,
  Users,
  BarChart3
} from 'lucide-react';
import type { UserDocument } from '@/types';

export default function AdminDashboardPage() {
  const { user, loading: authLoading } = useAuth();
  const { t, locale } = useLocale();
  const router = useRouter();
  const [userDoc, setUserDoc] = useState<UserDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAdminAccess = async () => {
      if (authLoading) return;

      if (!user) {
        router.push(`/${locale}/login`);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // جلب بيانات المستخدم من Firestore
        const userDocRef = doc(db, 'users', user.uid);
        const userSnapshot = await getDoc(userDocRef);

        if (!userSnapshot.exists()) {
          setError('لم يتم العثور على بيانات المستخدم');
          return;
        }

        const userData = userSnapshot.data() as UserDocument;
        setUserDoc(userData);

        // التحقق من صلاحيات الإدارة
        if (userData.userType !== 'admin' && !userData.isAdmin) {
          setError('ليس لديك صلاحية للوصول إلى لوحة التحكم الإدارية');
          // إعادة توجيه إلى الصفحة المناسبة
          setTimeout(() => {
            if (userData.userType === 'customer') {
              router.push(`/${locale}/dashboard`);
            } else if (userData.userType === 'merchant') {
              router.push(`/${locale}/merchant/dashboard`);
            } else {
              router.push(`/${locale}`);
            }
          }, 3000);
          return;
        }

      } catch (err) {
        console.error('Error checking admin access:', err);
        setError('حدث خطأ أثناء التحقق من الصلاحيات');
      } finally {
        setLoading(false);
      }
    };

    checkAdminAccess();
  }, [user, authLoading, router, locale]);

  // شاشة التحميل
  if (authLoading || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">جاري التحقق من الصلاحيات...</h2>
            <p className="text-muted-foreground">يرجى الانتظار</p>
          </div>
        </div>
      </div>
    );
  }

  // شاشة الخطأ
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <CardTitle className="text-xl font-semibold text-red-600">
                وصول مرفوض
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <Alert className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
              
              <div className="space-y-3">
                <Button 
                  variant="outline" 
                  onClick={() => router.back()}
                  className="w-full"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للخلف
                </Button>
                
                <Button 
                  variant="default" 
                  onClick={() => router.push(`/${locale}`)}
                  className="w-full"
                >
                  الذهاب للصفحة الرئيسية
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // التحقق من وجود بيانات المستخدم والصلاحيات
  if (!userDoc || (userDoc.userType !== 'admin' && !userDoc.isAdmin)) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
          <Card className="w-full max-w-md">
            <CardContent className="text-center p-8">
              <Shield className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">صلاحيات غير كافية</h2>
              <p className="text-muted-foreground mb-6">
                تحتاج إلى صلاحيات إدارية للوصول إلى هذه الصفحة
              </p>
              <Button onClick={() => router.push(`/${locale}`)}>
                العودة للصفحة الرئيسية
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // لوحة التحكم الإدارية
  return (
    <div className="container mx-auto px-4 py-8">
      {/* ترحيب بالمدير */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <ShieldCheck className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">
                  مرحباً، {userDoc.displayName}
                </h1>
                <p className="text-muted-foreground">
                  مدير النظام
                </p>
              </div>
            </div>
          </div>

          {/* روابط سريعة */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <a href={`/${locale}/admin/users`}>
                <Users className="h-4 w-4 mr-2" />
                إدارة المستخدمين
              </a>
            </Button>
            
            <Button variant="outline" size="sm" asChild>
              <a href={`/${locale}/admin/reports`}>
                <BarChart3 className="h-4 w-4 mr-2" />
                التقارير
              </a>
            </Button>
            
            <Button variant="outline" size="sm" asChild>
              <a href={`/${locale}/admin/settings`}>
                <Settings className="h-4 w-4 mr-2" />
                الإعدادات
              </a>
            </Button>
          </div>
        </div>
      </div>

      {/* لوحة التحكم الرئيسية */}
      <AdminDashboard />

      {/* معلومات إضافية */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">الوصول السريع</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
              <a href={`/${locale}/admin/merchant-approvals`}>
                موافقة التجار
              </a>
            </Button>
            <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
              <a href={`/${locale}/admin/representative-approvals`}>
                موافقة المندوبين
              </a>
            </Button>
            <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
              <a href={`/${locale}/admin/review-reports`}>
                تقارير المراجعات
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">معلومات النظام</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>إصدار النظام:</span>
              <span className="font-medium">v1.0.0</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>آخر تحديث:</span>
              <span className="font-medium">اليوم</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>حالة الخادم:</span>
              <span className="font-medium text-green-600">متصل</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">الدعم والمساعدة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button variant="ghost" size="sm" className="w-full justify-start">
              دليل المدير
            </Button>
            <Button variant="ghost" size="sm" className="w-full justify-start">
              الدعم التقني
            </Button>
            <Button variant="ghost" size="sm" className="w-full justify-start">
              تقرير مشكلة
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
