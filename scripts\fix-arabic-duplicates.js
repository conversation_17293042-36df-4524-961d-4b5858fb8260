const fs = require('fs');
const path = require('path');

// مسارات الملفات
const AR_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'ar.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content, 'utf8');
    console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    return backupPath;
  } catch (error) {
    console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error.message);
    return null;
  }
}

/**
 * البحث عن المفاتيح المكررة في ملف JSON
 */
function findDuplicateKeys(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const keyOccurrences = {};
    const duplicates = [];

    lines.forEach((line, index) => {
      const match = line.match(/^\s*"([^"]+)"\s*:/);
      if (match) {
        const key = match[1];
        if (!keyOccurrences[key]) {
          keyOccurrences[key] = [];
        }
        keyOccurrences[key].push(index + 1);
      }
    });

    Object.entries(keyOccurrences).forEach(([key, lineNumbers]) => {
      if (lineNumbers.length > 1) {
        duplicates.push({
          key,
          lines: lineNumbers,
          count: lineNumbers.length
        });
      }
    });

    return duplicates;
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return [];
  }
}

/**
 * إزالة المفاتيح المكررة من ملف JSON
 */
function removeDuplicateKeys(filePath) {
  try {
    console.log(`🔧 بدء إصلاح المفاتيح المكررة في: ${path.basename(filePath)}`);
    
    // إنشاء نسخة احتياطية
    const backupPath = createBackup(filePath);
    if (!backupPath) {
      throw new Error('فشل في إنشاء النسخة الاحتياطية');
    }

    // قراءة الملف وتحليله
    const content = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(content);
    
    // البحث عن المفاتيح المكررة قبل الإصلاح
    const duplicatesBefore = findDuplicateKeys(filePath);
    
    if (duplicatesBefore.length === 0) {
      console.log('✅ لا توجد مفاتيح مكررة للإصلاح');
      return { fixed: 0, duplicates: [] };
    }

    console.log(`📋 تم العثور على ${duplicatesBefore.length} مفتاح مكرر:`);
    duplicatesBefore.forEach(dup => {
      console.log(`   - "${dup.key}" (${dup.count} مرات)`);
    });

    // إعادة كتابة الملف بتنسيق صحيح (سيزيل المفاتيح المكررة تلقائياً)
    const cleanedContent = JSON.stringify(translations, null, 2);
    fs.writeFileSync(filePath, cleanedContent, 'utf8');

    // التحقق من النتيجة
    const duplicatesAfter = findDuplicateKeys(filePath);
    const fixedCount = duplicatesBefore.length - duplicatesAfter.length;

    console.log(`✅ تم إصلاح ${fixedCount} مفتاح مكرر`);
    
    if (duplicatesAfter.length > 0) {
      console.log(`⚠️  لا تزال هناك ${duplicatesAfter.length} مفاتيح مكررة:`);
      duplicatesAfter.forEach(dup => {
        console.log(`   - "${dup.key}"`);
      });
    }

    return {
      fixed: fixedCount,
      duplicates: duplicatesAfter,
      backup: backupPath
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح المفاتيح المكررة:', error.message);
    return { fixed: 0, duplicates: [], error: error.message };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء إصلاح المفاتيح المكررة في الملف العربي...\n');

  // إصلاح الملف العربي
  const result = removeDuplicateKeys(AR_TRANSLATIONS_PATH);

  console.log('\n📊 ملخص النتائج:');
  console.log(`✅ المفاتيح المصلحة: ${result.fixed}`);
  console.log(`⚠️  المفاتيح المكررة المتبقية: ${result.duplicates.length}`);
  
  if (result.backup) {
    console.log(`💾 النسخة الاحتياطية: ${path.basename(result.backup)}`);
  }

  if (result.error) {
    console.log(`❌ خطأ: ${result.error}`);
    process.exit(1);
  }

  console.log('\n💡 للتحقق من النتائج:');
  console.log('   node scripts/validate-translations.js');
  
  console.log('\n🎉 تم الانتهاء من إصلاح المفاتيح المكررة!');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  findDuplicateKeys,
  removeDuplicateKeys,
  createBackup
};
