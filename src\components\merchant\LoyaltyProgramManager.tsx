'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { 
  Settings, 
  Users, 
  Gift, 
  TrendingUp, 
  Star,
  Plus,
  Edit,
  Trash2,
  Award,
  Crown,
  Sparkles
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { loyaltyService } from '@/services/loyaltyService';
import { 
  LoyaltyProgram, 
  LoyaltyAnalytics, 
  CreateLoyaltyProgramData,
  LoyaltySettings,
  LoyaltyReward
} from '@/types/loyalty';
import { useLocale } from '@/hooks/use-locale';
import { toast } from 'sonner';

const loyaltySettingsSchema = z.object({
  name: z.string().min(3, 'اسم البرنامج يجب أن يكون 3 أحرف على الأقل'),
  description: z.string().min(10, 'الوصف يجب أن يكون 10 أحرف على الأقل'),
  pointsPerSAR: z.number().min(0.1, 'النقاط لكل ريال يجب أن تكون أكبر من 0.1'),
  minimumOrderForPoints: z.number().min(0, 'الحد الأدنى للطلب يجب أن يكون أكبر من أو يساوي 0'),
  pointsExpiryDays: z.number().min(30, 'انتهاء صلاحية النقاط يجب أن يكون 30 يوم على الأقل'),
  welcomeBonusPoints: z.number().min(0, 'نقاط الترحيب يجب أن تكون أكبر من أو تساوي 0'),
  birthdayBonusPoints: z.number().min(0, 'نقاط عيد الميلاد يجب أن تكون أكبر من أو تساوي 0'),
  referralBonusPoints: z.number().min(0, 'نقاط الإحالة يجب أن تكون أكبر من أو تساوي 0'),
  maxPointsPerOrder: z.number().min(0, 'أقصى نقاط لكل طلب يجب أن تكون أكبر من أو تساوي 0'),
  allowPartialRedemption: z.boolean(),
  autoTierUpgrade: z.boolean(),
});

type LoyaltySettingsFormData = z.infer<typeof loyaltySettingsSchema>;

interface LoyaltyProgramManagerProps {
  merchantId: string;
}

export function LoyaltyProgramManager({ merchantId }: LoyaltyProgramManagerProps) {
  const { t } = useLocale();
  const [program, setProgram] = useState<LoyaltyProgram | null>(null);
  const [analytics, setAnalytics] = useState<LoyaltyAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const form = useForm<LoyaltySettingsFormData>({
    resolver: zodResolver(loyaltySettingsSchema),
    defaultValues: {
      name: '',
      description: '',
      pointsPerSAR: 1,
      minimumOrderForPoints: 0,
      pointsExpiryDays: 365,
      welcomeBonusPoints: 100,
      birthdayBonusPoints: 50,
      referralBonusPoints: 200,
      maxPointsPerOrder: 1000,
      allowPartialRedemption: true,
      autoTierUpgrade: true,
    },
  });

  useEffect(() => {
    loadProgramData();
  }, [merchantId]);

  const loadProgramData = async () => {
    try {
      setLoading(true);
      const [programData, analyticsData] = await Promise.all([
        loyaltyService.getMerchantLoyaltyProgram(merchantId),
        loyaltyService.getLoyaltyAnalytics(merchantId).catch(() => null)
      ]);
      
      setProgram(programData);
      setAnalytics(analyticsData);

      if (programData) {
        form.reset({
          name: programData.name,
          description: programData.description,
          pointsPerSAR: programData.settings.pointsPerSAR,
          minimumOrderForPoints: programData.settings.minimumOrderForPoints,
          pointsExpiryDays: programData.settings.pointsExpiryDays,
          welcomeBonusPoints: programData.settings.welcomeBonusPoints,
          birthdayBonusPoints: programData.settings.birthdayBonusPoints,
          referralBonusPoints: programData.settings.referralBonusPoints,
          maxPointsPerOrder: programData.settings.maxPointsPerOrder,
          allowPartialRedemption: programData.settings.allowPartialRedemption,
          autoTierUpgrade: programData.settings.autoTierUpgrade,
        });
      }
    } catch (error) {
      console.error('Error loading program data:', error);
      toast.error('فشل في تحميل بيانات برنامج الولاء');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: LoyaltySettingsFormData) => {
    try {
      setSaving(true);

      const settings: LoyaltySettings = {
        pointsPerSAR: data.pointsPerSAR,
        minimumOrderForPoints: data.minimumOrderForPoints,
        pointsExpiryDays: data.pointsExpiryDays,
        welcomeBonusPoints: data.welcomeBonusPoints,
        birthdayBonusPoints: data.birthdayBonusPoints,
        referralBonusPoints: data.referralBonusPoints,
        maxPointsPerOrder: data.maxPointsPerOrder,
        allowPartialRedemption: data.allowPartialRedemption,
        autoTierUpgrade: data.autoTierUpgrade,
        notificationSettings: {
          pointsEarned: true,
          pointsRedeemed: true,
          pointsExpiring: true,
          tierUpgrade: true,
          rewardAvailable: true,
        },
      };

      if (program) {
        // تحديث برنامج موجود
        await loyaltyService.updateLoyaltyProgram(program.id, {
          name: data.name,
          description: data.description,
          settings,
        });
        toast.success('تم تحديث برنامج الولاء بنجاح');
      } else {
        // إنشاء برنامج جديد
        const programData: CreateLoyaltyProgramData = {
          name: data.name,
          description: data.description,
          settings,
          tiers: [],
          rewards: [],
        };

        await loyaltyService.createLoyaltyProgram(merchantId, programData);
        toast.success('تم إنشاء برنامج الولاء بنجاح');
      }

      await loadProgramData();
    } catch (error: any) {
      toast.error(error.message || 'فشل في حفظ برنامج الولاء');
    } finally {
      setSaving(false);
    }
  };

  const handleToggleProgram = async () => {
    if (!program) return;

    try {
      await loyaltyService.toggleLoyaltyProgram(program.id);
      toast.success(`تم ${program.isActive ? 'إيقاف' : 'تفعيل'} برنامج الولاء`);
      await loadProgramData();
    } catch (error) {
      toast.error('فشل في تغيير حالة برنامج الولاء');
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'bronze':
        return <Award className="h-4 w-4 text-amber-600" />;
      case 'silver':
        return <Star className="h-4 w-4 text-gray-400" />;
      case 'gold':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'platinum':
        return <Sparkles className="h-4 w-4 text-purple-500" />;
      default:
        return <Award className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والحالة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">برنامج الولاء</h1>
          <p className="text-gray-600 mt-1">
            إدارة برنامج ولاء العملاء ومكافآتهم
          </p>
        </div>
        {program && (
          <div className="flex items-center gap-4">
            <Badge variant={program.isActive ? 'default' : 'secondary'}>
              {program.isActive ? 'نشط' : 'غير نشط'}
            </Badge>
            <Button
              variant="outline"
              onClick={handleToggleProgram}
            >
              {program.isActive ? 'إيقاف' : 'تفعيل'} البرنامج
            </Button>
          </div>
        )}
      </div>

      {/* إحصائيات سريعة */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الأعضاء</p>
                  <p className="text-2xl font-bold">{analytics.totalMembers}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الأعضاء النشطون</p>
                  <p className="text-2xl font-bold text-green-600">{analytics.activeMembers}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">النقاط المصدرة</p>
                  <p className="text-2xl font-bold">{analytics.totalPointsIssued.toLocaleString()}</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Star className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">معدل الاستبدال</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {analytics.redemptionRate.toFixed(1)}%
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <Gift className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* التبويبات */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="settings">الإعدادات</TabsTrigger>
          <TabsTrigger value="rewards">المكافآت</TabsTrigger>
          <TabsTrigger value="members">الأعضاء</TabsTrigger>
        </TabsList>

        {/* نظرة عامة */}
        <TabsContent value="overview" className="space-y-4">
          {program ? (
            <div className="grid gap-6">
              {/* معلومات البرنامج */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات البرنامج</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold">{program.name}</h3>
                    <p className="text-gray-600">{program.description}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">نقاط لكل ريال:</p>
                      <p className="font-semibold">{program.settings.pointsPerSAR}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">نقاط الترحيب:</p>
                      <p className="font-semibold">{program.settings.welcomeBonusPoints}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* توزيع المستويات */}
              {analytics && (
                <Card>
                  <CardHeader>
                    <CardTitle>توزيع الأعضاء حسب المستوى</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analytics.tierDistribution.map((tier) => (
                        <div key={tier.tier} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            {getTierIcon(tier.tier)}
                            <div>
                              <div className="font-medium">{tier.tier}</div>
                              <div className="text-sm text-gray-500">
                                متوسط الإنفاق: {tier.averageSpent.toFixed(0)} ريال
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">{tier.count} عضو</div>
                            <div className="text-sm text-gray-500">{tier.percentage.toFixed(1)}%</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  لا يوجد برنامج ولاء
                </h3>
                <p className="text-gray-500 mb-4">
                  أنشئ برنامج ولاء لعملائك لزيادة الولاء والمبيعات
                </p>
                <Button onClick={() => setActiveTab('settings')}>
                  إنشاء برنامج الولاء
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* الإعدادات */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>إعدادات برنامج الولاء</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* المعلومات الأساسية */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>اسم البرنامج *</FormLabel>
                          <FormControl>
                            <Input placeholder="مثال: برنامج النجوم الذهبية" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pointsPerSAR"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نقاط لكل ريال *</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.1"
                              min="0.1"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            عدد النقاط التي يحصل عليها العميل لكل ريال ينفقه
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>وصف البرنامج *</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="وصف مختصر لبرنامج الولاء وفوائده"
                            className="resize-none"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* إعدادات النقاط */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="minimumOrderForPoints"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>الحد الأدنى للطلب (ريال)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            أقل مبلغ طلب لكسب النقاط
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxPointsPerOrder"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>أقصى نقاط لكل طلب</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            الحد الأقصى للنقاط في الطلب الواحد
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pointsExpiryDays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>انتهاء صلاحية النقاط (يوم)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="30"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            عدد الأيام قبل انتهاء صلاحية النقاط
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* نقاط المكافآت */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="welcomeBonusPoints"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نقاط الترحيب</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            نقاط للعضو الجديد عند الانضمام
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="birthdayBonusPoints"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نقاط عيد الميلاد</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            نقاط إضافية في عيد ميلاد العميل
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="referralBonusPoints"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نقاط الإحالة</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              {...field} 
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            نقاط لإحالة صديق جديد
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* الإعدادات المتقدمة */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="allowPartialRedemption"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              السماح بالاستبدال الجزئي
                            </FormLabel>
                            <FormDescription>
                              يمكن للعملاء استخدام جزء من نقاطهم فقط
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="autoTierUpgrade"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              الترقية التلقائية للمستوى
                            </FormLabel>
                            <FormDescription>
                              ترقية العملاء تلقائياً عند الوصول للحد المطلوب
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* أزرار الحفظ */}
                  <div className="flex gap-4 pt-6">
                    <Button type="submit" disabled={saving} className="flex-1">
                      {saving ? 'جاري الحفظ...' : program ? 'تحديث البرنامج' : 'إنشاء البرنامج'}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* المكافآت */}
        <TabsContent value="rewards" className="space-y-4">
          <Card>
            <CardContent className="p-6 text-center">
              <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                إدارة المكافآت
              </h3>
              <p className="text-gray-500">
                قريباً: إدارة مكافآت برنامج الولاء
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* الأعضاء */}
        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                إدارة الأعضاء
              </h3>
              <p className="text-gray-500">
                قريباً: عرض وإدارة أعضاء برنامج الولاء
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
