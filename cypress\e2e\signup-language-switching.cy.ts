describe('اختبار إنشاء الحساب وتبديل اللغة', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.visitWithLocale('/signup')
  })

  it('يجب أن تعمل عملية إنشاء حساب تاجر مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()
    
    // 1. النقر على إنشاء حساب (إذا لم نكن في صفحة التسجيل بالفعل)
    cy.url().then((url) => {
      if (url.includes('/auth')) {
        cy.get('[data-testid="switch-to-signup"]').should('be.visible').click()
        cy.waitForLoadingToFinish()
      }
    })
    
    // التحقق من أننا في صفحة التسجيل
    cy.url().should('include', '/signup')
    
    // 2. اختيار نوع المستخدم (تاجر)
    cy.get('[data-testid="user-type-merchant"], [data-cy="user-type-merchant"]')
      .should('be.visible')
      .click()
    
    // التحقق من ظهور حقول التاجر
    cy.get('[data-testid="merchant-fields"], [data-cy="merchant-fields"]')
      .should('be.visible')
    
    // 3. تبديل اللغة إلى الإنجليزية
    cy.get('[data-testid="language-switcher"]')
      .should('be.visible')
      .click()
    cy.wait(2000) // انتظار تحميل اللغة الجديدة
    
    // التحقق من تغيير اللغة
    cy.url().should('include', '/en/')
    cy.shouldContainEnglishText('Create')
    
    // التحقق من أن نوع المستخدم المحدد لا يزال محفوظاً
    cy.get('[data-testid="user-type-merchant"], [data-cy="user-type-merchant"]')
      .should('be.checked')
    
    // 4. ملء بيانات التسجيل بالإنجليزية
    cy.get('input[name="username"], [data-cy="username"]')
      .should('be.visible')
      .type('Test Merchant')
    
    cy.get('input[name="email"], [data-cy="email"]')
      .should('be.visible')
      .type('<EMAIL>')
    
    cy.get('input[name="password"], [data-cy="password"]')
      .should('be.visible')
      .type('Password123!')
    
    cy.get('input[name="confirmPassword"], [data-cy="confirm-password"]')
      .should('be.visible')
      .type('Password123!')
    
    // ملء بيانات التاجر الإضافية
    cy.get('input[name="businessName"], [data-cy="business-name"]')
      .should('be.visible')
      .type('Test Business')
    
    cy.get('input[name="phone"], [data-cy="phone"]')
      .should('be.visible')
      .type('0501234567')
    
    // 5. تبديل اللغة مرة أخرى إلى العربية
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // التحقق من العودة للعربية
    cy.url().should('include', '/ar/')
    cy.shouldContainArabicText('إنشاء')
    
    // التحقق من حفظ البيانات المدخلة
    cy.get('input[name="email"], [data-cy="email"]')
      .should('have.value', '<EMAIL>')
    
    // 6. إرسال النموذج
    cy.get('button[type="submit"], [data-cy="submit-signup"]')
      .should('be.visible')
      .click()
    
    // محاكاة نجاح التسجيل
    cy.mockSignup('merchant')
    
    // التحقق من الانتقال لصفحة انتظار الموافقة
    cy.url().should('include', '/merchant/pending-approval')
    cy.shouldContainArabicText('انتظار')
  })

  it('يجب أن تعمل عملية إنشاء حساب عميل مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()
    
    // اختيار نوع العميل
    cy.get('[data-testid="user-type-customer"], [data-cy="user-type-customer"]')
      .should('be.visible')
      .click()
    
    // تبديل اللغة أثناء التسجيل
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // التحقق من تغيير اللغة
    cy.url().should('include', '/en/')
    cy.shouldContainEnglishText('Create')
    
    // ملء البيانات بالإنجليزية
    cy.get('input[name="username"], [data-cy="username"]')
      .type('Test Customer')
    
    cy.get('input[name="email"], [data-cy="email"]')
      .type('<EMAIL>')
    
    cy.get('input[name="password"], [data-cy="password"]')
      .type('Password123!')
    
    cy.get('input[name="confirmPassword"], [data-cy="confirm-password"]')
      .type('Password123!')
    
    // العودة للعربية قبل الإرسال
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // إرسال النموذج
    cy.get('button[type="submit"], [data-cy="submit-signup"]').click()
    
    // محاكاة نجاح التسجيل
    cy.mockSignup('customer')
    
    // التحقق من الانتقال للوحة التحكم
    cy.url().should('include', '/dashboard')
  })

  it('يجب أن تعمل عملية إنشاء حساب مندوب مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()
    
    // اختيار نوع المندوب
    cy.get('[data-testid="user-type-representative"], [data-cy="user-type-representative"]')
      .should('be.visible')
      .click()
    
    // التحقق من ظهور حقول المندوب
    cy.get('[data-testid="representative-fields"], [data-cy="representative-fields"]')
      .should('be.visible')
    
    // ملء البيانات
    cy.get('input[name="username"], [data-cy="username"]')
      .type('مندوب تجريبي')
    
    cy.get('input[name="email"], [data-cy="email"]')
      .type('<EMAIL>')
    
    cy.get('input[name="password"], [data-cy="password"]')
      .type('Password123!')
    
    cy.get('input[name="confirmPassword"], [data-cy="confirm-password"]')
      .type('Password123!')
    
    // ملء بيانات المندوب الإضافية
    cy.get('input[name="phone"], [data-cy="phone"]')
      .type('0501234567')
    
    // تبديل اللغة قبل الإرسال
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // التحقق من حفظ البيانات بعد تبديل اللغة
    cy.get('input[name="email"], [data-cy="email"]')
      .should('have.value', '<EMAIL>')
    
    // إرسال النموذج
    cy.get('button[type="submit"], [data-cy="submit-signup"]').click()
    
    // محاكاة نجاح التسجيل
    cy.mockSignup('representative')
    
    // التحقق من الانتقال لصفحة تسجيل المندوبين
    cy.url().should('include', '/representative/signup')
  })

  it('يجب أن يحافظ على البيانات المدخلة عند تبديل اللغة', () => {
    cy.waitForLoadingToFinish()
    
    // اختيار نوع المستخدم
    cy.get('[data-testid="user-type-customer"], [data-cy="user-type-customer"]').click()
    
    // ملء بعض البيانات
    cy.get('input[name="username"], [data-cy="username"]').type('اسم المستخدم')
    cy.get('input[name="email"], [data-cy="email"]').type('<EMAIL>')
    
    // تبديل اللغة
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // التحقق من حفظ البيانات
    cy.get('input[name="username"], [data-cy="username"]')
      .should('have.value', 'اسم المستخدم')
    cy.get('input[name="email"], [data-cy="email"]')
      .should('have.value', '<EMAIL>')
    
    // تبديل اللغة مرة أخرى
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // التحقق من استمرار حفظ البيانات
    cy.get('input[name="username"], [data-cy="username"]')
      .should('have.value', 'اسم المستخدم')
    cy.get('input[name="email"], [data-cy="email"]')
      .should('have.value', '<EMAIL>')
  })

  it('يجب أن تعمل أزرار التنقل مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()
    
    // اختيار نوع المستخدم
    cy.get('[data-testid="user-type-merchant"], [data-cy="user-type-merchant"]').click()
    
    // التحقق من وجود أزرار التنقل
    cy.get('[data-cy="next-step"], [data-testid="next-step"]')
      .should('be.visible')
    
    // تبديل اللغة
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(2000)
    
    // التحقق من أن الأزرار لا تزال تعمل
    cy.get('[data-cy="next-step"], [data-testid="next-step"]')
      .should('be.visible')
      .should('not.be.disabled')
  })
})
