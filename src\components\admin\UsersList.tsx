'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { useLocale } from '@/hooks/use-locale';
import { ExtendedUserDocument } from '@/hooks/useUsersManagement';
import { UserEditDialog } from './UserEditDialog';
import { UserDetailsDialog } from './UserDetailsDialog';
import { 
  MoreHorizontal, 
  Edit, 
  Eye, 
  UserX, 
  UserCheck,
  Mail,
  Phone,
  Calendar,
  Star,
  ShoppingBag
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface UsersListProps {
  users: ExtendedUserDocument[];
  loading: boolean;
  error: string | null;
  selectedUsers: string[];
  onSelectionChange: (selectedUsers: string[]) => void;
}

export function UsersList({ 
  users, 
  loading, 
  error, 
  selectedUsers, 
  onSelectionChange 
}: UsersListProps) {
  const { t } = useLocale();
  const [editingUser, setEditingUser] = useState<ExtendedUserDocument | null>(null);
  const [viewingUser, setViewingUser] = useState<ExtendedUserDocument | null>(null);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(users.map(user => user.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedUsers, userId]);
    } else {
      onSelectionChange(selectedUsers.filter(id => id !== userId));
    }
  };

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'customer': return t('customer');
      case 'merchant': return t('merchant');
      case 'representative': return 'مندوب';
      case 'admin': return 'مدير';
      default: return userType;
    }
  };

  const getUserTypeBadgeColor = (userType: string) => {
    switch (userType) {
      case 'customer': return 'bg-blue-100 text-blue-800';
      case 'merchant': return 'bg-green-100 text-green-800';
      case 'representative': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-8 w-8" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-red-600">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (users.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">لا توجد مستخدمين</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header مع تحديد الكل */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Checkbox
                checked={selectedUsers.length === users.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm font-medium">
                {selectedUsers.length > 0 
                  ? `${selectedUsers.length} محدد من ${users.length}`
                  : `${users.length} مستخدم`
                }
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المستخدمين */}
      {users.map((user) => (
        <Card key={user.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              {/* Checkbox */}
              <Checkbox
                checked={selectedUsers.includes(user.id)}
                onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
              />

              {/* Avatar */}
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.photoURL || undefined} />
                <AvatarFallback>
                  {user.displayName?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>

              {/* معلومات المستخدم */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {user.displayName || 'بدون اسم'}
                  </h3>
                  <Badge className={`text-xs ${getUserTypeBadgeColor(user.userType)}`}>
                    {getUserTypeLabel(user.userType)}
                  </Badge>
                  {user.isActive && (
                    <Badge variant="outline" className="text-xs text-green-600">
                      نشط
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    <span className="truncate">{user.email}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {user.createdAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
                    </span>
                  </div>
                  
                  {user.totalOrders !== undefined && (
                    <div className="flex items-center gap-1">
                      <ShoppingBag className="h-3 w-3" />
                      <span>{user.totalOrders} طلب</span>
                    </div>
                  )}
                  
                  {user.averageRating !== undefined && user.averageRating > 0 && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      <span>{user.averageRating.toFixed(1)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* إحصائيات سريعة */}
              <div className="hidden md:flex items-center gap-4 text-xs text-gray-500">
                {user.totalSpent !== undefined && (
                  <div className="text-center">
                    <div className="font-medium text-gray-900">
                      {user.totalSpent.toLocaleString('ar-SA')} ر.س
                    </div>
                    <div>إجمالي الإنفاق</div>
                  </div>
                )}
                
                {user.loyaltyPoints !== undefined && (
                  <div className="text-center">
                    <div className="font-medium text-gray-900">
                      {user.loyaltyPoints}
                    </div>
                    <div>نقاط الولاء</div>
                  </div>
                )}
              </div>

              {/* قائمة الإجراءات */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setViewingUser(user)}>
                    <Eye className="h-4 w-4 mr-2" />
                    عرض التفاصيل
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => setEditingUser(user)}>
                    <Edit className="h-4 w-4 mr-2" />
                    تعديل
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem>
                    <Mail className="h-4 w-4 mr-2" />
                    إرسال إشعار
                  </DropdownMenuItem>
                  
                  {user.isActive ? (
                    <DropdownMenuItem className="text-red-600">
                      <UserX className="h-4 w-4 mr-2" />
                      إلغاء التفعيل
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem className="text-green-600">
                      <UserCheck className="h-4 w-4 mr-2" />
                      تفعيل
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* حوارات التعديل والعرض */}
      {editingUser && (
        <UserEditDialog
          user={editingUser}
          onClose={() => setEditingUser(null)}
          onSave={(updatedUser) => {
            // تحديث المستخدم في القائمة
            setEditingUser(null);
          }}
        />
      )}

      {viewingUser && (
        <UserDetailsDialog
          user={viewingUser}
          onClose={() => setViewingUser(null)}
        />
      )}
    </div>
  );
}
