// src/app/[locale]/merchant/pending-approval/page.tsx
"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useRouter, useParams } from 'next/navigation';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, CheckCircle, XCircle, FileText, Phone, Mail } from 'lucide-react';
import type { StoreDocument, UserDocument } from '@/types';

export default function PendingApprovalPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  const [storeData, setStoreData] = useState<StoreDocument | null>(null);
  const [userData, setUserData] = useState<UserDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      console.log('PendingApproval: Starting fetchData, user:', user?.uid);

      if (!user?.uid) {
        console.log('PendingApproval: No user, redirecting to login');
        router.push(`/${locale}/login`);
        return;
      }

      try {
        // جلب بيانات المستخدم
        console.log('PendingApproval: Fetching user document');
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (userDoc.exists()) {
          const userDocData = userDoc.data() as UserDocument;
          console.log('PendingApproval: User data:', userDocData);
          setUserData(userDocData);

          // التحقق من أن المستخدم تاجر
          if (userDocData.userType !== 'merchant') {
            console.log('PendingApproval: User is not merchant, redirecting to dashboard');
            router.push(`/${locale}/dashboard`);
            return;
          }
        } else {
          console.log('PendingApproval: User document not found');
          setError('لم يتم العثور على بيانات المستخدم');
          setLoading(false);
          return;
        }

        // جلب بيانات المتجر
        console.log('PendingApproval: Fetching store document');
        const storeDoc = await getDoc(doc(db, 'stores', user.uid));
        if (storeDoc.exists()) {
          const storeDocData = storeDoc.data() as StoreDocument;
          console.log('PendingApproval: Store data:', storeDocData);
          setStoreData(storeDocData);

          // إذا تم قبول التاجر، توجيهه للوحة التحكم
          if (storeDocData.approvalStatus === 'approved' && storeDocData.isActive) {
            console.log('PendingApproval: Merchant approved, redirecting to dashboard');
            router.push(`/${locale}/merchant/dashboard`);
            return;
          }
        } else {
          // إذا لم يوجد مستند متجر، قد يكون هناك خطأ في التسجيل
          console.log('PendingApproval: Store document not found for merchant:', user.uid);
          setError('لم يتم العثور على بيانات المتجر. يرجى المحاولة مرة أخرى أو التواصل مع الدعم.');
        }
      } catch (err) {
        console.error('PendingApproval: Error fetching data:', err);
        setError('حدث خطأ في جلب البيانات');
      } finally {
        console.log('PendingApproval: Finished loading');
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.uid, router, locale]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  const getStatusIcon = () => {
    switch (storeData?.approvalStatus) {
      case 'pending':
        return <Clock className="h-8 w-8 text-yellow-500" />;
      case 'approved':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-8 w-8 text-red-500" />;
      default:
        return <Clock className="h-8 w-8 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    switch (storeData?.approvalStatus) {
      case 'pending':
        return {
          title: t('approvalPending'),
          description: t('approvalPendingDesc'),
          color: 'text-yellow-600'
        };
      case 'approved':
        return {
          title: t('approvalApproved'),
          description: t('approvalApprovedDesc'),
          color: 'text-green-600'
        };
      case 'rejected':
        return {
          title: t('approvalRejected'),
          description: storeData?.approvalNotes || t('approvalRejectedDesc'),
          color: 'text-red-600'
        };
      default:
        return {
          title: t('unknownStatus'),
          description: t('unknownStatusDesc'),
          color: 'text-gray-600'
        };
    }
  };

  const statusInfo = getStatusMessage();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto text-center">
        <h1 className="text-3xl font-bold mb-8">حالة طلب الانضمام</h1>
        
        {/* بطاقة الحالة */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              {getStatusIcon()}
            </div>
            <CardTitle className={`text-xl ${statusInfo.color}`}>
              {statusInfo.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              {statusInfo.description}
            </p>
            
            {storeData?.submittedAt && (
              <p className="text-sm text-muted-foreground">
                تاريخ التقديم: {new Date(storeData.submittedAt.toDate()).toLocaleDateString('ar-SA')}
              </p>
            )}
            
            {storeData?.approvalDate && (
              <p className="text-sm text-muted-foreground">
                تاريخ المراجعة: {new Date(storeData.approvalDate.toDate()).toLocaleDateString('ar-SA')}
              </p>
            )}
          </CardContent>
        </Card>

        {/* معلومات إضافية */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <FileText className="h-5 w-5" />
              معلومات الطلب
            </CardTitle>
          </CardHeader>
          <CardContent className="text-right space-y-3">
            <div>
              <strong>اسم المتجر:</strong> {storeData?.storeName}
            </div>
            <div>
              <strong>اسم التاجر:</strong> {userData?.displayName || user?.displayName}
            </div>
            <div>
              <strong>البريد الإلكتروني:</strong> {userData?.email || user?.email}
            </div>
            {storeData?.commercialRegistrationURL && (
              <div>
                <strong>السجل التجاري:</strong> 
                <a 
                  href={storeData.commercialRegistrationURL} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline mr-2"
                >
                  عرض الملف
                </a>
              </div>
            )}
            {storeData?.otherLicensesURL && (
              <div>
                <strong>التراخيص الأخرى:</strong> 
                <a 
                  href={storeData.otherLicensesURL} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline mr-2"
                >
                  عرض الملف
                </a>
              </div>
            )}
          </CardContent>
        </Card>

        {/* معلومات الاتصال */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>تحتاج مساعدة؟</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              إذا كان لديك أي استفسارات حول طلبك، يمكنك التواصل معنا:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <EMAIL>
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                +966 50 123 4567
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* أزرار الإجراءات */}
        <div className="flex gap-4 justify-center">
          {storeData?.approvalStatus === 'approved' && storeData?.isActive && (
            <Button
              onClick={() => router.push(`/${locale}/merchant/dashboard`)}
              className="bg-green-600 hover:bg-green-700"
            >
              الذهاب للوحة التحكم
            </Button>
          )}

          {storeData?.approvalStatus === 'rejected' && (
            <Button
              onClick={() => router.push(`/${locale}/signup`)}
              variant="outline"
            >
              تقديم طلب جديد
            </Button>
          )}

          <Button
            onClick={() => router.push(`/${locale}`)}
            variant="outline"
          >
            العودة للصفحة الرئيسية
          </Button>
        </div>
      </div>
    </div>
  );
}
