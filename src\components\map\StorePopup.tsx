"use client";

import { memo } from 'react';
import { Overlay } from 'pigeon-maps';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  X, 
  MapPin, 
  Phone, 
  Clock, 
  Star, 
  Route, 
  Navigation,
  Store,
  Globe,
  ExternalLink
} from 'lucide-react';
import { useLocale } from '@/hooks/use-locale';
import Link from 'next/link';
import type { StoreDocument } from '@/types';

interface StoreWithDistance extends StoreDocument {
  distance: number;
  isOpen: boolean;
  estimatedTime: number;
}

interface StorePopupProps {
  store: StoreWithDistance;
  onClose: () => void;
  userLocation?: {
    latitude: number;
    longitude: number;
  };
}

const StorePopup = memo<StorePopupProps>(({ store, onClose, userLocation }) => {
  const { t } = useLocale();

  if (!store.location?.latitude || !store.location?.longitude) {
    return null;
  }

  // Format business hours
  const formatBusinessHours = () => {
    if (!store.businessHours) return null;
    
    const now = new Date();
    const currentDay = now.toLocaleLowerCase().substring(0, 3);
    const todayHours = store.businessHours[currentDay];
    
    if (!todayHours) return null;
    
    return todayHours.isOpen 
      ? `${todayHours.open} - ${todayHours.close}`
      : t('closed');
  };

  // Get directions URL
  const getDirectionsUrl = () => {
    if (!userLocation) return null;
    
    const { latitude: userLat, longitude: userLng } = userLocation;
    const { latitude: storeLat, longitude: storeLng } = store.location;
    
    // Google Maps directions URL
    return `https://www.google.com/maps/dir/${userLat},${userLng}/${storeLat},${storeLng}`;
  };

  return (
    <Overlay
      anchor={[store.location.latitude, store.location.longitude]}
      offset={[0, -60]}
    >
      <Card className="w-80 max-w-sm shadow-xl border-2 bg-white/95 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3 flex-1">
              <Avatar className="h-12 w-12">
                <AvatarImage src={store.logo} alt={store.name} />
                <AvatarFallback>
                  <Store className="h-6 w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg leading-tight truncate">
                  {store.name}
                </CardTitle>
                {store.category && (
                  <p className="text-sm text-muted-foreground">{store.category}</p>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 shrink-0"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Status and Rating */}
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2">
              {store.isOpen ? (
                <Badge variant="default" className="bg-green-500 text-white">
                  <Clock className="w-3 h-3 mr-1" />
                  {t('open')}
                </Badge>
              ) : (
                <Badge variant="secondary">
                  <Clock className="w-3 h-3 mr-1" />
                  {t('closed')}
                </Badge>
              )}
            </div>
            
            {store.rating && (
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">{store.rating.toFixed(1)}</span>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-3">
          {/* Description */}
          {store.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {store.description}
            </p>
          )}

          {/* Distance and Time */}
          {store.distance > 0 && (
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1 text-muted-foreground">
                <Route className="w-4 h-4" />
                <span>{store.distance.toFixed(1)} {t('km')}</span>
              </div>
              {store.estimatedTime > 0 && (
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Navigation className="w-4 h-4" />
                  <span>{store.estimatedTime} {t('minutes')}</span>
                </div>
              )}
            </div>
          )}

          {/* Business Hours */}
          {formatBusinessHours() && (
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-muted-foreground">{t('today')}: </span>
              <span className={store.isOpen ? 'text-green-600 font-medium' : 'text-red-600'}>
                {formatBusinessHours()}
              </span>
            </div>
          )}

          {/* Contact Info */}
          <div className="space-y-2">
            {store.phone && (
              <div className="flex items-center gap-2 text-sm">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <a 
                  href={`tel:${store.phone}`}
                  className="text-primary hover:underline"
                >
                  {store.phone}
                </a>
              </div>
            )}

            {store.address && (
              <div className="flex items-start gap-2 text-sm">
                <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                <span className="text-muted-foreground">{store.address}</span>
              </div>
            )}

            {store.website && (
              <div className="flex items-center gap-2 text-sm">
                <Globe className="w-4 h-4 text-muted-foreground" />
                <a 
                  href={store.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline flex items-center gap-1"
                >
                  {t('website')}
                  <ExternalLink className="w-3 h-3" />
                </a>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button asChild className="flex-1" size="sm">
              <Link href={`/stores/${store.id}`}>
                <Store className="w-4 h-4 mr-2" />
                {t('visitStore')}
              </Link>
            </Button>

            {getDirectionsUrl() && (
              <Button 
                variant="outline" 
                size="sm"
                asChild
                className="flex-1"
              >
                <a 
                  href={getDirectionsUrl()!}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Navigation className="w-4 h-4 mr-2" />
                  {t('directions')}
                </a>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </Overlay>
  );
});

StorePopup.displayName = 'StorePopup';

export default StorePopup;
