"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { CheckCircle, XCircle } from "lucide-react";
import type { SubscriptionPlan } from '@/types';
import { useLocale } from '@/hooks/use-locale';
import { Badge } from "@/components/ui/badge";

interface FeatureCardProps {
  plan: SubscriptionPlan;
  showPricing?: boolean;
}

export default function FeatureCard({ plan, showPricing = false }: FeatureCardProps) {
  const { t } = useLocale();

  const formatPrice = () => {
    if (plan.priceDisplayKey === 'free') {
      return t('free');
    }
    if (plan.priceValue !== undefined && plan.currencyKey && plan.periodKey) {
      return t(plan.priceDisplayKey, {
        price: plan.priceValue,
        currency: t(plan.currencyKey),
        period: t(plan.periodKey)
      });
    }
    return '';
  };

  return (
    <Card className={`flex flex-col ${plan.isPopular ? 'border-primary border-2 shadow-primary/30 shadow-lg' : 'shadow-md'} hover:shadow-xl transition-shadow duration-300`}>
      {plan.isPopular && (
        <Badge variant="default" className="absolute -top-3 start-1/2 -translate-x-1/2 bg-primary text-primary-foreground">
          {t('popular')}
        </Badge>
      )}
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-bold text-center text-primary">{t(plan.nameKey)}</CardTitle>
        {showPricing && (
          <CardDescription className="text-4xl font-extrabold text-center text-foreground pt-2">
            {formatPrice()}
          </CardDescription>
        )}
        {plan.commission !== undefined && plan.commissionKey && showPricing && (
           <p className="text-sm text-muted-foreground text-center pt-1">
             {t(plan.commissionKey, { value: plan.commission })}
           </p>
        )}
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="space-y-4">
          <div className="text-center">
            <h4 className="font-semibold text-lg text-primary mb-3">{t('allFeatures')}</h4>
            <div className="grid grid-cols-2 gap-2 mb-4 p-3 bg-muted/30 rounded-lg">
              <div className="text-center">
                <p className="text-xl font-bold text-primary">{plan.features.filter((f) => f.available).length}</p>
                <p className="text-xs text-muted-foreground">{t('availableFeatures')}</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-primary">{plan.features.length}</p>
                <p className="text-xs text-muted-foreground">{t('totalFeatures')}</p>
              </div>
            </div>
          </div>
          
          <ul className="space-y-3">
            {plan.features.map((feature) => (
              <li key={feature.nameKey} className="flex items-center justify-center space-x-2 rtl:space-x-reverse text-center">
                {feature.available ? (
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                )}
                <span className={`text-sm ${feature.available ? 'text-foreground' : 'text-muted-foreground line-through'}`}>
                  {t(feature.nameKey)}
                </span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
