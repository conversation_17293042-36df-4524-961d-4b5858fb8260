// src/components/merchant/QuickActions.tsx
"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useLocale } from '@/hooks/use-locale';
import Link from 'next/link';
import {
  Plus,
  Package,
  ShoppingCart,
  Settings,
  BarChart3,
  Warehouse,
  Store,
  FileText,
  Tag,
  Star
} from 'lucide-react';

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  variant?: 'default' | 'secondary' | 'outline';
}

function QuickActionCard({ title, description, icon, href, variant = 'outline' }: QuickActionProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="p-3 rounded-full bg-primary/10 text-primary">
            {icon}
          </div>
          <div>
            <h3 className="font-semibold text-center mb-2">{title}</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              {description}
            </p>
          </div>
          <Button asChild variant={variant} className="w-full">
            <Link href={href}>
              {title}
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface QuickActionsProps {
  className?: string;
}

export default function QuickActions({ className }: QuickActionsProps) {
  const { t, locale } = useLocale();

  const actions: QuickActionProps[] = [
    {
      title: t('addNewProduct'),
      description: 'إضافة منتج جديد إلى متجرك',
      icon: <Plus className="h-6 w-6" />,
      href: `/${locale}/merchant/products/add`,
      variant: 'default',
    },
    {
      title: t('manageProducts'),
      description: 'عرض وإدارة جميع منتجاتك',
      icon: <Package className="h-6 w-6" />,
      href: `/${locale}/merchant/products`,
    },
    {
      title: t('manageOrders'),
      description: 'متابعة وإدارة طلبات العملاء',
      icon: <ShoppingCart className="h-6 w-6" />,
      href: `/${locale}/merchant/orders`,
    },
    {
      title: t('storeSettings'),
      description: 'تحديث معلومات وإعدادات متجرك',
      icon: <Store className="h-6 w-6" />,
      href: `/${locale}/merchant/store/settings`,
    },
    {
      title: t('viewReports'),
      description: 'عرض تقارير المبيعات والتحليلات',
      icon: <BarChart3 className="h-6 w-6" />,
      href: `/${locale}/merchant/reports`,
    },
    {
      title: t('manageInventory'),
      description: 'مراقبة وإدارة مخزون المنتجات',
      icon: <Warehouse className="h-6 w-6" />,
      href: `/${locale}/merchant/inventory`,
    },
    {
      title: t('manageCoupons'),
      description: 'إنشاء وإدارة كوبونات الخصم',
      icon: <Tag className="h-6 w-6" />,
      href: `/${locale}/merchant/coupons`,
    },
    {
      title: 'برنامج الولاء',
      description: 'إدارة برنامج ولاء العملاء',
      icon: <Star className="h-6 w-6" />,
      href: `/${locale}/merchant/loyalty`,
    },
  ];

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-center mb-2">{t('quickActions')}</h2>
        <p className="text-muted-foreground text-center">
          {t('quickActionsSubtitle')}
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {actions.map((action, index) => (
          <QuickActionCard
            key={index}
            title={action.title}
            description={action.description}
            icon={action.icon}
            href={action.href}
            variant={action.variant}
          />
        ))}
      </div>
    </div>
  );
}
