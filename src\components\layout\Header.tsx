// src/components/layout/Header.tsx
"use client";

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import LanguageSwitcher from './LanguageSwitcher';
import { useLocale } from '@/hooks/use-locale';
import type { Locale } from '@/lib/i18n';
import { BrandLogo } from '@/components/Logo';
import { UserCircle, LogIn, LogOut, Loader2, Search } from 'lucide-react';
import { NavColoredIcon } from '@/components/ui/colored-icons';
import { useAuth } from '@/context/AuthContext';
import { auth } from '@/lib/firebase';
import { signOut } from 'firebase/auth';
import { useToast } from "@/hooks/use-toast";
import SearchBar from '@/components/customer/SearchBar';
import CartSidebar from '@/components/cart/CartSidebar';
import NotificationCenter from '@/components/notifications/NotificationCenter';
import { useIsMobile } from '@/hooks/use-mobile';

interface HeaderProps {
  locale: Locale;
}

export default function Header({ locale }: HeaderProps) {
  const { t } = useLocale();
  const pathname = usePathname();
  const { user, loading, initialLoadingCompleted } = useAuth();
  const isAuthenticated = !loading && !!user && initialLoadingCompleted;
  const router = useRouter();
  const { toast } = useToast();
  const isMobile = useIsMobile();

  const navLinks = [
    { href: `/${locale}`, labelKey: 'home', icon: <NavColoredIcon type="home" size={20} />, type: 'home' },
    { href: `/${locale}/stores`, labelKey: 'stores', icon: <NavColoredIcon type="store" size={20} />, type: 'store' },
    { href: `/${locale}/categories`, labelKey: 'categories', icon: <NavColoredIcon type="categories" size={20} />, type: 'categories' },
    { href: `/${locale}/pricing`, labelKey: 'pricing', icon: <NavColoredIcon type="pricing" size={20} />, type: 'pricing' },
  ];

  const handleLogout = async () => {
    try {
      // تسجيل الخروج من Firebase
      await signOut(auth);

      // مسح أي بيانات محلية مخزنة
      if (typeof window !== 'undefined') {
        localStorage.removeItem('firebase:authUser');
        sessionStorage.clear();
      }

      // عرض رسالة نجاح
      toast({
        title: t('logoutSuccessTitle'),
        description: t('logoutSuccessMessage')
      });

      // إعادة التوجيه إلى صفحة تسجيل الدخول
      router.replace(`/${locale}/login`);

      // إعادة تحميل الصفحة للتأكد من مسح جميع البيانات
      setTimeout(() => {
        window.location.reload();
      }, 100);

    } catch (error) {
      console.error("Logout error:", error);

      // في حالة فشل تسجيل الخروج، قم بمسح البيانات المحلية وإعادة التوجيه
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
      }

      toast({
        title: t('errorTitle'),
        description: t('logoutFailed'),
        variant: "destructive"
      });

      // إعادة التوجيه حتى لو فشل تسجيل الخروج
      router.replace(`/${locale}/login`);
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container max-w-screen-2xl px-4 sm:px-6 lg:px-8">
        {/* Main Header Row */}
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2 rtl:space-x-reverse">
            <BrandLogo size="small" showText={false} />
            <span className="font-bold text-xl">{t('appName')}</span>
          </Link>

          {/* Search Bar - Desktop */}
          {!isMobile && (
            <div className="flex-1 max-w-md mx-8">
              <SearchBar
                placeholder={t('searchPlaceholder')}
                variant="compact"
              />
            </div>
          )}

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {/* Notifications - Only for authenticated users */}
            {isAuthenticated && <NotificationCenter />}

            {/* Cart Icon */}
            <CartSidebar />

            <LanguageSwitcher currentLocale={locale} />

            {loading && !initialLoadingCompleted ? (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            ) : isAuthenticated ? (
              <>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/${locale}/profile`}>
                    <UserCircle className="me-1.5 rtl:ms-1.5" />
                    {!isMobile && (user?.displayName || t('profile'))}
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                >
                  <LogOut className="me-1.5 rtl:ms-1.5" />
                  {!isMobile && t('logout')}
                </Button>
              </>
            ) : (
              <>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/${locale}/login`}>
                    <LogIn className="me-1.5 rtl:ms-1.5" />
                    {!isMobile && t('login')}
                  </Link>
                </Button>
                <Button size="sm" asChild className="bg-accent hover:bg-accent/90 text-accent-foreground">
                  <Link href={`/${locale}/signup`}>
                    {!isMobile && t('signup')}
                  </Link>
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Navigation Row - Desktop */}
        {!isMobile && (
          <div className="border-t border-border/40 bg-gradient-to-r from-background via-background/95 to-background">
            <div className="flex justify-center items-center w-full">
              <nav className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-sm font-medium py-4 px-8">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`nav-item ${pathname === link.href ? 'active' : ''} relative group`}
                >
                  <span className="flex items-center gap-2 nav-text">
                    <span className="nav-icon">
                      {link.icon}
                    </span>
                    <span className="font-medium tracking-wide">
                      {t(link.labelKey)}
                    </span>
                  </span>
                </Link>
              ))}
            </nav>
            </div>
          </div>
        )}

        {/* Search Bar - Mobile */}
        {isMobile && (
          <div className="border-t border-border/40 py-3">
            <SearchBar
              placeholder={t('searchPlaceholder')}
              variant="compact"
            />
          </div>
        )}
      </div>

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md border-t border-border/40 safe-area-pb">
          <nav className="flex items-center justify-around py-2 px-4">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`nav-item mobile-nav-item ${pathname === link.href ? 'active' : ''} flex flex-col items-center justify-center min-w-0 flex-1 py-2`}
              >
                <span className="nav-icon mb-1">
                  {link.icon}
                </span>
                <span className="nav-text text-xs font-medium truncate max-w-full">
                  {t(link.labelKey)}
                </span>
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
}
