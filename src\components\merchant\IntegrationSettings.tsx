'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Plus, 
  Trash2, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Database,
  ShoppingCart,
  Zap,
  Clock,
  Activity
} from 'lucide-react';
import { useLocale } from '@/hooks/use-locale';
import { useAuth } from '@/hooks/useAuth';
import { erpIntegrationService } from '@/services/erpIntegration';
import { posIntegrationService } from '@/services/posIntegration';
import type { ERPIntegration, POSIntegration } from '@/types';

interface IntegrationSettingsProps {
  merchantId: string;
}

export function IntegrationSettings({ merchantId }: IntegrationSettingsProps) {
  const { t } = useLocale();
  const { user } = useAuth();
  
  // حالات البيانات
  const [erpIntegrations, setErpIntegrations] = useState<ERPIntegration[]>([]);
  const [posIntegrations, setPosIntegrations] = useState<POSIntegration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // حالات النماذج
  const [showERPForm, setShowERPForm] = useState(false);
  const [showPOSForm, setShowPOSForm] = useState(false);
  const [testingConnection, setTestingConnection] = useState<string | null>(null);
  const [syncing, setSyncing] = useState<string | null>(null);

  // بيانات نموذج ERP
  const [erpFormData, setErpFormData] = useState({
    systemType: 'sap' as ERPIntegration['systemType'],
    systemName: '',
    apiUrl: '',
    apiKey: '',
    username: '',
    password: '',
    database: '',
    syncProducts: true,
    syncInventory: true,
    syncOrders: true,
    syncCustomers: true,
    syncAccounting: false,
    syncInterval: 60,
    autoSync: true
  });

  // بيانات نموذج POS
  const [posFormData, setPosFormData] = useState({
    systemType: 'square' as POSIntegration['systemType'],
    systemName: '',
    apiUrl: '',
    apiKey: '',
    accessToken: '',
    storeId: '',
    locationId: '',
    environment: 'sandbox' as 'sandbox' | 'production',
    syncProducts: true,
    syncInventory: true,
    syncSales: true,
    syncCustomers: true,
    syncPayments: true,
    syncInterval: 30,
    autoSync: true
  });

  // تحميل البيانات عند بدء المكون
  useEffect(() => {
    loadIntegrations();
  }, [merchantId]);

  /**
   * تحميل جميع التكاملات
   */
  const loadIntegrations = async () => {
    try {
      setLoading(true);
      setError(null);

      const [erpData, posData] = await Promise.all([
        erpIntegrationService.getMerchantIntegrations(merchantId),
        posIntegrationService.getMerchantIntegrations(merchantId)
      ]);

      setErpIntegrations(erpData);
      setPosIntegrations(posData);
    } catch (err) {
      setError('فشل في تحميل بيانات التكامل');
      console.error('Error loading integrations:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * إنشاء تكامل ERP جديد
   */
  const createERPIntegration = async () => {
    try {
      setLoading(true);
      
      const integrationData: Omit<ERPIntegration, 'id' | 'createdAt' | 'updatedAt'> = {
        merchantId,
        systemType: erpFormData.systemType,
        systemName: erpFormData.systemName,
        isActive: true,
        configuration: {
          apiUrl: erpFormData.apiUrl,
          apiKey: erpFormData.apiKey,
          username: erpFormData.username,
          password: erpFormData.password,
          database: erpFormData.database
        },
        syncSettings: {
          syncProducts: erpFormData.syncProducts,
          syncInventory: erpFormData.syncInventory,
          syncOrders: erpFormData.syncOrders,
          syncCustomers: erpFormData.syncCustomers,
          syncAccounting: erpFormData.syncAccounting,
          syncInterval: erpFormData.syncInterval,
          autoSync: erpFormData.autoSync
        },
        fieldMapping: {
          productFields: {},
          customerFields: {},
          orderFields: {},
          inventoryFields: {}
        },
        status: 'disconnected'
      };

      await erpIntegrationService.createIntegration(integrationData);
      
      // إعادة تحميل البيانات
      await loadIntegrations();
      
      // إخفاء النموذج وإعادة تعيين البيانات
      setShowERPForm(false);
      resetERPForm();
      
    } catch (err) {
      setError('فشل في إنشاء تكامل ERP');
      console.error('Error creating ERP integration:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * إنشاء تكامل POS جديد
   */
  const createPOSIntegration = async () => {
    try {
      setLoading(true);
      
      const integrationData: Omit<POSIntegration, 'id' | 'createdAt' | 'updatedAt'> = {
        merchantId,
        systemType: posFormData.systemType,
        systemName: posFormData.systemName,
        isActive: true,
        configuration: {
          apiUrl: posFormData.apiUrl,
          apiKey: posFormData.apiKey,
          accessToken: posFormData.accessToken,
          storeId: posFormData.storeId,
          locationId: posFormData.locationId,
          environment: posFormData.environment
        },
        syncSettings: {
          syncProducts: posFormData.syncProducts,
          syncInventory: posFormData.syncInventory,
          syncSales: posFormData.syncSales,
          syncCustomers: posFormData.syncCustomers,
          syncPayments: posFormData.syncPayments,
          syncInterval: posFormData.syncInterval,
          autoSync: posFormData.autoSync
        },
        fieldMapping: {
          productFields: {},
          customerFields: {},
          saleFields: {},
          paymentFields: {}
        },
        status: 'disconnected'
      };

      await posIntegrationService.createIntegration(integrationData);
      
      // إعادة تحميل البيانات
      await loadIntegrations();
      
      // إخفاء النموذج وإعادة تعيين البيانات
      setShowPOSForm(false);
      resetPOSForm();
      
    } catch (err) {
      setError('فشل في إنشاء تكامل POS');
      console.error('Error creating POS integration:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * اختبار الاتصال
   */
  const testConnection = async (type: 'erp' | 'pos', integrationId: string) => {
    try {
      setTestingConnection(integrationId);
      
      let success = false;
      if (type === 'erp') {
        success = await erpIntegrationService.testConnection(integrationId);
      } else {
        success = await posIntegrationService.testConnection(integrationId);
      }
      
      if (success) {
        // إعادة تحميل البيانات لتحديث الحالة
        await loadIntegrations();
      }
      
    } catch (err) {
      setError('فشل في اختبار الاتصال');
      console.error('Error testing connection:', err);
    } finally {
      setTestingConnection(null);
    }
  };

  /**
   * مزامنة البيانات
   */
  const syncData = async (type: 'erp' | 'pos', integrationId: string, entityType: string) => {
    try {
      setSyncing(integrationId);
      
      if (type === 'erp') {
        await erpIntegrationService.syncData(integrationId, entityType as any);
      } else {
        await posIntegrationService.syncData(integrationId, entityType as any);
      }
      
      // إعادة تحميل البيانات
      await loadIntegrations();
      
    } catch (err) {
      setError('فشل في مزامنة البيانات');
      console.error('Error syncing data:', err);
    } finally {
      setSyncing(null);
    }
  };

  /**
   * حذف تكامل
   */
  const deleteIntegration = async (type: 'erp' | 'pos', integrationId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا التكامل؟')) {
      return;
    }

    try {
      setLoading(true);
      
      if (type === 'erp') {
        await erpIntegrationService.deleteIntegration(integrationId);
      } else {
        await posIntegrationService.deleteIntegration(integrationId);
      }
      
      // إعادة تحميل البيانات
      await loadIntegrations();
      
    } catch (err) {
      setError('فشل في حذف التكامل');
      console.error('Error deleting integration:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * إعادة تعيين نموذج ERP
   */
  const resetERPForm = () => {
    setErpFormData({
      systemType: 'sap',
      systemName: '',
      apiUrl: '',
      apiKey: '',
      username: '',
      password: '',
      database: '',
      syncProducts: true,
      syncInventory: true,
      syncOrders: true,
      syncCustomers: true,
      syncAccounting: false,
      syncInterval: 60,
      autoSync: true
    });
  };

  /**
   * إعادة تعيين نموذج POS
   */
  const resetPOSForm = () => {
    setPosFormData({
      systemType: 'square',
      systemName: '',
      apiUrl: '',
      apiKey: '',
      accessToken: '',
      storeId: '',
      locationId: '',
      environment: 'sandbox',
      syncProducts: true,
      syncInventory: true,
      syncSales: true,
      syncCustomers: true,
      syncPayments: true,
      syncInterval: 30,
      autoSync: true
    });
  };

  /**
   * الحصول على أيقونة الحالة
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'syncing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  /**
   * الحصول على لون شارة الحالة
   */
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'connected':
        return 'default';
      case 'error':
        return 'destructive';
      case 'syncing':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (loading && erpIntegrations.length === 0 && posIntegrations.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان الرئيسي */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إعدادات التكامل</h1>
          <p className="text-muted-foreground">
            ربط متجرك مع أنظمة ERP و POS الخارجية
          </p>
        </div>
        <Settings className="h-8 w-8 text-muted-foreground" />
      </div>

      {/* رسائل الخطأ */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* التبويبات الرئيسية */}
      <Tabs defaultValue="erp" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="erp" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            أنظمة ERP
          </TabsTrigger>
          <TabsTrigger value="pos" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            أنظمة POS
          </TabsTrigger>
        </TabsList>

        {/* تبويب أنظمة ERP */}
        <TabsContent value="erp" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">تكاملات أنظمة ERP</h2>
            <Button 
              onClick={() => setShowERPForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة تكامل ERP
            </Button>
          </div>

          {/* قائمة تكاملات ERP */}
          <div className="grid gap-4">
            {erpIntegrations.map((integration) => (
              <Card key={integration.id}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(integration.status)}
                    <div>
                      <CardTitle className="text-lg">{integration.systemName}</CardTitle>
                      <CardDescription>
                        {integration.systemType.toUpperCase()} - 
                        آخر مزامنة: {integration.syncSettings.lastSync ? 
                          new Date(integration.syncSettings.lastSync.toDate()).toLocaleString('ar-SA') : 
                          'لم تتم بعد'
                        }
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadgeVariant(integration.status)}>
                      {integration.status === 'connected' ? 'متصل' :
                       integration.status === 'error' ? 'خطأ' :
                       integration.status === 'syncing' ? 'جاري المزامنة' : 'غير متصل'}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testConnection('erp', integration.id)}
                      disabled={testingConnection === integration.id}
                    >
                      {testingConnection === integration.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4" />
                      )}
                      اختبار الاتصال
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => syncData('erp', integration.id, 'products')}
                      disabled={syncing === integration.id || integration.status !== 'connected'}
                    >
                      {syncing === integration.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Activity className="h-4 w-4" />
                      )}
                      مزامنة
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteIntegration('erp', integration.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">المنتجات:</span>
                      <Badge variant={integration.syncSettings.syncProducts ? 'default' : 'outline'}>
                        {integration.syncSettings.syncProducts ? 'مفعل' : 'معطل'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">المخزون:</span>
                      <Badge variant={integration.syncSettings.syncInventory ? 'default' : 'outline'}>
                        {integration.syncSettings.syncInventory ? 'مفعل' : 'معطل'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">الطلبات:</span>
                      <Badge variant={integration.syncSettings.syncOrders ? 'default' : 'outline'}>
                        {integration.syncSettings.syncOrders ? 'مفعل' : 'معطل'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>كل {integration.syncSettings.syncInterval} دقيقة</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {erpIntegrations.length === 0 && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Database className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد تكاملات ERP</h3>
                  <p className="text-muted-foreground text-center mb-4">
                    ابدأ بإضافة تكامل مع نظام ERP لمزامنة بياناتك
                  </p>
                  <Button onClick={() => setShowERPForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة تكامل ERP
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* تبويب أنظمة POS */}
        <TabsContent value="pos" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">تكاملات أنظمة POS</h2>
            <Button 
              onClick={() => setShowPOSForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة تكامل POS
            </Button>
          </div>

          {/* قائمة تكاملات POS */}
          <div className="grid gap-4">
            {posIntegrations.map((integration) => (
              <Card key={integration.id}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(integration.status)}
                    <div>
                      <CardTitle className="text-lg">{integration.systemName}</CardTitle>
                      <CardDescription>
                        {integration.systemType.toUpperCase()} - 
                        آخر مزامنة: {integration.syncSettings.lastSync ? 
                          new Date(integration.syncSettings.lastSync.toDate()).toLocaleString('ar-SA') : 
                          'لم تتم بعد'
                        }
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadgeVariant(integration.status)}>
                      {integration.status === 'connected' ? 'متصل' :
                       integration.status === 'error' ? 'خطأ' :
                       integration.status === 'syncing' ? 'جاري المزامنة' : 'غير متصل'}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testConnection('pos', integration.id)}
                      disabled={testingConnection === integration.id}
                    >
                      {testingConnection === integration.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4" />
                      )}
                      اختبار الاتصال
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => syncData('pos', integration.id, 'products')}
                      disabled={syncing === integration.id || integration.status !== 'connected'}
                    >
                      {syncing === integration.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Activity className="h-4 w-4" />
                      )}
                      مزامنة
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteIntegration('pos', integration.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">المنتجات:</span>
                      <Badge variant={integration.syncSettings.syncProducts ? 'default' : 'outline'}>
                        {integration.syncSettings.syncProducts ? 'مفعل' : 'معطل'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">المبيعات:</span>
                      <Badge variant={integration.syncSettings.syncSales ? 'default' : 'outline'}>
                        {integration.syncSettings.syncSales ? 'مفعل' : 'معطل'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">المدفوعات:</span>
                      <Badge variant={integration.syncSettings.syncPayments ? 'default' : 'outline'}>
                        {integration.syncSettings.syncPayments ? 'مفعل' : 'معطل'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>كل {integration.syncSettings.syncInterval} دقيقة</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {posIntegrations.length === 0 && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <ShoppingCart className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد تكاملات POS</h3>
                  <p className="text-muted-foreground text-center mb-4">
                    ابدأ بإضافة تكامل مع نظام POS لمزامنة مبيعاتك
                  </p>
                  <Button onClick={() => setShowPOSForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة تكامل POS
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
