// src/types/representative.ts
import { Timestamp } from 'firebase/firestore';

export type RepresentativeApprovalStatus = 'pending' | 'approved' | 'rejected' | 'suspended';

export type VehicleType = 'car' | 'motorcycle' | 'bicycle';

export type NationalIdType = 'national' | 'resident';

export interface DrivingLicense {
  number: string;
  issueDate: Timestamp;
  expiryDate: Timestamp;
  imageURL: string;
}

export interface VehicleInfo {
  type: VehicleType;
  model: string;
  year: number;
  plateNumber: string;
  color: string;
  imageURL?: string;
}

export interface VehicleInspection {
  certificateNumber: string;
  issueDate: Timestamp;
  expiryDate: Timestamp;
  imageURL: string;
}

export interface RepresentativeStats {
  totalDeliveries: number;
  completedDeliveries: number;
  cancelledDeliveries: number;
  averageRating: number;
  reviewCount: number;
  totalEarnings: number;
  monthlyEarnings: number;
  weeklyEarnings: number;
  dailyEarnings: number;
  averageDeliveryTime: number; // بالدقائق
  successRate: number; // نسبة النجاح
}

export interface RepresentativeDocument {
  // المعلومات الأساسية
  uid: string;
  email: string | null;
  displayName: string;
  phoneNumber: string;
  photoURL?: string | null;
  
  // معلومات الهوية
  nationalId: string;
  nationalIdType: NationalIdType;
  
  // رخصة القيادة
  drivingLicense: DrivingLicense;
  
  // معلومات المركبة
  vehicle: VehicleInfo;
  
  // شهادة الفحص الدوري
  vehicleInspection: VehicleInspection;
  
  // حالة الحساب والموافقة
  approvalStatus: RepresentativeApprovalStatus;
  approvalDate?: Timestamp;
  approvalNotes?: string;
  reviewedBy?: string; // UID للمدير الذي راجع الطلب
  
  // معلومات الخطة والعمولة
  planId: string;
  commissionRate: number; // نسبة العمولة للتطبيق
  
  // حالة النشاط
  isActive: boolean;
  isAvailable: boolean; // متاح لاستلام طلبات جديدة
  currentLocation?: {
    latitude: number;
    longitude: number;
    lastUpdated: Timestamp;
  };
  
  // الإحصائيات
  stats?: RepresentativeStats;
  
  // التواريخ
  createdAt: Timestamp;
  updatedAt: Timestamp;
  submittedAt: Timestamp;
}

// أنواع الطلبات للمندوبين
export type DeliveryStatus = 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';

export interface DeliveryOrder {
  id: string;
  orderId: string; // ربط بطلب العميل الأصلي
  representativeId: string;
  customerId: string;
  merchantId: string;
  
  // معلومات الاستلام
  pickupLocation: {
    address: string;
    latitude: number;
    longitude: number;
    contactName: string;
    contactPhone: string;
  };
  
  // معلومات التسليم
  deliveryLocation: {
    address: string;
    latitude: number;
    longitude: number;
    contactName: string;
    contactPhone: string;
  };
  
  // تفاصيل الطلب
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    price: number;
  }>;
  
  totalAmount: number;
  deliveryFee: number;
  representativeEarning: number;
  
  // حالة التوصيل
  status: DeliveryStatus;
  assignedAt?: Timestamp;
  pickedUpAt?: Timestamp;
  deliveredAt?: Timestamp;
  
  // ملاحظات وتقييم
  notes?: string;
  customerRating?: number;
  customerReview?: string;
  
  // التواريخ
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// نوع بيانات التقييم
export interface RepresentativeReview {
  id: string;
  representativeId: string;
  customerId: string;
  orderId: string;
  rating: number; // من 1 إلى 5
  review?: string;
  createdAt: Timestamp;
}

// نوع بيانات الأرباح
export interface EarningsRecord {
  id: string;
  representativeId: string;
  orderId: string;
  amount: number;
  commissionRate: number;
  date: Timestamp;
  status: 'pending' | 'paid' | 'cancelled';
  paymentMethod?: string;
  transactionId?: string;
}

// نوع بيانات إعدادات المندوب
export interface RepresentativeSettings {
  representativeId: string;
  workingHours: {
    start: string; // "09:00"
    end: string;   // "18:00"
  };
  workingDays: string[]; // ["monday", "tuesday", ...]
  maxDeliveryRadius: number; // بالكيلومتر
  vehicleCapacity: number; // عدد الطلبات القصوى في نفس الوقت
  autoAcceptOrders: boolean;
  notificationSettings: {
    newOrders: boolean;
    orderUpdates: boolean;
    earnings: boolean;
    promotions: boolean;
  };
  updatedAt: Timestamp;
}

// نوع بيانات طلب التسجيل
export interface RepresentativeSignupData {
  // المعلومات الشخصية
  displayName: string;
  phoneNumber: string;
  email: string;
  
  // معلومات الهوية
  nationalId: string;
  nationalIdType: NationalIdType;
  
  // رخصة القيادة
  drivingLicenseNumber: string;
  drivingLicenseIssueDate: Date;
  drivingLicenseExpiryDate: Date;
  drivingLicenseImage: File;
  
  // معلومات المركبة
  vehicleType: VehicleType;
  vehicleModel: string;
  vehicleYear: number;
  vehiclePlateNumber: string;
  vehicleColor: string;
  vehicleImage?: File;
  
  // شهادة الفحص
  inspectionCertificateNumber: string;
  inspectionIssueDate: Date;
  inspectionExpiryDate: Date;
  inspectionCertificateImage: File;
  
  // الصورة الشخصية
  profileImage?: File;
  
  // الموافقة على الشروط
  agreeToTerms: boolean;
}
