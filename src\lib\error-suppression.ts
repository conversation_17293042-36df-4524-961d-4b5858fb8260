// src/lib/error-suppression.ts
// مكتبة لقمع الأخطاء الشائعة وغير المهمة في وحدة التحكم

/**
 * قائمة الأخطاء التي يجب تجاهلها
 */
const IGNORED_ERRORS = [
  // أخطاء Firebase الشائعة
  'Failed to get document because the client is offline',
  'Request timeout',
  'Transport error',
  'deadline-exceeded',
  'unavailable',
  'cancelled',
  'unauthenticated',
  
  // أخطاء Stream
  'Stream is already ended',
  'failed to pipe response',
  'Cannot set headers after they are sent',
  'Response already sent',
  'Stream was destroyed',
  'premature close',
  
  // أخطاء الشبكة
  'Network request failed',
  'fetch failed',
  'connection refused',
  'timeout',
  'offline',
  
  // أخطاء المتصفح الشائعة
  'ResizeObserver loop limit exceeded',
  'Non-Error promise rejection captured',
  'Script error',
  'Loading chunk',
  'ChunkLoadError',
  
  // أخطاء Next.js الشائعة
  'Hydration failed',
  'Text content does not match',
  'Warning: validateDOMNesting',
];

/**
 * قائمة رموز الأخطاء التي يجب تجاهلها
 */
const IGNORED_ERROR_CODES = [
  'ERR_NETWORK',
  'ERR_INTERNET_DISCONNECTED',
  'ERR_CONNECTION_REFUSED',
  'ERR_STREAM_ALREADY_FINISHED',
  'ERR_STREAM_DESTROYED',
  'ERR_STREAM_WRITE_AFTER_END',
  'ERR_STREAM_PREMATURE_CLOSE',
];

/**
 * فحص ما إذا كان الخطأ يجب تجاهله
 */
export function shouldIgnoreError(error: any): boolean {
  if (!error) return false;

  const message = error.message || error.toString();
  const code = error.code || '';

  // فحص رسالة الخطأ
  const shouldIgnoreMessage = IGNORED_ERRORS.some(ignoredError =>
    message.toLowerCase().includes(ignoredError.toLowerCase())
  );

  // فحص رمز الخطأ
  const shouldIgnoreCode = IGNORED_ERROR_CODES.some(ignoredCode =>
    code.toLowerCase().includes(ignoredCode.toLowerCase())
  );

  return shouldIgnoreMessage || shouldIgnoreCode;
}

/**
 * معالج console.error محسن
 */
export function setupErrorSuppression() {
  if (typeof window === 'undefined') return;

  // حفظ console.error الأصلي
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  // استبدال console.error
  console.error = (...args: any[]) => {
    const message = args.join(' ');
    
    // تجاهل الأخطاء الشائعة
    if (shouldIgnoreError({ message })) {
      return;
    }

    // عرض الأخطاء المهمة فقط
    originalConsoleError.apply(console, args);
  };

  // استبدال console.warn
  console.warn = (...args: any[]) => {
    const message = args.join(' ');
    
    // تجاهل التحذيرات الشائعة
    if (shouldIgnoreError({ message })) {
      return;
    }

    // عرض التحذيرات المهمة فقط
    originalConsoleWarn.apply(console, args);
  };

  // معالج الأخطاء العامة
  window.addEventListener('error', (event) => {
    if (shouldIgnoreError(event.error)) {
      event.preventDefault();
      return false;
    }
  });

  // معالج Promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    if (shouldIgnoreError(event.reason)) {
      event.preventDefault();
      return false;
    }
  });
}

/**
 * إعادة تعيين console إلى حالته الأصلية
 */
export function restoreConsole() {
  // يمكن تنفيذ هذا إذا لزم الأمر
}

/**
 * تسجيل خطأ مهم (يتجاوز القمع)
 */
export function logImportantError(message: string, error?: any) {
  if (process.env.NODE_ENV === 'development') {
    console.group('🚨 Important Error');
    console.error(message);
    if (error) {
      console.error(error);
    }
    console.groupEnd();
  }
}

/**
 * تسجيل تحذير مهم (يتجاوز القمع)
 */
export function logImportantWarning(message: string, details?: any) {
  if (process.env.NODE_ENV === 'development') {
    console.group('⚠️ Important Warning');
    console.warn(message);
    if (details) {
      console.warn(details);
    }
    console.groupEnd();
  }
}
