describe('إعدادات النظام - صفحة الإدارة', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.mockLogin('admin')
    cy.visitWithLocale('/admin/settings')
  })

  it('يجب أن تعرض صفحة إعدادات النظام بنجاح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من العنوان الرئيسي
    cy.shouldContainArabicText('إعدادات النظام')
    cy.get('[data-testid="system-settings-page"]').should('be.visible')
    
    // التحقق من وجود التبويبات
    cy.get('[data-testid="settings-tabs"]').should('be.visible')
    cy.get('[data-testid="tab-general"]').should('be.visible')
    cy.get('[data-testid="tab-commission"]').should('be.visible')
    cy.get('[data-testid="tab-notifications"]').should('be.visible')
    cy.get('[data-testid="tab-payment"]').should('be.visible')
    cy.get('[data-testid="tab-security"]').should('be.visible')
    cy.get('[data-testid="tab-backup"]').should('be.visible')
  })

  it('يجب أن تعمل الإعدادات العامة', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من تبويب الإعدادات العامة
    cy.get('[data-testid="tab-general"]').click()
    cy.get('[data-testid="general-settings"]').should('be.visible')
    
    // تحديث اسم المنصة
    cy.get('[data-testid="platform-name"]').clear().type('مخلة - المحدث')
    cy.get('[data-testid="platform-description"]').clear().type('وصف محدث للمنصة')
    
    // تحديث معلومات الاتصال
    cy.get('[data-testid="contact-email"]').clear().type('<EMAIL>')
    cy.get('[data-testid="contact-phone"]').clear().type('+966501234567')
    
    // رفع شعار جديد
    cy.get('[data-testid="logo-upload"]').selectFile('cypress/fixtures/new-logo.png', { force: true })
    
    // حفظ الإعدادات
    cy.get('[data-testid="save-general-settings"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="general-settings-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ الإعدادات العامة بنجاح')
  })

  it('يجب أن تعمل إعدادات العمولات', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب العمولات
    cy.get('[data-testid="tab-commission"]').click()
    cy.get('[data-testid="commission-settings"]').should('be.visible')
    
    // تحديث عمولة التجار
    cy.get('[data-testid="merchant-commission"]').clear().type('5')
    cy.get('[data-testid="merchant-commission-type"]').select('percentage')
    
    // تحديث عمولة المندوبين
    cy.get('[data-testid="representative-commission"]').clear().type('10')
    cy.get('[data-testid="representative-commission-type"]').select('fixed')
    
    // تحديث عمولة المنصة
    cy.get('[data-testid="platform-commission"]').clear().type('2.5')
    
    // إعدادات العمولة المتدرجة
    cy.get('[data-testid="enable-tiered-commission"]').check()
    cy.get('[data-testid="tier-1-threshold"]').type('1000')
    cy.get('[data-testid="tier-1-rate"]').type('3')
    
    // حفظ إعدادات العمولات
    cy.get('[data-testid="save-commission-settings"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="commission-settings-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ إعدادات العمولات بنجاح')
  })

  it('يجب أن تعمل إعدادات الإشعارات', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب الإشعارات
    cy.get('[data-testid="tab-notifications"]').click()
    cy.get('[data-testid="notification-settings"]').should('be.visible')
    
    // إعدادات البريد الإلكتروني
    cy.get('[data-testid="email-notifications-enabled"]').check()
    cy.get('[data-testid="smtp-server"]').clear().type('smtp.gmail.com')
    cy.get('[data-testid="smtp-port"]').clear().type('587')
    cy.get('[data-testid="smtp-username"]').clear().type('<EMAIL>')
    cy.get('[data-testid="smtp-password"]').clear().type('password123')
    
    // إعدادات الإشعارات الفورية
    cy.get('[data-testid="push-notifications-enabled"]').check()
    cy.get('[data-testid="firebase-server-key"]').clear().type('test-firebase-key')
    
    // إعدادات SMS
    cy.get('[data-testid="sms-notifications-enabled"]').check()
    cy.get('[data-testid="sms-provider"]').select('twilio')
    cy.get('[data-testid="sms-api-key"]').clear().type('test-sms-key')
    
    // حفظ إعدادات الإشعارات
    cy.get('[data-testid="save-notification-settings"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="notification-settings-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ إعدادات الإشعارات بنجاح')
  })

  it('يجب أن تعمل إعدادات الدفع', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب الدفع
    cy.get('[data-testid="tab-payment"]').click()
    cy.get('[data-testid="payment-settings"]').should('be.visible')
    
    // تفعيل بوابات الدفع
    cy.get('[data-testid="enable-visa-mastercard"]').check()
    cy.get('[data-testid="enable-mada"]').check()
    cy.get('[data-testid="enable-apple-pay"]').check()
    cy.get('[data-testid="enable-stc-pay"]').check()
    
    // إعدادات بوابة الدفع الرئيسية
    cy.get('[data-testid="primary-payment-gateway"]').select('hyperpay')
    cy.get('[data-testid="payment-gateway-api-key"]').clear().type('test-payment-key')
    cy.get('[data-testid="payment-gateway-secret"]').clear().type('test-payment-secret')
    
    // إعدادات العملة
    cy.get('[data-testid="default-currency"]').select('SAR')
    cy.get('[data-testid="currency-symbol"]').clear().type('ر.س')
    
    // حفظ إعدادات الدفع
    cy.get('[data-testid="save-payment-settings"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="payment-settings-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ إعدادات الدفع بنجاح')
  })

  it('يجب أن تعمل إعدادات الأمان', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب الأمان
    cy.get('[data-testid="tab-security"]').click()
    cy.get('[data-testid="security-settings"]').should('be.visible')
    
    // إعدادات كلمات المرور
    cy.get('[data-testid="min-password-length"]').clear().type('8')
    cy.get('[data-testid="require-uppercase"]').check()
    cy.get('[data-testid="require-lowercase"]').check()
    cy.get('[data-testid="require-numbers"]').check()
    cy.get('[data-testid="require-special-chars"]').check()
    
    // إعدادات المصادقة الثنائية
    cy.get('[data-testid="enable-2fa"]').check()
    cy.get('[data-testid="2fa-method"]').select('sms')
    
    // إعدادات الجلسات
    cy.get('[data-testid="session-timeout"]').clear().type('30')
    cy.get('[data-testid="max-login-attempts"]').clear().type('5')
    cy.get('[data-testid="lockout-duration"]').clear().type('15')
    
    // حفظ إعدادات الأمان
    cy.get('[data-testid="save-security-settings"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="security-settings-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ إعدادات الأمان بنجاح')
  })

  it('يجب أن تعمل إعدادات النسخ الاحتياطية', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب النسخ الاحتياطية
    cy.get('[data-testid="tab-backup"]').click()
    cy.get('[data-testid="backup-settings"]').should('be.visible')
    
    // تفعيل النسخ الاحتياطية التلقائية
    cy.get('[data-testid="enable-auto-backup"]').check()
    cy.get('[data-testid="backup-frequency"]').select('daily')
    cy.get('[data-testid="backup-time"]').clear().type('02:00')
    
    // إعدادات التخزين
    cy.get('[data-testid="backup-storage"]').select('cloud')
    cy.get('[data-testid="backup-retention"]').clear().type('30')
    
    // إنشاء نسخة احتياطية فورية
    cy.get('[data-testid="create-backup-now"]').click()
    cy.get('[data-testid="backup-progress"]').should('be.visible')
    
    // حفظ إعدادات النسخ الاحتياطية
    cy.get('[data-testid="save-backup-settings"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="backup-settings-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ إعدادات النسخ الاحتياطية بنجاح')
  })

  it('يجب أن تعمل وظيفة اختبار الإعدادات', () => {
    cy.waitForLoadingToFinish()
    
    // اختبار إعدادات البريد الإلكتروني
    cy.get('[data-testid="tab-notifications"]').click()
    cy.get('[data-testid="test-email-settings"]').click()
    cy.get('[data-testid="test-email-address"]').type('<EMAIL>')
    cy.get('[data-testid="send-test-email"]').click()
    
    // التحقق من نتيجة الاختبار
    cy.get('[data-testid="email-test-result"]').should('be.visible')
    cy.shouldContainArabicText('تم إرسال البريد التجريبي بنجاح')
  })

  it('يجب أن تعمل وظيفة استيراد/تصدير الإعدادات', () => {
    cy.waitForLoadingToFinish()
    
    // تصدير الإعدادات
    cy.get('[data-testid="export-settings-button"]').click()
    cy.get('[data-testid="export-options"]').should('be.visible')
    cy.get('[data-testid="export-all-settings"]').click()
    
    // التحقق من بدء التحميل
    cy.get('[data-testid="export-progress"]').should('be.visible')
    
    // استيراد الإعدادات
    cy.get('[data-testid="import-settings-button"]').click()
    cy.get('[data-testid="settings-file-upload"]').selectFile('cypress/fixtures/settings-backup.json', { force: true })
    cy.get('[data-testid="import-settings-confirm"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="import-success"]').should('be.visible')
    cy.shouldContainArabicText('تم استيراد الإعدادات بنجاح')
  })

  it('يجب أن تعمل وظيفة إعادة تعيين الإعدادات', () => {
    cy.waitForLoadingToFinish()
    
    // إعادة تعيين إعدادات محددة
    cy.get('[data-testid="tab-general"]').click()
    cy.get('[data-testid="reset-general-settings"]').click()
    cy.get('[data-testid="confirm-reset"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="reset-success"]').should('be.visible')
    cy.shouldContainArabicText('تم إعادة تعيين الإعدادات للقيم الافتراضية')
  })

  it('يجب أن تتعامل مع التحقق من صحة البيانات', () => {
    cy.waitForLoadingToFinish()
    
    // محاولة حفظ إعدادات غير صحيحة
    cy.get('[data-testid="tab-commission"]').click()
    cy.get('[data-testid="merchant-commission"]').clear().type('-5')
    cy.get('[data-testid="save-commission-settings"]').click()
    
    // التحقق من رسالة الخطأ
    cy.get('[data-testid="commission-validation-error"]').should('be.visible')
    cy.shouldContainArabicText('يجب أن تكون العمولة قيمة موجبة')
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في حفظ الإعدادات
    cy.intercept('POST', '**/api/admin/settings**', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('settingsError')
    
    cy.get('[data-testid="tab-general"]').click()
    cy.get('[data-testid="platform-name"]').clear().type('اسم جديد')
    cy.get('[data-testid="save-general-settings"]').click()
    
    cy.wait('@settingsError')
    
    // التحقق من عرض رسالة الخطأ
    cy.get('[data-testid="settings-error"]').should('be.visible')
    cy.shouldContainArabicText('حدث خطأ في حفظ الإعدادات')
  })

  it('يجب أن تعمل على الأجهزة المحمولة', () => {
    cy.viewport('iphone-x')
    cy.waitForLoadingToFinish()
    
    // التحقق من التجاوب
    cy.get('[data-testid="system-settings-page"]').should('be.visible')
    
    // اختبار التبويبات على الهاتف المحمول
    cy.get('[data-testid="mobile-settings-menu"]').should('be.visible')
    cy.get('[data-testid="mobile-settings-menu"]').click()
    cy.get('[data-testid="mobile-settings-dropdown"]').should('be.visible')
  })
})
