#!/usr/bin/env node

/**
 * اختبار مبسط للتطبيق المباشر باستخدام fetch
 */

const https = require('https');
const http = require('http');

const APP_URL = 'http://localhost:9002';

/**
 * طلب HTTP بسيط
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * اختبار الصفحة الرئيسية العربية
 */
async function testArabicHomePage() {
  console.log('🏠 اختبار الصفحة الرئيسية العربية...');
  
  try {
    const response = await makeRequest(`${APP_URL}/ar`);
    
    if (response.statusCode === 200) {
      const hasArabicContent = /[\u0600-\u06FF]/.test(response.body);
      const hasRTL = response.body.includes('dir="rtl"') || response.body.includes("dir='rtl'");
      const hasArabicLang = response.body.includes('lang="ar"') || response.body.includes("lang='ar'");
      
      if (hasArabicContent && (hasRTL || hasArabicLang)) {
        console.log('✅ الصفحة الرئيسية العربية تعمل بشكل صحيح');
        return true;
      } else {
        console.log('❌ الصفحة الرئيسية العربية لا تحتوي على محتوى عربي صحيح');
        return false;
      }
    } else {
      console.log(`❌ خطأ HTTP: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ خطأ في طلب الصفحة الرئيسية العربية: ${error.message}`);
    return false;
  }
}

/**
 * اختبار الصفحة الرئيسية الإنجليزية
 */
async function testEnglishHomePage() {
  console.log('🏠 اختبار الصفحة الرئيسية الإنجليزية...');
  
  try {
    const response = await makeRequest(`${APP_URL}/en`);
    
    if (response.statusCode === 200) {
      const hasEnglishContent = /[a-zA-Z]/.test(response.body);
      const hasLTR = response.body.includes('dir="ltr"') || !response.body.includes('dir="rtl"');
      const hasEnglishLang = response.body.includes('lang="en"') || response.body.includes("lang='en'");
      
      if (hasEnglishContent && (hasLTR || hasEnglishLang)) {
        console.log('✅ الصفحة الرئيسية الإنجليزية تعمل بشكل صحيح');
        return true;
      } else {
        console.log('❌ الصفحة الرئيسية الإنجليزية لا تحتوي على محتوى إنجليزي صحيح');
        return false;
      }
    } else {
      console.log(`❌ خطأ HTTP: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ خطأ في طلب الصفحة الرئيسية الإنجليزية: ${error.message}`);
    return false;
  }
}

/**
 * اختبار صفحة البحث العربية
 */
async function testArabicSearchPage() {
  console.log('🔍 اختبار صفحة البحث العربية...');
  
  try {
    const response = await makeRequest(`${APP_URL}/ar/search`);
    
    if (response.statusCode === 200) {
      const hasSearchTerms = response.body.includes('البحث') || 
                           response.body.includes('ابحث') || 
                           response.body.includes('بحث');
      
      if (hasSearchTerms) {
        console.log('✅ صفحة البحث العربية تحتوي على المصطلحات المناسبة');
        return true;
      } else {
        console.log('❌ صفحة البحث العربية لا تحتوي على مصطلحات البحث');
        return false;
      }
    } else {
      console.log(`❌ خطأ HTTP: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ خطأ في طلب صفحة البحث العربية: ${error.message}`);
    return false;
  }
}

/**
 * اختبار صفحة البحث الإنجليزية
 */
async function testEnglishSearchPage() {
  console.log('🔍 اختبار صفحة البحث الإنجليزية...');
  
  try {
    const response = await makeRequest(`${APP_URL}/en/search`);
    
    if (response.statusCode === 200) {
      const hasAdvancedSearch = response.body.includes('Advanced Search');
      const hasSearchProducts = response.body.includes('Search products');
      const hasSearchTerms = response.body.includes('Search') || 
                           response.body.includes('search');
      
      if (hasAdvancedSearch || hasSearchProducts || hasSearchTerms) {
        console.log('✅ صفحة البحث الإنجليزية تحتوي على الترجمات الجديدة');
        return true;
      } else {
        console.log('❌ صفحة البحث الإنجليزية لا تحتوي على الترجمات المطلوبة');
        return false;
      }
    } else {
      console.log(`❌ خطأ HTTP: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ خطأ في طلب صفحة البحث الإنجليزية: ${error.message}`);
    return false;
  }
}

/**
 * اختبار صفحة الفئات الإنجليزية
 */
async function testEnglishCategoriesPage() {
  console.log('🏷️ اختبار صفحة الفئات الإنجليزية...');
  
  try {
    const response = await makeRequest(`${APP_URL}/en/categories`);
    
    if (response.statusCode === 200) {
      const categoryTranslations = [
        'Food & Beverages',
        'Fashion & Clothing',
        'Electronics',
        'Home & Garden',
        'Beauty & Health'
      ];
      
      const foundCategories = categoryTranslations.filter(category => 
        response.body.includes(category)
      );
      
      if (foundCategories.length >= 2) {
        console.log(`✅ صفحة الفئات تحتوي على ${foundCategories.length} ترجمة صحيحة`);
        console.log(`   الفئات الموجودة: ${foundCategories.join(', ')}`);
        return true;
      } else {
        console.log('❌ صفحة الفئات لا تحتوي على ترجمات الفئات الجديدة');
        return false;
      }
    } else {
      console.log(`❌ خطأ HTTP: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ خطأ في طلب صفحة الفئات الإنجليزية: ${error.message}`);
    return false;
  }
}

/**
 * اختبار توفر التطبيق
 */
async function testAppAvailability() {
  console.log('🌐 اختبار توفر التطبيق...');
  
  try {
    const response = await makeRequest(`${APP_URL}`);
    
    if (response.statusCode === 200 || response.statusCode === 302 || response.statusCode === 307) {
      console.log('✅ التطبيق متوفر ويستجيب للطلبات');
      return true;
    } else {
      console.log(`❌ التطبيق يرجع رمز خطأ: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ التطبيق غير متوفر: ${error.message}`);
    console.log('💡 تأكد من أن التطبيق يعمل على http://localhost:9002');
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🚀 بدء اختبار التطبيق المباشر المبسط...\n');
  console.log('=' .repeat(60));
  
  const tests = [
    { name: 'توفر التطبيق', fn: testAppAvailability },
    { name: 'الصفحة الرئيسية العربية', fn: testArabicHomePage },
    { name: 'الصفحة الرئيسية الإنجليزية', fn: testEnglishHomePage },
    { name: 'صفحة البحث العربية', fn: testArabicSearchPage },
    { name: 'صفحة البحث الإنجليزية', fn: testEnglishSearchPage },
    { name: 'صفحة الفئات الإنجليزية', fn: testEnglishCategoriesPage }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      if (await test.fn()) {
        passedTests++;
      }
    } catch (error) {
      console.log(`❌ خطأ في اختبار ${test.name}: ${error.message}`);
    }
    console.log(''); // سطر فارغ
  }
  
  // النتائج النهائية
  console.log('=' .repeat(60));
  console.log('📋 ملخص نتائج اختبار التطبيق المباشر:');
  console.log(`   🧪 الاختبارات المكتملة: ${passedTests}/${totalTests}`);
  console.log(`   📊 معدل نجاح الاختبارات: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 جميع اختبارات التطبيق المباشر نجحت!');
    console.log('✨ الترجمات تعمل بشكل مثالي في التطبيق الفعلي.');
    console.log('🚀 التطبيق جاهز للاستخدام مع الترجمات المحدثة.');
  } else if (passedTests > 0) {
    console.log(`\n⚠️  ${totalTests - passedTests} اختبار فشل من أصل ${totalTests}.`);
    console.log('🔧 بعض الميزات تعمل، لكن هناك مشاكل تحتاج إصلاح.');
  } else {
    console.log('\n❌ جميع الاختبارات فشلت.');
    console.log('🔧 يرجى التحقق من أن التطبيق يعمل بشكل صحيح.');
  }
  
  console.log('\n📝 ملاحظات:');
  console.log('   • تأكد من أن التطبيق يعمل على http://localhost:9002');
  console.log('   • هذا اختبار أساسي للتحقق من وجود الترجمات في HTML');
  console.log('   • للاختبار الشامل، استخدم Cypress عند توفره');
}

// تشغيل الاختبار
if (require.main === module) {
  main();
}

module.exports = {
  testArabicHomePage,
  testEnglishHomePage,
  testArabicSearchPage,
  testEnglishSearchPage,
  testEnglishCategoriesPage,
  testAppAvailability
};
