'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useLocale } from '@/hooks/use-locale';
import { 
  Save, 
  X,
  FolderTree,
  Upload,
  Image as ImageIcon
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Category {
  id?: string;
  name?: string;
  nameAr?: string;
  nameEn?: string;
  description?: string;
  icon?: string;
  imageUrl?: string;
  parentId?: string;
  order?: number;
  isActive?: boolean;
}

interface CategoryEditorProps {
  category?: Category | null;
  onClose: () => void;
  onSave: (category: Category) => void;
}

export function CategoryEditor({ category, onClose, onSave }: CategoryEditorProps) {
  const { t } = useLocale();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    nameAr: category?.nameAr || '',
    nameEn: category?.nameEn || '',
    description: category?.description || '',
    parentId: category?.parentId || '',
    icon: category?.icon || '',
    imageUrl: category?.imageUrl || '',
    isActive: category?.isActive ?? true,
    order: category?.order || 1
  });

  const isEditing = !!category?.id;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // محاكاة حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const savedCategory: Category = {
        ...category,
        ...formData,
        name: formData.nameAr, // استخدام الاسم العربي كاسم افتراضي
        id: category?.id || `cat_${Date.now()}`
      };

      onSave(savedCategory);
    } catch (error) {
      console.error('Error saving category:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // محاكاة رفع الصورة
      const reader = new FileReader();
      reader.onload = (e) => {
        handleInputChange('imageUrl', e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // فئات أب تجريبية
  const parentCategories = [
    { id: '', name: 'فئة رئيسية (بدون أب)' },
    { id: '1', name: 'الإلكترونيات' },
    { id: '2', name: 'الأزياء' },
    { id: '3', name: 'المنزل والحديقة' }
  ];

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderTree className="h-5 w-5" />
            {isEditing ? t('editCategory') : t('addCategory')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* الأسماء */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="nameAr">الاسم بالعربية *</Label>
              <Input
                id="nameAr"
                value={formData.nameAr}
                onChange={(e) => handleInputChange('nameAr', e.target.value)}
                placeholder="أدخل اسم الفئة بالعربية"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="nameEn">الاسم بالإنجليزية *</Label>
              <Input
                id="nameEn"
                value={formData.nameEn}
                onChange={(e) => handleInputChange('nameEn', e.target.value)}
                placeholder="Enter category name in English"
                required
              />
            </div>
          </div>

          {/* الوصف */}
          <div className="space-y-2">
            <Label htmlFor="description">الوصف</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="أدخل وصف الفئة (اختياري)"
              rows={3}
            />
          </div>

          {/* الفئة الأب والترتيب */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الفئة الأب</Label>
              <Select
                value={formData.parentId}
                onValueChange={(value) => handleInputChange('parentId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الفئة الأب" />
                </SelectTrigger>
                <SelectContent>
                  {parentCategories.map((parent) => (
                    <SelectItem key={parent.id} value={parent.id}>
                      {parent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="order">ترتيب العرض</Label>
              <Input
                id="order"
                type="number"
                value={formData.order}
                onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 1)}
                min="1"
                placeholder="1"
              />
            </div>
          </div>

          {/* الأيقونة والصورة */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="icon">أيقونة الفئة</Label>
              <Input
                id="icon"
                value={formData.icon}
                onChange={(e) => handleInputChange('icon', e.target.value)}
                placeholder="مثل: shopping-bag, laptop, shirt"
              />
              <p className="text-xs text-gray-500">
                يمكنك استخدام أسماء أيقونات Lucide React
              </p>
            </div>

            <div className="space-y-2">
              <Label>صورة الفئة</Label>
              <div className="flex items-center gap-4">
                {formData.imageUrl && (
                  <img
                    src={formData.imageUrl}
                    alt="معاينة"
                    className="w-20 h-20 object-cover rounded-lg border"
                  />
                )}
                
                <div className="flex-1">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById('image-upload')?.click()}
                    className="w-full flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    {formData.imageUrl ? 'تغيير الصورة' : 'رفع صورة'}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* حالة الفئة */}
          <div className="space-y-2">
            <Label>حالة الفئة</Label>
            <Select
              value={formData.isActive ? 'active' : 'inactive'}
              onValueChange={(value) => handleInputChange('isActive', value === 'active')}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">نشطة</SelectItem>
                <SelectItem value="inactive">غير نشطة</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* معاينة */}
          {(formData.nameAr || formData.nameEn) && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">معاينة الفئة</h4>
              <div className="flex items-center gap-3">
                {formData.imageUrl ? (
                  <img
                    src={formData.imageUrl}
                    alt="معاينة"
                    className="w-12 h-12 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    <ImageIcon className="h-6 w-6 text-gray-400" />
                  </div>
                )}
                
                <div>
                  <h5 className="font-medium text-gray-900">
                    {formData.nameAr || formData.nameEn}
                  </h5>
                  {formData.description && (
                    <p className="text-sm text-gray-600 truncate">
                      {formData.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              إلغاء
            </Button>
            
            <Button
              type="submit"
              disabled={loading || !formData.nameAr.trim() || !formData.nameEn.trim()}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  {isEditing ? 'حفظ التغييرات' : 'إضافة الفئة'}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
