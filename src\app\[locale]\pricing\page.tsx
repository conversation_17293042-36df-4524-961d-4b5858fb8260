import type { Locale } from '@/lib/i18n';
import { getTranslations } from '@/context/locale-context';
import PricingTabs from '@/components/pricing/PricingTabs';
import { merchantPlans, customerPlans, representativePlans } from '@/constants/plans';

export default async function PricingPage({ params }: { params: { locale: Locale } }) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale);

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-bold text-center mb-12 text-primary">{t('pricing')}</h1>
      <PricingTabs
        merchantPlans={merchantPlans}
        customerPlans={customerPlans}
        representativePlans={representativePlans}
        locale={locale}
      />
    </div>
  );
}
