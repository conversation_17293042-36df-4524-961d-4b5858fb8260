"use client";

import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useOrder } from '@/hooks/useOrders';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  User, 
  MapPin, 
  Phone, 
  Mail, 
  Package, 
  DollarSign,
  Calendar,
  CreditCard,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Store
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import OrderTracker, { DeliveryEstimate } from '@/components/customer/OrderTracker';
import type { OrderDocument, OrderStatus } from '@/types';

export default function CustomerOrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { t, locale } = useLocale();

  const orderId = params.orderId as string;
  const { order, loading, error } = useOrder(orderId);

  // Check if this order belongs to the current customer
  const isAuthorized = order && order.customerId === user?.uid;

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4" />;
      case 'preparing':
        return <Package className="h-4 w-4" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4" />;
      case 'shipped':
        return <Truck className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadgeVariant = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'confirmed':
        return 'default';
      case 'preparing':
        return 'outline';
      case 'ready':
        return 'default';
      case 'shipped':
        return 'default';
      case 'delivered':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return t('orderStatusPending');
      case 'confirmed':
        return t('orderStatusConfirmed');
      case 'preparing':
        return t('orderStatusPreparing');
      case 'ready':
        return t('orderStatusReady');
      case 'shipped':
        return t('orderStatusShipped');
      case 'delivered':
        return t('orderStatusDelivered');
      case 'cancelled':
        return t('orderStatusCancelled');
      default:
        return status;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp || !timestamp.toDate) return '';
    const date = timestamp.toDate();
    return format(date, 'PPP p', { 
      locale: locale === 'ar' ? ar : enUS 
    });
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ${t('SAR')}`;
  };

  const getOrderProgress = (status: OrderStatus) => {
    const steps = ['pending', 'confirmed', 'preparing', 'ready', 'shipped', 'delivered'];
    const currentIndex = steps.indexOf(status);
    return currentIndex >= 0 ? ((currentIndex + 1) / steps.length) * 100 : 0;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center gap-4">
                      <Skeleton className="h-16 w-16 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-48" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                      <Skeleton className="h-4 w-20" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order || !isAuthorized) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || (!isAuthorized ? t('accessDenied') : t('orderNotFound'))}
            </AlertDescription>
          </Alert>
          
          <div className="mt-6 text-center">
            <Button asChild>
              <Link href={`/${locale}/dashboard/orders`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('backToOrders')}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Button asChild variant="outline" size="sm">
            <Link href={`/${locale}/dashboard/orders`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('backToOrders')}
            </Link>
          </Button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {t('orderDetails')} #{order.orderNumber}
            </h1>
            <p className="text-muted-foreground">
              {t('orderPlacedOn')} {formatDate(order.createdAt)}
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            {getStatusIcon(order.status)}
            <Badge variant={getStatusBadgeVariant(order.status)} className="text-sm">
              {getStatusText(order.status)}
            </Badge>
          </div>
        </div>

        {/* Order Progress */}
        {order.status !== 'cancelled' && (
          <div className="mt-6">
            <div className="flex justify-between text-sm text-muted-foreground mb-2">
              <span>{t('orderProgress')}</span>
              <span>{Math.round(getOrderProgress(order.status))}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${getOrderProgress(order.status)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Items */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t('orderItems')} ({order.items.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                      {item.imageUrl ? (
                        <img
                          src={item.imageUrl}
                          alt={item.productName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Package className="w-8 h-8 text-muted-foreground" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-medium">{item.productName}</h4>
                      <p className="text-sm text-muted-foreground">
                        {t('quantity')}: {item.quantity}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {t('unitPrice')}: {formatCurrency(item.price)}
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <p className="font-semibold">
                        {formatCurrency(item.price * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary & Info */}
        <div className="space-y-6">
          {/* Order Tracking */}
          <OrderTracker
            currentStatus={order.status}
            orderDate={order.createdAt}
          />

          {/* Delivery Estimate */}
          <DeliveryEstimate status={order.status} />

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                {t('orderSummary')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>{t('subtotal')}:</span>
                <span>{formatCurrency(order.totalAmount)}</span>
              </div>
              {order.deliveryFee && (
                <div className="flex justify-between">
                  <span>{t('deliveryFee')}:</span>
                  <span>{formatCurrency(order.deliveryFee)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>{t('total')}:</span>
                <span>{formatCurrency(order.finalTotal || order.totalAmount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('shippingAddress')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p>{order.shippingInfo.address}</p>
              <p>{order.shippingInfo.city}</p>
              {order.shippingInfo.postalCode && (
                <p>{order.shippingInfo.postalCode}</p>
              )}
              {order.shippingInfo.phone && (
                <div className="flex items-center gap-2 mt-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{order.shippingInfo.phone}</span>
                </div>
              )}
              {order.shippingInfo.notes && (
                <div className="mt-3 p-3 bg-muted rounded-lg">
                  <p className="text-sm font-medium mb-1">{t('deliveryNotes')}:</p>
                  <p className="text-sm">{order.shippingInfo.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                {t('paymentInformation')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>{t('paymentMethod')}:</span>
                <span className="font-medium">
                  {order.paymentInfo.method === 'cash' && t('cashOnDelivery')}
                  {order.paymentInfo.method === 'card' && t('creditCard')}
                  {order.paymentInfo.method === 'online' && t('onlinePayment')}
                </span>
              </div>
              <div className="flex justify-between">
                <span>{t('paymentStatus')}:</span>
                <Badge variant={order.paymentInfo.status === 'paid' ? 'default' : 'secondary'}>
                  {order.paymentInfo.status === 'paid' && t('paid')}
                  {order.paymentInfo.status === 'pending' && t('pending')}
                  {order.paymentInfo.status === 'failed' && t('failed')}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
