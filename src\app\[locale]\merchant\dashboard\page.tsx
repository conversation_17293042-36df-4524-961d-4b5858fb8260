// src/app/[locale]/merchant/dashboard/page.tsx
"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import DashboardStats from '@/components/merchant/DashboardStats';
import QuickActions from '@/components/merchant/QuickActions';
import RecentOrders from '@/components/merchant/RecentOrders';
import { AlertCircle, Store } from 'lucide-react';
import type { StoreDocument } from '@/types';

export default function MerchantDashboard() {
  const { user } = useAuth();
  const { t } = useLocale();
  const [storeData, setStoreData] = useState<StoreDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoreData = async () => {
      if (!user?.uid) {
        setLoading(false);
        return;
      }

      try {
        // First verify user is a merchant
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.userType !== 'merchant') {
            // Redirect non-merchants
            window.location.href = `/${window.location.pathname.split('/')[1]}/dashboard`;
            return;
          }
        }

        // Fetch store data
        const storeDoc = await getDoc(doc(db, 'stores', user.uid));
        if (storeDoc.exists()) {
          setStoreData({ id: storeDoc.id, ...storeDoc.data() } as StoreDocument);
        } else {
          // Store doesn't exist yet - this is normal for new merchants
          console.log('Store document not found - new merchant');
          setStoreData(null);
        }
      } catch (err) {
        console.error('Error fetching store data:', err);
        setError(t('errorFetchingStoreData'));
      } finally {
        setLoading(false);
      }
    };

    fetchStoreData();
  }, [user?.uid, t]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Skeleton className="h-8 w-64 mx-auto mb-2" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {[...Array(4)].map((_, i) => (
                  <Skeleton key={i} className="h-24" />
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[...Array(6)].map((_, i) => (
                  <Skeleton key={i} className="h-32" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  const storeName = storeData?.storeName || 'متجر جديد';
  const merchantName = user?.displayName || user?.email?.split('@')[0] || t('merchant');

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Welcome Header */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">
          {t('welcomeMerchant', { name: merchantName })}
        </h1>
        <p className="text-muted-foreground mb-4">
          {t('merchantDashboardSubtitle')}
        </p>
        
        {/* Store Info Card */}
        <Card className="max-w-md mx-auto">
          <CardContent className="flex items-center justify-center p-4">
            <Store className="h-5 w-5 text-primary mr-2" />
            <span className="font-medium">{t('storeNameLabel')} {storeName}</span>
          </CardContent>
        </Card>
      </div>

      {/* Store Status Alert */}
      {!storeData && (
        <Alert className="mb-6">
          <Store className="h-4 w-4" />
          <AlertDescription>
            {t('newMerchantWelcome')}
          </AlertDescription>
        </Alert>
      )}

      {storeData && !storeData.isActive && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t('storeNotActive')}
          </AlertDescription>
        </Alert>
      )}

      {/* Dashboard Stats */}
      <div className="mb-8">
        <DashboardStats />
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <QuickActions />
      </div>

      {/* Recent Orders Section */}
      <RecentOrders />
    </div>
  );
}
