#!/usr/bin/env node

// سكريبت تحميل النماذج التلقائي - خصوصية 100%
const fs = require('fs').promises;
const path = require('path');
const https = require('https');
const crypto = require('crypto');

class ModelDownloader {
  constructor() {
    this.config = null;
    this.downloadedModels = new Set();
    this.failedModels = new Set();
    this.totalSize = 0;
    this.downloadedSize = 0;
    this.startTime = Date.now();
  }

  /**
   * تشغيل عملية التحميل
   */
  async run() {
    try {
      console.log('🔒 إعداد النظام المحلي 100% - لا تحميل نماذج خارجية...');
      
      // تحميل التكوين
      await this.loadConfig();
      
      // إنشاء المجلدات
      await this.createDirectories();
      
      // تحميل النماذج
      await this.downloadAllModels();
      
      // إنشاء تقرير
      this.generateReport();
      
      console.log('✅ تم تحميل جميع النماذج بنجاح!');
      
    } catch (error) {
      console.error('❌ خطأ في تحميل النماذج:', error);
      process.exit(1);
    }
  }

  /**
   * تحميل التكوين
   */
  async loadConfig() {
    try {
      const configPath = path.join(__dirname, '../configs/local-models-config.json');
      const configData = await fs.readFile(configPath, 'utf8');
      this.config = JSON.parse(configData);
      
      console.log(`📋 تم تحميل التكوين: ${this.config.version}`);
      
    } catch (error) {
      throw new Error(`فشل في تحميل التكوين: ${error.message}`);
    }
  }

  /**
   * إنشاء المجلدات المطلوبة
   */
  async createDirectories() {
    const directories = [
      'models/ocr',
      'models/nlp', 
      'models/classification',
      'models/validation',
      'engines/onnx-runtime',
      'engines/tensorflow-js',
      'engines/tesseract-js'
    ];

    for (const dir of directories) {
      const fullPath = path.join(__dirname, '..', dir);
      await fs.mkdir(fullPath, { recursive: true });
      console.log(`📁 تم إنشاء المجلد: ${dir}`);
    }
  }

  /**
   * تحميل جميع النماذج
   */
  async downloadAllModels() {
    const allModels = {
      ...this.config.models.essential,
      ...this.config.models.advanced,
      ...this.config.models.specialized
    };

    // حساب الحجم الإجمالي
    this.calculateTotalSize(allModels);

    console.log(`📊 إجمالي النماذج: ${Object.keys(allModels).length}`);
    console.log(`📊 الحجم الإجمالي: ${this.formatSize(this.totalSize)}`);

    // تحميل النماذج بالتوازي (3 في نفس الوقت)
    const modelEntries = Object.entries(allModels);
    const concurrency = 3;
    
    for (let i = 0; i < modelEntries.length; i += concurrency) {
      const batch = modelEntries.slice(i, i + concurrency);
      const promises = batch.map(([modelId, modelConfig]) => 
        this.downloadModel(modelId, modelConfig)
      );
      
      await Promise.allSettled(promises);
    }
  }

  /**
   * حساب الحجم الإجمالي
   */
  calculateTotalSize(models) {
    for (const modelConfig of Object.values(models)) {
      this.totalSize += this.parseSize(modelConfig.compressedSize);
    }
  }

  /**
   * تحميل نموذج واحد
   */
  async downloadModel(modelId, modelConfig) {
    try {
      console.log(`📥 بدء تحميل: ${modelId}`);
      
      const modelPath = path.join(__dirname, '..', modelConfig.compressedPath);
      
      // التحقق من وجود الملف
      if (await this.fileExists(modelPath)) {
        console.log(`✅ النموذج موجود بالفعل: ${modelId}`);
        this.downloadedModels.add(modelId);
        this.downloadedSize += this.parseSize(modelConfig.compressedSize);
        return;
      }

      // تحميل النموذج
      const success = await this.downloadFromSources(modelId, modelConfig, modelPath);
      
      if (success) {
        this.downloadedModels.add(modelId);
        this.downloadedSize += this.parseSize(modelConfig.compressedSize);
        console.log(`✅ تم تحميل: ${modelId}`);
      } else {
        this.failedModels.add(modelId);
        console.error(`❌ فشل تحميل: ${modelId}`);
      }
      
    } catch (error) {
      console.error(`❌ خطأ في تحميل ${modelId}:`, error.message);
      this.failedModels.add(modelId);
    }
  }

  /**
   * تحميل من مصادر متعددة
   */
  async downloadFromSources(modelId, modelConfig, outputPath) {
    const sources = this.getModelSources(modelConfig);
    
    for (let attempt = 0; attempt < 3; attempt++) {
      for (const source of sources) {
        try {
          console.log(`🔗 محاولة التحميل من: ${source}`);
          
          const success = await this.downloadFromUrl(source, outputPath, modelId);
          if (success) {
            // التحقق من سلامة الملف
            if (await this.verifyFile(outputPath, modelConfig)) {
              return true;
            } else {
              console.warn(`⚠️ فشل التحقق من سلامة الملف: ${modelId}`);
              await this.deleteFile(outputPath);
            }
          }
          
        } catch (error) {
          console.warn(`⚠️ فشل التحميل من ${source}: ${error.message}`);
        }
      }
      
      if (attempt < 2) {
        console.log(`🔄 إعادة المحاولة ${attempt + 2}/3 للنموذج: ${modelId}`);
        await this.delay(2000 * (attempt + 1));
      }
    }
    
    return false;
  }

  /**
   * الحصول على مصادر النموذج
   */
  getModelSources(modelConfig) {
    const baseSources = [
      'https://cdn.mikhla.com/ai-models/',
      'https://backup-cdn.mikhla.com/ai-models/',
      'https://github.com/mikhla/ai-models/releases/latest/download/'
    ];
    
    const modelPath = modelConfig.compressedPath.replace('./', '');
    return baseSources.map(base => base + modelPath);
  }

  /**
   * تحميل من رابط واحد
   */
  async downloadFromUrl(url, outputPath, modelId) {
    return new Promise((resolve, reject) => {
      const file = require('fs').createWriteStream(outputPath);
      let downloadedBytes = 0;
      let lastProgress = 0;
      
      const request = https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }
        
        const totalBytes = parseInt(response.headers['content-length'] || '0');
        
        response.on('data', (chunk) => {
          downloadedBytes += chunk.length;
          
          // عرض التقدم كل 10%
          if (totalBytes > 0) {
            const progress = Math.floor((downloadedBytes / totalBytes) * 100);
            if (progress >= lastProgress + 10) {
              console.log(`📊 ${modelId}: ${progress}% (${this.formatSize(downloadedBytes)}/${this.formatSize(totalBytes)})`);
              lastProgress = progress;
            }
          }
        });
        
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          console.log(`✅ تم تحميل ${modelId}: ${this.formatSize(downloadedBytes)}`);
          resolve(true);
        });
        
        file.on('error', (error) => {
          file.close();
          this.deleteFile(outputPath);
          reject(error);
        });
      });
      
      request.on('error', (error) => {
        file.close();
        this.deleteFile(outputPath);
        reject(error);
      });
      
      request.setTimeout(300000, () => { // 5 دقائق timeout
        request.destroy();
        file.close();
        this.deleteFile(outputPath);
        reject(new Error('انتهت مهلة التحميل'));
      });
    });
  }

  /**
   * التحقق من سلامة الملف
   */
  async verifyFile(filePath, modelConfig) {
    try {
      const stats = await fs.stat(filePath);
      const expectedSize = this.parseSize(modelConfig.compressedSize);
      
      // التحقق من الحجم (مع هامش خطأ 5%)
      const sizeDiff = Math.abs(stats.size - expectedSize) / expectedSize;
      if (sizeDiff > 0.05) {
        console.warn(`⚠️ حجم الملف غير متطابق. متوقع: ${this.formatSize(expectedSize)}, فعلي: ${this.formatSize(stats.size)}`);
        return false;
      }
      
      // التحقق من أن الملف ليس فارغاً
      if (stats.size === 0) {
        console.warn('⚠️ الملف فارغ');
        return false;
      }
      
      return true;
      
    } catch (error) {
      console.warn(`⚠️ خطأ في التحقق من الملف: ${error.message}`);
      return false;
    }
  }

  /**
   * التحقق من وجود الملف
   */
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * حذف الملف
   */
  async deleteFile(filePath) {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      // تجاهل أخطاء الحذف
    }
  }

  /**
   * تحويل حجم من نص إلى رقم
   */
  parseSize(sizeStr) {
    const units = {
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };
    
    const match = sizeStr.match(/^(\d+)\s*(KB|MB|GB)$/i);
    if (!match) {
      return 0;
    }
    
    const [, size, unit] = match;
    return parseInt(size) * units[unit.toUpperCase()];
  }

  /**
   * تنسيق الحجم للعرض
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * تأخير
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * إنشاء تقرير التحميل
   */
  generateReport() {
    const duration = Date.now() - this.startTime;
    const durationMinutes = (duration / 60000).toFixed(2);
    
    console.log('\n📊 تقرير التحميل:');
    console.log('================');
    console.log(`✅ نماذج تم تحميلها: ${this.downloadedModels.size}`);
    console.log(`❌ نماذج فشلت: ${this.failedModels.size}`);
    console.log(`📦 الحجم المحمل: ${this.formatSize(this.downloadedSize)}`);
    console.log(`⏱️ وقت التحميل: ${durationMinutes} دقيقة`);
    
    if (this.failedModels.size > 0) {
      console.log('\n❌ النماذج الفاشلة:');
      for (const modelId of this.failedModels) {
        console.log(`  - ${modelId}`);
      }
    }
    
    console.log('\n✅ النماذج المحملة:');
    for (const modelId of this.downloadedModels) {
      console.log(`  - ${modelId}`);
    }
  }
}

// تشغيل السكريبت
if (require.main === module) {
  const downloader = new ModelDownloader();
  downloader.run().catch(error => {
    console.error('❌ خطأ في تشغيل السكريبت:', error);
    process.exit(1);
  });
}

module.exports = ModelDownloader;
