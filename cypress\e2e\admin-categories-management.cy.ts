describe('إدارة الفئات والمنتجات - صفحة الإدارة', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.mockLogin('admin')
    cy.visitWithLocale('/admin/categories')
  })

  it('يجب أن تعرض صفحة إدارة الفئات بنجاح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من العنوان الرئيسي
    cy.shouldContainArabicText('إدارة الفئات والمنتجات')
    cy.get('[data-testid="categories-management-page"]').should('be.visible')
    
    // التحقق من وجود التبويبات
    cy.get('[data-testid="categories-tabs"]').should('be.visible')
    cy.get('[data-testid="tab-categories"]').should('be.visible')
    cy.get('[data-testid="tab-products"]').should('be.visible')
    cy.get('[data-testid="tab-brands"]').should('be.visible')
  })

  it('يجب أن تعرض شجرة الفئات', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من تبويب الفئات
    cy.get('[data-testid="tab-categories"]').click()
    cy.get('[data-testid="category-tree"]').should('be.visible')
    
    // التحقق من وجود الفئات الرئيسية
    cy.get('[data-testid="main-categories"]').should('be.visible')
    cy.get('[data-testid="category-node"]').should('have.length.greaterThan', 0)
    
    // التحقق من إمكانية توسيع الفئات
    cy.get('[data-testid="expand-category-1"]').click()
    cy.get('[data-testid="subcategories-1"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة إضافة فئة جديدة', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على زر إضافة فئة
    cy.get('[data-testid="add-category-button"]').click()
    cy.get('[data-testid="category-editor-dialog"]').should('be.visible')
    
    // ملء بيانات الفئة الجديدة
    cy.get('[data-testid="category-name-ar"]').type('فئة تجريبية')
    cy.get('[data-testid="category-name-en"]').type('Test Category')
    cy.get('[data-testid="category-description"]').type('وصف الفئة التجريبية')
    
    // اختيار الفئة الأب
    cy.get('[data-testid="parent-category-select"]').click()
    cy.get('[data-testid="parent-option-1"]').click()
    
    // رفع صورة الفئة
    cy.get('[data-testid="category-image-upload"]').selectFile('cypress/fixtures/test-image.jpg', { force: true })
    
    // حفظ الفئة
    cy.get('[data-testid="save-category-button"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="category-success-message"]').should('be.visible')
    cy.shouldContainArabicText('تم إضافة الفئة بنجاح')
  })

  it('يجب أن تعمل وظيفة تعديل الفئة', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على زر تعديل فئة موجودة
    cy.get('[data-testid="edit-category-1"]').click()
    cy.get('[data-testid="category-editor-dialog"]').should('be.visible')
    
    // تعديل اسم الفئة
    cy.get('[data-testid="category-name-ar"]').clear().type('فئة محدثة')
    cy.get('[data-testid="category-description"]').clear().type('وصف محدث للفئة')
    
    // تحديث الحالة
    cy.get('[data-testid="category-status"]').select('active')
    
    // حفظ التغييرات
    cy.get('[data-testid="save-category-button"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="category-update-success"]').should('be.visible')
    cy.shouldContainArabicText('تم تحديث الفئة بنجاح')
  })

  it('يجب أن تعمل وظيفة ترتيب الفئات بالسحب والإفلات', () => {
    cy.waitForLoadingToFinish()
    
    // اختبار السحب والإفلات
    cy.get('[data-testid="category-node-1"]')
      .trigger('dragstart')
    
    cy.get('[data-testid="category-node-2"]')
      .trigger('dragover')
      .trigger('drop')
    
    // التحقق من تحديث الترتيب
    cy.get('[data-testid="reorder-success"]').should('be.visible')
    cy.shouldContainArabicText('تم تحديث ترتيب الفئات')
  })

  it('يجب أن تعرض قسم مراجعة المنتجات', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب المنتجات
    cy.get('[data-testid="tab-products"]').click()
    cy.get('[data-testid="products-moderation"]').should('be.visible')
    
    // التحقق من وجود المنتجات المعلقة
    cy.get('[data-testid="pending-products"]').should('be.visible')
    cy.shouldContainArabicText('المنتجات المعلقة')
    
    // التحقق من إحصائيات المراجعة
    cy.get('[data-testid="moderation-stats"]').should('be.visible')
    cy.get('[data-testid="pending-count"]').should('contain.text', /\d+/)
  })

  it('يجب أن تعمل وظيفة مراجعة المنتجات', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب المنتجات
    cy.get('[data-testid="tab-products"]').click()
    
    // مراجعة منتج معلق
    cy.get('[data-testid="review-product-1"]').click()
    cy.get('[data-testid="product-review-dialog"]').should('be.visible')
    
    // التحقق من تفاصيل المنتج
    cy.get('[data-testid="product-details"]').should('be.visible')
    cy.get('[data-testid="product-images"]').should('be.visible')
    cy.get('[data-testid="product-description"]').should('be.visible')
    
    // اعتماد المنتج
    cy.get('[data-testid="approve-product-button"]').click()
    cy.get('[data-testid="approval-reason"]').type('المنتج يلبي جميع المعايير')
    cy.get('[data-testid="confirm-approval"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="approval-success"]').should('be.visible')
    cy.shouldContainArabicText('تم اعتماد المنتج بنجاح')
  })

  it('يجب أن تعمل وظيفة رفض المنتجات', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب المنتجات
    cy.get('[data-testid="tab-products"]').click()
    
    // رفض منتج معلق
    cy.get('[data-testid="review-product-2"]').click()
    cy.get('[data-testid="product-review-dialog"]').should('be.visible')
    
    // رفض المنتج
    cy.get('[data-testid="reject-product-button"]').click()
    cy.get('[data-testid="rejection-reason"]').type('المنتج لا يلبي معايير الجودة')
    cy.get('[data-testid="confirm-rejection"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="rejection-success"]').should('be.visible')
    cy.shouldContainArabicText('تم رفض المنتج')
  })

  it('يجب أن تعرض قسم إدارة العلامات التجارية', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب العلامات التجارية
    cy.get('[data-testid="tab-brands"]').click()
    cy.get('[data-testid="brand-management"]').should('be.visible')
    
    // التحقق من قائمة العلامات التجارية
    cy.get('[data-testid="brands-list"]').should('be.visible')
    cy.get('[data-testid="brand-item"]').should('have.length.greaterThan', 0)
    
    // التحقق من إحصائيات العلامات التجارية
    cy.get('[data-testid="brands-stats"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة إضافة علامة تجارية', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب العلامات التجارية
    cy.get('[data-testid="tab-brands"]').click()
    
    // النقر على زر إضافة علامة تجارية
    cy.get('[data-testid="add-brand-button"]').click()
    cy.get('[data-testid="brand-editor-dialog"]').should('be.visible')
    
    // ملء بيانات العلامة التجارية
    cy.get('[data-testid="brand-name"]').type('علامة تجارية تجريبية')
    cy.get('[data-testid="brand-description"]').type('وصف العلامة التجارية')
    cy.get('[data-testid="brand-website"]').type('https://example.com')
    
    // رفع شعار العلامة التجارية
    cy.get('[data-testid="brand-logo-upload"]').selectFile('cypress/fixtures/test-logo.png', { force: true })
    
    // حفظ العلامة التجارية
    cy.get('[data-testid="save-brand-button"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="brand-success-message"]').should('be.visible')
    cy.shouldContainArabicText('تم إضافة العلامة التجارية بنجاح')
  })

  it('يجب أن تعمل وظيفة البحث في الفئات', () => {
    cy.waitForLoadingToFinish()
    
    // البحث في الفئات
    cy.get('[data-testid="categories-search"]').type('إلكترونيات')
    cy.get('[data-testid="search-categories-button"]').click()
    
    // التحقق من نتائج البحث
    cy.get('[data-testid="search-results"]').should('be.visible')
    cy.get('[data-testid="category-search-result"]').should('contain.text', 'إلكترونيات')
  })

  it('يجب أن تعمل وظيفة تصفية المنتجات', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب المنتجات
    cy.get('[data-testid="tab-products"]').click()
    
    // تطبيق مرشحات المنتجات
    cy.get('[data-testid="product-status-filter"]').select('pending')
    cy.get('[data-testid="product-category-filter"]').select('electronics')
    cy.get('[data-testid="apply-product-filters"]').click()
    
    // التحقق من تحديث النتائج
    cy.get('[data-testid="filtered-products"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة المراجعة الجماعية', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لتبويب المنتجات
    cy.get('[data-testid="tab-products"]').click()
    
    // تحديد عدة منتجات
    cy.get('[data-testid="product-checkbox-1"]').check()
    cy.get('[data-testid="product-checkbox-2"]').check()
    
    // التحقق من ظهور شريط الإجراءات الجماعية
    cy.get('[data-testid="bulk-moderation-bar"]').should('be.visible')
    
    // اعتماد جماعي
    cy.get('[data-testid="bulk-approve-button"]').click()
    cy.get('[data-testid="bulk-approval-reason"]').type('منتجات مطابقة للمعايير')
    cy.get('[data-testid="confirm-bulk-approval"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="bulk-approval-success"]').should('be.visible')
    cy.shouldContainArabicText('تم اعتماد المنتجات بنجاح')
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في تحميل الفئات
    cy.intercept('GET', '**/api/admin/categories**', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('categoriesError')
    
    cy.visitWithLocale('/admin/categories')
    cy.wait('@categoriesError')
    
    // التحقق من عرض رسالة الخطأ
    cy.get('[data-testid="categories-error"]').should('be.visible')
    cy.shouldContainArabicText('حدث خطأ في تحميل الفئات')
    
    // التحقق من وجود زر إعادة المحاولة
    cy.get('[data-testid="retry-categories-button"]').should('be.visible')
  })

  it('يجب أن تعمل على الأجهزة المحمولة', () => {
    cy.viewport('iphone-x')
    cy.waitForLoadingToFinish()
    
    // التحقق من التجاوب
    cy.get('[data-testid="categories-management-page"]').should('be.visible')
    
    // اختبار التبويبات على الهاتف المحمول
    cy.get('[data-testid="mobile-tabs-menu"]').should('be.visible')
    cy.get('[data-testid="mobile-tabs-menu"]').click()
    cy.get('[data-testid="mobile-tabs-dropdown"]').should('be.visible')
  })
})
