// src/services/customerAnalyticsService.ts
"use client";

import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy,
  Timestamp,
  limit
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  CustomerProfile, 
  CustomerInteraction, 
  CRMAnalytics,
  OrderDocument,
  CustomerReview
} from '@/types';

export class CustomerAnalyticsService {
  private customersCollection = collection(db, 'customer_profiles');
  private interactionsCollection = collection(db, 'customer_interactions');
  private ordersCollection = collection(db, 'orders');
  private reviewsCollection = collection(db, 'reviews');

  // ===== تحليلات شاملة =====

  // إنشاء تقرير تحليلي شامل
  async generateCRMAnalytics(
    merchantId: string,
    period: {
      startDate: Date;
      endDate: Date;
      type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    }
  ): Promise<CRMAnalytics> {
    try {
      const [
        customerMetrics,
        interactionMetrics,
        salesMetrics,
        predictions
      ] = await Promise.all([
        this.calculateCustomerMetrics(merchantId, period),
        this.calculateInteractionMetrics(merchantId, period),
        this.calculateSalesMetrics(merchantId, period),
        this.generatePredictions(merchantId, period)
      ]);

      return {
        merchantId,
        period: {
          startDate: Timestamp.fromDate(period.startDate),
          endDate: Timestamp.fromDate(period.endDate),
          type: period.type
        },
        customerMetrics,
        interactionMetrics,
        salesMetrics,
        predictions,
        generatedAt: Timestamp.now()
      };
    } catch (error) {
      console.error('Error generating CRM analytics:', error);
      throw new Error('فشل في إنشاء تحليلات CRM');
    }
  }

  // حساب مقاييس العملاء
  private async calculateCustomerMetrics(
    merchantId: string,
    period: { startDate: Date; endDate: Date }
  ) {
    const customersQuery = query(
      this.customersCollection,
      where('merchantId', '==', merchantId)
    );

    const customersSnapshot = await getDocs(customersQuery);
    const allCustomers = customersSnapshot.docs.map(doc => 
      ({ id: doc.id, ...doc.data() }) as CustomerProfile
    );

    const totalCustomers = allCustomers.length;

    // العملاء الجدد في الفترة
    const newCustomers = allCustomers.filter(customer => {
      const createdDate = customer.createdAt.toDate();
      return createdDate >= period.startDate && createdDate <= period.endDate;
    }).length;

    // العملاء النشطين (لديهم تفاعل في آخر 30 يوم)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const activeCustomers = allCustomers.filter(customer => 
      customer.lastInteractionDate && 
      customer.lastInteractionDate.toDate() > thirtyDaysAgo
    ).length;

    // العملاء العائدين (لديهم أكثر من طلب واحد)
    const returningCustomers = allCustomers.filter(customer => 
      customer.shoppingBehavior.totalOrders > 1
    ).length;

    // العملاء المفقودين (لم يتفاعلوا لأكثر من 90 يوم)
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    const lostCustomers = allCustomers.filter(customer => 
      customer.lastInteractionDate && 
      customer.lastInteractionDate.toDate() < ninetyDaysAgo
    ).length;

    // حساب المعدلات
    const customerRetentionRate = totalCustomers > 0 
      ? ((totalCustomers - lostCustomers) / totalCustomers) * 100 
      : 0;
    
    const customerChurnRate = 100 - customerRetentionRate;

    // متوسط تكلفة اكتساب العميل (تقدير)
    const customerAcquisitionCost = 50; // يمكن حسابه من بيانات التسويق

    // متوسط قيمة العميل مدى الحياة
    const customerLifetimeValue = allCustomers.length > 0
      ? allCustomers.reduce((sum, customer) => sum + customer.segmentation.lifetimeValue, 0) / allCustomers.length
      : 0;

    // توزيع العملاء حسب المستوى
    const customersByTier = {
      bronze: allCustomers.filter(c => c.segmentation.tier === 'bronze').length,
      silver: allCustomers.filter(c => c.segmentation.tier === 'silver').length,
      gold: allCustomers.filter(c => c.segmentation.tier === 'gold').length,
      platinum: allCustomers.filter(c => c.segmentation.tier === 'platinum').length
    };

    return {
      totalCustomers,
      newCustomers,
      activeCustomers,
      returningCustomers,
      lostCustomers,
      customerRetentionRate,
      customerChurnRate,
      customerAcquisitionCost,
      customerLifetimeValue,
      customersByTier,
      customersBySegment: {} // سيتم تطويره لاحقاً
    };
  }

  // حساب مقاييس التفاعل
  private async calculateInteractionMetrics(
    merchantId: string,
    period: { startDate: Date; endDate: Date }
  ) {
    const interactionsQuery = query(
      this.interactionsCollection,
      where('merchantId', '==', merchantId),
      where('createdAt', '>=', Timestamp.fromDate(period.startDate)),
      where('createdAt', '<=', Timestamp.fromDate(period.endDate))
    );

    const interactionsSnapshot = await getDocs(interactionsQuery);
    const interactions = interactionsSnapshot.docs.map(doc => 
      ({ id: doc.id, ...doc.data() }) as CustomerInteraction
    );

    const totalInteractions = interactions.length;

    // تفاعلات حسب النوع
    const interactionsByType = interactions.reduce((acc, interaction) => {
      acc[interaction.type] = (acc[interaction.type] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    // تفاعلات حسب القناة
    const interactionsByChannel = interactions.reduce((acc, interaction) => {
      acc[interaction.details.channel] = (acc[interaction.details.channel] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    // متوسط وقت الاستجابة (بالساعات)
    const resolvedInteractions = interactions.filter(i => 
      i.details.outcome === 'resolved' && i.details.duration
    );
    const averageResponseTime = resolvedInteractions.length > 0
      ? resolvedInteractions.reduce((sum, i) => sum + (i.details.duration || 0), 0) / resolvedInteractions.length / 60
      : 0;

    // معدل الحل
    const resolutionRate = totalInteractions > 0
      ? (interactions.filter(i => i.details.outcome === 'resolved').length / totalInteractions) * 100
      : 0;

    // نقاط رضا العملاء
    const ratedInteractions = interactions.filter(i => i.rating?.score);
    const customerSatisfactionScore = ratedInteractions.length > 0
      ? ratedInteractions.reduce((sum, i) => sum + (i.rating?.score || 0), 0) / ratedInteractions.length
      : 0;

    return {
      totalInteractions,
      interactionsByType,
      interactionsByChannel,
      averageResponseTime,
      resolutionRate,
      customerSatisfactionScore
    };
  }

  // حساب مقاييس المبيعات
  private async calculateSalesMetrics(
    merchantId: string,
    period: { startDate: Date; endDate: Date }
  ) {
    const ordersQuery = query(
      this.ordersCollection,
      where('merchantUid', '==', merchantId),
      where('createdAt', '>=', Timestamp.fromDate(period.startDate)),
      where('createdAt', '<=', Timestamp.fromDate(period.endDate))
    );

    const ordersSnapshot = await getDocs(ordersQuery);
    const orders = ordersSnapshot.docs.map(doc => 
      ({ id: doc.id, ...doc.data() }) as OrderDocument
    );

    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

    // معدل الشراء المتكرر
    const customerOrderCounts = orders.reduce((acc, order) => {
      acc[order.customerId] = (acc[order.customerId] || 0) + 1;
      return acc;
    }, {} as { [customerId: string]: number });

    const repeatCustomers = Object.values(customerOrderCounts).filter(count => count > 1).length;
    const totalUniqueCustomers = Object.keys(customerOrderCounts).length;
    const repeatPurchaseRate = totalUniqueCustomers > 0 
      ? (repeatCustomers / totalUniqueCustomers) * 100 
      : 0;

    // معدلات البيع المتقاطع والبيع الإضافي (تقدير)
    const crossSellRate = 15; // يمكن حسابه من تحليل سلة التسوق
    const upsellRate = 8; // يمكن حسابه من مقارنة قيم الطلبات

    // معدل الاسترداد
    const refundedOrders = orders.filter(order => order.status === 'cancelled').length;
    const refundRate = orders.length > 0 ? (refundedOrders / orders.length) * 100 : 0;

    return {
      totalRevenue,
      averageOrderValue,
      repeatPurchaseRate,
      crossSellRate,
      upsellRate,
      refundRate
    };
  }

  // إنشاء التوقعات
  private async generatePredictions(
    merchantId: string,
    period: { startDate: Date; endDate: Date }
  ) {
    // حساب معدل النمو الشهري
    const previousPeriodStart = new Date(period.startDate);
    previousPeriodStart.setMonth(previousPeriodStart.getMonth() - 1);
    const previousPeriodEnd = new Date(period.endDate);
    previousPeriodEnd.setMonth(previousPeriodEnd.getMonth() - 1);

    const [currentRevenue, previousRevenue] = await Promise.all([
      this.calculatePeriodRevenue(merchantId, period.startDate, period.endDate),
      this.calculatePeriodRevenue(merchantId, previousPeriodStart, previousPeriodEnd)
    ]);

    const growthRate = previousRevenue > 0 
      ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 
      : 0;

    // توقع إيرادات الشهر القادم
    const nextMonthRevenue = currentRevenue * (1 + (growthRate / 100));

    // تحليل مخاطر فقدان العملاء
    const customersQuery = query(
      this.customersCollection,
      where('merchantId', '==', merchantId)
    );

    const customersSnapshot = await getDocs(customersQuery);
    const customers = customersSnapshot.docs.map(doc => 
      ({ id: doc.id, ...doc.data() }) as CustomerProfile
    );

    const churnRisk = {
      high: customers.filter(c => c.segmentation.churnProbability > 0.7).length,
      medium: customers.filter(c => c.segmentation.churnProbability > 0.4 && c.segmentation.churnProbability <= 0.7).length,
      low: customers.filter(c => c.segmentation.churnProbability <= 0.4).length
    };

    return {
      nextMonthRevenue,
      churnRisk,
      growthRate
    };
  }

  // حساب إيرادات فترة معينة
  private async calculatePeriodRevenue(
    merchantId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const ordersQuery = query(
      this.ordersCollection,
      where('merchantUid', '==', merchantId),
      where('createdAt', '>=', Timestamp.fromDate(startDate)),
      where('createdAt', '<=', Timestamp.fromDate(endDate))
    );

    const ordersSnapshot = await getDocs(ordersQuery);
    const orders = ordersSnapshot.docs.map(doc => doc.data() as OrderDocument);
    
    return orders.reduce((sum, order) => sum + order.totalAmount, 0);
  }

  // ===== تحليلات متقدمة =====

  // تحليل سلوك العملاء
  async analyzeCustomerBehavior(merchantId: string): Promise<any> {
    try {
      const customersQuery = query(
        this.customersCollection,
        where('merchantId', '==', merchantId)
      );

      const customersSnapshot = await getDocs(customersQuery);
      const customers = customersSnapshot.docs.map(doc => 
        ({ id: doc.id, ...doc.data() }) as CustomerProfile
      );

      // تحليل أنماط الشراء
      const purchasePatterns = {
        averageOrderFrequency: customers.reduce((sum, c) => 
          sum + (c.shoppingBehavior.averageTimeBetweenOrders || 0), 0) / customers.length,
        
        mostPopularCategories: this.getMostPopularCategories(customers),
        
        seasonalTrends: await this.analyzeSeasonalTrends(merchantId),
        
        customerLifecycle: this.analyzeCustomerLifecycle(customers)
      };

      return purchasePatterns;
    } catch (error) {
      console.error('Error analyzing customer behavior:', error);
      throw new Error('فشل في تحليل سلوك العملاء');
    }
  }

  // الحصول على الفئات الأكثر شعبية
  private getMostPopularCategories(customers: CustomerProfile[]): { [category: string]: number } {
    const categoryCount: { [category: string]: number } = {};
    
    customers.forEach(customer => {
      customer.shoppingBehavior.favoriteCategories.forEach(category => {
        categoryCount[category] = (categoryCount[category] || 0) + 1;
      });
    });

    return categoryCount;
  }

  // تحليل الاتجاهات الموسمية
  private async analyzeSeasonalTrends(merchantId: string): Promise<any> {
    // تحليل بسيط للاتجاهات الموسمية
    // في التطبيق الحقيقي، نحتاج لتحليل أكثر تعقيداً
    return {
      peakMonths: ['11', '12', '1'], // نوفمبر، ديسمبر، يناير
      lowMonths: ['6', '7', '8'], // يونيو، يوليو، أغسطس
      weekendVsWeekday: {
        weekend: 0.3,
        weekday: 0.7
      }
    };
  }

  // تحليل دورة حياة العميل
  private analyzeCustomerLifecycle(customers: CustomerProfile[]): any {
    const lifecycle = {
      new: customers.filter(c => c.shoppingBehavior.totalOrders <= 1).length,
      growing: customers.filter(c => c.shoppingBehavior.totalOrders > 1 && c.shoppingBehavior.totalOrders <= 5).length,
      mature: customers.filter(c => c.shoppingBehavior.totalOrders > 5 && c.shoppingBehavior.totalOrders <= 15).length,
      loyal: customers.filter(c => c.shoppingBehavior.totalOrders > 15).length
    };

    return lifecycle;
  }
}

export const customerAnalyticsService = new CustomerAnalyticsService();
