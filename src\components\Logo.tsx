"use client";

import { useState, useEffect, useCallback } from 'react';
import type { SVGProps } from 'react';
import { cn } from '@/lib/utils';
import { useLocale } from '@/hooks/use-locale';

interface LogoProps extends SVGProps<SVGSVGElement> {
  variant?: 'simple' | 'animated' | 'brand';
  size?: 'small' | 'default' | 'large';
  interactive?: boolean;
  showText?: boolean;
  animated?: boolean;
}

export default function Logo({
  variant = 'simple',
  size = 'default',
  interactive = true,
  showText = false,
  animated = false,
  className,
  ...props
}: LogoProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const { t } = useLocale();

  // تحديد الحجم
  const logoSize = size === 'small' ? 40 : size === 'large' ? 120 : 80;
  const textSize = size === 'small' ? 'text-lg' : size === 'large' ? 'text-3xl' : 'text-2xl';

  // تفعيل الرسوم المتحركة عند الحاجة
  useEffect(() => {
    if (animated || variant === 'animated') {
      const timer = setTimeout(() => setIsAnimating(true), 1000);
      return () => clearTimeout(timer);
    }
  }, [animated, variant]);

  const handleMouseEnter = useCallback(() => {
    if (interactive) {
      setIsHovered(true);
      setIsAnimating(true);
    }
  }, [interactive]);

  const handleMouseLeave = useCallback(() => {
    if (interactive) {
      setIsHovered(false);
    }
  }, [interactive]);

  // مكون Logo البسيط
  if (variant === 'simple') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        width={logoSize}
        height={logoSize}
        aria-label="مِخْلاة"
        role="img"
        className={cn('logo', className)}
        {...props}
      >
        <defs>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: 'hsl(var(--primary))', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: 'hsl(var(--accent))', stopOpacity: 1 }} />
          </linearGradient>
        </defs>
        <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" />
        <text
          x="50%"
          y="50%"
          dy=".3em"
          textAnchor="middle"
          fontSize="40"
          fontWeight="bold"
          fill="hsl(var(--primary-foreground))"
          fontFamily="var(--font-geist-sans)"
        >
          م
        </text>
      </svg>
    );
  }

  // مكون Logo مع العلامة التجارية
  if (variant === 'brand') {
    return (
      <div 
        className={cn(
          'flex items-center gap-3 transition-all duration-300',
          isHovered && 'scale-105',
          className
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className={cn(
          'relative transition-all duration-300',
          isHovered && 'rotate-6'
        )}>
          <Logo 
            variant="animated"
            size={size}
            animated={isAnimating}
            className={cn(
              'transition-all duration-500',
              isHovered && 'filter drop-shadow-lg'
            )}
          />
          
          {isHovered && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="w-full h-full absolute animate-ping rounded-full bg-accent/20 scale-110" />
            </div>
          )}
        </div>

        {showText && (
          <div className={cn(
            'font-bold transition-all duration-300 text-primary-foreground',
            textSize,
            isHovered && 'text-accent'
          )}>
            {t('appName')}
          </div>
        )}
      </div>
    );
  }

  // مكون Logo المتحرك (افتراضي)
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 100"
      width={logoSize}
      height={logoSize}
      aria-label="مِخْلاة"
      role="img"
      className={cn('logo', className)}
      {...props}
    >
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{ stopColor: 'hsl(var(--primary))', stopOpacity: 1 }} />
          <stop offset="100%" style={{ stopColor: 'hsl(var(--accent))', stopOpacity: 1 }} />
        </linearGradient>
        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="2" result="blur" />
          <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
      </defs>

      <circle 
        className={cn(isAnimating && "animate-pulse")} 
        cx="50" 
        cy="50" 
        r="45" 
        fill="url(#logoGradient)" 
        filter="url(#glow)"
      />

      <g stroke="hsl(var(--primary-foreground))" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M30,30 C30,25 40,20 50,20 C60,20 70,25 70,30"
        />
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M70,30 L70,65 C70,70 60,75 50,75 C40,75 30,70 30,65 L30,30"
        />
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M40,40 C40,40 50,45 60,40"
        />
        <path 
          className={cn(isAnimating && "animate-pulse")} 
          d="M40,50 C40,50 50,55 60,50"
        />
      </g>

      <text
        className={cn(isAnimating && "animate-pulse")}
        x="50%"
        y="50%"
        dy=".3em"
        textAnchor="middle"
        fontSize="40"
        fontWeight="bold"
        fill="hsl(var(--primary-foreground))"
        fontFamily="var(--font-geist-sans)"
      >
        م
      </text>
    </svg>
  );
}

// تصدير أنواع إضافية للتوافق مع الإصدارات السابقة
export const SimpleLogo = (props: SVGProps<SVGSVGElement>) => <Logo variant="simple" {...props} />;
export const AnimatedLogo = (props: LogoProps) => <Logo variant="animated" {...props} />;
export const BrandLogo = (props: LogoProps) => <Logo variant="brand" showText {...props} />;
