"use client";

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocale } from '@/hooks/use-locale';
import { User, Store, Truck, ArrowRight } from 'lucide-react';
import type { UserType } from '@/types';
import GoogleSignInButton from './GoogleSignInButton';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface UserTypeSelectionProps {
  onUserTypeSelect: (userType: UserType) => void;
  onContinueWithGoogle: (userType: UserType) => void;
}

export default function UserTypeSelection({ onUserTypeSelect, onContinueWithGoogle }: UserTypeSelectionProps) {
  const { t, isLoading } = useLocale();
  const params = useParams();
  const locale = params.locale as string;
  const [selectedUserType, setSelectedUserType] = useState<UserType | null>(null);

  const userTypes = [
    {
      type: 'customer' as UserType,
      icon: User,
      title: t('customer'),
      description: t('customerDescription'),
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/10',
      borderColor: 'border-blue-200 dark:border-blue-800',
      buttonText: t('continueAsCustomer')
    },
    {
      type: 'merchant' as UserType,
      icon: Store,
      title: t('merchant'),
      description: t('merchantDescription'),
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/10',
      borderColor: 'border-green-200 dark:border-green-800',
      buttonText: t('continueAsMerchant')
    },
    {
      type: 'representative' as UserType,
      icon: Truck,
      title: t('representative'),
      description: t('representativeDescription'),
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-900/10',
      borderColor: 'border-purple-200 dark:border-purple-800',
      buttonText: t('continueAsRepresentative')
    }
  ];

  const handleManualSignup = () => {
    if (selectedUserType) {
      onUserTypeSelect(selectedUserType);
    }
  };

  const handleGoogleSignup = () => {
    if (selectedUserType) {
      onContinueWithGoogle(selectedUserType);
    }
  };

  // Show loading state while translations are loading
  if (isLoading) {
    return (
      <div className="w-full max-w-4xl mx-auto space-y-4">
        <div className="text-center space-y-2">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse max-w-2xl mx-auto"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border rounded-lg p-6 space-y-4">
              <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto animate-pulse"></div>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-4">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-primary">{t('welcomeToMikhla')}</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          {t('selectUserTypeSubtitle')}
        </p>
      </div>

      {/* User Type Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {userTypes.map((userType) => {
          const Icon = userType.icon;
          const isSelected = selectedUserType === userType.type;
          
          return (
            <Card
              key={userType.type}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                isSelected 
                  ? `ring-2 ring-primary shadow-lg ${userType.bgColor}` 
                  : 'hover:shadow-md'
              }`}
              onClick={() => setSelectedUserType(userType.type)}
            >
              <CardHeader className="text-center pb-4">
                <div className={`mx-auto p-4 rounded-full bg-gradient-to-r ${userType.color} text-white mb-4`}>
                  <Icon className="h-8 w-8" />
                </div>
                <CardTitle className="text-xl">{userType.title}</CardTitle>
                <CardDescription className="text-center">
                  {userType.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-center">
                  {isSelected && (
                    <div className="flex items-center text-primary font-medium">
                      <span className="text-sm">{t('chooseAccountType')}</span>
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Action Buttons */}
      {selectedUserType && (
        <div className="space-y-4 animate-in fade-in-50 duration-500">
          {/* Google Sign-in Option - للعملاء فقط */}
          {selectedUserType === 'customer' && (
            <div className="space-y-3">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    {t('orContinueWith')}
                  </span>
                </div>
              </div>

              <GoogleSignInButton
                userType={selectedUserType}
                variant="outline"
                className="w-full h-12"
                onClick={handleGoogleSignup}
              />
            </div>
          )}

          {/* رسالة توضيحية للتجار والمندوبين */}
          {(selectedUserType === 'merchant' || selectedUserType === 'representative') && (
            <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {selectedUserType === 'merchant' ? t('merchantGoogleSignupNotice') : t('representativeGoogleSignupNotice')}
                  </h3>
                  <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    {selectedUserType === 'merchant' ? t('merchantManualSignupRequired') : t('representativeManualSignupRequired')}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Manual Signup Option */}
          <div className="space-y-3">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  {selectedUserType === 'customer' ? t('orSignupManually') : t('signupManually')}
                </span>
              </div>
            </div>

            <Button
              onClick={handleManualSignup}
              className="w-full h-12 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              {userTypes.find(ut => ut.type === selectedUserType)?.buttonText}
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      )}

      {/* Error message if no type selected */}
      {!selectedUserType && (
        <div className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            {t('accountTypeRequired')}
          </p>
        </div>
      )}

      {/* Login link */}
      <div className="text-center pt-4">
        <p className="text-sm text-muted-foreground">
          {t('alreadyHaveAccount')}{' '}
          <Link href={`/${locale}/login`} className="font-medium text-primary hover:text-primary/80">
            {t('login')}
          </Link>
        </p>
      </div>
    </div>
  );
}
