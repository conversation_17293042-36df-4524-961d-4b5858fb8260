describe('نظام التوصيل والمندوبين', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.mockGeolocation(24.7136, 46.6753) // الرياض
  })

  context('واجهة المندوب', () => {
    beforeEach(() => {
      cy.mockLogin('representative')
      cy.visitWithLocale('/representative/dashboard')
    })

    it('يجب أن تعرض لوحة تحكم المندوب بنجاح', () => {
      cy.waitForLoadingToFinish()
      
      // التحقق من العنوان الرئيسي
      cy.shouldContainArabicText('لوحة تحكم المندوب')
      cy.get('[data-testid="representative-dashboard"]').should('be.visible')
      
      // التحقق من الإحصائيات
      cy.get('[data-testid="delivery-stats"]').should('be.visible')
      cy.get('[data-testid="total-deliveries-stat"]').should('be.visible')
      cy.get('[data-testid="completed-deliveries-stat"]').should('be.visible')
      cy.get('[data-testid="earnings-stat"]').should('be.visible')
      cy.get('[data-testid="rating-stat"]').should('be.visible')
    })

    it('يجب أن تعرض الطلبات المتاحة للتوصيل', () => {
      cy.waitForLoadingToFinish()
      
      // التحقق من قسم الطلبات المتاحة
      cy.get('[data-testid="available-orders"]').should('be.visible')
      cy.shouldContainArabicText('الطلبات المتاحة')
      
      // التحقق من قائمة الطلبات
      cy.get('[data-testid="orders-list"]').should('be.visible')
      cy.get('[data-testid="order-card"]').should('have.length.greaterThan', 0)
      
      // التحقق من تفاصيل الطلب
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="order-number"]').should('be.visible')
        cy.get('[data-testid="pickup-address"]').should('be.visible')
        cy.get('[data-testid="delivery-address"]').should('be.visible')
        cy.get('[data-testid="order-value"]').should('be.visible')
        cy.get('[data-testid="delivery-fee"]').should('be.visible')
        cy.get('[data-testid="distance"]').should('be.visible')
      })
    })

    it('يجب أن تعمل وظيفة قبول الطلب', () => {
      cy.waitForLoadingToFinish()
      
      // قبول طلب توصيل
      cy.get('[data-testid="accept-order-1"]').click()
      cy.get('[data-testid="confirm-accept-dialog"]').should('be.visible')
      
      // تأكيد القبول
      cy.get('[data-testid="confirm-accept-button"]').click()
      
      // التحقق من رسالة النجاح
      cy.get('[data-testid="accept-success"]').should('be.visible')
      cy.shouldContainArabicText('تم قبول الطلب بنجاح')
      
      // التحقق من انتقال الطلب للطلبات النشطة
      cy.get('[data-testid="active-orders"]').should('be.visible')
      cy.get('[data-testid="active-order-1"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة تتبع الطلب', () => {
      // قبول طلب أولاً
      cy.get('[data-testid="accept-order-1"]').click()
      cy.get('[data-testid="confirm-accept-button"]').click()
      cy.waitForLoadingToFinish()
      
      // بدء التوصيل
      cy.get('[data-testid="start-delivery-1"]').click()
      cy.get('[data-testid="delivery-tracking"]').should('be.visible')
      
      // تحديث حالة التوصيل
      cy.get('[data-testid="update-status-button"]').click()
      cy.get('[data-testid="status-options"]').should('be.visible')
      cy.get('[data-testid="status-picked-up"]').click()
      
      // التحقق من تحديث الحالة
      cy.get('[data-testid="current-status"]').should('contain.text', 'تم الاستلام')
      
      // وصول للعميل
      cy.get('[data-testid="update-status-button"]').click()
      cy.get('[data-testid="status-delivered"]').click()
      
      // تأكيد التسليم
      cy.get('[data-testid="confirm-delivery"]').should('be.visible')
      cy.get('[data-testid="delivery-notes"]').type('تم التسليم بنجاح للعميل')
      cy.get('[data-testid="confirm-delivery-button"]').click()
      
      // التحقق من إكمال التوصيل
      cy.get('[data-testid="delivery-completed"]').should('be.visible')
      cy.shouldContainArabicText('تم إكمال التوصيل بنجاح')
    })

    it('يجب أن تعرض الخريطة والتنقل', () => {
      // قبول طلب أولاً
      cy.get('[data-testid="accept-order-1"]').click()
      cy.get('[data-testid="confirm-accept-button"]').click()
      cy.waitForLoadingToFinish()
      
      // فتح الخريطة
      cy.get('[data-testid="open-map-1"]').click()
      cy.get('[data-testid="delivery-map"]').should('be.visible')
      
      // التحقق من عناصر الخريطة
      cy.get('[data-testid="pickup-marker"]').should('be.visible')
      cy.get('[data-testid="delivery-marker"]').should('be.visible')
      cy.get('[data-testid="route-path"]').should('be.visible')
      
      // اختبار التنقل
      cy.get('[data-testid="navigate-to-pickup"]').click()
      cy.get('[data-testid="navigation-started"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة الاتصال بالعميل', () => {
      // قبول طلب أولاً
      cy.get('[data-testid="accept-order-1"]').click()
      cy.get('[data-testid="confirm-accept-button"]').click()
      cy.waitForLoadingToFinish()
      
      // الاتصال بالعميل
      cy.get('[data-testid="call-customer-1"]').click()
      cy.get('[data-testid="call-options"]').should('be.visible')
      
      // اختيار نوع الاتصال
      cy.get('[data-testid="voice-call"]').click()
      
      // التحقق من بدء الاتصال
      cy.get('[data-testid="call-initiated"]').should('be.visible')
      cy.shouldContainArabicText('جاري الاتصال بالعميل')
    })

    it('يجب أن تعرض تاريخ التوصيلات', () => {
      cy.waitForLoadingToFinish()
      
      // الانتقال لصفحة التاريخ
      cy.get('[data-testid="delivery-history-tab"]').click()
      cy.get('[data-testid="delivery-history"]').should('be.visible')
      
      // التحقق من قائمة التوصيلات السابقة
      cy.get('[data-testid="history-list"]').should('be.visible')
      cy.get('[data-testid="history-item"]').should('have.length.greaterThan', 0)
      
      // التحقق من تفاصيل التوصيل
      cy.get('[data-testid="history-item"]').first().within(() => {
        cy.get('[data-testid="delivery-date"]').should('be.visible')
        cy.get('[data-testid="delivery-earnings"]').should('be.visible')
        cy.get('[data-testid="delivery-rating"]').should('be.visible')
      })
    })

    it('يجب أن تعمل وظيفة تصفية التوصيلات', () => {
      cy.waitForLoadingToFinish()
      
      // تصفية الطلبات حسب المسافة
      cy.get('[data-testid="distance-filter"]').select('5km')
      cy.get('[data-testid="apply-filters"]').click()
      
      // التحقق من تحديث النتائج
      cy.get('[data-testid="filtered-orders"]').should('be.visible')
      
      // تصفية حسب قيمة الطلب
      cy.get('[data-testid="value-filter"]').select('100+')
      cy.get('[data-testid="apply-filters"]').click()
      
      // التحقق من تحديث النتائج
      cy.get('[data-testid="filtered-orders"]').should('be.visible')
    })

    it('يجب أن تعرض الأرباح والإحصائيات', () => {
      cy.waitForLoadingToFinish()
      
      // الانتقال لصفحة الأرباح
      cy.get('[data-testid="earnings-tab"]').click()
      cy.get('[data-testid="earnings-page"]').should('be.visible')
      
      // التحقق من إحصائيات الأرباح
      cy.get('[data-testid="daily-earnings"]').should('be.visible')
      cy.get('[data-testid="weekly-earnings"]').should('be.visible')
      cy.get('[data-testid="monthly-earnings"]').should('be.visible')
      
      // التحقق من الرسم البياني
      cy.get('[data-testid="earnings-chart"]').should('be.visible')
    })
  })

  context('إدارة التوصيل للتجار', () => {
    beforeEach(() => {
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/delivery')
    })

    it('يجب أن تعرض صفحة إدارة التوصيل للتاجر', () => {
      cy.waitForLoadingToFinish()
      
      // التحقق من العنوان
      cy.shouldContainArabicText('إدارة التوصيل')
      cy.get('[data-testid="merchant-delivery-page"]').should('be.visible')
      
      // التحقق من الطلبات المعلقة
      cy.get('[data-testid="pending-deliveries"]').should('be.visible')
      cy.shouldContainArabicText('الطلبات المعلقة للتوصيل')
    })

    it('يجب أن تعمل وظيفة تعيين مندوب', () => {
      cy.waitForLoadingToFinish()
      
      // تعيين مندوب لطلب
      cy.get('[data-testid="assign-representative-1"]').click()
      cy.get('[data-testid="representatives-list"]').should('be.visible')
      
      // اختيار مندوب
      cy.get('[data-testid="representative-option-1"]').click()
      cy.get('[data-testid="confirm-assignment"]').click()
      
      // التحقق من رسالة النجاح
      cy.get('[data-testid="assignment-success"]').should('be.visible')
      cy.shouldContainArabicText('تم تعيين المندوب بنجاح')
    })

    it('يجب أن تعمل وظيفة تتبع التوصيلات', () => {
      cy.waitForLoadingToFinish()
      
      // تتبع توصيل نشط
      cy.get('[data-testid="track-delivery-1"]').click()
      cy.get('[data-testid="delivery-tracking-modal"]').should('be.visible')
      
      // التحقق من معلومات التتبع
      cy.get('[data-testid="delivery-status"]').should('be.visible')
      cy.get('[data-testid="representative-info"]').should('be.visible')
      cy.get('[data-testid="estimated-time"]').should('be.visible')
      
      // التحقق من الخريطة
      cy.get('[data-testid="tracking-map"]').should('be.visible')
    })
  })

  context('إدارة التوصيل للإدارة', () => {
    beforeEach(() => {
      cy.mockLogin('admin')
      cy.visitWithLocale('/admin/delivery')
    })

    it('يجب أن تعرض لوحة تحكم التوصيل للإدارة', () => {
      cy.waitForLoadingToFinish()
      
      // التحقق من العنوان
      cy.shouldContainArabicText('إدارة نظام التوصيل')
      cy.get('[data-testid="admin-delivery-dashboard"]').should('be.visible')
      
      // التحقق من الإحصائيات العامة
      cy.get('[data-testid="delivery-overview"]').should('be.visible')
      cy.get('[data-testid="total-representatives"]').should('be.visible')
      cy.get('[data-testid="active-deliveries"]').should('be.visible')
      cy.get('[data-testid="completed-today"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة إدارة المندوبين', () => {
      cy.waitForLoadingToFinish()
      
      // الانتقال لإدارة المندوبين
      cy.get('[data-testid="manage-representatives"]').click()
      cy.get('[data-testid="representatives-management"]').should('be.visible')
      
      // التحقق من قائمة المندوبين
      cy.get('[data-testid="representatives-list"]').should('be.visible')
      cy.get('[data-testid="representative-item"]').should('have.length.greaterThan', 0)
      
      // تفعيل/إلغاء تفعيل مندوب
      cy.get('[data-testid="toggle-representative-1"]').click()
      cy.get('[data-testid="status-change-success"]').should('be.visible')
    })

    it('يجب أن تعمل وظيفة مراقبة التوصيلات المباشرة', () => {
      cy.waitForLoadingToFinish()
      
      // فتح مراقبة التوصيلات المباشرة
      cy.get('[data-testid="live-tracking"]').click()
      cy.get('[data-testid="live-tracking-dashboard"]').should('be.visible')
      
      // التحقق من الخريطة المباشرة
      cy.get('[data-testid="live-map"]').should('be.visible')
      cy.get('[data-testid="active-deliveries-markers"]').should('be.visible')
      
      // التحقق من قائمة التوصيلات النشطة
      cy.get('[data-testid="active-deliveries-list"]').should('be.visible')
    })
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في تحميل الطلبات
    cy.intercept('GET', '**/api/delivery/orders**', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('deliveryError')
    
    cy.mockLogin('representative')
    cy.visitWithLocale('/representative/dashboard')
    cy.wait('@deliveryError')
    
    // التحقق من عرض رسالة الخطأ
    cy.get('[data-testid="delivery-error"]').should('be.visible')
    cy.shouldContainArabicText('حدث خطأ في تحميل الطلبات')
  })

  it('يجب أن تعمل على الأجهزة المحمولة', () => {
    cy.viewport('iphone-x')
    cy.mockLogin('representative')
    cy.visitWithLocale('/representative/dashboard')
    cy.waitForLoadingToFinish()
    
    // التحقق من التجاوب
    cy.get('[data-testid="representative-dashboard"]').should('be.visible')
    
    // اختبار القائمة المحمولة
    cy.get('[data-testid="mobile-menu-toggle"]').should('be.visible')
    cy.get('[data-testid="mobile-menu-toggle"]').click()
    cy.get('[data-testid="mobile-navigation"]').should('be.visible')
  })
})
