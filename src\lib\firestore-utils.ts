// src/lib/firestore-utils.ts
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  collection, 
  query, 
  getDocs,
  DocumentReference,
  DocumentSnapshot,
  QuerySnapshot,
  FirestoreError
} from 'firebase/firestore';
import { db, handleFirestoreError, checkNetworkStatus } from './firebase';

// نوع للخيارات المتقدمة
interface FirestoreOptions {
  retries?: number;
  timeout?: number;
  enableOffline?: boolean;
}

// دالة محسنة للحصول على مستند مع إعادة المحاولة وتقليل الأخطاء
export const getDocumentWithRetry = async <T = any>(
  docPath: string,
  options: FirestoreOptions = {}
): Promise<{ data: T | null; exists: boolean; error?: string; success: boolean }> => {
  const { retries = 2, timeout = 8000, enableOffline = true } = options; // تقليل المحاولات والوقت
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      // التحقق من حالة الشبكة
      if (!checkNetworkStatus() && !enableOffline) {
        throw new Error('No network connection available');
      }

      // إنشاء promise مع timeout
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      );

      const docRef = doc(db, docPath);
      const docSnap = await Promise.race([
        getDoc(docRef),
        timeoutPromise
      ]) as DocumentSnapshot;

      if (docSnap.exists()) {
        return {
          data: docSnap.data() as T,
          exists: true,
          success: true
        };
      } else {
        return {
          data: null,
          exists: false,
          success: true
        };
      }

    } catch (error: any) {
      // تجاهل الأخطاء الشائعة في المحاولة الأولى
      const isCommonError = error.code === 'unavailable' ||
                           error.code === 'deadline-exceeded' ||
                           error.message?.includes('offline') ||
                           error.message?.includes('timeout');

      const errorInfo = handleFirestoreError(error, `getDocument (attempt ${attempt})`);

      // إذا كان خطأ شائع في المحاولة الأولى، حاول مرة أخرى بصمت
      if (attempt === 1 && isCommonError) {
        await new Promise(resolve => setTimeout(resolve, 500));
        continue;
      }

      // إذا كان الخطأ غير قابل للإعادة أو هذه المحاولة الأخيرة
      if (!errorInfo.retry || attempt === retries) {
        return {
          data: null,
          exists: false,
          success: false,
          error: errorInfo.message
        };
      }

      // انتظار قبل إعادة المحاولة (exponential backoff)
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 3000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return {
    data: null,
    exists: false,
    success: false,
    error: 'فشل في جلب البيانات بعد عدة محاولات'
  };
};

// دالة محسنة لحفظ مستند مع إعادة المحاولة
export const setDocumentWithRetry = async <T = any>(
  docPath: string,
  data: T,
  options: FirestoreOptions = {}
): Promise<{ success: boolean; error?: string }> => {
  const { retries = 3, timeout = 10000 } = options;
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      // التحقق من حالة الشبكة
      if (!checkNetworkStatus()) {
        throw new Error('No network connection available');
      }

      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      );

      const docRef = doc(db, docPath);
      await Promise.race([
        setDoc(docRef, data),
        timeoutPromise
      ]);

      return { success: true };

    } catch (error: any) {
      const errorInfo = handleFirestoreError(error, `setDocument (attempt ${attempt})`);
      
      if (!errorInfo.retry || attempt === retries) {
        return {
          success: false,
          error: errorInfo.message
        };
      }

      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return {
    success: false,
    error: 'فشل في حفظ البيانات بعد عدة محاولات'
  };
};

// دالة محسنة لتحديث مستند مع إعادة المحاولة
export const updateDocumentWithRetry = async <T = any>(
  docPath: string,
  data: Partial<T>,
  options: FirestoreOptions = {}
): Promise<{ success: boolean; error?: string }> => {
  const { retries = 3, timeout = 10000 } = options;
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      if (!checkNetworkStatus()) {
        throw new Error('No network connection available');
      }

      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      );

      const docRef = doc(db, docPath);
      await Promise.race([
        updateDoc(docRef, data as any),
        timeoutPromise
      ]);

      return { success: true };

    } catch (error: any) {
      const errorInfo = handleFirestoreError(error, `updateDocument (attempt ${attempt})`);
      
      if (!errorInfo.retry || attempt === retries) {
        return {
          success: false,
          error: errorInfo.message
        };
      }

      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return {
    success: false,
    error: 'فشل في تحديث البيانات بعد عدة محاولات'
  };
};

// دالة محسنة للاستعلام عن مجموعة مع إعادة المحاولة
export const getCollectionWithRetry = async <T = any>(
  collectionPath: string,
  queryConstraints: any[] = [],
  options: FirestoreOptions = {}
): Promise<{ data: T[]; error?: string }> => {
  const { retries = 3, timeout = 15000 } = options;
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      if (!checkNetworkStatus()) {
        throw new Error('No network connection available');
      }

      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      );

      const collectionRef = collection(db, collectionPath);
      const q = queryConstraints.length > 0 ? query(collectionRef, ...queryConstraints) : collectionRef;
      
      const querySnapshot = await Promise.race([
        getDocs(q),
        timeoutPromise
      ]) as QuerySnapshot;

      const data: T[] = [];
      querySnapshot.forEach((doc) => {
        data.push({ id: doc.id, ...doc.data() } as T);
      });

      return { data };

    } catch (error: any) {
      const errorInfo = handleFirestoreError(error, `getCollection (attempt ${attempt})`);
      
      if (!errorInfo.retry || attempt === retries) {
        return {
          data: [],
          error: errorInfo.message
        };
      }

      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return {
    data: [],
    error: 'فشل في جلب البيانات بعد عدة محاولات'
  };
};

// دالة للتحقق من صحة الاتصال بـ Firestore
export const testFirestoreConnection = async (): Promise<boolean> => {
  try {
    const testDoc = doc(db, 'test', 'connection');
    await getDoc(testDoc);
    return true;
  } catch (error) {
    console.error('Firestore connection test failed:', error);
    return false;
  }
};

// دالة لمسح cache Firestore
export const clearFirestoreCache = async (): Promise<void> => {
  try {
    // مسح cache المحلي
    if (typeof window !== 'undefined') {
      const firebaseKeys = Object.keys(localStorage).filter(key =>
        key.startsWith('firebase:') || key.startsWith('firestore:')
      );
      firebaseKeys.forEach(key => localStorage.removeItem(key));
      
      const sessionKeys = Object.keys(sessionStorage).filter(key =>
        key.startsWith('firebase:') || key.startsWith('firestore:')
      );
      sessionKeys.forEach(key => sessionStorage.removeItem(key));
    }
  } catch (error) {
    console.error('Error clearing Firestore cache:', error);
  }
};
