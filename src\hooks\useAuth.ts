'use client';

import { useState, useEffect } from 'react';
import {
  User,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  userType: 'admin' | 'merchant' | 'representative' | 'customer';
  merchantId?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  profileImage?: string;
  phone?: string;
  preferences?: {
    language: string;
    notifications: boolean;
    theme: 'light' | 'dark';
  };
}

export function useAuth() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // جلب بيانات المستخدم من Firestore
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            setUser({
              uid: firebaseUser.uid,
              email: firebaseUser.email!,
              displayName: firebaseUser.displayName || userData.displayName || '',
              userType: userData.userType || 'customer',
              merchantId: userData.merchantId,
              isActive: userData.isActive !== false,
              createdAt: userData.createdAt?.toDate() || new Date(),
              updatedAt: userData.updatedAt?.toDate() || new Date(),
              profileImage: userData.profileImage,
              phone: userData.phone,
              preferences: userData.preferences || {
                language: 'ar',
                notifications: true,
                theme: 'light'
              }
            });
          } else {
            // إنشاء ملف تعريف جديد إذا لم يكن موجوداً
            const newUserProfile: UserProfile = {
              uid: firebaseUser.uid,
              email: firebaseUser.email!,
              displayName: firebaseUser.displayName || '',
              role: 'customer',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date(),
              preferences: {
                language: 'ar',
                notifications: true,
                theme: 'light'
              }
            };

            await setDoc(doc(db, 'users', firebaseUser.uid), newUserProfile);
            setUser(newUserProfile);
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
          setUser(null);
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error: any) {
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const signUp = async (email: string, password: string, userData: Partial<UserProfile>) => {
    try {
      const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);

      // تحديث ملف التعريف في Firebase Auth
      if (userData.displayName) {
        await updateProfile(firebaseUser, {
          displayName: userData.displayName
        });
      }

      // إنشاء ملف التعريف في Firestore
      const userProfile: UserProfile = {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: userData.displayName || '',
        userType: userData.userType || 'customer',
        merchantId: userData.merchantId,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        phone: userData.phone,
        preferences: userData.preferences || {
          language: 'ar',
          notifications: true,
          theme: 'light'
        }
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), userProfile);
    } catch (error: any) {
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
    } catch (error: any) {
      throw new Error('فشل في تسجيل الخروج');
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const updateUserProfile = async (data: Partial<UserProfile>) => {
    if (!user) throw new Error('المستخدم غير مسجل الدخول');

    try {
      const updatedData = {
        ...data,
        updatedAt: new Date()
      };

      await updateDoc(doc(db, 'users', user.uid), updatedData);

      // تحديث الحالة المحلية
      setUser(prev => prev ? { ...prev, ...updatedData } : null);
    } catch (error: any) {
      throw new Error('فشل في تحديث الملف الشخصي');
    }
  };

  // دوال مساعدة للتحقق من نوع المستخدم
  const isAdmin = () => user?.userType === 'admin';
  const isMerchant = () => user?.userType === 'merchant';
  const isRepresentative = () => user?.userType === 'representative';
  const isCustomer = () => user?.userType === 'customer';

  // دالة للتحقق من الصلاحيات
  const hasPermission = (requiredUserType: string | string[]) => {
    if (!user) return false;

    if (Array.isArray(requiredUserType)) {
      return requiredUserType.includes(user.userType);
    }

    return user.userType === requiredUserType;
  };

  return {
    user,
    loading,
    signIn,
    signUp,
    logout,
    resetPassword,
    updateUserProfile,
    // دوال التحقق من نوع المستخدم
    isAdmin,
    isMerchant,
    isRepresentative,
    isCustomer,
    hasPermission
  };
}

// رسائل الخطأ المترجمة
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'المستخدم غير موجود';
    case 'auth/wrong-password':
      return 'كلمة المرور غير صحيحة';
    case 'auth/email-already-in-use':
      return 'البريد الإلكتروني مستخدم بالفعل';
    case 'auth/weak-password':
      return 'كلمة المرور ضعيفة';
    case 'auth/invalid-email':
      return 'البريد الإلكتروني غير صحيح';
    case 'auth/user-disabled':
      return 'الحساب معطل';
    case 'auth/too-many-requests':
      return 'محاولات كثيرة، حاول لاحقاً';
    default:
      return 'حدث خطأ في المصادقة';
  }
}
