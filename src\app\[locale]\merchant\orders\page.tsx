// src/app/[locale]/merchant/orders/page.tsx
"use client";

import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useOrders } from '@/hooks/useOrders';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ShoppingCart, 
  AlertCircle, 
  Calendar, 
  User, 
  DollarSign,
  Package,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';
import type { OrderDocument } from '@/types';

export default function MerchantOrdersPage() {
  const { user } = useAuth();
  const { t, locale } = useLocale();
  const { orders, loading, error } = useOrders({
    merchantUid: user?.uid,
    realtime: true
  });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'confirmed':
        return 'default';
      case 'preparing':
        return 'outline';
      case 'ready':
        return 'default';
      case 'shipped':
        return 'default';
      case 'delivered':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return t('orderStatusPending');
      case 'confirmed':
        return t('orderStatusConfirmed');
      case 'preparing':
        return t('orderStatusPreparing');
      case 'ready':
        return t('orderStatusReady');
      case 'shipped':
        return t('orderStatusShipped');
      case 'delivered':
        return t('orderStatusDelivered');
      case 'cancelled':
        return t('orderStatusCancelled');
      default:
        return status;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp || !timestamp.toDate) return '';
    return timestamp.toDate().toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ${t('SAR')}`;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-48" />
                  </div>
                  <div className="text-right space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Button asChild variant="outline" size="sm">
            <Link href={`/${locale}/merchant/dashboard`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('backToDashboard')}
            </Link>
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold text-center mb-2">
          {t('orderManagement')}
        </h1>
        <p className="text-muted-foreground text-center">
          {t('orderManagementSubtitle')}
        </p>
      </div>

      {/* Orders List */}
      {orders.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <ShoppingCart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {t('noRecentOrders')}
            </h3>
            <p className="text-muted-foreground mb-6">
              {t('noOrdersDescription')}
            </p>
            <div className="flex gap-4 justify-center">
              <Button asChild>
                <Link href={`/${locale}/merchant/products/add`}>
                  <Package className="h-4 w-4 mr-2" />
                  {t('addNewProduct')}
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href={`/${locale}/merchant/dashboard`}>
                  {t('backToDashboard')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {orders.map((order) => (
            <Card key={order.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <h3 className="font-semibold text-lg">
                        #{order.orderNumber}
                      </h3>
                      <Badge variant={getStatusBadgeVariant(order.status)}>
                        {getStatusText(order.status)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span>{order.customerInfo.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(order.createdAt)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        <span>{order.items.length} {t('items')}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-1 font-bold text-lg mb-2">
                      <DollarSign className="h-5 w-5" />
                      <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/${locale}/merchant/orders/${order.id}`}>
                        {t('viewDetails')}
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
