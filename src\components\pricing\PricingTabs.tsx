"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import SubscriptionCard from "./SubscriptionCard";
import type { SubscriptionPlan } from '@/types';
import type { Locale } from '@/lib/i18n';
import { useLocale } from '@/hooks/use-locale';

interface PricingTabsProps {
  merchantPlans: SubscriptionPlan[];
  customerPlans: SubscriptionPlan[];
  representativePlans: SubscriptionPlan[];
  locale: Locale; // Passed for initial hydration, useLocale hook handles client-side updates
}

export default function PricingTabs({ merchantPlans, customerPlans, representativePlans, locale }: PricingTabsProps) {
  const { t } = useLocale(); // Use client-side hook for translations

  return (
    <Tabs defaultValue="merchants" className="w-full">
      <TabsList className="grid w-full grid-cols-3 md:w-2/3 mx-auto mb-8">
        <TabsTrigger value="merchants">{t('merchants')}</TabsTrigger>
        <TabsTrigger value="customers">{t('customers')}</TabsTrigger>
        <TabsTrigger value="representatives">{t('representatives')}</TabsTrigger>
      </TabsList>
      <TabsContent value="merchants">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-semibold">{t('merchantPlansTitle')}</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {merchantPlans.map((plan) => (
            <SubscriptionCard key={plan.id} plan={plan} />
          ))}
        </div>
      </TabsContent>
      <TabsContent value="customers">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-semibold">{t('customerPlansTitle')}</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {customerPlans.map((plan) => (
            <SubscriptionCard key={plan.id} plan={plan} />
          ))}
        </div>
      </TabsContent>
      <TabsContent value="representatives">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-semibold">{t('representativePlansTitle')}</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {representativePlans.map((plan) => (
            <SubscriptionCard key={plan.id} plan={plan} />
          ))}
        </div>
      </TabsContent>
    </Tabs>
  );
}
