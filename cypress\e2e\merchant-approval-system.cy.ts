// cypress/e2e/merchant-approval-system.cy.ts
describe('نظام الموافقة على التجار', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockFirebaseAuth();
    cy.mockFirestore();
  });

  describe('تسجيل التاجر الجديد', () => {
    it('يجب أن يسمح للتاجر بالتسجيل بنجاح', () => {
      cy.visit('/ar/signup');
      
      // اختيار نوع المستخدم
      cy.get('[data-cy="user-type-merchant"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // الموافقة على الشروط
      cy.get('[data-cy="terms-checkbox"]').check();
      cy.get('[data-cy="next-step"]').click();
      
      // تأكيد الاختيار
      cy.get('[data-cy="next-step"]').click();
      
      // ملء البيانات الأساسية
      cy.get('[data-cy="username"]').type('تاجر جديد');
      cy.get('[data-cy="email"]').type('<EMAIL>');
      cy.get('[data-cy="next-detailed-step"]').click();
      
      // كلمة المرور
      cy.get('[data-cy="password"]').type('password123');
      cy.get('[data-cy="confirm-password"]').type('password123');
      cy.get('[data-cy="next-detailed-step"]').click();
      
      // رفع الملفات (محاكاة)
      cy.get('[data-cy="commercial-registration"]').selectFile('cypress/fixtures/test-document.pdf');
      cy.get('[data-cy="freelance-document"]').selectFile('cypress/fixtures/test-document.pdf');
      cy.get('[data-cy="next-detailed-step"]').click();
      
      // إرسال الطلب
      cy.get('[data-cy="submit-signup"]').click();
      
      // التحقق من التوجيه لصفحة انتظار الموافقة
      cy.url().should('include', '/merchant/pending-approval');
      cy.contains('طلبك قيد المراجعة').should('be.visible');
    });

    it('يجب أن يمنع التسجيل بإيميل مكرر', () => {
      cy.visit('/ar/signup');
      
      // محاكاة إيميل موجود مسبقاً
      cy.intercept('POST', '**/auth/signup', {
        statusCode: 400,
        body: { error: { code: 'auth/email-already-in-use' } }
      });
      
      // اختيار نوع المستخدم والمتابعة
      cy.get('[data-cy="user-type-merchant"]').click();
      cy.get('[data-cy="next-step"]').click();
      cy.get('[data-cy="terms-checkbox"]').check();
      cy.get('[data-cy="next-step"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // ملء بيانات بإيميل موجود
      cy.get('[data-cy="username"]').type('تاجر آخر');
      cy.get('[data-cy="email"]').type('<EMAIL>');
      cy.get('[data-cy="next-detailed-step"]').click();
      
      // التحقق من ظهور رسالة الخطأ
      cy.contains('البريد الإلكتروني مستخدم بالفعل').should('be.visible');
    });
  });

  describe('صفحة انتظار الموافقة', () => {
    beforeEach(() => {
      // تسجيل دخول كتاجر في انتظار الموافقة
      cy.loginAs('merchant', {
        uid: 'pending-merchant-123',
        email: '<EMAIL>',
        userType: 'merchant'
      });
      
      // محاكاة بيانات المتجر في انتظار الموافقة
      cy.mockStoreData({
        merchantUid: 'pending-merchant-123',
        approvalStatus: 'pending',
        storeName: 'متجر في الانتظار',
        submittedAt: new Date()
      });
    });

    it('يجب أن تعرض صفحة انتظار الموافقة بشكل صحيح', () => {
      cy.visit('/ar/merchant/pending-approval');
      
      // التحقق من عناصر الصفحة
      cy.contains('طلبك قيد المراجعة').should('be.visible');
      cy.contains('نحن نراجع طلب انضمامك كتاجر').should('be.visible');
      cy.contains('متجر في الانتظار').should('be.visible');
      cy.get('[data-cy="pending-icon"]').should('be.visible');
    });

    it('يجب أن تمنع الوصول لصفحات التاجر الأخرى', () => {
      cy.visit('/ar/merchant/dashboard');
      
      // التحقق من التوجيه لصفحة انتظار الموافقة
      cy.url().should('include', '/merchant/pending-approval');
    });
  });

  describe('لوحة تحكم المدير', () => {
    beforeEach(() => {
      // تسجيل دخول كمدير
      cy.loginAs('admin', {
        uid: 'admin-123',
        email: '<EMAIL>',
        userType: 'admin'
      });
      
      // محاكاة طلبات التجار في الانتظار
      cy.mockPendingMerchants([
        {
          storeData: {
            merchantUid: 'merchant-1',
            storeName: 'متجر الاختبار 1',
            approvalStatus: 'pending',
            submittedAt: new Date()
          },
          userData: {
            uid: 'merchant-1',
            displayName: 'تاجر الاختبار 1',
            email: '<EMAIL>',
            userType: 'merchant'
          }
        }
      ]);
    });

    it('يجب أن تعرض لوحة الموافقات بشكل صحيح', () => {
      cy.visit('/ar/admin/merchant-approvals');
      
      // التحقق من العنوان والإحصائيات
      cy.contains('إدارة موافقات التجار').should('be.visible');
      cy.get('[data-cy="pending-stats"]').should('contain', '1');
      cy.get('[data-cy="approved-stats"]').should('be.visible');
      cy.get('[data-cy="rejected-stats"]').should('be.visible');
      cy.get('[data-cy="total-stats"]').should('be.visible');
      
      // التحقق من عرض طلب التاجر
      cy.contains('متجر الاختبار 1').should('be.visible');
      cy.contains('تاجر الاختبار 1').should('be.visible');
      cy.contains('<EMAIL>').should('be.visible');
    });

    it('يجب أن يسمح بقبول التاجر', () => {
      cy.visit('/ar/admin/merchant-approvals');
      
      // النقر على مراجعة الطلب
      cy.get('[data-cy="review-application"]').first().click();
      
      // إضافة ملاحظات
      cy.get('[data-cy="approval-notes"]').type('تم قبول التاجر بعد مراجعة الوثائق');
      
      // النقر على قبول
      cy.get('[data-cy="approve-merchant"]').click();
      
      // التحقق من نجاح العملية
      cy.contains('تم قبول التاجر بنجاح').should('be.visible');
      
      // التحقق من تحديث الإحصائيات
      cy.get('[data-cy="pending-stats"]').should('contain', '0');
      cy.get('[data-cy="approved-stats"]').should('contain', '1');
    });

    it('يجب أن يسمح برفض التاجر', () => {
      cy.visit('/ar/admin/merchant-approvals');
      
      // النقر على مراجعة الطلب
      cy.get('[data-cy="review-application"]').first().click();
      
      // إضافة ملاحظات الرفض
      cy.get('[data-cy="approval-notes"]').type('الوثائق غير مكتملة');
      
      // النقر على رفض
      cy.get('[data-cy="reject-merchant"]').click();
      
      // التحقق من نجاح العملية
      cy.contains('تم رفض التاجر').should('be.visible');
      
      // التحقق من تحديث الإحصائيات
      cy.get('[data-cy="pending-stats"]').should('contain', '0');
      cy.get('[data-cy="rejected-stats"]').should('contain', '1');
    });

    it('يجب أن يمنع غير المدراء من الوصول', () => {
      // تسجيل دخول كعميل
      cy.loginAs('customer', {
        uid: 'customer-123',
        email: '<EMAIL>',
        userType: 'customer'
      });
      
      cy.visit('/ar/admin/merchant-approvals');
      
      // التحقق من التوجيه للصفحة الرئيسية
      cy.url().should('not.include', '/admin');
    });
  });

  describe('تحديد نوع المستخدم', () => {
    it('يجب أن يوجه التاجر المعتمد للوحة التحكم', () => {
      // تسجيل دخول كتاجر معتمد
      cy.loginAs('merchant', {
        uid: 'approved-merchant-123',
        email: '<EMAIL>',
        userType: 'merchant'
      });
      
      cy.mockStoreData({
        merchantUid: 'approved-merchant-123',
        approvalStatus: 'approved',
        isActive: true
      });
      
      cy.visit('/ar/merchant/dashboard');
      
      // التحقق من الوصول للوحة التحكم
      cy.url().should('include', '/merchant/dashboard');
      cy.contains('لوحة تحكم التاجر').should('be.visible');
    });

    it('يجب أن يمنع العميل من الوصول لصفحات التاجر', () => {
      // تسجيل دخول كعميل
      cy.loginAs('customer', {
        uid: 'customer-123',
        email: '<EMAIL>',
        userType: 'customer'
      });
      
      cy.visit('/ar/merchant/dashboard');
      
      // التحقق من التوجيه للوحة العميل
      cy.url().should('include', '/dashboard');
    });
  });

  describe('اختبار التكامل الشامل', () => {
    it('رحلة التاجر الكاملة: من التسجيل إلى الموافقة', () => {
      // 1. تسجيل التاجر
      cy.visit('/ar/signup');
      cy.signupAsMerchant('تاجر جديد', '<EMAIL>', 'password123');
      
      // 2. التحقق من صفحة انتظار الموافقة
      cy.url().should('include', '/merchant/pending-approval');
      cy.contains('طلبك قيد المراجعة').should('be.visible');
      
      // 3. تسجيل دخول المدير
      cy.logout();
      cy.loginAs('admin', {
        uid: 'admin-123',
        email: '<EMAIL>',
        userType: 'admin'
      });
      
      // 4. موافقة المدير على التاجر
      cy.visit('/ar/admin/merchant-approvals');
      cy.approveMerchant('<EMAIL>', 'تم قبول التاجر');
      
      // 5. تسجيل دخول التاجر مرة أخرى
      cy.logout();
      cy.loginAs('merchant', {
        uid: 'newmerchant-uid',
        email: '<EMAIL>',
        userType: 'merchant'
      });
      
      // 6. التحقق من الوصول للوحة التحكم
      cy.visit('/ar/merchant/dashboard');
      cy.url().should('include', '/merchant/dashboard');
      cy.contains('لوحة تحكم التاجر').should('be.visible');
    });
  });
});
