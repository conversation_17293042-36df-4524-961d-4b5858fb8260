#!/usr/bin/env node

/**
 * سكريبت إضافة الترجمات المفقودة ذات الأولوية العالية
 * يركز على الترجمات الأساسية والأكثر استخداماً
 */

const fs = require('fs');
const path = require('path');

// مسارات الملفات
const AR_FILE_PATH = path.join(__dirname, '..', 'src', 'locales', 'ar.json');
const EN_FILE_PATH = path.join(__dirname, '..', 'src', 'locales', 'en.json');
const BACKUP_DIR = path.join(__dirname, '..', 'src', 'locales');

/**
 * إنشاء نسخة احتياطية
 */
function createBackup(filePath, lang) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(BACKUP_DIR, `${lang}_backup_${timestamp}.json`);
    
    try {
        fs.copyFileSync(filePath, backupPath);
        console.log(`✅ نسخة احتياطية ${lang}: ${path.basename(backupPath)}`);
        return backupPath;
    } catch (error) {
        console.error(`❌ فشل في إنشاء النسخة الاحتياطية ${lang}: ${error.message}`);
        throw error;
    }
}

/**
 * تحميل ملف JSON
 */
function loadJsonFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error(`❌ فشل في تحميل الملف ${filePath}: ${error.message}`);
        throw error;
    }
}

/**
 * حفظ ملف JSON
 */
function saveJsonFile(filePath, data) {
    try {
        const content = JSON.stringify(data, null, 2);
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ تم حفظ الملف: ${path.basename(filePath)}`);
    } catch (error) {
        console.error(`❌ فشل في حفظ الملف ${filePath}: ${error.message}`);
        throw error;
    }
}

/**
 * الترجمات ذات الأولوية العالية للإنجليزية
 */
const HIGH_PRIORITY_EN_TRANSLATIONS = {
    // ترجمات أساسية مفقودة من common
    "representative": "Representative",
    "sar": "SAR",
    "status": "Status", 
    "processing": "Processing",
    "cancel": "Cancel",
    "loading": "Loading...",
    "close": "Close",
    "actions": "Actions",
    "category": "Category",
    "price": "Price",
    "stock": "Stock",
    "active": "Active",
    "inactive": "Inactive",
    "delete": "Delete",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "search": "Search",
    "filter": "Filter",
    "store": "Store",
    "available": "Available",
    "outOfStock": "Out of Stock",
    "address": "Address",
    "businessHours": "Business Hours",
    "totalOrders": "Total Orders",
    "productNotFound": "Product Not Found",
    "storeNotFound": "Store Not Found",
    "description": "Description",
    "moderationNotes": "Moderation Notes",
    
    // ترجمات المتاجر والمنتجات
    "reviews": "Reviews",
    "follow": "Follow",
    "share": "Share",
    "aboutStore": "About Store",
    "noDescriptionAvailable": "No description available",
    "trending": "Trending",
    "location": "Location",
    "errorFetchingProduct": "Error fetching product",
    "inStock": "In Stock",
    "viewStore": "View Store",
    "contactAvailable": "Contact Available",
    "websiteAvailable": "Website Available",
    
    // ترجمات التقييمات الأساسية
    "addReview": "Add Review",
    "writeReview": "Write Review", 
    "rateProduct": "Rate Product",
    
    // ترجمات السلة والتسوق
    "addToWishlist": "Add to Wishlist",
    "removeFromCart": "Remove from Cart",
    "updateQuantity": "Update Quantity",
    
    // ترجمات الإشعارات
    "notifications": "Notifications",
    "markAsRead": "Mark as Read",
    
    // ترجمات الدعم
    "faq": "FAQ",
    "reportIssue": "Report Issue"
};

/**
 * الترجمات ذات الأولوية العالية للعربية
 */
const HIGH_PRIORITY_AR_TRANSLATIONS = {
    // ترجمات التقييمات المفقودة
    "reviews.optional": "اختياري",
    "reviews.uploadImages": "رفع الصور",
    "reviews.uploadingImages": "جاري رفع الصور...",
    "reviews.imagesUploaded": "تم رفع الصور بنجاح",
    "reviews.imageUploadFailed": "فشل في رفع الصور",
    "reviews.maxImagesExceeded": "تم تجاوز الحد الأقصى للصور",
    "reviews.minCharacters": "الحد الأدنى للأحرف",
    "reviews.submitting": "جاري الإرسال...",
    "reviews.ratingLabels.1": "سيء جداً",
    "reviews.ratingLabels.2": "سيء",
    "reviews.ratingLabels.3": "متوسط",
    "reviews.ratingLabels.4": "جيد",
    "reviews.ratingLabels.5": "ممتاز",
    
    // ترجمات الخريطة
    "map.loading": "جاري تحميل الخريطة...",
    "map.error": "خطأ في الخريطة",
    "map.noLocation": "لا يوجد موقع",
    "map.enableLocation": "تمكين الموقع",
    "map.locationDenied": "تم رفض الوصول للموقع",
    "map.locationUnavailable": "الموقع غير متاح",
    "map.findStores": "البحث عن المتاجر",
    "map.nearbyStores": "المتاجر القريبة",
    "map.storeDetails": "تفاصيل المتجر",
    "map.getDirections": "الحصول على الاتجاهات",
    "map.distance": "المسافة",
    "map.estimatedTime": "الوقت المقدر",
    
    // ترجمات المتاجر
    "stores.featured": "متاجر مميزة",
    "stores.popular": "متاجر شائعة",
    "stores.newest": "أحدث المتاجر",
    "stores.topRated": "الأعلى تقييماً",
    "stores.openNow": "مفتوح الآن",
    "stores.closedNow": "مغلق الآن"
};

/**
 * إضافة الترجمات المفقودة
 */
function addMissingTranslations(data, translations, lang) {
    let addedCount = 0;
    const updatedData = { ...data };
    
    console.log(`\n🔄 إضافة الترجمات المفقودة للغة ${lang}...`);
    
    Object.entries(translations).forEach(([key, value]) => {
        if (!updatedData.hasOwnProperty(key)) {
            updatedData[key] = value;
            addedCount++;
            console.log(`✅ أضيف: "${key}" = "${value}"`);
        } else {
            console.log(`⚠️  موجود مسبقاً: "${key}"`);
        }
    });
    
    console.log(`📊 تم إضافة ${addedCount} ترجمة جديدة للغة ${lang}`);
    return { updatedData, addedCount };
}

/**
 * الدالة الرئيسية
 */
async function main() {
    console.log('🚀 بدء إضافة الترجمات ذات الأولوية العالية...\n');
    
    try {
        // إنشاء نسخ احتياطية
        console.log('📋 إنشاء نسخ احتياطية...');
        createBackup(AR_FILE_PATH, 'ar');
        createBackup(EN_FILE_PATH, 'en');
        
        // تحميل الملفات
        console.log('\n📖 تحميل ملفات الترجمة...');
        const arData = loadJsonFile(AR_FILE_PATH);
        const enData = loadJsonFile(EN_FILE_PATH);
        
        console.log(`📊 المفاتيح الحالية - العربية: ${Object.keys(arData).length}`);
        console.log(`📊 المفاتيح الحالية - الإنجليزية: ${Object.keys(enData).length}`);
        
        // إضافة الترجمات الإنجليزية
        const { updatedData: newEnData, addedCount: enAddedCount } = 
            addMissingTranslations(enData, HIGH_PRIORITY_EN_TRANSLATIONS, 'الإنجليزية');
        
        // إضافة الترجمات العربية
        const { updatedData: newArData, addedCount: arAddedCount } = 
            addMissingTranslations(arData, HIGH_PRIORITY_AR_TRANSLATIONS, 'العربية');
        
        // حفظ الملفات المحدثة
        if (enAddedCount > 0) {
            console.log('\n💾 حفظ الملف الإنجليزي المحدث...');
            saveJsonFile(EN_FILE_PATH, newEnData);
        }
        
        if (arAddedCount > 0) {
            console.log('\n💾 حفظ الملف العربي المحدث...');
            saveJsonFile(AR_FILE_PATH, newArData);
        }
        
        // ملخص النتائج
        console.log('\n📊 ملخص النتائج:');
        console.log(`✅ ترجمات إنجليزية مضافة: ${enAddedCount}`);
        console.log(`✅ ترجمات عربية مضافة: ${arAddedCount}`);
        console.log(`✅ إجمالي الترجمات المضافة: ${enAddedCount + arAddedCount}`);
        
        console.log(`📊 المفاتيح الجديدة - العربية: ${Object.keys(newArData).length}`);
        console.log(`📊 المفاتيح الجديدة - الإنجليزية: ${Object.keys(newEnData).length}`);
        
        if (enAddedCount + arAddedCount > 0) {
            console.log('\n🎉 تم إضافة الترجمات ذات الأولوية العالية بنجاح!');
            console.log('💡 يُنصح بتشغيل سكريبت التحقق للتأكد من النتائج');
        } else {
            console.log('\n✅ جميع الترجمات ذات الأولوية العالية موجودة مسبقاً');
        }
        
    } catch (error) {
        console.error(`\n❌ خطأ في إضافة الترجمات: ${error.message}`);
        process.exit(1);
    }
}

// تشغيل السكريبت
if (require.main === module) {
    main();
}

module.exports = {
    HIGH_PRIORITY_EN_TRANSLATIONS,
    HIGH_PRIORITY_AR_TRANSLATIONS,
    addMissingTranslations
};
