// src/components/auth/SessionManager.tsx
"use client";

import { useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged, signOut } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useLocale } from '@/hooks/use-locale';

interface SessionManagerProps {
  locale: string;
}

export default function SessionManager({ locale }: SessionManagerProps) {
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const { t } = useLocale();

  useEffect(() => {
    let sessionTimeout: NodeJS.Timeout;
    let warningTimeout: NodeJS.Timeout;

    const resetSessionTimer = () => {
      // مسح المؤقتات السابقة
      if (sessionTimeout) clearTimeout(sessionTimeout);
      if (warningTimeout) clearTimeout(warningTimeout);

      if (user) {
        // تحذير بعد 25 دقيقة من عدم النشاط
        warningTimeout = setTimeout(() => {
          toast({
            title: 'تحذير انتهاء الجلسة',
            description: 'ستنتهي جلستك خلال 5 دقائق بسبب عدم النشاط.',
            variant: 'default',
          });
        }, 25 * 60 * 1000); // 25 دقيقة

        // تسجيل خروج تلقائي بعد 30 دقيقة من عدم النشاط
        sessionTimeout = setTimeout(async () => {
          try {
            await signOut(auth);
            
            // مسح البيانات المحلية
            if (typeof window !== 'undefined') {
              localStorage.removeItem('firebase:authUser');
              sessionStorage.clear();
            }
            
            toast({
              title: t('logoutSuccessTitle'),
              description: 'تم تسجيل خروجك تلقائياً بسبب عدم النشاط.',
              variant: 'default',
            });
            
            router.replace(`/${locale}/login`);
            
          } catch (error) {
            console.error('Auto logout error:', error);
            // في حالة فشل تسجيل الخروج، قم بمسح البيانات وإعادة التوجيه
            if (typeof window !== 'undefined') {
              localStorage.clear();
              sessionStorage.clear();
            }
            router.replace(`/${locale}/login`);
            window.location.reload();
          }
        }, 30 * 60 * 1000); // 30 دقيقة
      }
    };

    // إعادة تعيين المؤقت عند تغيير حالة المصادقة
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (currentUser) {
        resetSessionTimer();
      } else {
        // مسح المؤقتات عند تسجيل الخروج
        if (sessionTimeout) clearTimeout(sessionTimeout);
        if (warningTimeout) clearTimeout(warningTimeout);
      }
    });

    // إعادة تعيين المؤقت عند النشاط
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      if (user) {
        resetSessionTimer();
      }
    };

    // إضافة مستمعي الأحداث
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // تنظيف عند إلغاء التحميل
    return () => {
      unsubscribe();
      if (sessionTimeout) clearTimeout(sessionTimeout);
      if (warningTimeout) clearTimeout(warningTimeout);
      
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [user, router, toast, t, locale]);

  return null; // هذا المكون لا يعرض أي شيء
}
