'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useLocale } from '@/hooks/use-locale';
import { 
  Upload,
  Image as ImageIcon,
  Palette,
  Type,
  Globe
} from 'lucide-react';

interface GeneralSettingsProps {
  onSettingsChange: () => void;
}

export function GeneralSettings({ onSettingsChange }: GeneralSettingsProps) {
  const { t } = useLocale();
  const [settings, setSettings] = useState({
    platformName: 'مِخْلاة',
    platformNameEn: 'Mikhla',
    platformDescription: 'منصة التجارة الإلكترونية المحلية الرائدة',
    platformDescriptionEn: 'Leading local e-commerce platform',
    logoUrl: '/logo.png',
    faviconUrl: '/favicon.ico',
    primaryColor: '#3B82F6',
    secondaryColor: '#10B981',
    accentColor: '#F59E0B',
    fontFamily: 'Cairo',
    supportEmail: '<EMAIL>',
    supportPhone: '+966501234567',
    address: 'الرياض، المملكة العربية السعودية',
    socialMedia: {
      twitter: '@mikhla_sa',
      instagram: '@mikhla_sa',
      facebook: 'mikhla.sa',
      linkedin: 'company/mikhla'
    }
  });

  const handleInputChange = (field: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    onSettingsChange();
  };

  const handleSocialMediaChange = (platform: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      socialMedia: {
        ...prev.socialMedia,
        [platform]: value
      }
    }));
    onSettingsChange();
  };

  const handleLogoUpload = (type: 'logo' | 'favicon') => {
    // محاكاة رفع الملف
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const url = e.target?.result as string;
          if (type === 'logo') {
            handleInputChange('logoUrl', url);
          } else {
            handleInputChange('faviconUrl', url);
          }
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  return (
    <div className="space-y-6">
      {/* معلومات المنصة الأساسية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            معلومات المنصة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="platformName">اسم المنصة (عربي)</Label>
              <Input
                id="platformName"
                value={settings.platformName}
                onChange={(e) => handleInputChange('platformName', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="platformNameEn">اسم المنصة (إنجليزي)</Label>
              <Input
                id="platformNameEn"
                value={settings.platformNameEn}
                onChange={(e) => handleInputChange('platformNameEn', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="platformDescription">وصف المنصة (عربي)</Label>
            <Textarea
              id="platformDescription"
              value={settings.platformDescription}
              onChange={(e) => handleInputChange('platformDescription', e.target.value)}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="platformDescriptionEn">وصف المنصة (إنجليزي)</Label>
            <Textarea
              id="platformDescriptionEn"
              value={settings.platformDescriptionEn}
              onChange={(e) => handleInputChange('platformDescriptionEn', e.target.value)}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* الشعار والأيقونات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            الشعار والأيقونات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الشعار */}
            <div className="space-y-3">
              <Label>شعار المنصة</Label>
              <div className="flex items-center gap-4">
                <img
                  src={settings.logoUrl}
                  alt="شعار المنصة"
                  className="w-16 h-16 object-contain border rounded-lg"
                />
                <Button
                  variant="outline"
                  onClick={() => handleLogoUpload('logo')}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  تغيير الشعار
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                الحد الأقصى: 2MB، الأبعاد المفضلة: 200x200px
              </p>
            </div>

            {/* الأيقونة المفضلة */}
            <div className="space-y-3">
              <Label>الأيقونة المفضلة (Favicon)</Label>
              <div className="flex items-center gap-4">
                <img
                  src={settings.faviconUrl}
                  alt="الأيقونة المفضلة"
                  className="w-8 h-8 object-contain border rounded"
                />
                <Button
                  variant="outline"
                  onClick={() => handleLogoUpload('favicon')}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  تغيير الأيقونة
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                الحد الأقصى: 1MB، الأبعاد المفضلة: 32x32px
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الألوان والخطوط */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            الألوان والخطوط
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primaryColor">اللون الأساسي</Label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  id="primaryColor"
                  value={settings.primaryColor}
                  onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                  className="w-12 h-10 border rounded cursor-pointer"
                />
                <Input
                  value={settings.primaryColor}
                  onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                  placeholder="#3B82F6"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryColor">اللون الثانوي</Label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  id="secondaryColor"
                  value={settings.secondaryColor}
                  onChange={(e) => handleInputChange('secondaryColor', e.target.value)}
                  className="w-12 h-10 border rounded cursor-pointer"
                />
                <Input
                  value={settings.secondaryColor}
                  onChange={(e) => handleInputChange('secondaryColor', e.target.value)}
                  placeholder="#10B981"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accentColor">لون التمييز</Label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  id="accentColor"
                  value={settings.accentColor}
                  onChange={(e) => handleInputChange('accentColor', e.target.value)}
                  className="w-12 h-10 border rounded cursor-pointer"
                />
                <Input
                  value={settings.accentColor}
                  onChange={(e) => handleInputChange('accentColor', e.target.value)}
                  placeholder="#F59E0B"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="fontFamily">عائلة الخط</Label>
            <select
              id="fontFamily"
              value={settings.fontFamily}
              onChange={(e) => handleInputChange('fontFamily', e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="Cairo">Cairo</option>
              <option value="Tajawal">Tajawal</option>
              <option value="Amiri">Amiri</option>
              <option value="Noto Sans Arabic">Noto Sans Arabic</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* معلومات الاتصال */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات الاتصال</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="supportEmail">بريد الدعم الفني</Label>
              <Input
                id="supportEmail"
                type="email"
                value={settings.supportEmail}
                onChange={(e) => handleInputChange('supportEmail', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supportPhone">هاتف الدعم الفني</Label>
              <Input
                id="supportPhone"
                value={settings.supportPhone}
                onChange={(e) => handleInputChange('supportPhone', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">العنوان</Label>
            <Input
              id="address"
              value={settings.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* وسائل التواصل الاجتماعي */}
      <Card>
        <CardHeader>
          <CardTitle>وسائل التواصل الاجتماعي</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="twitter">تويتر</Label>
              <Input
                id="twitter"
                value={settings.socialMedia.twitter}
                onChange={(e) => handleSocialMediaChange('twitter', e.target.value)}
                placeholder="@mikhla_sa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="instagram">إنستغرام</Label>
              <Input
                id="instagram"
                value={settings.socialMedia.instagram}
                onChange={(e) => handleSocialMediaChange('instagram', e.target.value)}
                placeholder="@mikhla_sa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="facebook">فيسبوك</Label>
              <Input
                id="facebook"
                value={settings.socialMedia.facebook}
                onChange={(e) => handleSocialMediaChange('facebook', e.target.value)}
                placeholder="mikhla.sa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="linkedin">لينكد إن</Label>
              <Input
                id="linkedin"
                value={settings.socialMedia.linkedin}
                onChange={(e) => handleSocialMediaChange('linkedin', e.target.value)}
                placeholder="company/mikhla"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
