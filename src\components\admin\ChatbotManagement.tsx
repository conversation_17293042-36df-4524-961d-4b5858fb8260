"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Bot,
  MessageSquare,
  TrendingUp,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Edit,
  Trash2,
  Download,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import IntelligentChatbotService, { KnowledgeBase, ChatIntent } from '@/services/intelligentChatbotService';

interface ChatbotStats {
  totalSessions: number;
  totalMessages: number;
  averageConfidence: number;
  escalationRate: number;
  topIntents: { intent: string; count: number }[];
}

/**
 * لوحة إدارة الشات بوت للمسؤولين
 * إدارة شاملة لقاعدة المعرفة والإحصائيات
 */
const ChatbotManagement: React.FC = () => {
  const [stats, setStats] = useState<ChatbotStats>({
    totalSessions: 0,
    totalMessages: 0,
    averageConfidence: 0,
    escalationRate: 0,
    topIntents: []
  });
  
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase[]>([]);
  const [selectedKnowledge, setSelectedKnowledge] = useState<KnowledgeBase | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // نموذج إضافة/تعديل المعرفة
  const [knowledgeForm, setKnowledgeForm] = useState({
    question: '',
    answer: '',
    category: '',
    keywords: '',
    language: 'ar' as 'ar' | 'en',
    priority: 1,
    isActive: true
  });

  useEffect(() => {
    loadChatbotData();
  }, []);

  const loadChatbotData = async () => {
    setIsLoading(true);
    try {
      // تحميل الإحصائيات
      const statsData = await IntelligentChatbotService.getChatbotStats();
      setStats(statsData);

      // تحميل قاعدة المعرفة (محاكاة)
      const mockKnowledgeBase: KnowledgeBase[] = [
        {
          id: '1',
          question: 'ما هي مِخْلاة؟',
          answer: 'مِخْلاة هي منصة تجارة إلكترونية محلية تربط بين التجار والعملاء في المملكة العربية السعودية.',
          category: 'عام',
          keywords: ['مخلاة', 'منصة', 'تعريف'],
          language: 'ar',
          priority: 1,
          isActive: true
        },
        {
          id: '2',
          question: 'كيف أسجل كتاجر؟',
          answer: 'يمكنك التسجيل كتاجر من خلال الضغط على "تسجيل تاجر" وملء البيانات المطلوبة.',
          category: 'تسجيل',
          keywords: ['تسجيل', 'تاجر', 'حساب'],
          language: 'ar',
          priority: 1,
          isActive: true
        }
      ];
      setKnowledgeBase(mockKnowledgeBase);

    } catch (error) {
      console.error('خطأ في تحميل بيانات الشات بوت:', error);
      toast.error('حدث خطأ في تحميل البيانات');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveKnowledge = async () => {
    if (!knowledgeForm.question.trim() || !knowledgeForm.answer.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      const newKnowledge: KnowledgeBase = {
        id: isEditing ? selectedKnowledge!.id : Date.now().toString(),
        question: knowledgeForm.question.trim(),
        answer: knowledgeForm.answer.trim(),
        category: knowledgeForm.category.trim(),
        keywords: knowledgeForm.keywords.split(',').map(k => k.trim()).filter(k => k),
        language: knowledgeForm.language,
        priority: knowledgeForm.priority,
        isActive: knowledgeForm.isActive
      };

      if (isEditing) {
        // تحديث المعرفة الموجودة
        setKnowledgeBase(prev => 
          prev.map(kb => kb.id === newKnowledge.id ? newKnowledge : kb)
        );
        toast.success('تم تحديث المعرفة بنجاح');
      } else {
        // إضافة معرفة جديدة
        setKnowledgeBase(prev => [...prev, newKnowledge]);
        toast.success('تم إضافة المعرفة بنجاح');
      }

      // إعادة تعيين النموذج
      resetForm();

    } catch (error) {
      console.error('خطأ في حفظ المعرفة:', error);
      toast.error('حدث خطأ في حفظ المعرفة');
    }
  };

  const handleEditKnowledge = (knowledge: KnowledgeBase) => {
    setSelectedKnowledge(knowledge);
    setKnowledgeForm({
      question: knowledge.question,
      answer: knowledge.answer,
      category: knowledge.category,
      keywords: knowledge.keywords.join(', '),
      language: knowledge.language,
      priority: knowledge.priority,
      isActive: knowledge.isActive
    });
    setIsEditing(true);
  };

  const handleDeleteKnowledge = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه المعرفة؟')) return;

    try {
      setKnowledgeBase(prev => prev.filter(kb => kb.id !== id));
      toast.success('تم حذف المعرفة بنجاح');
    } catch (error) {
      console.error('خطأ في حذف المعرفة:', error);
      toast.error('حدث خطأ في حذف المعرفة');
    }
  };

  const resetForm = () => {
    setKnowledgeForm({
      question: '',
      answer: '',
      category: '',
      keywords: '',
      language: 'ar',
      priority: 1,
      isActive: true
    });
    setSelectedKnowledge(null);
    setIsEditing(false);
  };

  const exportKnowledgeBase = () => {
    const dataStr = JSON.stringify(knowledgeBase, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `knowledge-base-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success('تم تصدير قاعدة المعرفة');
  };

  return (
    <div className="space-y-6">
      {/* إحصائيات الشات بوت */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الجلسات</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalSessions.toLocaleString()}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الرسائل</p>
                <p className="text-2xl font-bold text-green-600">{stats.totalMessages.toLocaleString()}</p>
              </div>
              <Users className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط الدقة</p>
                <p className="text-2xl font-bold text-purple-600">{Math.round(stats.averageConfidence * 100)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معدل التصعيد</p>
                <p className="text-2xl font-bold text-orange-600">{Math.round(stats.escalationRate * 100)}%</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* إدارة الشات بوت */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                <Bot className="w-6 h-6" />
                <span>إدارة الشات بوت الذكي</span>
              </CardTitle>
              <CardDescription>
                إدارة قاعدة المعرفة والإعدادات المتقدمة للشات بوت
              </CardDescription>
            </div>
            <div className="flex space-x-2 rtl:space-x-reverse">
              <Button onClick={loadChatbotData} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                تحديث
              </Button>
              <Button onClick={exportKnowledgeBase} variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                تصدير
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="knowledge" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="knowledge">قاعدة المعرفة</TabsTrigger>
              <TabsTrigger value="analytics">التحليلات</TabsTrigger>
              <TabsTrigger value="settings">الإعدادات</TabsTrigger>
            </TabsList>

            {/* قاعدة المعرفة */}
            <TabsContent value="knowledge" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                {/* نموذج إضافة/تعديل المعرفة */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {isEditing ? 'تعديل المعرفة' : 'إضافة معرفة جديدة'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="question">السؤال</Label>
                      <Input
                        id="question"
                        value={knowledgeForm.question}
                        onChange={(e) => setKnowledgeForm(prev => ({ ...prev, question: e.target.value }))}
                        placeholder="أدخل السؤال..."
                      />
                    </div>

                    <div>
                      <Label htmlFor="answer">الإجابة</Label>
                      <Textarea
                        id="answer"
                        value={knowledgeForm.answer}
                        onChange={(e) => setKnowledgeForm(prev => ({ ...prev, answer: e.target.value }))}
                        placeholder="أدخل الإجابة..."
                        rows={4}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="category">الفئة</Label>
                        <Input
                          id="category"
                          value={knowledgeForm.category}
                          onChange={(e) => setKnowledgeForm(prev => ({ ...prev, category: e.target.value }))}
                          placeholder="فئة السؤال..."
                        />
                      </div>

                      <div>
                        <Label htmlFor="language">اللغة</Label>
                        <Select
                          value={knowledgeForm.language}
                          onValueChange={(value: 'ar' | 'en') => setKnowledgeForm(prev => ({ ...prev, language: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ar">العربية</SelectItem>
                            <SelectItem value="en">English</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="keywords">الكلمات المفتاحية</Label>
                      <Input
                        id="keywords"
                        value={knowledgeForm.keywords}
                        onChange={(e) => setKnowledgeForm(prev => ({ ...prev, keywords: e.target.value }))}
                        placeholder="كلمة1, كلمة2, كلمة3..."
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Switch
                          id="isActive"
                          checked={knowledgeForm.isActive}
                          onCheckedChange={(checked) => setKnowledgeForm(prev => ({ ...prev, isActive: checked }))}
                        />
                        <Label htmlFor="isActive">نشط</Label>
                      </div>

                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Label htmlFor="priority">الأولوية:</Label>
                        <Select
                          value={knowledgeForm.priority.toString()}
                          onValueChange={(value) => setKnowledgeForm(prev => ({ ...prev, priority: parseInt(value) }))}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1</SelectItem>
                            <SelectItem value="2">2</SelectItem>
                            <SelectItem value="3">3</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex space-x-2 rtl:space-x-reverse">
                      <Button onClick={handleSaveKnowledge} className="flex-1">
                        {isEditing ? 'تحديث' : 'إضافة'}
                      </Button>
                      {isEditing && (
                        <Button onClick={resetForm} variant="outline">
                          إلغاء
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* قائمة المعرفة الموجودة */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">قاعدة المعرفة الحالية</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {knowledgeBase.map((knowledge) => (
                        <div key={knowledge.id} className="border rounded-lg p-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{knowledge.question}</h4>
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                {knowledge.answer}
                              </p>
                              <div className="flex items-center space-x-2 rtl:space-x-reverse mt-2">
                                <Badge variant="secondary" className="text-xs">
                                  {knowledge.category}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {knowledge.language === 'ar' ? 'عربي' : 'English'}
                                </Badge>
                                {knowledge.isActive ? (
                                  <CheckCircle className="w-3 h-3 text-green-500" />
                                ) : (
                                  <XCircle className="w-3 h-3 text-red-500" />
                                )}
                              </div>
                            </div>
                            <div className="flex space-x-1 rtl:space-x-reverse ml-2">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleEditKnowledge(knowledge)}
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleDeleteKnowledge(knowledge.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* التحليلات */}
            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>أهم الاستفسارات</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stats.topIntents.map((intent, index) => (
                        <div key={intent.intent} className="flex items-center justify-between">
                          <span className="text-sm">{intent.intent}</span>
                          <Badge variant="secondary">{intent.count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>إحصائيات الأداء</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">معدل الرضا</span>
                        <span className="font-bold text-green-600">92%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">متوسط وقت الاستجابة</span>
                        <span className="font-bold">1.2 ثانية</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">معدل حل المشاكل</span>
                        <span className="font-bold text-blue-600">87%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* الإعدادات */}
            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>إعدادات الشات بوت</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>تفعيل الشات بوت</Label>
                      <p className="text-sm text-gray-600">تشغيل/إيقاف الشات بوت للمستخدمين</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>التصعيد التلقائي</Label>
                      <p className="text-sm text-gray-600">تحويل المحادثات المعقدة للدعم البشري</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>حفظ المحادثات</Label>
                      <p className="text-sm text-gray-600">حفظ سجل المحادثات للتحليل</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChatbotManagement;
