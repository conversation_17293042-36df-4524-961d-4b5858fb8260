import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, query, where, orderBy, limit, serverTimestamp } from 'firebase/firestore';

// أنواع البيانات للشات بوت
export interface ChatMessage {
  id: string;
  sessionId: string;
  userId?: string;
  message: string;
  response: string;
  intent: string;
  confidence: number;
  timestamp: Date;
  isResolved: boolean;
  escalatedToHuman: boolean;
  language: 'ar' | 'en';
}

export interface ChatSession {
  id: string;
  userId?: string;
  startTime: Date;
  endTime?: Date;
  messageCount: number;
  isActive: boolean;
  escalatedToHuman: boolean;
  satisfactionRating?: number;
  tags: string[];
}

export interface ChatIntent {
  name: string;
  patterns: string[];
  responses: string[];
  requiresHuman: boolean;
  category: string;
  confidence_threshold: number;
}

export interface KnowledgeBase {
  id: string;
  question: string;
  answer: string;
  category: string;
  keywords: string[];
  language: 'ar' | 'en';
  priority: number;
  isActive: boolean;
}

/**
 * خدمة الشات بوت الذكي المتقدم
 * نظام محادثة ذكي يدعم العربية والإنجليزية مع تكامل كامل
 */
class IntelligentChatbotService {
  private static readonly CONFIDENCE_THRESHOLD = 0.7;
  private static readonly MAX_SESSION_DURATION = 30 * 60 * 1000; // 30 دقيقة
  private static readonly ESCALATION_KEYWORDS = [
    'مشكلة', 'شكوى', 'مدير', 'مسؤول', 'خطأ', 'عطل',
    'problem', 'complaint', 'manager', 'supervisor', 'error', 'bug'
  ];

  // قاعدة المعرفة الأساسية
  private static readonly KNOWLEDGE_BASE: KnowledgeBase[] = [
    {
      id: '1',
      question: 'ما هي مِخْلاة؟',
      answer: 'مِخْلاة هي منصة تجارة إلكترونية محلية تربط بين التجار والعملاء في المملكة العربية السعودية. نوفر خدمات التوصيل السريع والدفع الآمن.',
      category: 'عام',
      keywords: ['مخلاة', 'منصة', 'تعريف', 'ما هي'],
      language: 'ar',
      priority: 1,
      isActive: true
    },
    {
      id: '2',
      question: 'كيف أسجل كتاجر؟',
      answer: 'يمكنك التسجيل كتاجر من خلال الضغط على "تسجيل تاجر" في الصفحة الرئيسية، ثم ملء البيانات المطلوبة وتحميل المستندات. سيتم مراجعة طلبك خلال 24-48 ساعة.',
      category: 'تسجيل',
      keywords: ['تسجيل', 'تاجر', 'حساب', 'انضمام'],
      language: 'ar',
      priority: 1,
      isActive: true
    },
    {
      id: '3',
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نوفر عدة طرق دفع: الدفع نقداً عند التسليم، بطاقات مدى، فيزا، ماستركارد، Apple Pay، Google Pay، STC Pay، والدفع بالتقسيط عبر تمارا وتابي.',
      category: 'دفع',
      keywords: ['دفع', 'طرق الدفع', 'مدى', 'فيزا', 'تقسيط'],
      language: 'ar',
      priority: 1,
      isActive: true
    },
    {
      id: '4',
      question: 'كم تستغرق عملية التوصيل؟',
      answer: 'نوفر توصيل سريع خلال 30-60 دقيقة داخل المدن الرئيسية، و2-4 ساعات للمناطق الأخرى. يمكنك تتبع طلبك في الوقت الفعلي.',
      category: 'توصيل',
      keywords: ['توصيل', 'وقت', 'سرعة', 'تتبع'],
      language: 'ar',
      priority: 1,
      isActive: true
    },
    {
      id: '5',
      question: 'How do I track my order?',
      answer: 'You can track your order in real-time through the "My Orders" section in your account, or by using the tracking link sent to your phone via SMS.',
      category: 'orders',
      keywords: ['track', 'order', 'status', 'delivery'],
      language: 'en',
      priority: 1,
      isActive: true
    }
  ];

  // الأنماط والاستجابات الذكية
  private static readonly INTENTS: ChatIntent[] = [
    {
      name: 'greeting',
      patterns: [
        'مرحبا', 'السلام عليكم', 'أهلا', 'صباح الخير', 'مساء الخير',
        'hello', 'hi', 'good morning', 'good evening', 'hey'
      ],
      responses: [
        'مرحباً بك في مِخْلاة! كيف يمكنني مساعدتك اليوم؟',
        'أهلاً وسهلاً! أنا هنا لمساعدتك في أي استفسار.',
        'Hello! Welcome to Mikhla. How can I help you today?'
      ],
      requiresHuman: false,
      category: 'greeting',
      confidence_threshold: 0.8
    },
    {
      name: 'order_status',
      patterns: [
        'حالة الطلب', 'أين طلبي', 'تتبع الطلب', 'وين الطلب',
        'order status', 'where is my order', 'track order', 'delivery status'
      ],
      responses: [
        'يمكنك تتبع طلبك من خلال قسم "طلباتي" في حسابك، أو باستخدام رقم الطلب.',
        'لتتبع طلبك، يرجى تزويدي برقم الطلب وسأساعدك فوراً.'
      ],
      requiresHuman: false,
      category: 'orders',
      confidence_threshold: 0.7
    },
    {
      name: 'payment_issue',
      patterns: [
        'مشكلة في الدفع', 'فشل الدفع', 'خطأ في الدفعة', 'لم يتم الدفع',
        'payment failed', 'payment error', 'payment issue', 'transaction failed'
      ],
      responses: [
        'أعتذر عن هذه المشكلة. سأقوم بتحويلك إلى فريق الدعم المختص لحل مشكلة الدفع فوراً.',
        'مشاكل الدفع تحتاج اهتمام خاص. دعني أحولك إلى مختص.'
      ],
      requiresHuman: true,
      category: 'payment',
      confidence_threshold: 0.6
    },
    {
      name: 'complaint',
      patterns: [
        'شكوى', 'مشكلة', 'غير راضي', 'خدمة سيئة', 'أريد مدير',
        'complaint', 'problem', 'issue', 'bad service', 'manager'
      ],
      responses: [
        'أعتذر بشدة عن أي إزعاج. سأقوم بتحويلك فوراً إلى مدير خدمة العملاء لحل مشكلتك.',
        'شكواك مهمة جداً بالنسبة لنا. دعني أحولك إلى المختص المناسب.'
      ],
      requiresHuman: true,
      category: 'complaint',
      confidence_threshold: 0.5
    }
  ];

  /**
   * معالجة رسالة المستخدم وإرجاع الرد المناسب
   */
  static async processMessage(
    message: string,
    sessionId: string,
    userId?: string
  ): Promise<{
    response: string;
    intent: string;
    confidence: number;
    requiresHuman: boolean;
    suggestions?: string[];
  }> {
    try {
      console.log('🤖 معالجة رسالة:', message);

      // تنظيف وتحليل الرسالة
      const cleanMessage = this.cleanMessage(message);
      const language = this.detectLanguage(cleanMessage);

      // البحث في قاعدة المعرفة أولاً
      const knowledgeResult = await this.searchKnowledgeBase(cleanMessage, language);
      if (knowledgeResult.confidence > 0.8) {
        await this.saveChatMessage(sessionId, userId, message, knowledgeResult.answer, 'knowledge_base', knowledgeResult.confidence, language);
        return {
          response: knowledgeResult.answer,
          intent: 'knowledge_base',
          confidence: knowledgeResult.confidence,
          requiresHuman: false,
          suggestions: this.generateSuggestions(language)
        };
      }

      // تحليل النية (Intent Recognition)
      const intentResult = this.analyzeIntent(cleanMessage);
      
      // اختيار الرد المناسب
      const response = this.selectResponse(intentResult, language);
      
      // حفظ المحادثة
      await this.saveChatMessage(sessionId, userId, message, response, intentResult.intent, intentResult.confidence, language);

      // فحص الحاجة للتصعيد
      const needsEscalation = intentResult.requiresHuman || this.checkEscalationKeywords(cleanMessage);

      if (needsEscalation) {
        await this.escalateToHuman(sessionId, userId, message);
      }

      return {
        response,
        intent: intentResult.intent,
        confidence: intentResult.confidence,
        requiresHuman: needsEscalation,
        suggestions: this.generateSuggestions(language)
      };

    } catch (error) {
      console.error('خطأ في معالجة الرسالة:', error);
      return {
        response: 'أعتذر، حدث خطأ تقني. سأقوم بتحويلك إلى أحد المختصين.',
        intent: 'error',
        confidence: 0,
        requiresHuman: true
      };
    }
  }

  /**
   * تنظيف الرسالة من الرموز والمسافات الزائدة
   */
  private static cleanMessage(message: string): string {
    return message
      .trim()
      .toLowerCase()
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, ' ');
  }

  /**
   * كشف لغة الرسالة
   */
  private static detectLanguage(message: string): 'ar' | 'en' {
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicPattern.test(message) ? 'ar' : 'en';
  }

  /**
   * البحث في قاعدة المعرفة
   */
  private static async searchKnowledgeBase(
    message: string, 
    language: 'ar' | 'en'
  ): Promise<{ answer: string; confidence: number }> {
    const relevantKnowledge = this.KNOWLEDGE_BASE.filter(
      kb => kb.language === language && kb.isActive
    );

    let bestMatch = { answer: '', confidence: 0 };

    for (const kb of relevantKnowledge) {
      const confidence = this.calculateSimilarity(message, kb.question, kb.keywords);
      if (confidence > bestMatch.confidence) {
        bestMatch = { answer: kb.answer, confidence };
      }
    }

    return bestMatch;
  }

  /**
   * حساب التشابه بين النصوص
   */
  private static calculateSimilarity(message: string, question: string, keywords: string[]): number {
    const messageWords = message.split(' ');
    const questionWords = question.toLowerCase().split(' ');
    
    let matches = 0;
    let totalWords = messageWords.length;

    // فحص التطابق مع الكلمات المفتاحية
    for (const keyword of keywords) {
      if (message.includes(keyword.toLowerCase())) {
        matches += 2; // وزن أعلى للكلمات المفتاحية
      }
    }

    // فحص التطابق مع كلمات السؤال
    for (const word of messageWords) {
      if (questionWords.includes(word)) {
        matches += 1;
      }
    }

    return Math.min(matches / totalWords, 1);
  }

  /**
   * تحليل نية المستخدم
   */
  private static analyzeIntent(message: string): { intent: string; confidence: number; requiresHuman: boolean } {
    let bestIntent = { intent: 'unknown', confidence: 0, requiresHuman: false };

    for (const intent of this.INTENTS) {
      let confidence = 0;
      
      for (const pattern of intent.patterns) {
        if (message.includes(pattern.toLowerCase())) {
          confidence = Math.max(confidence, 0.9);
        }
      }

      if (confidence > bestIntent.confidence && confidence >= intent.confidence_threshold) {
        bestIntent = {
          intent: intent.name,
          confidence,
          requiresHuman: intent.requiresHuman
        };
      }
    }

    return bestIntent;
  }

  /**
   * اختيار الرد المناسب
   */
  private static selectResponse(intentResult: { intent: string; confidence: number }, language: 'ar' | 'en'): string {
    const intent = this.INTENTS.find(i => i.name === intentResult.intent);
    
    if (intent && intent.responses.length > 0) {
      // اختيار رد عشوائي من الردود المتاحة
      const randomIndex = Math.floor(Math.random() * intent.responses.length);
      return intent.responses[randomIndex];
    }

    // رد افتراضي
    return language === 'ar' 
      ? 'شكراً لتواصلك معنا. يمكنني مساعدتك في الاستفسارات العامة، أو تحويلك إلى مختص إذا كنت تحتاج مساعدة خاصة.'
      : 'Thank you for contacting us. I can help with general inquiries or connect you with a specialist if needed.';
  }

  /**
   * فحص كلمات التصعيد
   */
  private static checkEscalationKeywords(message: string): boolean {
    return this.ESCALATION_KEYWORDS.some(keyword => 
      message.includes(keyword.toLowerCase())
    );
  }

  /**
   * توليد اقتراحات للمستخدم
   */
  private static generateSuggestions(language: 'ar' | 'en'): string[] {
    if (language === 'ar') {
      return [
        'كيف أتتبع طلبي؟',
        'ما هي طرق الدفع المتاحة؟',
        'كيف أسجل كتاجر؟',
        'كم يستغرق التوصيل؟'
      ];
    } else {
      return [
        'How do I track my order?',
        'What payment methods are available?',
        'How do I register as a merchant?',
        'How long does delivery take?'
      ];
    }
  }

  /**
   * حفظ رسالة المحادثة
   */
  private static async saveChatMessage(
    sessionId: string,
    userId: string | undefined,
    message: string,
    response: string,
    intent: string,
    confidence: number,
    language: 'ar' | 'en'
  ): Promise<void> {
    try {
      const chatRef = collection(db, 'chat_messages');
      await addDoc(chatRef, {
        sessionId,
        userId: userId || null,
        message,
        response,
        intent,
        confidence,
        language,
        timestamp: serverTimestamp(),
        isResolved: intent !== 'unknown' && confidence > this.CONFIDENCE_THRESHOLD,
        escalatedToHuman: false
      });
    } catch (error) {
      console.error('خطأ في حفظ رسالة المحادثة:', error);
    }
  }

  /**
   * تصعيد المحادثة للدعم البشري
   */
  private static async escalateToHuman(
    sessionId: string,
    userId: string | undefined,
    message: string
  ): Promise<void> {
    try {
      // إنشاء تذكرة دعم
      const ticketRef = collection(db, 'support_tickets');
      await addDoc(ticketRef, {
        sessionId,
        userId: userId || null,
        subject: 'تصعيد من الشات بوت',
        message,
        priority: 'high',
        status: 'open',
        assignedTo: null,
        createdAt: serverTimestamp(),
        source: 'chatbot'
      });

      // تحديث جلسة المحادثة
      const sessionRef = collection(db, 'chat_sessions');
      const sessionQuery = query(sessionRef, where('id', '==', sessionId));
      const sessionDocs = await getDocs(sessionQuery);
      
      if (!sessionDocs.empty) {
        // تحديث الجلسة لتشير إلى التصعيد
        console.log('تم تصعيد المحادثة للدعم البشري');
      }

    } catch (error) {
      console.error('خطأ في تصعيد المحادثة:', error);
    }
  }

  /**
   * إنشاء جلسة محادثة جديدة
   */
  static async createChatSession(userId?: string): Promise<string> {
    try {
      const sessionRef = collection(db, 'chat_sessions');
      const docRef = await addDoc(sessionRef, {
        userId: userId || null,
        startTime: serverTimestamp(),
        messageCount: 0,
        isActive: true,
        escalatedToHuman: false,
        tags: []
      });

      return docRef.id;
    } catch (error) {
      console.error('خطأ في إنشاء جلسة المحادثة:', error);
      return 'temp_session_' + Date.now();
    }
  }

  /**
   * إنهاء جلسة المحادثة
   */
  static async endChatSession(sessionId: string, satisfactionRating?: number): Promise<void> {
    try {
      const sessionRef = collection(db, 'chat_sessions');
      const sessionQuery = query(sessionRef, where('id', '==', sessionId));
      const sessionDocs = await getDocs(sessionQuery);
      
      if (!sessionDocs.empty) {
        // تحديث الجلسة
        console.log('تم إنهاء جلسة المحادثة:', sessionId);
      }
    } catch (error) {
      console.error('خطأ في إنهاء جلسة المحادثة:', error);
    }
  }

  /**
   * الحصول على إحصائيات الشات بوت
   */
  static async getChatbotStats(): Promise<{
    totalSessions: number;
    totalMessages: number;
    averageConfidence: number;
    escalationRate: number;
    topIntents: { intent: string; count: number }[];
  }> {
    try {
      // هذه دالة للحصول على إحصائيات شاملة
      // في التطبيق الحقيقي، ستقوم بجلب البيانات من Firebase
      
      return {
        totalSessions: 0,
        totalMessages: 0,
        averageConfidence: 0,
        escalationRate: 0,
        topIntents: []
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات الشات بوت:', error);
      throw error;
    }
  }
}

export default IntelligentChatbotService;
export type { ChatMessage, ChatSession, ChatIntent, KnowledgeBase };
