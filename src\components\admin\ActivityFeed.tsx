// src/components/admin/ActivityFeed.tsx
"use client";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useLocale } from '@/hooks/use-locale';
import type { ActivityItem } from '@/services/adminDashboardService';
import { 
  UserPlus, 
  ShoppingCart, 
  Store, 
  Truck, 
  CheckCircle, 
  XCircle, 
  CreditCard,
  Clock,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

interface ActivityFeedProps {
  activities: ActivityItem[];
  loading?: boolean;
  className?: string;
  maxHeight?: number;
  showRefresh?: boolean;
  onRefresh?: () => void;
}

export function ActivityFeed({
  activities,
  loading = false,
  className,
  maxHeight = 400,
  showRefresh = true,
  onRefresh
}: ActivityFeedProps) {
  const { t } = useLocale();

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_registration':
        return <UserPlus className="h-4 w-4 text-blue-600" />;
      case 'order_placed':
        return <ShoppingCart className="h-4 w-4 text-green-600" />;
      case 'store_registration':
        return <Store className="h-4 w-4 text-purple-600" />;
      case 'representative_application':
        return <Truck className="h-4 w-4 text-orange-600" />;
      case 'order_completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'order_cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'payment_received':
        return <CreditCard className="h-4 w-4 text-emerald-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActivityBadgeVariant = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_registration':
      case 'store_registration':
      case 'representative_application':
        return 'secondary';
      case 'order_placed':
      case 'order_completed':
      case 'payment_received':
        return 'default';
      case 'order_cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'الآن';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `منذ ${days} يوم`;
    }
  };

  const getUserInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-start gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold">
          {t('recentActivity')}
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {activities.length} نشاط
          </Badge>
          {showRefresh && onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea style={{ height: `${maxHeight}px` }}>
          <div className="p-6 pt-0">
            {activities.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {t('noDataAvailable')}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {activities.map((activity, index) => (
                  <div
                    key={activity.id}
                    className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    {/* أيقونة النشاط */}
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>

                    {/* صورة المستخدم */}
                    {activity.userName && (
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarImage src="" alt={activity.userName} />
                        <AvatarFallback className="text-xs">
                          {getUserInitials(activity.userName)}
                        </AvatarFallback>
                      </Avatar>
                    )}

                    {/* محتوى النشاط */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">
                            {activity.title}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {activity.description}
                          </p>
                          
                          {/* معلومات إضافية */}
                          {activity.metadata && (
                            <div className="flex items-center gap-2 mt-2">
                              {activity.metadata.amount && (
                                <Badge variant="outline" className="text-xs">
                                  {new Intl.NumberFormat('ar-SA', {
                                    style: 'currency',
                                    currency: 'SAR',
                                  }).format(activity.metadata.amount)}
                                </Badge>
                              )}
                              {activity.metadata.orderId && (
                                <Badge 
                                  variant={getActivityBadgeVariant(activity.type)}
                                  className="text-xs"
                                >
                                  طلب #{activity.metadata.orderId.slice(-6)}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>

                        {/* الوقت والإجراءات */}
                        <div className="flex flex-col items-end gap-1">
                          <span className="text-xs text-muted-foreground">
                            {formatTimeAgo(activity.timestamp)}
                          </span>
                          
                          {/* زر عرض التفاصيل */}
                          {(activity.metadata?.orderId || activity.userId) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// مكون مبسط لعرض النشاط في الشريط الجانبي
interface ActivitySummaryProps {
  activities: ActivityItem[];
  loading?: boolean;
  limit?: number;
}

export function ActivitySummary({
  activities,
  loading = false,
  limit = 3
}: ActivitySummaryProps) {
  const { t } = useLocale();

  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: limit }).map((_, index) => (
          <div key={index} className="flex items-center gap-3">
            <Skeleton className="h-6 w-6 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-3 w-3/4 mb-1" />
              <Skeleton className="h-2 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  const recentActivities = activities.slice(0, limit);

  return (
    <div className="space-y-3">
      {recentActivities.map((activity) => (
        <div key={activity.id} className="flex items-center gap-3">
          <div className="flex-shrink-0">
            {getActivityIcon(activity.type)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {activity.title}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatTimeAgo(activity.timestamp)}
            </p>
          </div>
        </div>
      ))}
      
      {activities.length > limit && (
        <Button variant="ghost" size="sm" className="w-full text-xs">
          عرض المزيد ({activities.length - limit})
        </Button>
      )}
    </div>
  );
}
