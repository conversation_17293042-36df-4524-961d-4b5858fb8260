describe('نظام المصادقة', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.visitWithLocale('/auth')
  })

  it('يجب أن تعرض صفحة تسجيل الدخول', () => {
    cy.waitForLoadingToFinish()

    // التحقق من وجود نموذج تسجيل الدخول
    cy.get('[data-testid="login-form"]').should('be.visible')
    cy.shouldContainArabicText('تسجيل الدخول')

    // التحقق من وجود حقول الإدخال
    cy.get('input[type="email"]').should('be.visible')
    cy.get('input[type="password"]').should('be.visible')
    cy.get('button[type="submit"]').should('be.visible')
  })

  // اختبار شامل لعملية إنشاء الحساب وتبديل اللغة
  it('يجب أن تعمل عملية إنشاء الحساب مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()

    // 1. النقر على إنشاء حساب
    cy.get('[data-testid="switch-to-signup"]').should('be.visible').click()
    cy.waitForLoadingToFinish()

    // التحقق من الانتقال لصفحة إنشاء الحساب
    cy.get('[data-testid="signup-form"]').should('be.visible')
    cy.shouldContainArabicText('إنشاء حساب جديد')

    // 2. اختيار نوع المستخدم (تاجر)
    cy.get('[data-testid="user-type-merchant"]').should('be.visible').click()

    // التحقق من ظهور حقول التاجر
    cy.get('[data-testid="merchant-fields"]').should('be.visible')

    // 3. تبديل اللغة إلى الإنجليزية
    cy.get('[data-testid="language-switcher"]').should('be.visible').click()
    cy.wait(1000) // انتظار تحميل اللغة الجديدة

    // التحقق من تغيير اللغة
    cy.url().should('include', '/en/')
    cy.shouldContainEnglishText('Create New Account')

    // التحقق من أن نوع المستخدم المحدد لا يزال محفوظاً
    cy.get('[data-testid="user-type-merchant"]').should('be.checked')

    // 4. تبديل اللغة مرة أخرى إلى العربية
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(1000)

    // التحقق من العودة للعربية
    cy.url().should('include', '/ar/')
    cy.shouldContainArabicText('إنشاء حساب جديد')

    // 5. ملء بيانات التسجيل
    cy.get('input[name="username"]').type('تاجر تجريبي')
    cy.get('input[name="email"]').type('<EMAIL>')
    cy.get('input[name="password"]').type('Password123!')
    cy.get('input[name="confirmPassword"]').type('Password123!')

    // ملء بيانات التاجر الإضافية
    cy.get('input[name="businessName"]').type('متجر تجريبي')
    cy.get('input[name="phone"]').type('**********')

    // 6. إرسال النموذج
    cy.get('button[type="submit"]').click()

    // محاكاة نجاح التسجيل
    cy.mockSignup('merchant')

    // التحقق من الانتقال لصفحة انتظار الموافقة
    cy.url().should('include', '/merchant/pending-approval')
    cy.shouldContainArabicText('في انتظار الموافقة')
  })

  it('يجب أن تعمل عملية تسجيل الدخول', () => {
    cy.waitForLoadingToFinish()
    
    // ملء نموذج تسجيل الدخول
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('input[type="password"]').type('password123')
    
    // النقر على زر تسجيل الدخول
    cy.get('button[type="submit"]').click()
    
    // محاكاة نجاح تسجيل الدخول
    cy.mockLogin('customer')
    
    // التحقق من الانتقال للصفحة الرئيسية أو لوحة التحكم
    cy.url().should('not.include', '/auth')
  })

  it('يجب أن تعرض أخطاء التحقق من صحة البيانات', () => {
    cy.waitForLoadingToFinish()
    
    // محاولة تسجيل الدخول بدون بيانات
    cy.get('button[type="submit"]').click()
    
    // التحقق من ظهور رسائل الخطأ
    cy.get('[data-testid="email-error"]').should('be.visible')
    cy.get('[data-testid="password-error"]').should('be.visible')
  })

  it('يجب أن تعمل عملية تسجيل الدخول بـ Google', () => {
    cy.waitForLoadingToFinish()
    
    // البحث عن زر تسجيل الدخول بـ Google
    cy.get('[data-testid="google-signin-button"]').should('be.visible')
    cy.get('[data-testid="google-signin-button"]').click()
    
    // محاكاة نجاح تسجيل الدخول بـ Google
    cy.mockLogin('customer')
    
    // التحقق من الانتقال
    cy.url().should('not.include', '/auth')
  })

  it('يجب أن تعمل عملية التبديل بين تسجيل الدخول والتسجيل', () => {
    cy.waitForLoadingToFinish()

    // التحقق من وجود رابط التسجيل
    cy.get('[data-testid="switch-to-signup"]').should('be.visible')
    cy.get('[data-testid="switch-to-signup"]').click()

    // التحقق من تغيير النموذج
    cy.get('[data-testid="signup-form"]').should('be.visible')
    cy.shouldContainArabicText('إنشاء حساب جديد')

    // العودة لتسجيل الدخول
    cy.get('[data-testid="switch-to-login"]').click()
    cy.get('[data-testid="login-form"]').should('be.visible')
  })

  // اختبار تبديل اللغة في صفحة تسجيل الدخول
  it('يجب أن يعمل تبديل اللغة في صفحة تسجيل الدخول', () => {
    cy.waitForLoadingToFinish()

    // التحقق من اللغة العربية الافتراضية
    cy.url().should('include', '/ar/')
    cy.shouldContainArabicText('تسجيل الدخول')

    // تبديل إلى الإنجليزية
    cy.get('[data-testid="language-switcher"]').should('be.visible').click()
    cy.wait(1000)

    // التحقق من تغيير اللغة والمحتوى
    cy.url().should('include', '/en/')
    cy.shouldContainEnglishText('Sign In')

    // التحقق من أن النموذج لا يزال يعمل
    cy.get('[data-testid="login-form"]').should('be.visible')
    cy.get('input[type="email"]').should('be.visible')
    cy.get('input[type="password"]').should('be.visible')

    // العودة للعربية
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(1000)

    cy.url().should('include', '/ar/')
    cy.shouldContainArabicText('تسجيل الدخول')
  })

  // اختبار إنشاء حساب عميل مع تبديل اللغة
  it('يجب أن تعمل عملية إنشاء حساب عميل مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()

    // الانتقال لصفحة التسجيل
    cy.get('[data-testid="switch-to-signup"]').click()
    cy.waitForLoadingToFinish()

    // اختيار نوع العميل
    cy.get('[data-testid="user-type-customer"]').should('be.visible').click()

    // تبديل اللغة أثناء التسجيل
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(1000)

    // التحقق من تغيير اللغة
    cy.url().should('include', '/en/')
    cy.shouldContainEnglishText('Create New Account')

    // ملء البيانات بالإنجليزية
    cy.get('input[name="username"]').type('Test Customer')
    cy.get('input[name="email"]').type('<EMAIL>')
    cy.get('input[name="password"]').type('Password123!')
    cy.get('input[name="confirmPassword"]').type('Password123!')

    // العودة للعربية قبل الإرسال
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(1000)

    // إرسال النموذج
    cy.get('button[type="submit"]').click()

    // محاكاة نجاح التسجيل
    cy.mockSignup('customer')

    // التحقق من الانتقال للوحة التحكم
    cy.url().should('include', '/dashboard')
  })

  // اختبار إنشاء حساب مندوب مع تبديل اللغة
  it('يجب أن تعمل عملية إنشاء حساب مندوب مع تبديل اللغة', () => {
    cy.waitForLoadingToFinish()

    // الانتقال لصفحة التسجيل
    cy.get('[data-testid="switch-to-signup"]').click()
    cy.waitForLoadingToFinish()

    // اختيار نوع المندوب
    cy.get('[data-testid="user-type-representative"]').should('be.visible').click()

    // التحقق من ظهور حقول المندوب
    cy.get('[data-testid="representative-fields"]').should('be.visible')

    // ملء البيانات
    cy.get('input[name="username"]').type('مندوب تجريبي')
    cy.get('input[name="email"]').type('<EMAIL>')
    cy.get('input[name="password"]').type('Password123!')
    cy.get('input[name="confirmPassword"]').type('Password123!')

    // ملء بيانات المندوب الإضافية
    cy.get('input[name="phone"]').type('**********')
    cy.get('input[name="vehicleType"]').select('motorcycle')

    // تبديل اللغة قبل الإرسال
    cy.get('[data-testid="language-switcher"]').click()
    cy.wait(1000)

    // التحقق من حفظ البيانات بعد تبديل اللغة
    cy.get('input[name="email"]').should('have.value', '<EMAIL>')

    // إرسال النموذج
    cy.get('button[type="submit"]').click()

    // محاكاة نجاح التسجيل
    cy.mockSignup('representative')

    // التحقق من الانتقال لصفحة تسجيل المندوبين
    cy.url().should('include', '/representative/signup')
  })

  it('يجب أن تعمل عملية اختيار نوع المستخدم', () => {
    cy.waitForLoadingToFinish()
    
    // الانتقال لصفحة التسجيل
    cy.get('[data-testid="switch-to-signup"]').click()
    
    // التحقق من وجود خيارات نوع المستخدم
    cy.get('[data-testid="user-type-customer"]').should('be.visible')
    cy.get('[data-testid="user-type-merchant"]').should('be.visible')
    cy.get('[data-testid="user-type-representative"]').should('be.visible')
    
    // اختيار نوع المستخدم
    cy.get('[data-testid="user-type-merchant"]').click()
    
    // التحقق من تحديث النموذج
    cy.get('[data-testid="merchant-fields"]').should('be.visible')
  })

  it('يجب أن تعمل عملية إعادة تعيين كلمة المرور', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على رابط نسيت كلمة المرور
    cy.get('[data-testid="forgot-password-link"]').click()
    
    // التحقق من ظهور نموذج إعادة التعيين
    cy.get('[data-testid="reset-password-form"]').should('be.visible')
    
    // إدخال البريد الإلكتروني
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('button[type="submit"]').click()
    
    // التحقق من ظهور رسالة النجاح
    cy.get('[data-testid="reset-success-message"]').should('be.visible')
  })

  it('يجب أن تعمل عملية تسجيل الخروج', () => {
    // تسجيل الدخول أولاً
    cy.mockLogin('customer')
    cy.visitWithLocale('/dashboard')
    cy.waitForLoadingToFinish()
    
    // النقر على زر تسجيل الخروج
    cy.get('[data-testid="logout-button"]').click()
    
    // التحقق من تسجيل الخروج
    cy.mockLogout()
    
    // التحقق من الانتقال لصفحة تسجيل الدخول
    cy.url().should('include', '/auth')
  })

  it('يجب أن تحمي الصفحات المحمية', () => {
    // محاولة الوصول لصفحة محمية بدون تسجيل دخول
    cy.visitWithLocale('/dashboard')
    
    // التحقق من إعادة التوجيه لصفحة تسجيل الدخول
    cy.url().should('include', '/auth')
    cy.shouldContainArabicText('يجب تسجيل الدخول أولاً')
  })

  it('يجب أن تتعامل مع أخطاء المصادقة', () => {
    cy.waitForLoadingToFinish()
    
    // محاكاة خطأ في تسجيل الدخول
    cy.intercept('POST', '**/auth/**', {
      statusCode: 401,
      body: { error: 'Invalid credentials' }
    }).as('authError')
    
    // ملء النموذج
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('input[type="password"]').type('wrongpassword')
    cy.get('button[type="submit"]').click()
    
    // التحقق من ظهور رسالة الخطأ
    cy.get('[data-testid="auth-error-message"]').should('be.visible')
    cy.shouldContainArabicText('بيانات الدخول غير صحيحة')
  })
})
