"use client";

import React, { useState, useEffect } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  <PERSON><PERSON>hart as LineChartIcon,
  Download,
  RefreshCw,
  Calendar,
  Filter
} from 'lucide-react';

interface ChartData {
  name: string;
  value: number;
  date?: string;
  category?: string;
  [key: string]: any;
}

interface InteractiveChartsProps {
  title: string;
  description?: string;
  data: ChartData[];
  chartType?: 'line' | 'area' | 'bar' | 'pie';
  timeRange?: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange?: (range: string) => void;
  onExport?: () => void;
  onRefresh?: () => void;
  loading?: boolean;
  className?: string;
}

const COLORS = [
  '#3B82F6', // blue
  '#10B981', // emerald
  '#F59E0B', // amber
  '#EF4444', // red
  '#8B5CF6', // violet
  '#06B6D4', // cyan
  '#84CC16', // lime
  '#F97316', // orange
];

export default function InteractiveCharts({
  title,
  description,
  data,
  chartType = 'line',
  timeRange = '30d',
  onTimeRangeChange,
  onExport,
  onRefresh,
  loading = false,
  className
}: InteractiveChartsProps) {
  const [selectedChart, setSelectedChart] = useState(chartType);
  const [filteredData, setFilteredData] = useState(data);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    setFilteredData(data);
  }, [data]);

  // فلترة البيانات حسب الفئة
  const filterDataByCategory = (category: string) => {
    if (category === 'all') {
      setFilteredData(data);
    } else {
      setFilteredData(data.filter(item => item.category === category));
    }
    setSelectedCategory(category);
  };

  // الحصول على الفئات المتاحة
  const getAvailableCategories = () => {
    const categories = [...new Set(data.map(item => item.category).filter(Boolean))];
    return categories;
  };

  // حساب الإحصائيات
  const calculateStats = () => {
    if (filteredData.length === 0) return { total: 0, change: 0, trend: 'neutral' };

    const total = filteredData.reduce((sum, item) => sum + item.value, 0);
    const average = total / filteredData.length;
    
    // حساب الاتجاه (مقارنة النصف الأول بالنصف الثاني)
    const midPoint = Math.floor(filteredData.length / 2);
    const firstHalf = filteredData.slice(0, midPoint);
    const secondHalf = filteredData.slice(midPoint);
    
    const firstAvg = firstHalf.reduce((sum, item) => sum + item.value, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + item.value, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    const trend = change > 5 ? 'up' : change < -5 ? 'down' : 'neutral';

    return { total, change: Math.abs(change), trend };
  };

  const stats = calculateStats();

  // تنسيق الأرقام
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
  };

  // تصدير البيانات
  const handleExport = () => {
    if (onExport) {
      onExport();
    } else {
      // تصدير افتراضي كـ CSV
      const csvContent = [
        ['Name', 'Value', 'Date', 'Category'],
        ...filteredData.map(item => [
          item.name,
          item.value,
          item.date || '',
          item.category || ''
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${title.replace(/\s+/g, '_')}_data.csv`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  // رسم الخط
  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={filteredData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip formatter={(value) => [formatNumber(Number(value)), 'القيمة']} />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="value" 
          stroke={COLORS[0]} 
          strokeWidth={2}
          dot={{ fill: COLORS[0], strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  // رسم المنطقة
  const renderAreaChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={filteredData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip formatter={(value) => [formatNumber(Number(value)), 'القيمة']} />
        <Legend />
        <Area 
          type="monotone" 
          dataKey="value" 
          stroke={COLORS[0]} 
          fill={COLORS[0]}
          fillOpacity={0.3}
        />
      </AreaChart>
    </ResponsiveContainer>
  );

  // رسم الأعمدة
  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={filteredData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip formatter={(value) => [formatNumber(Number(value)), 'القيمة']} />
        <Legend />
        <Bar dataKey="value" fill={COLORS[0]} />
      </BarChart>
    </ResponsiveContainer>
  );

  // رسم دائري
  const renderPieChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={filteredData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {filteredData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value) => [formatNumber(Number(value)), 'القيمة']} />
      </PieChart>
    </ResponsiveContainer>
  );

  const renderChart = () => {
    switch (selectedChart) {
      case 'area':
        return renderAreaChart();
      case 'bar':
        return renderBarChart();
      case 'pie':
        return renderPieChart();
      default:
        return renderLineChart();
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {title}
              <Badge variant={stats.trend === 'up' ? 'default' : stats.trend === 'down' ? 'destructive' : 'secondary'}>
                {stats.trend === 'up' && <TrendingUp className="w-3 h-3" />}
                {stats.trend === 'down' && <TrendingDown className="w-3 h-3" />}
                {stats.change.toFixed(1)}%
              </Badge>
            </CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {/* اختيار نوع الرسم */}
            <Select value={selectedChart} onValueChange={setSelectedChart}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">
                  <div className="flex items-center gap-2">
                    <LineChartIcon className="w-4 h-4" />
                    خطي
                  </div>
                </SelectItem>
                <SelectItem value="area">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    منطقة
                  </div>
                </SelectItem>
                <SelectItem value="bar">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    أعمدة
                  </div>
                </SelectItem>
                <SelectItem value="pie">
                  <div className="flex items-center gap-2">
                    <PieChartIcon className="w-4 h-4" />
                    دائري
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* فلتر الفترة الزمنية */}
            {onTimeRangeChange && (
              <Select value={timeRange} onValueChange={onTimeRangeChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">7 أيام</SelectItem>
                  <SelectItem value="30d">30 يوم</SelectItem>
                  <SelectItem value="90d">90 يوم</SelectItem>
                  <SelectItem value="1y">سنة</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* فلتر الفئات */}
            {getAvailableCategories().length > 0 && (
              <Select value={selectedCategory} onValueChange={filterDataByCategory}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">الكل</SelectItem>
                  {getAvailableCategories().map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            {/* أزرار الإجراءات */}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
            >
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* الإحصائيات السريعة */}
        <div className="flex items-center gap-4 mt-4">
          <div className="text-2xl font-bold">
            {formatNumber(stats.total)}
          </div>
          <div className="text-sm text-muted-foreground">
            إجمالي القيم
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {filteredData.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            لا توجد بيانات للعرض
          </div>
        ) : (
          renderChart()
        )}
      </CardContent>
    </Card>
  );
}
