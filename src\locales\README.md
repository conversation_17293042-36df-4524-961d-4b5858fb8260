# ملفات الترجمة - Translation Files

## 📁 هيكل الملفات

```
src/locales/
├── translations.json          # 🆕 الملف المدموج الجديد (موصى به)
├── ar.json                   # الترجمات العربية (قديم)
├── en.json                   # الترجمات الإنجليزية (قديم)
└── README.md                 # هذا الملف
```

## 🔄 الملف المدموج الجديد

تم إنشاء ملف `translations.json` الذي يحتوي على جميع الترجمات في ملف واحد موحد:

```json
{
  "ar": {
    "appName": "مِخْلاة",
    "home": "الرئيسية",
    // ... جميع الترجمات العربية
  },
  "en": {
    "appName": "Mikhla",
    "home": "Home",
    // ... جميع الترجمات الإنجليزية
  }
}
```

## ✅ المزايا

### 🎯 **ملف واحد موحد**
- سهولة الإدارة والصيانة
- تقليل عدد الملفات
- تنظيم أفضل للترجمات

### 🚀 **أداء محسن**
- تحميل واحد بدلاً من تحميلين منفصلين
- تقليل طلبات الشبكة
- cache أفضل

### 🔧 **سهولة التطوير**
- إضافة ترجمات جديدة في مكان واحد
- مقارنة سهلة بين اللغات
- تجنب التكرار والأخطاء

## 📊 الإحصائيات

- **الترجمات العربية**: 1,787 مفتاح
- **الترجمات الإنجليزية**: 1,167 مفتاح
- **إجمالي المفاتيح**: 2,954 مفتاح
- **حجم الملف**: 203 KB

## 🛠️ كيفية الاستخدام

### في الكود

تم تحديث ملف `src/lib/i18n.ts` ليستخدم الملف المدموج:

```typescript
// استيراد الملف المدموج
const mergedTranslations = () => import('@/locales/translations.json').then((module) => module.default);

export const locales = {
  en: () => mergedTranslations().then((translations) => translations.en),
  ar: () => mergedTranslations().then((translations) => translations.ar),
};
```

### في المكونات

```typescript
import { getTranslations } from '@/context/locale-context';

// في Server Component
const { t } = await getTranslations(locale);
const title = t('appName'); // "مِخْلاة" أو "Mikhla"
```

## 🔧 السكريبتات المساعدة

### دمج الملفات
```bash
node scripts/merge-translations.js
```

### اختبار الملف المدموج
```bash
node scripts/test-merged-translations.js
```

### تنظيف الملفات القديمة
```bash
node scripts/cleanup-old-translations.js
```

## 📝 إضافة ترجمات جديدة

### الطريقة الموصى بها

1. **تحرير الملف المدموج مباشرة**:
   ```json
   {
     "ar": {
       "newKey": "النص العربي الجديد"
     },
     "en": {
       "newKey": "New English Text"
     }
   }
   ```

2. **استخدام السكريبت**:
   ```bash
   node scripts/add-translation.js "newKey" "النص العربي" "English Text"
   ```

### الطريقة التقليدية

1. إضافة الترجمة في `ar.json` و `en.json`
2. تشغيل سكريبت الدمج:
   ```bash
   node scripts/merge-translations.js
   ```

## 🚨 ملاحظات مهمة

### ⚠️ التوافق مع النسخة القديمة

- الملفات القديمة (`ar.json`, `en.json`) ما زالت موجودة للتوافق
- يمكن حذفها بعد التأكد من عمل الملف المدموج
- تأكد من تحديث جميع المراجع في الكود

### 🔄 النسخ الاحتياطية

- يتم إنشاء نسخ احتياطية تلقائياً عند التحديث
- يتم الاحتفاظ بآخر 3 نسخ احتياطية
- استخدم سكريبت التنظيف لحذف النسخ القديمة

### 📦 حجم الملف

- الملف الحالي: 203 KB (مناسب)
- إذا تجاوز 500 KB، فكر في تقسيمه
- استخدم lazy loading للترجمات الكبيرة

## 🔍 استكشاف الأخطاء

### مشكلة: الترجمات لا تظهر

1. تحقق من صحة JSON:
   ```bash
   node -e "console.log(JSON.parse(require('fs').readFileSync('src/locales/translations.json', 'utf8')))"
   ```

2. تحقق من تحديث `i18n.ts`

3. امسح cache المتصفح

### مشكلة: ترجمات مفقودة

1. شغل سكريبت الاختبار:
   ```bash
   node scripts/test-merged-translations.js
   ```

2. قارن مع الملفات الأصلية

3. أعد دمج الملفات:
   ```bash
   node scripts/merge-translations.js
   ```

## 📈 الخطوات التالية

1. **اختبار شامل** للتطبيق مع الملف الجديد
2. **حذف الملفات القديمة** بعد التأكد من الاستقرار
3. **تحديث الوثائق** والتعليمات للفريق
4. **إعداد CI/CD** للتحقق من صحة الترجمات

---

**تم إنشاء هذا النظام في**: يونيو 2025  
**آخر تحديث**: تم دمج الملفات بنجاح ✅
