'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { Bell, Mail, MessageSquare, Smartphone } from 'lucide-react';
import { Switch } from '@/components/ui/switch';

interface NotificationSettingsProps {
  onSettingsChange: () => void;
}

export function NotificationSettings({ onSettingsChange }: NotificationSettingsProps) {
  const { t } = useLocale();
  const [settings, setSettings] = useState({
    email: {
      enabled: true,
      newOrders: true,
      orderUpdates: true,
      newUsers: true,
      systemAlerts: true
    },
    push: {
      enabled: true,
      newOrders: true,
      orderUpdates: true,
      promotions: false,
      systemAlerts: true
    },
    sms: {
      enabled: false,
      orderConfirmation: false,
      deliveryUpdates: false,
      emergencyAlerts: true
    }
  });

  const handleSettingChange = (category: string, setting: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: value
      }
    }));
    onSettingsChange();
  };

  return (
    <div className="space-y-6">
      {/* إشعارات البريد الإلكتروني */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            إشعارات البريد الإلكتروني
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="email-enabled">تفعيل إشعارات البريد الإلكتروني</Label>
            <Switch
              id="email-enabled"
              checked={settings.email.enabled}
              onCheckedChange={(checked) => handleSettingChange('email', 'enabled', checked)}
            />
          </div>

          {settings.email.enabled && (
            <div className="space-y-3 pl-4 border-l-2 border-gray-200">
              <div className="flex items-center justify-between">
                <Label>الطلبات الجديدة</Label>
                <Switch
                  checked={settings.email.newOrders}
                  onCheckedChange={(checked) => handleSettingChange('email', 'newOrders', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>تحديثات الطلبات</Label>
                <Switch
                  checked={settings.email.orderUpdates}
                  onCheckedChange={(checked) => handleSettingChange('email', 'orderUpdates', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>المستخدمون الجدد</Label>
                <Switch
                  checked={settings.email.newUsers}
                  onCheckedChange={(checked) => handleSettingChange('email', 'newUsers', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>تنبيهات النظام</Label>
                <Switch
                  checked={settings.email.systemAlerts}
                  onCheckedChange={(checked) => handleSettingChange('email', 'systemAlerts', checked)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* الإشعارات الفورية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            الإشعارات الفورية (Push)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="push-enabled">تفعيل الإشعارات الفورية</Label>
            <Switch
              id="push-enabled"
              checked={settings.push.enabled}
              onCheckedChange={(checked) => handleSettingChange('push', 'enabled', checked)}
            />
          </div>

          {settings.push.enabled && (
            <div className="space-y-3 pl-4 border-l-2 border-gray-200">
              <div className="flex items-center justify-between">
                <Label>الطلبات الجديدة</Label>
                <Switch
                  checked={settings.push.newOrders}
                  onCheckedChange={(checked) => handleSettingChange('push', 'newOrders', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>تحديثات الطلبات</Label>
                <Switch
                  checked={settings.push.orderUpdates}
                  onCheckedChange={(checked) => handleSettingChange('push', 'orderUpdates', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>العروض والترويج</Label>
                <Switch
                  checked={settings.push.promotions}
                  onCheckedChange={(checked) => handleSettingChange('push', 'promotions', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>تنبيهات النظام</Label>
                <Switch
                  checked={settings.push.systemAlerts}
                  onCheckedChange={(checked) => handleSettingChange('push', 'systemAlerts', checked)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* الرسائل النصية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            الرسائل النصية (SMS)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="sms-enabled">تفعيل الرسائل النصية</Label>
            <Switch
              id="sms-enabled"
              checked={settings.sms.enabled}
              onCheckedChange={(checked) => handleSettingChange('sms', 'enabled', checked)}
            />
          </div>

          {settings.sms.enabled && (
            <div className="space-y-3 pl-4 border-l-2 border-gray-200">
              <div className="flex items-center justify-between">
                <Label>تأكيد الطلبات</Label>
                <Switch
                  checked={settings.sms.orderConfirmation}
                  onCheckedChange={(checked) => handleSettingChange('sms', 'orderConfirmation', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>تحديثات التوصيل</Label>
                <Switch
                  checked={settings.sms.deliveryUpdates}
                  onCheckedChange={(checked) => handleSettingChange('sms', 'deliveryUpdates', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>التنبيهات الطارئة</Label>
                <Switch
                  checked={settings.sms.emergencyAlerts}
                  onCheckedChange={(checked) => handleSettingChange('sms', 'emergencyAlerts', checked)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
