// src/components/auth/AuthErrorHandler.tsx
"use client";

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Button } from '@/components/ui/button';
import { AlertTriangle, LogOut } from 'lucide-react';
import { auth } from '@/lib/firebase';
import { signOut } from 'firebase/auth';
import { useToast } from '@/hooks/use-toast';
import type { Locale } from '@/lib/i18n';

interface AuthErrorHandlerProps {
  locale: Locale;
  error?: string;
  onRetry?: () => void;
}

export default function AuthErrorHandler({ locale, error, onRetry }: AuthErrorHandlerProps) {
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useLocale();
  const { toast } = useToast();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleForceLogout = async () => {
    setIsLoggingOut(true);
    try {
      // تسجيل الخروج من Firebase
      await signOut(auth);

      // مسح البيانات المحلية
      if (typeof window !== 'undefined') {
        localStorage.removeItem('firebase:authUser');
        sessionStorage.clear();
      }

      toast({
        title: t('logoutSuccessTitle'),
        description: t('securityLogoutMessage'),
      });

      router.replace(`/${locale}/login`);

      // إعادة تحميل الصفحة للتأكد من مسح جميع البيانات
      setTimeout(() => {
        window.location.reload();
      }, 100);

    } catch (error) {
      console.error("Force logout error:", error);

      // في حالة فشل تسجيل الخروج، قم بمسح البيانات المحلية
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
      }

      toast({
        title: t('errorTitle'),
        description: t('logoutFailed'),
        variant: "destructive",
      });

      // إعادة التوجيه حتى لو فشل تسجيل الخروج
      router.replace(`/${locale}/login`);
      setTimeout(() => {
        window.location.reload();
      }, 100);

    } finally {
      setIsLoggingOut(false);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-6 p-8 bg-card shadow-xl rounded-lg border border-destructive/20">
        <div className="flex flex-col items-center space-y-4 text-center">
          <AlertTriangle className="h-16 w-16 text-destructive" />
          <h2 className="text-2xl font-semibold text-destructive">{t('authenticationError')}</h2>
          <p className="text-muted-foreground">
            {error || t('authenticationErrorMessage')}
          </p>
          <p className="text-sm text-muted-foreground">
            {t('securityRecommendation')}
          </p>
        </div>
        
        <div className="space-y-3">
          {onRetry && (
            <Button 
              onClick={onRetry} 
              variant="outline" 
              className="w-full"
              disabled={isLoggingOut}
            >
              {t('retry')}
            </Button>
          )}
          
          <Button 
            onClick={handleForceLogout} 
            variant="destructive" 
            className="w-full"
            disabled={isLoggingOut}
          >
            <LogOut className="me-2 h-4 w-4" />
            {isLoggingOut ? t('loggingOut') : t('secureLogout')}
          </Button>
        </div>
      </div>
    </div>
  );
}
