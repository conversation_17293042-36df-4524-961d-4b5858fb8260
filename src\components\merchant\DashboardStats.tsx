// src/components/merchant/DashboardStats.tsx
"use client";

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useLocale } from '@/hooks/use-locale';
import { useMerchantStats } from '@/hooks/useMerchantStats';
import { 
  Package, 
  ShoppingCart, 
  TrendingUp, 
  Star,
  DollarSign,
  Eye,
  AlertCircle
} from 'lucide-react';
import type { MerchantStats } from '@/types';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
  loading?: boolean;
}

function StatCard({ title, value, icon, description, trend, loading }: StatCardProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Skeleton className="h-4 w-24" />
          </CardTitle>
          <Skeleton className="h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-16 mb-1" />
          <Skeleton className="h-3 w-32" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-center w-full">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-center mb-1">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground text-center">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

interface DashboardStatsProps {
  className?: string;
}

export default function DashboardStats({ className }: DashboardStatsProps) {
  const { t } = useLocale();
  const { stats, loading, error } = useMerchantStats();

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ${t('SAR')}`;
  };

  const formatRating = (rating: number) => {
    return rating > 0 ? `${rating}/5` : t('notSet');
  };

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-center mb-2">{t('quickStats')}</h2>
        <p className="text-muted-foreground text-center">
          {t('dashboardOverview')}
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title={t('totalProducts')}
          value={stats.totalProducts}
          icon={<Package className="h-4 w-4" />}
          description={`${stats.activeProducts} ${t('active').toLowerCase()}`}
          loading={loading}
        />
        
        <StatCard
          title={t('totalOrders')}
          value={stats.totalOrders}
          icon={<ShoppingCart className="h-4 w-4" />}
          description={`${stats.newOrders} ${t('newOrders').toLowerCase()}`}
          loading={loading}
        />
        
        <StatCard
          title={t('monthlySales')}
          value={formatCurrency(stats.monthlySales)}
          icon={<TrendingUp className="h-4 w-4" />}
          description={t('monthlyReport').toLowerCase()}
          loading={loading}
        />
        
        <StatCard
          title={t('averageRating')}
          value={formatRating(stats.averageRating)}
          icon={<Star className="h-4 w-4" />}
          description={`${stats.reviewCount} ${t('customerReviews').toLowerCase()}`}
          loading={loading}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 mt-4">
        <StatCard
          title={t('totalRevenue')}
          value={formatCurrency(stats.totalRevenue)}
          icon={<DollarSign className="h-4 w-4" />}
          description={t('allOrders').toLowerCase()}
          loading={loading}
        />
        
        <StatCard
          title={t('activeProducts')}
          value={stats.activeProducts}
          icon={<Eye className="h-4 w-4" />}
          description={t('products').toLowerCase()}
          loading={loading}
        />
      </div>
    </div>
  );
}
