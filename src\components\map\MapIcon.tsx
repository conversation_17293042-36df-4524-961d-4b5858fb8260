// src/components/map/MapIcon.tsx
"use client";

import { FC, useState, useEffect } from 'react';
import { MapPin, Navigation, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useGeolocation } from '@/hooks/useGeolocation';

interface MapIconProps {
  onClick: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'floating' | 'minimal';
  showStatus?: boolean;
}

const MapIcon: FC<MapIconProps> = ({
  onClick,
  className = '',
  size = 'md',
  variant = 'default',
  showStatus = true
}) => {
  const { permissionStatus, loading, error } = useGeolocation();
  const [isHovered, setIsHovered] = useState(false);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const getStatusColor = () => {
    switch (permissionStatus) {
      case 'granted':
        return 'text-green-500';
      case 'denied':
        return 'text-red-500';
      case 'loading':
        return 'text-blue-500';
      default:
        return 'text-muted-foreground';
    }
  };

  const getStatusText = () => {
    switch (permissionStatus) {
      case 'granted':
        return 'الموقع متاح - اضغط لعرض الخريطة';
      case 'denied':
        return 'تم رفض إذن الموقع - اضغط للمحاولة مرة أخرى';
      case 'loading':
        return 'جاري تحديد الموقع...';
      default:
        return 'اضغط لعرض الخريطة وتحديد موقعك';
    }
  };

  const renderIcon = () => {
    if (loading) {
      return <Loader2 className={`${iconSizes[size]} animate-spin`} />;
    }

    if (permissionStatus === 'granted') {
      return <Navigation className={`${iconSizes[size]} ${getStatusColor()}`} />;
    }

    return <MapPin className={`${iconSizes[size]} ${getStatusColor()}`} />;
  };

  const baseButton = (
    <Button
      onClick={onClick}
      variant={variant === 'minimal' ? 'ghost' : 'default'}
      size="icon"
      className={`
        ${sizeClasses[size]}
        ${variant === 'floating' ? 'shadow-lg hover:shadow-xl' : ''}
        ${variant === 'floating' ? 'bg-primary hover:bg-primary/90' : ''}
        ${className}
        transition-all duration-200 ease-in-out
        ${isHovered ? 'scale-110' : 'scale-100'}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      disabled={loading}
    >
      {renderIcon()}
      {showStatus && permissionStatus === 'granted' && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
      )}
      {showStatus && error && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-background" />
      )}
    </Button>
  );

  if (variant === 'minimal') {
    return baseButton;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {baseButton}
        </TooltipTrigger>
        <TooltipContent side="bottom" className="max-w-xs text-center">
          <p className="text-sm">{getStatusText()}</p>
          {error && (
            <p className="text-xs text-muted-foreground mt-1">{error}</p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default MapIcon;
