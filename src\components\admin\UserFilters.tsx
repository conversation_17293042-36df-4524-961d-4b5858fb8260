'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { UserFilters as UserFiltersType } from '@/hooks/useUsersManagement';
import { X, Filter } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface UserFiltersProps {
  filters: UserFiltersType;
  onFiltersChange: (filters: UserFiltersType) => void;
  onClose: () => void;
}

export function UserFilters({ filters, onFiltersChange, onClose }: UserFiltersProps) {
  const { t } = useLocale();

  const handleFilterChange = (key: keyof UserFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      userType: 'all',
      status: 'all',
      verificationStatus: 'all'
    });
  };

  const hasActiveFilters = 
    filters.userType !== 'all' || 
    filters.status !== 'all' || 
    filters.verificationStatus !== 'all' ||
    filters.registrationDateFrom ||
    filters.registrationDateTo;

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Filter className="h-5 w-5" />
            {t('filterUsers')}
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* نوع المستخدم */}
          <div className="space-y-2">
            <Label>{t('filterByUserType')}</Label>
            <Select
              value={filters.userType || 'all'}
              onValueChange={(value) => handleFilterChange('userType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأنواع</SelectItem>
                <SelectItem value="customer">{t('customer')}</SelectItem>
                <SelectItem value="merchant">{t('merchant')}</SelectItem>
                <SelectItem value="representative">مندوب</SelectItem>
                <SelectItem value="admin">مدير</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* حالة المستخدم */}
          <div className="space-y-2">
            <Label>{t('filterByStatus')}</Label>
            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="inactive">غير نشط</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* حالة التحقق */}
          <div className="space-y-2">
            <Label>حالة التحقق</Label>
            <Select
              value={filters.verificationStatus || 'all'}
              onValueChange={(value) => handleFilterChange('verificationStatus', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="verified">محقق</SelectItem>
                <SelectItem value="unverified">غير محقق</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* تاريخ التسجيل من */}
          <div className="space-y-2">
            <Label>تاريخ التسجيل من</Label>
            <input
              type="date"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={filters.registrationDateFrom?.toISOString().split('T')[0] || ''}
              onChange={(e) => handleFilterChange('registrationDateFrom', e.target.value ? new Date(e.target.value) : undefined)}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* تاريخ التسجيل إلى */}
          <div className="space-y-2">
            <Label>تاريخ التسجيل إلى</Label>
            <input
              type="date"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={filters.registrationDateTo?.toISOString().split('T')[0] || ''}
              onChange={(e) => handleFilterChange('registrationDateTo', e.target.value ? new Date(e.target.value) : undefined)}
            />
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex items-end gap-2">
            {hasActiveFilters && (
              <Button variant="outline" onClick={clearFilters} className="flex-1">
                مسح المرشحات
              </Button>
            )}
          </div>
        </div>

        {/* عرض المرشحات النشطة */}
        {hasActiveFilters && (
          <div className="pt-4 border-t">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>المرشحات النشطة:</span>
              <div className="flex flex-wrap gap-2">
                {filters.userType !== 'all' && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">
                    النوع: {filters.userType === 'customer' ? 'عميل' : 
                           filters.userType === 'merchant' ? 'تاجر' : 
                           filters.userType === 'representative' ? 'مندوب' : 'مدير'}
                  </span>
                )}
                {filters.status !== 'all' && (
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs">
                    الحالة: {filters.status === 'active' ? 'نشط' : 'غير نشط'}
                  </span>
                )}
                {filters.verificationStatus !== 'all' && (
                  <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs">
                    التحقق: {filters.verificationStatus === 'verified' ? 'محقق' : 'غير محقق'}
                  </span>
                )}
                {filters.registrationDateFrom && (
                  <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded-md text-xs">
                    من: {filters.registrationDateFrom.toLocaleDateString('ar-SA')}
                  </span>
                )}
                {filters.registrationDateTo && (
                  <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded-md text-xs">
                    إلى: {filters.registrationDateTo.toLocaleDateString('ar-SA')}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
