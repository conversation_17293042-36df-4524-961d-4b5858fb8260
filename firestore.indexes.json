{
  "indexes": [
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "userType",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "stores",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "approvalStatus",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "products",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "category",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "representatives",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "approvalStatus",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    }
    {
      "collectionGroup": "coupons",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "coupons",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "type",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "coupons",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "validUntil",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "coupons",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "code",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "coupon_usage_logs",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "usedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "coupon_usage_logs",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "customerId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "usedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "coupon_usage_logs",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "couponId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "usedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_programs",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_members",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "joinedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_members",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "customerId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_members",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "currentTier",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "joinedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_transactions",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "customerId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_transactions",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "type",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_redemptions",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "customerId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "redeemedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "loyalty_redemptions",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "merchantId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "status",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "redeemedAt",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
}
