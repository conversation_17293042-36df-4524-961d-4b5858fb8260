import { useState, useEffect } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  getDocs, 
  doc, 
  getDoc, 
  updateDoc, 
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { OrderDocument, OrderStatus } from '@/types';

interface UseOrdersOptions {
  merchantUid?: string;
  customerId?: string;
  representativeUid?: string | null; // null للطلبات غير المعينة لمندوب
  status?: OrderStatus;
  realtime?: boolean;
  limit?: number;
}

interface UseOrdersReturn {
  orders: OrderDocument[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
}

export function useOrders(options: UseOrdersOptions = {}): UseOrdersReturn {
  const { merchantUid, customerId, representativeUid, status, realtime = false, limit } = options;
  const [orders, setOrders] = useState<OrderDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      let q = query(collection(db, 'orders'));

      // Add filters
      if (merchantUid) {
        q = query(q, where('merchantUid', '==', merchantUid));
      }
      if (customerId) {
        q = query(q, where('customerId', '==', customerId));
      }
      if (representativeUid !== undefined) {
        if (representativeUid === null) {
          // البحث عن الطلبات غير المعينة لمندوب
          q = query(q, where('representativeUid', '==', null));
        } else {
          // البحث عن طلبات مندوب محدد
          q = query(q, where('representativeUid', '==', representativeUid));
        }
      }
      if (status) {
        q = query(q, where('status', '==', status));
      }

      // Add ordering
      q = query(q, orderBy('createdAt', 'desc'));

      // Add limit if specified
      if (limit) {
        q = query(q, limit);
      }

      const snapshot = await getDocs(q);
      const ordersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as OrderDocument[];

      setOrders(ordersData);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to fetch orders');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, status: OrderStatus) => {
    try {
      await updateDoc(doc(db, 'orders', orderId), {
        status,
        updatedAt: serverTimestamp()
      });

      // Update local state
      setOrders(prev => 
        prev.map(order => 
          order.id === orderId 
            ? { ...order, status }
            : order
        )
      );
    } catch (err) {
      console.error('Error updating order status:', err);
      throw new Error('Failed to update order status');
    }
  };

  useEffect(() => {
    let unsubscribe: Unsubscribe | undefined;

    if (realtime && (merchantUid || customerId || representativeUid !== undefined)) {
      // Set up real-time listener
      let q = query(collection(db, 'orders'));

      if (merchantUid) {
        q = query(q, where('merchantUid', '==', merchantUid));
      }
      if (customerId) {
        q = query(q, where('customerId', '==', customerId));
      }
      if (representativeUid !== undefined) {
        if (representativeUid === null) {
          q = query(q, where('representativeUid', '==', null));
        } else {
          q = query(q, where('representativeUid', '==', representativeUid));
        }
      }
      if (status) {
        q = query(q, where('status', '==', status));
      }

      q = query(q, orderBy('createdAt', 'desc'));

      if (limit) {
        q = query(q, limit);
      }

      setLoading(true);
      unsubscribe = onSnapshot(q, 
        (snapshot) => {
          const ordersData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as OrderDocument[];

          setOrders(ordersData);
          setLoading(false);
          setError(null);
        },
        (err) => {
          console.error('Error in orders listener:', err);
          setError('Failed to listen to orders');
          setLoading(false);
        }
      );
    } else {
      // Fetch once
      fetchOrders();
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [merchantUid, customerId, representativeUid, status, realtime, limit]);

  return {
    orders,
    loading,
    error,
    refetch: fetchOrders,
    updateOrderStatus
  };
}

interface UseOrderReturn {
  order: OrderDocument | null;
  loading: boolean;
  error: string | null;
  updateStatus: (status: OrderStatus) => Promise<void>;
  refetch: () => Promise<void>;
}

export function useOrder(orderId: string, merchantUid?: string): UseOrderReturn {
  const [order, setOrder] = useState<OrderDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = async () => {
    if (!orderId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const orderDoc = await getDoc(doc(db, 'orders', orderId));
      
      if (!orderDoc.exists()) {
        setError('Order not found');
        setOrder(null);
        return;
      }

      const orderData = { id: orderDoc.id, ...orderDoc.data() } as OrderDocument;
      
      // Check if this order belongs to the specified merchant
      if (merchantUid && orderData.merchantUid !== merchantUid) {
        setError('Access denied');
        setOrder(null);
        return;
      }

      setOrder(orderData);
    } catch (err) {
      console.error('Error fetching order:', err);
      setError('Failed to fetch order');
      setOrder(null);
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (status: OrderStatus) => {
    if (!order) return;

    try {
      await updateDoc(doc(db, 'orders', order.id), {
        status,
        updatedAt: serverTimestamp()
      });

      setOrder(prev => prev ? { ...prev, status } : null);
    } catch (err) {
      console.error('Error updating order status:', err);
      throw new Error('Failed to update order status');
    }
  };

  useEffect(() => {
    fetchOrder();
  }, [orderId, merchantUid]);

  return {
    order,
    loading,
    error,
    updateStatus,
    refetch: fetchOrder
  };
}

// Hook for order statistics
interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
}

interface UseOrderStatsReturn {
  stats: OrderStats;
  loading: boolean;
  error: string | null;
}

export function useOrderStats(merchantUid: string): UseOrderStatsReturn {
  const [stats, setStats] = useState<OrderStats>({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalRevenue: 0,
    averageOrderValue: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      if (!merchantUid) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const q = query(
          collection(db, 'orders'),
          where('merchantUid', '==', merchantUid)
        );

        const snapshot = await getDocs(q);
        const orders = snapshot.docs.map(doc => doc.data()) as OrderDocument[];

        const totalOrders = orders.length;
        const pendingOrders = orders.filter(order => 
          ['pending', 'confirmed', 'preparing', 'ready', 'shipped'].includes(order.status)
        ).length;
        const completedOrders = orders.filter(order => order.status === 'delivered').length;
        const totalRevenue = orders
          .filter(order => order.status === 'delivered')
          .reduce((sum, order) => sum + (order.finalTotal || order.totalAmount), 0);
        const averageOrderValue = completedOrders > 0 ? totalRevenue / completedOrders : 0;

        setStats({
          totalOrders,
          pendingOrders,
          completedOrders,
          totalRevenue,
          averageOrderValue
        });
      } catch (err) {
        console.error('Error fetching order stats:', err);
        setError('Failed to fetch order statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [merchantUid]);

  return { stats, loading, error };
}
