// src/app/[locale]/merchant/reports/page.tsx
"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import {
  ArrowLeft, BarChart3, TrendingUp, TrendingDown, DollarSign, Package,
  ShoppingCart, Users, Calendar, Download, FileText, PieChart,
  Activity, Target, Clock, Star, Loader2
} from 'lucide-react';
import Link from 'next/link';
import { collection, query, where, getDocs, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { OrderDocument, ProductDocument } from '@/types';

interface ReportData {
  totalSales: number;
  totalOrders: number;
  totalProducts: number;
  averageOrderValue: number;
  topProducts: Array<{
    id: string;
    name: string;
    sales: number;
    orders: number;
    revenue: number;
  }>;
  salesByPeriod: Array<{
    period: string;
    sales: number;
    orders: number;
  }>;
  ordersByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
}

type ReportPeriod = 'week' | 'month' | 'quarter' | 'year';

export default function MerchantReportsPage() {
  const { user } = useAuth();
  const { t, locale } = useLocale();
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<ReportPeriod>('month');
  const [reportData, setReportData] = useState<ReportData>({
    totalSales: 0,
    totalOrders: 0,
    totalProducts: 0,
    averageOrderValue: 0,
    topProducts: [],
    salesByPeriod: [],
    ordersByStatus: []
  });

  // جلب بيانات التقارير
  const fetchReportData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // حساب تاريخ البداية حسب الفترة المحددة
      const now = new Date();
      const startDate = new Date();

      switch (selectedPeriod) {
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      // جلب الطلبات
      const ordersQuery = query(
        collection(db, 'orders'),
        where('merchantUid', '==', user.uid),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        orderBy('createdAt', 'desc')
      );

      const ordersSnapshot = await getDocs(ordersQuery);
      const orders = ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as OrderDocument[];

      // جلب المنتجات
      const productsQuery = query(
        collection(db, 'products'),
        where('merchantUid', '==', user.uid)
      );

      const productsSnapshot = await getDocs(productsQuery);
      const products = productsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ProductDocument[];

      // حساب الإحصائيات
      const totalSales = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
      const totalOrders = orders.length;
      const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

      // أفضل المنتجات
      const productSales = new Map();
      orders.forEach(order => {
        order.items?.forEach(item => {
          const existing = productSales.get(item.productId) || {
            id: item.productId,
            name: item.productName,
            sales: 0,
            orders: 0,
            revenue: 0
          };
          existing.sales += item.quantity;
          existing.orders += 1;
          existing.revenue += item.price * item.quantity;
          productSales.set(item.productId, existing);
        });
      });

      const topProducts = Array.from(productSales.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5);

      // المبيعات حسب الفترة
      const salesByPeriod = generatePeriodData(orders, selectedPeriod);

      // الطلبات حسب الحالة
      const statusCounts = new Map();
      orders.forEach(order => {
        const count = statusCounts.get(order.status) || 0;
        statusCounts.set(order.status, count + 1);
      });

      const ordersByStatus = Array.from(statusCounts.entries()).map(([status, count]) => ({
        status,
        count: count as number,
        percentage: totalOrders > 0 ? ((count as number) / totalOrders) * 100 : 0
      }));

      setReportData({
        totalSales,
        totalOrders,
        totalProducts: products.length,
        averageOrderValue,
        topProducts,
        salesByPeriod,
        ordersByStatus
      });

    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('حدث خطأ في جلب بيانات التقارير');
    } finally {
      setLoading(false);
    }
  };

  // توليد بيانات الفترة
  const generatePeriodData = (orders: OrderDocument[], period: ReportPeriod) => {
    const data = new Map();
    const now = new Date();

    // إنشاء الفترات
    const periods = [];
    for (let i = 0; i < (period === 'week' ? 7 : period === 'month' ? 30 : 12); i++) {
      const date = new Date(now);
      if (period === 'week') {
        date.setDate(now.getDate() - i);
        periods.push(date.toLocaleDateString('ar-SA', { weekday: 'short' }));
      } else if (period === 'month') {
        date.setDate(now.getDate() - i);
        periods.push(date.toLocaleDateString('ar-SA', { day: 'numeric', month: 'short' }));
      } else {
        date.setMonth(now.getMonth() - i);
        periods.push(date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }));
      }
    }

    // تجميع البيانات
    orders.forEach(order => {
      const orderDate = order.createdAt?.toDate();
      if (!orderDate) return;

      let periodKey = '';
      if (period === 'week') {
        periodKey = orderDate.toLocaleDateString('ar-SA', { weekday: 'short' });
      } else if (period === 'month') {
        periodKey = orderDate.toLocaleDateString('ar-SA', { day: 'numeric', month: 'short' });
      } else {
        periodKey = orderDate.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' });
      }

      const existing = data.get(periodKey) || { period: periodKey, sales: 0, orders: 0 };
      existing.sales += order.totalAmount || 0;
      existing.orders += 1;
      data.set(periodKey, existing);
    });

    return periods.map(period => data.get(period) || { period, sales: 0, orders: 0 }).reverse();
  };

  // تصدير التقرير
  const exportReport = async (format: 'pdf' | 'excel') => {
    setExporting(true);
    try {
      // هنا يمكن إضافة منطق التصدير الفعلي
      await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة التصدير
      toast.success(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`);
    } catch (error) {
      toast.error('حدث خطأ في تصدير التقرير');
    } finally {
      setExporting(false);
    }
  };

  // تحديث البيانات عند تغيير الفترة
  useEffect(() => {
    fetchReportData();
  }, [user, selectedPeriod]);

  // ترجمة حالات الطلبات
  const getStatusLabel = (status: string) => {
    const statusLabels: { [key: string]: string } = {
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'preparing': 'قيد التحضير',
      'ready': 'جاهز',
      'shipped': 'تم الشحن',
      'delivered': 'تم التسليم',
      'cancelled': 'ملغي'
    };
    return statusLabels[status] || status;
  };

  // ترجمة الفترات
  const getPeriodLabel = (period: ReportPeriod) => {
    const periodLabels: { [key: string]: string } = {
      'week': 'آخر أسبوع',
      'month': 'آخر شهر',
      'quarter': 'آخر 3 أشهر',
      'year': 'آخر سنة'
    };
    return periodLabels[period];
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-20" />
              <div>
                <Skeleton className="h-8 w-40 mb-2" />
                <Skeleton className="h-4 w-60" />
              </div>
            </div>
            <Skeleton className="h-10 w-32" />
          </div>

          {/* Stats Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-24 mb-1" />
                  <Skeleton className="h-3 w-16" />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Charts Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-64 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link href={`/${locale}/merchant/dashboard`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <BarChart3 className="h-8 w-8 text-primary" />
                التقارير والتحليلات
              </h1>
              <p className="text-muted-foreground">
                تحليل شامل لأداء متجرك ومبيعاتك
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <Select value={selectedPeriod} onValueChange={(value: ReportPeriod) => setSelectedPeriod(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">آخر أسبوع</SelectItem>
                <SelectItem value="month">آخر شهر</SelectItem>
                <SelectItem value="quarter">آخر 3 أشهر</SelectItem>
                <SelectItem value="year">آخر سنة</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => exportReport('excel')}
              disabled={exporting}
            >
              {exporting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              تصدير Excel
            </Button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold">{reportData.totalSales.toLocaleString('ar-SA')} ريال</p>
                  <p className="text-xs text-muted-foreground">{getPeriodLabel(selectedPeriod)}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">عدد الطلبات</p>
                  <p className="text-2xl font-bold">{reportData.totalOrders.toLocaleString('ar-SA')}</p>
                  <p className="text-xs text-muted-foreground">{getPeriodLabel(selectedPeriod)}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ShoppingCart className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">متوسط قيمة الطلب</p>
                  <p className="text-2xl font-bold">{reportData.averageOrderValue.toLocaleString('ar-SA')} ريال</p>
                  <p className="text-xs text-muted-foreground">لكل طلب</p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold">{reportData.totalProducts.toLocaleString('ar-SA')}</p>
                  <p className="text-xs text-muted-foreground">منتج نشط</p>
                </div>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* الرسوم البيانية والتحليلات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* مخطط المبيعات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                اتجاه المبيعات - {getPeriodLabel(selectedPeriod)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.salesByPeriod.length > 0 ? (
                  <div className="space-y-3">
                    {reportData.salesByPeriod.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.period}</span>
                        <div className="flex items-center gap-2">
                          <div className="text-right">
                            <div className="text-sm font-bold">{item.sales.toLocaleString('ar-SA')} ريال</div>
                            <div className="text-xs text-muted-foreground">{item.orders} طلب</div>
                          </div>
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${Math.max(5, (item.sales / Math.max(...reportData.salesByPeriod.map(s => s.sales))) * 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>لا توجد بيانات مبيعات للفترة المحددة</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* توزيع حالات الطلبات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                توزيع حالات الطلبات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.ordersByStatus.length > 0 ? (
                  <div className="space-y-3">
                    {reportData.ordersByStatus.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{
                              backgroundColor: [
                                '#3b82f6', '#10b981', '#f59e0b',
                                '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16'
                              ][index % 7]
                            }}
                          />
                          <span className="text-sm font-medium">{getStatusLabel(item.status)}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-bold">{item.count}</div>
                          <div className="text-xs text-muted-foreground">{item.percentage.toFixed(1)}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <PieChart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>لا توجد طلبات للفترة المحددة</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* أفضل المنتجات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              أفضل المنتجات أداءً - {getPeriodLabel(selectedPeriod)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {reportData.topProducts.length > 0 ? (
              <div className="space-y-4">
                {reportData.topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                        <span className="text-sm font-bold text-primary">#{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {product.sales} قطعة مباعة في {product.orders} طلب
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">
                        {product.revenue.toLocaleString('ar-SA')} ريال
                      </div>
                      <div className="text-sm text-muted-foreground">إجمالي الإيرادات</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>لا توجد مبيعات منتجات للفترة المحددة</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* أزرار التصدير */}
        <div className="flex justify-center gap-4 mt-8">
          <Button
            variant="outline"
            onClick={() => exportReport('pdf')}
            disabled={exporting}
          >
            {exporting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FileText className="h-4 w-4 mr-2" />
            )}
            تصدير PDF
          </Button>

          <Button
            onClick={() => exportReport('excel')}
            disabled={exporting}
          >
            {exporting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            تصدير Excel
          </Button>
        </div>

        {/* ملاحظة */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <Activity className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">ملاحظة حول البيانات</h4>
              <p className="text-sm text-blue-700">
                جميع البيانات المعروضة تعكس أداء متجرك للفترة المحددة.
                يتم تحديث التقارير في الوقت الفعلي مع كل طلب جديد أو تحديث حالة.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
