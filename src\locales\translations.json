{"ar": {"appName": "مِخْلاة", "tagline": "ربط المتاجر المحلية بالعملاء من خلال تجارة إلكترونية سلسة.", "home": "الرئيسية", "pricing": "خطط الاشتراك", "login": "تسجيل الدخول", "signup": "إنشاء حساب", "profile": "الملف الشخصي", "logout": "تسجيل الخروج", "language": "اللغة", "english": "الإنجليزية", "arabic": "العربية", "homeHeroTitle": "مِخْلاة: كنوز مدينتك، تصلك حيث كنت.", "homeHeroSubtitle": "اكتشف سوقًا نابضًا بالحياة يجمع منتجات فريدة من المتاجر المحلية والحرفيين. منتجات طازجة، مصنوعات يدوية، أزياء محلية، والمزيد – ادعم جيرانك وتمتع بالجودة.", "getStarted": "اب<PERSON><PERSON> الآن", "getStartedFree": "ابدأ مجاناً", "browseStores": "تص<PERSON><PERSON> المتاجر", "copyright": "© {{year}} مِخْلاة. جميع الحقوق محفوظة.", "merchants": "التجار", "customers": "العملاء", "representative": "مندوب", "representatives": "المندوبين", "merchantPlansTitle": "<PERSON><PERSON><PERSON> التجار", "customerPlansTitle": "<PERSON>طط العملاء", "plan_basic_name": "التاجر الأساسي", "plan_premium_name": "التاجر المميز", "plan_business_name": "تاجر الأعمال", "plan_customer_basic_name": "العميل الأساسي", "plan_customer_premium_name": "العميل المميز", "SAR": "ريال سعودي", "sar": "ريال", "monthly": "شهرياً", "month": "شهر", "free": "مجاني", "pricePerPeriod": "{{price}} {{currency}} / {{period}}", "commission": "عمولة {{value}}% على المبيعات", "basicStoreManagement": "إدارة أساسية للمتجر", "addManageProducts": "إضافة وإدارة المنتجات", "emailSupport": "دعم عبر البريد الإلكتروني", "simpleDashboard": "لوحة تحكم بسيطة", "basicStats": "إحصائيات أساسية", "basicOrderManagement": "إدارة أساسية للطلبات", "standardStorePage": "صفحة متجر قياسية", "productImages3": "حتى 3 صور لكل منتج", "basicInventory": "إدارة أساسية للمخزون", "simpleMonthlyReports": "تقارير شهرية بسيطة", "basicPaymentIntegration": "تكامل أساسي مع أنظمة الدفع", "basicRatingSystem": "نظام تقييم أساسي", "basicEmailNotifications": "إشعارات البريد الإلكتروني الأساسية", "basicDataExport": "تصدير البيانات الأساسي", "manualDataBackup": "نسخ احتياطي يدوي للبيانات", "password": "كلمة المرور", "subscription": "الاشتراك", "orders": "الطلبات", "changePassword": "تغيير كلمة المرور", "changePasswordDescription": "قم بتحديث كلمة المرور الخاصة بك. يجب أن تكون كلمة المرور الجديدة مكونة من 8 أحرف على الأقل.", "currentPassword": "كلمة المرور الحالية", "newPassword": "كلمة المرور الجديدة", "confirmPassword": "تأكيد كلمة المرور", "enterCurrentPassword": "أدخل كلمة المرور الحالية", "enterNewPassword": "أدخل كلمة المرور الجديدة", "confirmNewPassword": "أكد كلمة المرور الجديدة", "updatePassword": "تحديث كلمة المرور", "passwordChangeSuccessTitle": "تم تغيير كلمة المرور", "passwordChangeSuccessMessage": "تم تحديث كلمة المرور الخاصة بك بنجاح.", "wrongCurrentPassword": "كلمة المرور الحالية غير صحيحة", "weakPassword": "كلمة المرور الجديدة ضعيفة جدًا. يجب أن تكون مكونة من 8 أحرف على الأقل.", "recentLoginRequired": "يجب إعادة تسجيل الدخول قبل تغيير كلمة المرور", "passwordChangeFailed": "فشل تغيير كلمة المرور. يرجى المحاولة مرة أخرى.", "subscriptionInfo": "معلومات الاشتراك", "subscriptionNotFound": "لم يتم العثور على معلومات الاشتراك", "subscriptionError": "خطأ في الاشتراك", "subscriptionErrorMessage": "حد<PERSON> خطأ أثناء تحميل معلومات الاشتراك. يرجى المحاولة مرة أخرى.", "contactSupportForHelp": "تواصل مع الدعم الفني للحصول على المساعدة", "userDocumentNotFound": "لم يتم العثور على بيانات المستخدم", "accountSetupComplete": "تم إعداد الحساب بنجاح", "customerAccountCreated": "تم إنشاء حساب عميل وتفعيل الخطة المجانية تلقائياً", "failedToCreateUserDocument": "فشل في إنشاء بيانات المستخدم", "retryLoading": "إعادة المحاولة", "contactSupport": "تواصل مع الدعم", "popular": "الأكثر شيوعًا", "merchantAccount": "حساب تاجر", "customerAccount": "<PERSON><PERSON><PERSON><PERSON> عميل", "planFeatures": "ميزات الخطة", "andMoreFeatures": "و {{count}} ميزات أخرى", "viewAllPlans": "عرض جميع الخطط", "upgradeForMoreFeatures": "قم بالترقية للحصول على المزيد من الميزات", "upgradePlan": "ترقية الخطة", "customerUpgradeBenefits": "احصل على شحن مجاني وخصومات حصرية", "merchantUpgradeBenefits": "احصل على ميزات متقدمة وتحليلات شاملة", "currentPlan": "الخطة الحالية", "availableFeatures": "الميزات المتاحة", "productImages": "صور المنتج", "selectPlan": "اختيار الخطة", "activePlan": "الخطة النشطة", "defaultPlanInfo": "معلومات الخطة الافتراضية", "defaultCustomerPlanMessage": "أنت مسجل كعميل مع الخطة المجانية الأساسية", "defaultMerchantPlanMessage": "أنت مسجل كتاجر مع الخطة المجانية الأساسية", "features": "المميزات", "featuresPageTitle": "مميزات المنصة", "featuresPageSubtitle": "اكتشف جميع المميزات المتاحة لكل نوع من المستخدمين", "customerFeaturesTitle": "مميزات العملاء", "merchantFeaturesTitle": "مميزات التجار", "allFeatures": "جميع المميزات", "featureIncluded": "متضمنة", "featureNotIncluded": "غير متضمنة", "totalFeatures": "إجمالي المميزات", "loginRequired": "تسجيل الدخول مطلوب", "loginToViewSubscription": "يرجى تسجيل الدخول لعرض معلومات الاشتراك", "loadingSubscriptionInfo": "جاري تحميل معلومات الاشتراك...", "myOrders": "طلباتي", "recentOrders": "الطلبات الحديثة", "recentOrdersDescription": "تتبع وإدارة طلباتك الحديثة", "noOrdersYet": "لا توجد طلبات حتى الآن", "startShoppingMessage": "ابدأ التسوق واستمتع بتجربة مِخْلاة", "orderNumber": "رق<PERSON> الطلب", "date": "التاريخ", "items": "العناصر", "total": "المجموع", "status": "الحالة", "viewAllOrders": "عرض جميع الطلبات", "pending": "قيد الانتظار", "processing": "قيد المعالجة", "shipped": "تم الشحن", "delivered": "تم التسليم", "cancelled": "ملغي", "customerAvatarInfo": "العملاء يستخدمون الصورة الرمزية التلقائية بناءً على الاسم", "username": "اسم المستخدم", "emailAddress": "الب<PERSON>يد الإلكتروني", "notSet": "<PERSON>ي<PERSON> محدد", "editProfile": "تعديل الملف الشخصي", "uploadImage": "رفع صورة", "generateAvatar": "إنشاء صورة رمزية", "saveChanges": "حفظ التغييرات", "cancel": "إلغاء", "loadingProfile": "جاري تحميل الملف الشخصي...", "loadingTerms": "جاري تحميل الشروط والأحكام...", "loading": "جاري التحميل...", "alreadyLoggedIn": "أنت مسجل دخول بالفعل", "signupSuccessful": "تم التسجيل بنجاح!", "redirectingToYourDashboard": "جاري توجيهك إلى لوحة التحكم الخاصة بك...", "redirectingToYourAccount": "جاري توجيهك إلى حسابك...", "authenticationError": "خطأ في المصادقة", "authenticationErrorMessage": "حد<PERSON> خطأ أثناء التحقق من هويتك. يرجى المحاولة مرة أخرى.", "securityRecommendation": "لأمانك، نوصي بتسجيل الخروج وإعادة تسجيل الدخول.", "secureLogout": "تسجيل خروج آمن", "securityLogoutMessage": "تم تسجيل خروجك لأسباب أمنية.", "retry": "إعادة المحاولة", "loggingOut": "جاري تسجيل الخروج...", "myProfile": "ملفي الشخصي", "discoverNearbyTitle": "اكتشف المتاجر القريبة منك", "discoverNearbySubtitle": "تصفح المتاجر المحلية القريبة من موقعك واستمتع بتجربة تسوق فريدة", "fetchingLocation": "جاري تحديد موقعك...", "locationPermissionLoginRequired": "يرجى تسجيل الدخول للوصول إلى المتاجر القريبة منك", "locationPermissionDenied": "تم رفض الوصول إلى الموقع. يرجى تمكين خدمة الموقع في متصفحك للوصول إلى هذه الميزة", "locationUnavailable": "معلومات الموقع غير متاحة حاليًا", "locationRequestTimeout": "انتهت مهلة طلب الموقع. يرجى المحاولة مرة أخرى", "locationErrorUnknown": "حد<PERSON> خطأ غير معروف أثناء محاولة تحديد موقعك", "geolocationNotSupported": "متصفحك لا يدعم خدمة تحديد الموقع", "popularCategoriesTitle": "الفئات الشائعة", "popularCategoriesSubtitle": "استكشف مجموعة متنوعة من المنتجات المحلية المميزة", "categoryGroceries": "البقالة", "categoryGroceriesDesc": "منتجات طازجة ومواد غذائية من المتاجر المحلية", "categoryHandicrafts": "الحرف اليدوية", "categoryHandicraftsDesc": "منتجات يدوية فريدة من الحرفيين المحليين", "categoryFashion": "الأزياء", "categoryFashionDesc": "ملابس وإكسسوارات من المصممين المحليين", "categoryHealthBeauty": "الصحة والجمال", "categoryHealthBeautyDesc": "منتجات طبيعية للعناية بالصحة والجمال", "merchantCtaTitle": "هل أنت صاحب متجر؟", "merchantCtaSubtitle": "انضم إلى مجتمع مِخْلاة وابدأ في بيع منتجاتك عبر الإنترنت", "merchantBenefitsTitle": "مزايا الانضمام إلينا", "merchantBenefit1": "وصول إلى قاعدة عملاء واسعة في منطقتك", "merchantBenefit2": "نظام إدارة متجر سهل الاستخدام", "merchantBenefit3": "رسوم منخفضة وشفافة", "merchantBenefit4": "دعم فني على مدار الساعة", "listYourStore": "سجل متجرك الآن", "switchToEnglish": "التبديل إلى اللغة الإنجليزية", "switchToArabic": "التبديل إلى اللغة العربية", "termsAndConditions": "الشروط والأحكام", "termsAndConditionsTitle": "الشروط والأحكام", "termsAndConditionsForCustomers": "الشروط والأحكام للعملاء", "termsAndConditionsForMerchants": "الشروط والأحكام للتجار", "termsAndConditionsForRepresentatives": "الشروط والأحكام للمندوبين", "generalTermsText": "يرجى اختيار نوع الحسا<PERSON> (عميل أو تاجر) لعرض الشروط والأحكام الخاصة بك.", "close": "إغلاق", "customerTermsText": "مرحباً بك في منصة مِخْلاة!\n\nتحدد هذه الاتفاقية ('الشروط'، 'الاتفاقية') القواعد واللوائح الملزمة قانونياً لاستخدامك لمنصة مِخْلاة ('المنصة'، 'الخدمة') كعميل ('أنت'، 'العميل'). بالتسجيل واستخدام المنصة، فإنك تقر بأنك قد قرأت وفهمت ووافقت على الالتزام بجميع هذه الشروط.\n\n1. **تسجيل الحساب والأهلية القانونية**\n2. **المشتريات والمدفوعات**\n3. **التوصيل والاستلام** (يتضمن الموافقة على الوصول للموقع)\n4. **الإرجاع والاستبدال وحقوق المستهلك**\n5. **دور منصة مِخْلاة والمسؤوليات**\n6. **استخدام المنصة والسلوك المقبول**\n7. **الخصوصية وحماية البيانات**\n8. **التواصل والإشعارات**\n9. **الشكاوى وحل النزاعات**\n10. **الملكية الفكرية**\n11. **تعديل الشروط والإشعارات**\n12. **إنهاء الحساب**\n13. **القانون الحاكم وحل النزاعات**\n\nشكراً لثقتك واستخدامك لمنصة مِخْلاة. نتمنى لك تجربة تسوق ممتعة وآمنة!", "merchantTermsText": "مرحباً بك في منصة مِخْلاة للتجار!\n\nتحدد هذه الاتفاقية ('الشروط'، 'الاتفاقية') القواعد واللوائح الملزمة قانونياً لاستخدامك لمنصة مِخْلاة ('المنصة'، 'الخدمة') كتاجر ('أنت'، 'التاجر'). بالتسجيل واستخدام المنصة، فإنك تقر بأنك قد قرأت وفهمت ووافقت على الالتزام بجميع هذه الشروط. إذا لم توافق على أي جزء من هذه الشروط، فيجب عليك عدم استخدام المنصة.\n\n1. **تسجيل حساب التاجر والتحقق منه**\n2. **إدارة المتجر وقوائم المنتجات**\n3. **تنفيذ الطلبات والشحن**\n4. **الرسوم والعمولات والمدفوعات**\n5. **المرتجعات والتبادل وخدمة العملاء**\n6. **البيانات والخصوصية والملكية الفكرية**\n7. **الامتثال والمسؤوليات القانونية**\n8. **إنهاء الاتفاقية**\n9. **أحكام عامة**\n\nشكراً لاستخدامك منصة مِخْلاة للتجار!", "iAgreeToThe": "أ<PERSON><PERSON><PERSON><PERSON> على", "orSignupManually": "أو التسجيل يدوياً", "alreadyHaveAccount": "لديك حساب بالفعل؟", "errorLoadingTerms": "حد<PERSON> خطأ أثناء تحميل الشروط والأحكام", "createAnAccount": "إنشاء حساب", "passwordLengthHint": "يجب أن تتكون كلمة المرور من 8 أحرف على الأقل", "userType": "نوع الحساب", "customer": "عميل", "merchant": "تاجر", "errorUserTypeRequired": "يرجى اختيار نوع المستخدم.", "errorTermsRequired": "يجب عليك قبول الشروط والأحكام للمتابعة.", "errorCommercialRegistrationRequired": "يرجى تحميل شهادة السجل التجاري الخاصة بك.", "errorFreelanceDocumentRequired": "يرجى تحميل وثيقة العمل الحر من منصة العمل الحر.", "fileFormatsAllowed": "الصيغ المسموح بها: PDF، JPG، PNG.", "commercialRegistration": "السجل التجاري", "otherLicenses": "التراخيص الأخرى", "freelanceDocument": "وثيقة العمل الحر", "passwordsDoNotMatch": "كلمات المرور غير متطابقة", "errorTitle": "خطأ", "signupInProgressTitle": "جاري إنشاء الحساب", "signupInProgressMessage": "جاري إنشاء حسابك، يرجى الانتظار...", "signupSuccessTitle": "تم إنشاء الحساب بنجاح", "signupSuccessMessage": "تم إنشاء حسابك بنجاح. سيتم توجيهك إلى لوحة التحكم الخاصة بك.", "signupErrorTitle": "فشل في إنشاء الحساب", "signupErrorMessage": "حدث خطأ أثناء إنشاء حسابك. يرجى المحاولة مرة أخرى لاحقًا.", "passwordTooShort": "كلمة المرور قصيرة جدًا. يجب أن تكون 6 أحرف على الأقل.", "loginToAccount": "تسجيل الدخول إلى حسابك", "rememberMe": "تذكرني", "forgotPassword": "نسيت كلمة المرور؟", "needAccount": "تحتاج إلى حساب؟", "loginSuccessTitle": "تم تسجيل الدخول بنجاح", "loginSuccessMessage": "مرحباً بك مرة أخرى!", "loginFailed": "فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.", "invalidCredentials": "البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.", "logoutSuccessTitle": "تم تسجيل الخروج", "logoutSuccessMessage": "تم تسجيل خروجك بنجاح.", "accountDisabled": "تم تعطيل هذا الحساب. يرجى التواصل مع الدعم الفني.", "tooManyRequests": "تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً.", "networkError": "خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت.", "signingIn": "جاري تسجيل الدخول...", "profileSettings": "إعدادات الملف الشخصي", "manageYourAccountSettings": "إدارة إعدادات حسابك الشخصي", "pleaseWait": "يرجى الانتظار...", "securePayment": "د<PERSON><PERSON> آمن", "premiumSupport": "دعم مميز", "unlimitedProducts": "منتجات غير محدودة", "prioritySupport247": "دعم أولوية على مدار الساعة", "advancedDashboard": "لوحة تحكم متقدمة", "fullStoreCustomization": "تخصيص كامل لصفحة المتجر", "advancedSalesAnalytics": "تحليلات مبيعات متقدمة", "advancedInventorySystem": "نظام مخزون متقدم", "socialMediaIntegration": "تكامل مع وسائل التواصل الاجتماعي", "customerLoyaltySystem": "نظام ولاء العملاء", "detailedWeeklyReports": "تقارير أسبوعية مفصلة", "advancedMarketingTools": "أدوات تسويق متقدمة", "multipleShippingOptions": "خيارات شحن متعددة", "couponsDiscountsSystem": "نظام كوبونات وخصومات", "multiFormatReportExport": "تصدير التقارير بصيغ متعددة", "autoDailyBackup": "نسخ احتياطي تلقائي يومي", "vipSupport": "دعم VIP مع مدير حساب مخصص", "smartAnalyticsPredictions": "تحليلات ذكية مع توقعات مستقبلية", "erpPosIntegration": "تكامل مع أنظمة ERP و POS", "crmSystem": "نظام إدارة علاقات العملاء", "fullSystemCustomization": "تخصيص كامل للنظام", "processMarketingAutomation": "أتمتة العمليات والتسويق", "realtimeAdvancedAnalytics": "تقارير تحليلية متقدمة في الوقت الفعلي", "multiBranchManagement": "إدارة متعددة الفروع", "advancedPerformanceMonitoring": "نظام مراقبة الأداء المتقدم", "advancedFraudProtection": "حماية متقدمة من الاحتيال", "accountingIntegration": "تكامل مع أنظمة المحاسبة", "autoHourlyBackup": "نسخ احتياطي تلقائي كل ساعة", "businessConsultingServices": "خدمات استشارات الأعمال", "customTeamTraining": "تدريب مخصص للفريق", "freeAccountCreation": "إنشاء حساب مجاني", "browseProducts": "تصف<PERSON> المنتجات", "addToCartWishlist": "إضافة إلى السلة وقائمة الأمنيات", "basicSearch": "ب<PERSON><PERSON> أ<PERSON>ا<PERSON>ي", "basicOrderTracking": "تتبع أساسي للطلبات", "ePayment": "الدفع الإلكتروني", "ratingsReviews": "التقييمات والمراجعات", "saveAddresses": "ح<PERSON><PERSON> العناوين", "orderHistory": "تاريخ الطلبات", "socialSharing": "المشاركة الاجتماعية", "profileManagement": "إدارة الملف الشخصي", "passwordRecovery": "استرداد كلمة المرور", "basicAlerts": "تنبيهات أساسية", "unlimitedFreeShipping": "شحن مجاني غير محدود", "discount20all": "خصم 20% على جميع المشتريات", "priorityOrders": "أولوية في الطلبات", "exclusiveOffers": "عروض حصرية", "doubleRewardPoints": "نقاط مكافآت مضاعفة", "freeOrderCancellation": "إلغاء مجاني للطلبات", "earlyAccessSales": "وصول مبكر للتخفيضات", "vipCustomerService": "خد<PERSON>ة عملاء VIP", "advancedOrderTracking": "تتبع متقدم للطلبات", "instantNotifications": "إشعارات فورية", "monthlyVouchers": "قسائم شراء شهرية", "cashbackPurchases": "استرداد نقدي على المشتريات", "premiumClubMembership": "عضوية النادي المميز", "allOrders": "جميع الطلبات", "ordersFound": "تم العثور على {{count}} طلب", "noOrdersDescription": "لم تتلق أي طلبات بعد. ستظهر الطلبات هنا عندما يبدأ العملاء بالطلب من متجرك.", "backToDashboard": "العودة إلى لوحة التحكم", "viewDetails": "عرض التفاصيل", "actions": "الإجراءات", "showing": "<PERSON><PERSON><PERSON>", "of": "من", "loadingProducts": "جاري تحميل المنتجات...", "browseAllProductsTitle": "تصفح جميع المنتجات", "noProductsAvailableTitle": "لا توجد منتجات متاحة حتى الآن", "noProductsAvailableDesc": "يبدو أنه لا توجد منتجات مدرجة في الوقت الحالي. تحقق مرة أخرى قريباً!", "errorFetchingProductsGeneral": "حدث خطأ أثناء جلب المنتجات. يرجى المحاولة مرة أخرى لاحقاً.", "byStore": "بواسطة: {{storeName}}", "unknownStore": "متجر غير معروف", "addToCart": "إضافة للسلة", "noImageAvailable": "لا توجد صورة", "loadingStoreData": "جاري تحميل بيانات المتجر...", "myProducts": "منتجاتي", "errorFetchingProducts": "خطأ في جلب منتجاتك. يرجى المحاولة مرة أخرى.", "noProductsFoundTitle": "لم يتم العثور على منتجات", "noProductsFoundDesc": "لم تقم بإضافة أي منتجات بعد. ابدأ بإضافة منتجك الأول.", "addProductNow": "إضافة منتج الآن", "productList": "قائمة المنتجات", "manageYourProductsDesc": "هنا يمكنك عرض أو تعديل أو حذف منتجاتك.", "image": "الصورة", "productNameTable": "الاسم", "category": "الفئة", "price": "السعر", "stock": "المخزون", "active": "نشط", "inactive": "غير نشط", "editProduct": "تعديل المنتج", "deleteProduct": "<PERSON><PERSON><PERSON> المنتج", "noImage": "لا توجد صورة", "mustBeLoggedInAsMerchant": "يجب أن تكون مسجل دخول كتاجر للوصول إلى هذه الصفحة.", "notAllowedToAccessMerchantPage": "غير مخول لك الوصول إلى صفحة التاجر هذه.", "loadingMerchantSection": "جاري تحميل قسم التاجر...", "merchantApproval": "موافقة التاجر", "pendingApproval": "في انتظار الموافقة", "approvalPending": "طلبك قيد المراجعة", "approvalApproved": "تم قبول طلبك!", "approvalRejected": "تم رفض طلبك", "approvalPendingDesc": "نحن نراجع طلب انضمامك كتاجر. سيتم إشعارك بالنتيجة قريباً.", "approvalApprovedDesc": "مبروك! تم قبول طلبك وتفعيل متجرك. يمكنك الآن الوصول للوحة التحكم.", "approvalRejectedDesc": "تم رفض طلبك. يرجى مراجعة المتطلبات والمحاولة مرة أخرى.", "submissionDate": "تاريخ التقديم", "reviewDate": "تاريخ المراجعة", "applicationInfo": "معلومات الطلب", "merchantName": "اسم التاجر", "viewFile": "عرض الملف", "needHelp": "تحتاج مساعدة؟", "goToDashboard": "الذهاب للوحة التحكم", "submitNewApplication": "تقديم طلب جديد", "backToHome": "العودة للرئيسية", "accessDenied": "الوصول مرفوض", "mustBeLoggedInToAccessMerchant": "يجب أن تكون مسجل دخول كتاجر للوصول إلى هذا القسم.", "mustBeMerchantToAccess": "يجب أن تكون تاجراً للوصول إلى هذه الصفحة.", "errorStoreDataNotFound": "لم يتم العثور على بيانات المتجر. يرجى المحاولة مرة أخرى أو التواصل مع الدعم.", "errorFetchingPlan": "خطأ في جلب بيانات خطة الاشتراك.", "confirmDeleteTitle": "تأكيد الحذف", "confirmDeleteProductMessage": "هل أنت متأكد من أنك تريد حذف المنتج \"{{productName}}\"؟ لا يمكن التراجع عن هذا الإجراء.", "delete": "<PERSON><PERSON><PERSON>", "deletingProduct": "جاري حذف المنتج...", "productDeletedSuccessTitle": "تم حذف المنتج", "productDeletedSuccessMessage": "تم حذف المنتج \"{{productName}}\" بنجاح.", "errorDeletingProduct": "فشل في حذف المنتج \"{{productName}}\".", "errorDeletingProductImages": "فشل في حذف صورة أو أكثر للمنتج \"{{productName}}\". تم حذف وثيقة المنتج رغم ذلك.", "unauthorizedMerchantAction": "غير مخول: غير مسموح لك بتنفيذ هذا الإجراء.", "storeNotFoundOrNoAccess": "المتجر غير موجود أو ليس لديك إذن للوصول إليه.", "uploadingAvatarTitle": "جاري رفع الصورة الرمزية...", "uploadingAvatarDesc": "يرجى الانتظار بينما يتم رفع صورتك الرمزية الجديدة إلى Cloudinary...", "profileAvatarUploadSuccess": "تم رفع الصورة الرمزية بنجاح", "profileAvatarUploadSuccessDesc": "تم تعيين صورتك الرمزية الجديدة باستخدام Cloudinary.", "profileAvatarUploadFailed": "فشل رفع الصورة الرمزية إلى Cloudinary. يرجى المحاولة مرة أخرى.", "profileAvatarGenerateSuccess": "تم إنشاء الصورة الرمزية.", "profileOnlyMerchantsCanUploadAvatar": "يمكن للتجار فقط رفع أو إنشاء صورة رمزية.", "uploadingFilesTitle": "جاري رفع الملفات إلى Cloudinary...", "uploadingFilesDesc": "يتم رفع مستنداتك. قد يستغرق هذا لحظة...", "fileUploadSuccessTitle": "تم رفع الملفات إلى Cloudinary", "fileUploadSuccessDesc": "تم رفع مستنداتك بنجاح وربطها بمتجرك.", "errorFileUploadFailed": "فشل الرفع لـ: {{file}}. يرجى المحاولة مرة أخرى أو التواصل مع الدعم.", "crUploadSuccessDescCloudinary": "تم رفع السجل التجاري بنجاح إلى Cloudinary.", "otherLicensesUploadSuccessDescCloudinary": "تم رفع التراخيص الأخرى بنجاح إلى Cloudinary.", "freelanceDocumentUploadSuccessDescCloudinary": "تم رفع وثيقة العمل الحر بنجاح إلى Cloudinary.", "uploadingCRDescCloudinary": "جاري رفع السجل التجاري إلى Cloudinary...", "uploadingOtherLicensesDescCloudinary": "جاري رفع التراخيص الأخرى إلى Cloudinary...", "uploadingFreelanceDocumentDescCloudinary": "جاري رفع وثيقة العمل الحر إلى Cloudinary...", "errorInvalidEmailFormat": "ير<PERSON>ى إدخال تنسيق بريد إلكتروني صحيح.", "loginRedirecting": "جاري توجيهك إلى لوحة التحكم الخاصة بك...", "loadingDashboard": "جاري تحميل لوحة التحكم...", "customerDashboardTitle": "لوحة تحكم العميل", "customerDashboardSubtitle": "إدارة طلباتك وملفك الشخصي والمزيد.", "welcomeUser": "مرحباً، {{name}}!", "quickActions": "إجراءات سريعة", "quickActionsSubtitle": "تنقل بسهولة عبر حسابك.", "browseProductsDashboard": "تصف<PERSON> المنتجات", "manageProfile": "إدارة الملف الشخصي", "accountOverview": "نظرة عامة على الحساب", "accountOverviewSubtitle": "ملخص لنشاطك الأخير.", "accountOverviewPlaceholder": "ستظهر طلباتك وأنشطتك الأخيرة هنا قريباً.", "recommendationsTitle": "التوصيات", "recommendationsSubtitle": "منتجات قد تعجبك.", "recommendationsPlaceholder": "سيكون لدينا بعض التوصيات لك قريباً!", "goToHomepage": "الذها<PERSON> إلى الصفحة الرئيسية", "errorFetchingUserData": "حدث خطأ أثناء جلب بيانات المستخدم الخاصة بك. يرجى المحاولة مرة أخرى.", "redirectingTitle": "جاري التوجيه", "merchantDashboard": "لوحة تحكم التاجر", "welcomeMerchant": "مرحباً بك، {{name}}", "storeNameLabel": "اسم المتجر:", "representativeDashboard": "لوحة تحكم المندوب", "welcomeRepresentative": "مرحباً، {{representativeName}}!", "representativeAccount": "حساب مندوب", "becomeRepresentative": "كن مندوباً", "representativeSignup": "تسجيل مندوب", "representativeLogin": "دخول المندوب", "representativeProfile": "الملف الشخصي للمندوب", "representativeOrders": "طلبات التوصيل", "representativeEarnings": "الأرباح", "representativeStats": "إحصائيات المندوب", "representativeSettings": "إعدادات المندوب", "plan_representative_basic_name": "الخطة الأساسية", "plan_representative_premium_name": "الخطة المميزة", "unlimitedDeliveries": "توصيلات غير محدودة", "basicSupport": "دعم أساسي", "advancedStats": "إحصائيات متقدمة", "detailedReports": "تقارير مفصلة", "advancedGpsTracking": "تتبع GPS متقدم", "advancedNotifications": "إشعارات متقدمة", "dailyPayouts": "دفعات يومية", "weeklyPayouts": "دفعات أسبوعية", "commission5Percent": "عمولة 5%", "commission10Percent": "عمولة 10%", "bonusIncentives": "حوا<PERSON>ز إضافية", "flexibleSchedule": "جدول مرن", "basicTraining": "تدريب أساسي", "premiumTraining": "تدريب متقدم", "standardPriority": "أولوية عادية", "basicEarningsReports": "تقارير أرباح أساسية", "mobileApp": "تطبيق الجوال", "gpsTracking": "تتبع GPS", "customerRatings": "تقييمات العملاء", "basicNotifications": "إشعارات أساسية", "upgradeToPremium": "ترقية للمميزة", "personalInformation": "المعلومات الشخصية", "identityInformation": "معلومات الهوية", "drivingLicense": "رخصة القيادة", "vehicleInformation": "معلومات المركبة", "vehicleInspection": "شهادة الفحص الدوري", "documentsUpload": "رفع الوثائق", "fullName": "الاسم الكامل", "phoneNumber": "رقم الهاتف", "nationalId": "رقم الهوية", "nationalIdType": "نوع الهوية", "nationalIdCard": "هوية وطنية", "residentId": "إقامة", "licenseNumber": "رقم الرخصة", "issueDate": "تاريخ الإصدار", "expiryDate": "تاريخ الانتهاء", "licenseImage": "صورة الرخصة", "vehicleType": "نوع المركبة", "vehicleModel": "موديل المركبة", "vehicleYear": "سنة الصنع", "plateNumber": "رقم اللوحة", "vehicleColor": "لون المركبة", "vehicleImage": "صورة المركبة", "car": "سيارة", "motorcycle": "دراجة نارية", "bicycle": "دراجة هوائية", "certificateNumber": "رقم الشهادة", "inspectionImage": "صورة شهادة الفحص", "totalDeliveries": "إجمالي التوصيلات", "completedDeliveries": "التوصيلات المكتملة", "cancelledDeliveries": "التوصيلات الملغية", "averageRating": "متوسط التقييم", "totalEarnings": "إجمالي الأرباح", "monthlyEarnings": "الأرباح الشهرية", "weeklyEarnings": "الأرباح الأسبوعية", "dailyEarnings": "الأرباح اليومية", "averageDeliveryTime": "متوسط وقت التوصيل", "successRate": "معدل النجاح", "interactiveMap": "الخريطة التفاعلية", "findNearbyStores": "اعثر على المتاجر القريبة منك", "detectingLocation": "جاري تحديد موقعك", "detectingLocationDesc": "<PERSON>حن نحدد موقعك لإظهار المتاجر القريبة منك", "enableLocation": "تمكين الموقع", "locationDetected": "تم تحديد الموقع", "locationError": "خطأ في الموقع", "tryAgain": "حاول مرة أخرى", "searchStores": "البحث في المتاجر...", "filters": "الفلاتر", "clearFilters": "<PERSON><PERSON><PERSON> الفلا<PERSON>ر", "storesMap": "خريطة المتاجر", "locationRequired": "الموقع مطلوب", "enableLocationToSeeMap": "قم بتمكين الموقع لرؤية الخريطة", "nearbyStores": "المتاجر القريبة", "noStoresFound": "لم يتم العثور على متاجر", "tryDifferentFilters": "جرب فلاتر مختلفة", "open": "مفتوح", "closed": "مغلق", "km": "كم", "minutes": "دقيقة", "today": "اليوم", "website": "الموقع الإلكتروني", "visitStore": "زيارة المتجر", "directions": "الاتجاهات", "distanceCalculator": "حاسبة المسافة", "distanceTo": "المسافة إلى", "coupons": "الكوبونات", "coupon": "كوبون", "couponCode": "<PERSON>و<PERSON> الكوبون", "couponManagement": "إدارة الكوبونات", "createCoupon": "إنشاء كوبون", "createNewCoupon": "إنشاء كوبون جديد", "editCoupon": "تعديل الكوبون", "deleteCoupon": "<PERSON>ذ<PERSON> الكوبون", "applyCoupon": "تطبيق الكوبون", "removeCoupon": "إزالة الكوبون", "couponApplied": "تم تطبيق الكوبون", "couponRemoved": "تم إزالة الكوبون", "couponDiscount": "خصم الكوبون", "couponType": "نوع الكوبون", "couponValue": "قيمة الكوبون", "couponStatus": "حالة الكوبون", "couponUsage": "استخدام الكوبون", "couponExpiry": "انتهاء الكوبون", "couponLimit": "<PERSON><PERSON> ال<PERSON>و<PERSON>ون", "couponDescription": "وصف الكوبون", "couponConditions": "شروط الكوبون", "percentageDiscount": "خصم نسبة مئوية", "fixedDiscount": "خصم مبلغ ثابت", "freeShipping": "<PERSON><PERSON><PERSON>اني", "minimumOrder": "الح<PERSON> الأدنى للطلب", "maximumDiscount": "الح<PERSON> الأقصى للخصم", "usageLimit": "ح<PERSON> الاستخدام", "usedCount": "عدد مرات الاستخدام", "validFrom": "صالح من", "validUntil": "صا<PERSON><PERSON>تى", "activeCoupons": "الكوبونات النشطة", "expiredCoupons": "الكوبونات المنتهية", "inactiveCoupons": "الكوبونات غير النشطة", "usedUpCoupons": "الكوبونات المستنفدة", "couponAnalytics": "تحليلات الكوبونات", "totalCoupons": "إجمالي الكوبونات", "totalUsage": "إجمالي الاستخدام", "totalDiscount": "إجمالي الخصم", "averageDiscount": "متوسط الخصم", "topPerformingCoupons": "أفضل الكوبونات أداءً", "couponPerformance": "أداء الكوبون", "conversionRate": "معدل التحويل", "monthlyUsage": "الاستخدام الشهري", "usageByType": "الاستخدام حسب النوع", "couponSettings": "إعدادات الكوبونات", "couponTemplates": "قوالب الكوبونات", "generateCode": "توليد الكود", "duplicateCoupon": "نسخ الكوبون", "toggleStatus": "تغيير الحالة", "couponHistory": "تاريخ الكوبون", "usageHistory": "تاريخ الاستخدام", "couponLogs": "سجلات الكوبون", "invalidCoupon": "كوبون غير صالح", "expiredCoupon": "كوبون منتهي الصلاحية", "usedUpCoupon": "كوبون مستنفد", "inactiveCoupon": "كوبون غير نشط", "minimumOrderNotMet": "لم يتم الوصول للحد الأدنى للطلب", "productNotApplicable": "المنتج غير مشمول بالكوبون", "customerNotEligible": "العميل غير مؤهل للكوبون", "couponAlreadyUsed": "تم استخدام الكوبون مسبقاً", "couponSuccessfullyApplied": "تم تطبيق الكوبون بنجاح", "couponValidationFailed": "فشل في التحقق من الكوبون", "enterCouponCode": "<PERSON><PERSON><PERSON><PERSON> كود الكوبون", "applyCouponButton": "تطبيق", "couponCodeRequired": "كود الكوبون مطلوب", "couponCodeInvalid": "كود الكوبون غير صالح", "couponCodeExists": "كود الكوبون موجود بالفعل", "couponCreatedSuccessfully": "تم إنشاء الكوبون بنجاح", "couponUpdatedSuccessfully": "تم تحديث الكوبون بنجاح", "couponDeletedSuccessfully": "تم حذف الكوبون بنجاح", "couponStatusChanged": "تم تغيير حالة الكوبون", "couponDuplicated": "تم نسخ الكوبون", "failedToCreateCoupon": "فشل في إنشاء الكوبون", "failedToUpdateCoupon": "فشل في تحديث الكوبون", "failedToDeleteCoupon": "فشل في حذف الكوبون", "failedToApplyCoupon": "فشل في تطبيق الكوبون", "failedToValidateCoupon": "فشل في التحقق من الكوبون", "couponNotFound": "الكوبون غير موجود", "noCouponsAvailable": "لا توجد كوبونات متاحة", "createFirstCoupon": "إنشاء أول كوبون", "couponListEmpty": "قائمة الكوبونات فارغة", "searchCoupons": "البحث في الكوبونات", "filterCoupons": "فلترة الكوبونات", "sortCoupons": "ترتيب الكوبونات", "allCouponTypes": "جميع الأنواع", "allCouponStatuses": "جميع الحالات", "newestFirst": "الأحدث أولاً", "oldestFirst": "الأقدم أولاً", "expiringFirst": "ينتهي قريباً", "mostUsed": "الأكثر استخداماً", "highestValue": "الأعلى قيمة", "couponFilters": "فلاتر الكوبونات", "clearCouponFilters": "<PERSON><PERSON><PERSON> الفلا<PERSON>ر", "applyCouponFilters": "تطبيق الفلاتر", "couponDateRange": "نطاق التاريخ", "couponUsageRange": "نطاق الاستخدام", "couponValueRange": "نطاق القيمة", "couponSearchTerm": "مص<PERSON><PERSON><PERSON> البحث", "couponAdvancedOptions": "خيارات متقدمة", "couponApplicableProducts": "المنتجات المشمولة", "couponApplicableCategories": "الفئات المشمولة", "couponExcludedProducts": "المنتجات المستثناة", "couponExcludedCategories": "الفئات المستثناة", "couponCustomerRestrictions": "قيود العملاء", "firstTimeCustomersOnly": "للعملاء الجدد فقط", "specificCustomers": "عملاء محددين", "customerTiers": "مستويات العملاء", "couponInternalNotes": "ملاحظات داخلية", "couponPublicDescription": "الوصف العام", "couponValidationRules": "قوا<PERSON>د التحقق", "couponUsageRules": "قواعد الاستخدام", "couponStackingAllowed": "السماح بتراكم الكوبونات", "maxCouponsPerOrder": "<PERSON><PERSON><PERSON><PERSON> عدد كوبونات لكل طلب", "couponAutoGeneration": "التوليد التلقائي", "couponApprovalRequired": "يتطلب موافقة", "couponNotificationSettings": "إعدادات الإشعارات", "notifyOnCouponCreated": "إشعار عند الإنشاء", "notifyOnCouponUsed": "إشعار عند الاستخدام", "notifyOnCouponExpired": "إشعار عند الانتهاء", "lowUsageAlert": "تنبيه الاستخدام المنخفض", "lowUsageThreshold": "<PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON>ه", "couponReports": "تقارير الكوبونات", "couponPerformanceReport": "تقرير الأداء", "couponUsageReport": "تقرير الاستخدام", "couponRevenueReport": "تقرير الإيرادات", "couponCustomerReport": "تقرير العملاء", "couponCategoryReport": "تقرير الفئات", "couponTimeSeriesReport": "تقرير السلاسل الزمنية", "exportCouponData": "تصدير بيانات الكوبونات", "importCouponData": "استيراد بيانات الكوبونات", "couponBulkActions": "إجراءات مجمعة", "bulkActivate": "تفعيل مجمع", "bulkDeactivate": "إلغاء تفعيل مجمع", "bulkDelete": "حذ<PERSON> مجمع", "bulkExport": "تصدير مجمع", "selectedCoupons": "الكوبونات المحددة", "selectAllCoupons": "تحدي<PERSON> الكل", "deselectAllCoupons": "إلغاء تحديد الكل", "couponBatchOperations": "عمليات الدفعة", "processingCoupons": "جاري معالجة الكوبونات", "couponOperationCompleted": "تمت العملية بنجاح", "couponOperationFailed": "فشلت العملية", "partialCouponOperation": "تمت العملية جزئياً", "loyalty": "الولاء", "loyaltyProgram": "برنامج الولاء", "loyaltyPoints": "نقاط الولاء", "loyaltyRewards": "مكافآت الولاء", "loyaltyTier": "مستوى الولاء", "loyaltyMember": "عضو الولاء", "loyaltyMembers": "أعضاء الولاء", "loyaltyAnalytics": "تحليلات الولاء", "loyaltySettings": "إعدادات الولاء", "loyaltyHistory": "تاريخ الولاء", "loyaltyTransactions": "معاملات الولاء", "joinLoyaltyProgram": "انضم لبرنامج الولاء", "createLoyaltyProgram": "إنشاء برنامج الولاء", "manageLoyaltyProgram": "إدارة برنامج الولاء", "availablePoints": "النقاط المتاحة", "totalPoints": "إجمالي النقاط", "earnedPoints": "النقاط المكتسبة", "redeemedPoints": "النقاط المستبدلة", "expiredPoints": "النقاط المنتهية", "bonusPoints": "النقاط الإضافية", "pointsPerSAR": "نقاط لكل ريال", "minimumOrderForPoints": "ال<PERSON><PERSON> الأدنى للطلب لكسب النقاط", "pointsExpiryDays": "انتهاء صلاحية النقاط بالأيام", "welcomeBonusPoints": "نقاط الترحيب", "birthdayBonusPoints": "نقاط عيد الميلاد", "referralBonusPoints": "نقاط الإحالة", "maxPointsPerOrder": "أقصى نقاط لكل طلب", "allowPartialRedemption": "السماح بالاستبدال الجزئي", "autoTierUpgrade": "الترقية التلقائية للمستوى", "tierBronze": "البرونزي", "tierSilver": "الفضي", "tierGold": "الذهبي", "tierPlatinum": "البلاتيني", "currentTier": "المستوى الحالي", "nextTier": "المستوى التالي", "tierProgress": "تقدم المستوى", "tierUpgrade": "ترقية المستوى", "tierBenefits": "مزايا المستوى", "pointsMultiplier": "م<PERSON><PERSON><PERSON><PERSON> النقاط", "lifetimeSpent": "إجمالي الإنفاق", "lifetimeOrders": "إجمالي الطلبات", "averageOrderValue": "متوسط قيمة الطلب", "memberSince": "عضو منذ", "lastActivity": "آخر نشاط", "rewardType": "نوع المكافأة", "rewardValue": "قيمة المكافأة", "rewardCost": "تكلفة المكافأة", "rewardDescription": "وصف المكافأة", "rewardExpiry": "انتهاء صلاحية المكافأة", "rewardRedemption": "استبدال المكافأة", "rewardRedemptions": "استبدالات المكافآت", "redeemReward": "استبدال المكافأة", "rewardRedeemed": "تم استبدال المكافأة", "rewardAvailable": "مكافأة متاحة", "rewardUnavailable": "مكافأة غير متاحة", "insufficientPoints": "نقاط غير كافية", "tierRestricted": "مقي<PERSON> بالمستوى", "rewardSoldOut": "نفدت الكمية", "rewardExpired": "منتهي الصلاحية", "rewardInactive": "غير نشط", "redemptionCode": "كود الاستبدال", "redemptionStatus": "حالة الاستبدال", "redemptionPending": "في انتظار التأكيد", "redemptionApproved": "تم الموافقة", "redemptionRedeemed": "تم الاستبدال", "redemptionCancelled": "تم الإلغاء", "redemptionExpired": "منتهي الصلاحية", "discountReward": "مكافأة خصم", "freeProductReward": "منتج مجاني", "freeShippingReward": "<PERSON><PERSON><PERSON>اني", "giftCardReward": "بطاقة هدية", "experienceReward": "تجربة خاصة", "loyaltyProgramActive": "برنامج الولاء نشط", "loyaltyProgramInactive": "برنامج الولاء غير نشط", "loyaltyProgramSuspended": "برنامج الولاء معلق", "activateProgram": "تفعيل البرنامج", "deactivateProgram": "<PERSON>ي<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON><PERSON>ج", "suspendProgram": "تعليق البرنامج", "programName": "اسم البرنامج", "programDescription": "وصف البرنامج", "programSettings": "إعدادات البرنامج", "programTiers": "مستويات البرنامج", "programRewards": "مكافآت البرنامج", "programMembers": "أعضاء البرنامج", "programAnalytics": "تحليلات البرنامج", "totalMembers": "إجمالي الأعضاء", "activeMembers": "الأعضاء النشطون", "newMembers": "<PERSON><PERSON><PERSON><PERSON><PERSON> جدد", "memberRetention": "الاحتفاظ بالأعضاء", "memberEngagement": "تفاعل الأعضاء", "pointsIssued": "النقاط المصدرة", "pointsRedeemed": "النقاط المستبدلة", "redemptionRate": "معدل الاستبدال", "averagePointsPerMember": "متوسط النقاط لكل عضو", "topRewards": "أفضل المكافآت", "rewardPerformance": "أداء المكافآت", "membershipGrowth": "نمو العضوية", "tierDistribution": "توزيع المستويات", "loyaltyROI": "عائد الاستثمار للولاء", "customerLifetimeValue": "القيمة الدائمة للعميل", "repeatPurchaseRate": "معدل الشراء المتكرر", "loyaltyNotifications": "إشعارات الولاء", "pointsEarnedNotification": "إشعار النقاط المكتسبة", "pointsRedeemedNotification": "إشعار النقاط المستبدلة", "pointsExpiringNotification": "إشعار انتهاء صلاحية النقاط", "tierUpgradeNotification": "إشعار ترقية المستوى", "rewardAvailableNotification": "إشعار توفر المكافأة", "birthdayBonusNotification": "إشعار نقاط عيد الميلاد", "loyaltyTips": "نصائح الولاء", "earnMorePoints": "اكس<PERSON> المزيد من النقاط", "shopRegularly": "تسوق بانتظام", "referFriends": "ادع أصدقاءك", "birthdayBonus": "مكافأة عيد الميلاد", "specialOffers": "العروض الخاصة", "doublePoints": "نقاط مضاعفة", "loyaltyJoined": "تم الانضمام لبرنامج الولاء", "loyaltyJoinFailed": "فشل في الانضمام لبرنامج الولاء", "loyaltyProgramCreated": "تم إنشاء برنامج الولاء", "loyaltyProgramUpdated": "تم تحديث برنامج الولاء", "loyaltyProgramDeleted": "تم حذف برنا<PERSON>ج الولاء", "loyaltyProgramNotFound": "برنامج الولاء غير موجود", "loyaltyMemberNotFound": "عضو الولاء غير موجود", "loyaltyRewardNotFound": "مكافأة الولاء غير موجودة", "loyaltyTransactionFailed": "فشل في معاملة الولاء", "loyaltyRedemptionFailed": "فشل في استبدال المكافأة", "loyaltyPointsAdded": "تم إضافة نقاط الولاء", "loyaltyPointsDeducted": "تم خصم نقاط الولاء", "loyaltyTierUpgraded": "تم ترقية مستوى الولاء", "walking": "المشي", "reviews": "تقييم", "common": {"save": "<PERSON><PERSON><PERSON>", "edit": "تعديل", "view": "<PERSON><PERSON><PERSON>", "back": "رجوع", "next": "التالي", "previous": "السابق", "submit": "إرسال", "confirm": "تأكيد", "search": "ب<PERSON><PERSON>", "filter": "فلتر", "sort": "ترتيب", "store": "المتجر", "product": "المنتج", "available": "متوفر", "outOfStock": "ن<PERSON><PERSON> المخزون", "verified": "مُتحقق منه", "phone": "الهاتف", "email": "الب<PERSON>يد الإلكتروني", "address": "العنوان", "businessHours": "ساعات العمل", "storeInfo": "معلومات المتجر", "productInfo": "معلومات المنتج", "storeStats": "إحصائيات المتجر", "totalOrders": "إجمالي الطلبات", "totalSales": "إجمالي المبيعات", "productNotFound": "المنتج غير موجود", "productNotFoundDescription": "لم يتم العثور على المنتج المطلوب", "storeNotFound": "المتجر غير موجود", "storeNotFoundDescription": "لم يتم العثور على المتجر المطلوب", "backToProducts": "العودة للمنتجات", "backToStores": "العودة للمتاجر", "backToProduct": "العودة للمنتج", "backToStore": "العودة للمتجر"}, "admin": {"reviewReports": "تقارير المراجعات", "reviewReportsDescription": "مراجعة والتعامل مع التقارير المرسلة حول المراجعات", "noReportsFound": "لا توجد تقارير معلقة", "noReportsFoundDescription": "جميع التقارير تم مراجعتها أو لا توجد تقارير جديدة", "pendingReports": "التقارير المعلقة", "reporter": "المُبلغ", "reason": "السبب", "reviewContent": "محتوى المراجعة", "reportDate": "تاريخ الإبلاغ", "review": "مراجعة", "by": "بواسطة", "reviewNotFound": "المراجعة غير موجودة", "reviewReport": "مراجعة التقرير", "reviewReportDescription": "راجع التقرير واتخذ الإجراء المناسب", "reportDetails": "تفاصيل التقرير", "description": "الوصف", "reportedReview": "المراجعة المُبلغ عنها", "moderationNotes": "ملاحظات الإدارة", "moderationNotesPlaceholder": "أضف ملاحظ<PERSON><PERSON> حول قرار الإدارة...", "moderationWarning": "تحذير مهم", "moderationWarningDescription": "قرارات الإدارة تؤثر على تجربة المستخدمين. تأكد من اتخاذ القرار الصحيح.", "approveReview": "قبول المراجعة", "rejectReview": "رفض المراجعة", "reportApproved": "تم قبول المراجعة وإغلاق التقرير", "reportRejected": "تم رفض المراجعة وإخفاؤها", "errorFetchingReports": "خطأ في جلب التقارير", "errorResolvingReport": "خطأ في معالجة التقرير"}, "cycling": "الدراجة", "driving": "القيادة", "getDirections": "الحصول على الاتجاهات", "note": "ملاحظة", "estimatedTimesNote": "الأوقات المقدرة تقريبية وقد تختلف حسب حالة الطرق والطقس", "errorLoadingStores": "خطأ في تحميل المتاجر", "checkout": "الدفع", "completeYourOrder": "أكمل طلبك", "customerInformation": "معلومات العميل", "shippingAddress": "عنوان الشحن", "streetAddress": "عنوان الشارع", "city": "المدينة", "postalCode": "الر<PERSON>ز البريدي", "deliveryNotes": "ملاحظات التوصيل", "deliveryNotesPlaceholder": "تعليمات خاصة للتوصيل (اختياري)", "paymentMethod": "طريقة الدفع", "cashOnDelivery": "الدفع عند الاستلام", "creditCard": "بطاقة ائتمان", "onlinePayment": "دفع إلكتروني", "orderNotes": "ملاحظات الطلب", "orderNotesPlaceholder": "ملاحظات إضافية للطلب (اختياري)", "orderSummary": "ملخص الطلب", "quantity": "الكمية", "subtotal": "المجموع الفرعي", "deliveryFee": "رسوم التوصيل", "freeDeliveryNotice": "أضف {{amount}} ريال أخرى للحصول على توصيل مجاني", "processingOrder": "جاري معالجة الطلب", "placeOrder": "تأ<PERSON>يد الطلب", "secureCheckoutNotice": "عملية دفع آمنة ومحمية", "orderSuccessTitle": "تم تأكيد طلبك بنجاح!", "orderSuccessMessage": "شكراً لك! تم استلام طلبك وسيتم معالجته قريباً.", "viewMyOrders": "عرض طلباتي", "continueShopping": "متابعة التسوق", "orderCreationFailed": "فشل في إنشاء الطلب. يرجى المحاولة مرة أخرى.", "stores": "المتاجر", "map": "الخريطة", "allStores": "جميع المتاجر", "featuredStores": "المتاجر المميزة", "storeDetails": "تفاصيل المتجر", "viewStore": "عرض المتجر", "contactAvailable": "متاح للتواصل", "websiteAvailable": "موقع إلكتروني", "openNow": "م<PERSON><PERSON><PERSON><PERSON> الآن", "closedNow": "م<PERSON><PERSON><PERSON> الآن", "closedToday": "م<PERSON><PERSON><PERSON> اليوم", "hoursNotAvailable": "ساعات العمل غير متاحة", "following": "متابع", "follow": "متابعة", "share": "مشاركة", "aboutStore": "عن المتجر", "noDescriptionAvailable": "لا يوجد وصف متاح", "sunday": "ال<PERSON><PERSON>د", "monday": "الاثنين", "tuesday": "الثلاثاء", "wednesday": "الأربعاء", "thursday": "الخميس", "friday": "الجمعة", "saturday": "السبت", "reviewsComingSoon": "المراجعات قريباً", "reviewsFeatureWillBeAvailable": "ستكون ميزة المراجعات متاحة قريباً", "noProductsInStore": "لا توجد منتجات في المتجر", "storeHasNoProducts": "هذا المتجر لا يحتوي على منتجات حالياً", "errorFetchingStore": "خطأ في جلب بيانات المتجر", "discoverLocalStores": "اكتشف المتاجر المحلية في منطقتك", "findNearbyStoresDescription": "اعثر على المتاجر القريبة من موقعك واستمتع بتجربة تسوق محلية مميزة", "locationPermissionDeniedPreviously": "تم رفض الوصول للموقع سابقاً", "retryLocationPermission": "إعادة المحاولة", "storesFound": "تم العثور على {{count}} متجر", "searchResults": "نتائج البحث", "noStoresAvailable": "لا توجد متاجر متاحة حالياً", "errorFetchingStores": "خطأ في جلب المتاجر", "products": "المنتجات", "allProducts": "جميع المنتجات", "productDetails": "تفاصيل المنتج", "lowStock": "مخزون منخفض", "cart": "السلة", "searchPlaceholder": "ابحث عن المنتجات والمتاجر...", "searching": "جاري البحث...", "recentSearches": "عمليات البحث الأخيرة", "clearAll": "<PERSON><PERSON><PERSON> ال<PERSON>", "trendingSearches": "عمليات البحث الرائجة", "noSuggestions": "لا توجد اقتراحات", "trending": "رائج", "categories": "الفئات", "priceRange": "نطاق السعر", "rating": "التقييم", "distance": "المسافة", "sortBy": "ترتيب حسب", "sortByRelevance": "الصلة", "sortByPriceLow": "السعر: من الأقل للأعلى", "sortByPriceHigh": "السعر: من الأعلى للأقل", "sortByRating": "التقييم", "sortByDistance": "المسافة", "sortByNewest": "الأحدث", "location": "الموقع", "errorFetchingProduct": "خطأ في جلب بيانات المنتج", "inStock": "متوفر", "returnPolicy": "سياسة الإرجاع", "specifications": "المواصفات", "sku": "رقم المنتج", "currency": "العملة", "tags": "العلامات", "relatedProducts": "منتجات ذات صلة", "about": "حو<PERSON>", "loadingCart": "جاري تحميل السلة...", "emptyCart": "السلة فارغة", "emptyCartDescription": "لم تقم بإضافة أي منتجات للسلة بعد", "startShopping": "ابد<PERSON> التسوق", "freeDeliveryThreshold": "شحن مجاني للطلبات أكثر من {{amount}}", "proceedToCheckout": "متابعة للدفع", "secureCheckout": "دفع آمن ومحمي", "addedToCart": "تم إضافة المنتج للسلة", "productAddedToCart": "تم إضافة {{productName}} ({{quantity}}) للسلة", "failedToAddToCart": "فشل في إضافة المنتج للسلة", "error": "خطأ", "maxQuantityInCart": "الح<PERSON> الأقصى في السلة", "added": "تم الإضافة", "addMore": "إضافة المزيد", "quantityExceedsStock": "الكمية تتجاوز المخزون المتاح", "onlyXLeft": "متبقي {{count}} فقط", "deliveryStatus": "حالة التوصيل", "assigned": "مُعين", "picked_up": "تم الاستلام", "in_transit": "في الطريق", "pickupLocation": "موقع الاستلام", "deliveryLocation": "موقع التسليم", "representativeEarning": "<PERSON><PERSON><PERSON> المندوب", "workingHours": "ساعات العمل", "workingDays": "أيام العمل", "maxDeliveryRadius": "نطاق التوصيل الأقصى", "vehicleCapacity": "سعة المركبة", "autoAcceptOrders": "قبول الطلبات تلقائياً", "notificationSettings": "إعدادات الإشعارات", "newOrders": "الطلبات الجديدة", "orderUpdates": "تحديثات الطلبات", "earnings": "الأرباح", "promotions": "العروض الترويجية", "enterYourPersonalInformation": "أدخل معلوماتك الشخصية", "enterFullName": "أد<PERSON>ل الاسم الكامل", "enterPhoneNumber": "أدخل رقم الهاتف", "enterEmail": "أد<PERSON><PERSON> البريد الإلكتروني", "enterYourIdentityInformation": "أدخل معلومات الهوية", "enterNationalId": "أدخل رقم الهوية", "uploadYourDrivingLicense": "ارفع رخصة القيادة", "enterLicenseNumber": "أدخل رقم الرخصة", "enterVehicleDetails": "أدخل تفاصيل المركبة", "enterVehicleModel": "أد<PERSON>ل موديل المركبة", "enterPlateNumber": "أدخل رقم اللوحة", "enterVehicleColor": "أدخل لون المركبة", "enterCertificateNumber": "أدخل رقم الشهادة", "reviewAndAcceptTerms": "راجع واقبل الشروط والأحكام", "representativeTermsTitle": "شروط وأحكام المندوبين", "representativeTermsIntro": "مرحباً بك في فريق مندوبي التوصيل لدى مِخْلاة", "eligibilityRequirements": "متطلبات الأهلية", "vehicleRequirements": "متطلبات المركبة", "deliveryResponsibilities": "مسؤوليات التوصيل", "commissionStructure": "هيكل العمولات", "conductStandards": "معايير السلوك", "terminationConditions": "شروط إنهاء الخدمة", "joinOurDeliveryTeam": "انضم إلى فريق التوصيل لدينا", "step": "خطوة", "submitApplication": "إرسال الطلب", "pleaseCompleteAllRequiredFields": "ير<PERSON>ى إكمال جميع الحقول المطلوبة", "representativeSignupSuccess": "تم إرسال طلب التسجيل بنجاح! سيتم مراجعته قريباً.", "representativeSignupError": "حد<PERSON> خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.", "basicInformation": "المعلومات الأساسية", "enterBasicInformation": "أدخل معلوماتك الأساسية", "passwordSetup": "إعداد كلمة المرور", "createSecurePassword": "أنشئ كلمة مرور آمنة", "accountType": "نوع الحساب", "selectAccountType": "اختر نوع حسابك", "uploadRequiredDocuments": "ارفع الوثائق المطلوبة", "joinOurCommunity": "انضم إلى مجتمعنا", "createAccount": "إنشاء الحساب", "enterUsername": "أدخل اسم المستخدم", "enterPassword": "أد<PERSON>ل كلمة المرور", "enterConfirmPassword": "أ<PERSON><PERSON> كلمة المرور", "confirmYourPassword": "أ<PERSON><PERSON> كلمة المرور", "optionalFileFormatsAllowed": "الصيغ المسموحة (اختياري): PDF, JPG, PNG", "fileSelected": "تم اختيار الملف", "errorUsernameRequired": "اسم المستخدم مطلوب", "errorEmailRequired": "البريد الإلكتروني مطلوب", "chooseYourAccountType": "اختر نوع حسابك", "customerDescription": "تسوق من المتاجر المحلية واستمتع بتجربة تسوق مميزة", "merchantDescription": "أنشئ متجرك الإلكتروني وابدأ في بيع منتجاتك", "representativeDescription": "انضم كمندوب توصيل واحصل على دخل إضافي", "termsAndConditionsIntro": "يرجى قراءة ومراجعة الشروط والأحكام التالية بعناية", "userRights": "حقوق المستخدم", "userResponsibilities": "مسؤوليات المستخدم", "privacyPolicy": "سياسة الخصوصية", "serviceTerms": "شروط الخدمة", "paymentTerms": "شروط الدفع", "readyToStart": "جاهز للبدء", "proceedToCreateAccount": "المتابعة لإنشاء الحساب", "customerAccountSummary": "ستتمكن من تصفح المنتجات والتسوق من المتاجر المحلية", "merchantAccountSummary": "ستتمكن من إنشاء متجرك وبيع منتجاتك للعملاء", "representativeAccountSummary": "ستتمكن من الانضمام إلى فريق التوصيل وكسب دخل إضافي", "clickNextToProceed": "اضغط التالي للمتابعة إلى نموذج التسجيل المفصل", "completeYourRegistration": "أكمل تسجيلك", "startRegistration": "بدء التسجيل", "finalStep": "الخطوة الأخيرة", "reviewYourInformation": "راجع معلوماتك", "uploaded": "تم الرفع", "notUploaded": "لم يتم الرفع", "clickCreateToFinish": "اضغط إنشاء الحساب لإنهاء التسجيل", "loadingRepresentativeSection": "جاري تحميل قسم المندوبين...", "mustBeLoggedInToAccessRepresentative": "يجب تسجيل الدخول للوصول إلى قسم المندوبين", "loadingRepresentativeData": "جاري تحميل بيانات المندوب...", "noRepresentativeDataFound": "لم يتم العثور على بيانات المندوب", "pleaseCompleteRegistration": "ير<PERSON>ى إكمال عملية التسجيل", "completeRegistration": "إكمال التسجيل", "checkYourApplicationStatus": "تحقق من حالة طلبك", "representativeName": "اسم المندوب", "reapply": "إعادة التقديم", "helpCenter": "مركز المساعدة", "suspended": "معلق", "representativeDashboardSubtitle": "إدارة طلبات التوصيل والأرباح من هنا", "currentStatus": "الحالة الحالية", "workingStatus": "حالة العمل", "commissionRate": "معدل العمولة", "recentActivity": "النشاط الحديث", "recentActivitySubtitle": "آخر أنشطتك وطلباتك", "noRecentActivity": "لا يوجد نشاط حديث", "startAcceptingOrders": "ابد<PERSON> بقبول الطلبات لرؤية النشاط هنا", "representativeApprovals": "موافقات المندوبين", "reviewAndApproveRepresentatives": "مراجعة وموافقة طلبات المندوبين", "loadingRepresentatives": "جاري تحميل المندوبين...", "noPendingRepresentatives": "لا توجد طلبات معلقة", "allRepresentativesReviewed": "تم مراجعة جميع طلبات المندوبين", "submittedOn": "تم التقديم في", "showDetails": "عرض التفاصيل", "hideDetails": "إخفاء التفاصيل", "viewLicense": "عرض الرخصة", "viewCertificate": "عرض الشهادة", "reviewNotes": "ملاحظات المراجعة", "addReviewNotes": "أض<PERSON> ملاحظ<PERSON><PERSON> المراجعة (اختياري)", "approve": "موافقة", "reject": "<PERSON><PERSON><PERSON>", "representativeApproved": "تم قبول المندوب بنجاح", "representativeRejected": "تم رفض المندوب", "errorFetchingRepresentatives": "خطأ في جلب بيانات المندوبين", "errorUpdatingRepresentative": "خطأ في تحديث حالة المندوب", "representativePlansTitle": "<PERSON>طط المندوبين", "joinDeliveryTeamSubtitle": "انضم إلى فريق التوصيل واحصل على دخل إضافي مرن", "representativeBenefitsTitle": "مميزات العمل كمندوب:", "flexibleWorkingHours": "ساعات عمل مرنة", "competitiveCommissions": "عمولات تنافسية", "weeklyPayments": "دفعات أسبوعية", "comprehensiveSupport": "دعم شامل ومتواصل", "joinDeliveryTeam": "انضم لفريق التوصيل", "pleaseReadTermsCarefully": "يرجى قراءة الشروط والأحكام بعناية", "acceptTerms": "قبول الشروط", "manageYourStore": "إدارة متجرك ومنتجاتك من هنا.", "addNewProductCta": "إضافة منتج جديد", "errorFetchingStoreData": "خطأ في جلب بيانات متجرك. يرجى المحاولة مرة أخرى.", "storeNotActive": "متجرك غير نشط بعد. يرجى إكمال جميع المعلومات المطلوبة أو التواصل مع الدعم.", "defaultStoreName": "متجري", "cloudinaryNotConfiguredError": "Cloudinary غير مكون بشكل صحيح. يرجى التحقق من متغيرات البيئة.", "storeNameNotLoadedError": "لا يمكن تحميل اسم المتجر. يرجى المحاولة مرة أخرى أو التواصل مع الدعم.", "savingProductTitle": "جاري حفظ المنتج", "savingProductDesc": "يرجى الانتظار بينما يتم حفظ منتجك...", "uploadingProductImagesTitle": "جاري رفع صور المنتج", "uploadingProductImagesDesc": "يرجى الانتظار بينما يتم رفع صور المنتج إلى Cloudinary...", "imageUploadProgressTitle": "تقدم رفع الصور", "imageUploadProgressDesc": "تم رفع {{current}} من {{total}} صور: {{fileName}}", "errorIndividualImageUploadFailed": "فشل في رفع الصورة '{{fileName}}': {{message}}", "addNewProduct": "إضافة منتج جديد", "addNewProductSubtitle": "املأ التفاصيل أدناه لإضافة منتج جديد إلى متجرك.", "productName": "اسم المنتج", "productDescription": "وصف المنتج", "productPrice": "السعر", "productCategory": "فئة المنتج", "productStockQuantity": "كمية المخزون", "maxImages": "ح<PERSON> {{count}} صور", "uploadFiles": "رفع الملفات", "orDragAndDrop": "أو اسحب وأفلت", "imageTypesAndMaxSize": "الصور (PNG، JPG، GIF) حتى {{size}}MB لكل صورة. حد أقصى {{count}} مسموح.", "productIsActive": "المنتج نشط (مرئي للبيع)", "saveProduct": "<PERSON><PERSON><PERSON> المنتج", "savingProduct": "جاري حفظ المنتج...", "productAddedSuccessTitle": "تم إضافة المنتج بنجاح", "productAddedSuccessMessage": "تم إضافة المنتج '{{productName}}' إلى متجرك.", "errorAddingProductFailed": "فشل في إضافة المنتج. يرجى المحاولة مرة أخرى.", "errorProductNameRequired": "اسم المنتج مطلوب.", "errorPriceInvalid": "يجب أن يكون السعر رقماً موجباً.", "errorCategoryRequired": "فئة المنتج مطلوبة.", "errorStockInvalid": "يجب أن تكون كمية المخزون عدداً صحيحاً غير سالب.", "errorMinOneImage": "مطلوب صورة واحدة على الأقل للمنتج.", "productImagePreview": "معاينة صورة المنتج", "removeImage": "إزالة الصورة", "errorMaxImagesReached": "لقد وصلت إلى الحد الأقصى لعدد الصور المسموح ({{count}}).", "errorImageSizeExceeded": "حجم الصورة يتجاوز الحد المسموح ({{size}}MB).", "errorFileTypeInvalid": "نوع الملف غير صحيح. يرجى رفع ملف صورة.", "errorImageUploadFailedSummary": "فشل في رفع صورة أو أكثر.", "errorPartialImageUpload": "فشل في رفع بعض الصور. لم يتم حفظ المنتج.", "currencySAR": "ريال سعودي", "productCategoryPlaceholder": "مثل: الإلكترونيات، الملابس، الكتب", "dashboardOverview": "نظرة عامة", "merchantDashboardSubtitle": "إدارة متجرك ومراقبة أدائه", "quickStats": "إحصائيات سريعة", "totalProducts": "إجمالي المنتجات", "activeProducts": "المنتجات النشطة", "monthlySales": "المبيعات الشهرية", "totalRevenue": "إجمالي الإيرادات", "customerReviews": "تقييمات العملاء", "manageProducts": "إدارة المنتجات", "manageOrders": "إدارة الطلبات", "storeSettings": "إعدادات المتجر", "viewReports": "عرض التقارير", "manageInventory": "إدارة المخزون", "manageCoupons": "إدارة الكوبونات", "noRecentOrders": "لا توجد طلبات حديثة", "customerName": "اسم العميل", "orderAmount": "<PERSON><PERSON><PERSON><PERSON> الطلب", "orderStatus": "حالة الطلب", "orderDate": "تاريخ الطلب", "storeManagement": "إدارة المتجر", "storeName": "اسم المتجر", "storeDescription": "وصف المتجر", "storeLogo": "شعار المتجر", "storeBanner": "صورة غلاف المتجر", "contactInfo": "معلومات الاتصال", "storeAddress": "عنوان المتجر", "socialMedia": "وسائل التواصل الاجتماعي", "orderManagement": "إدارة الطلبات", "orderDetails": "تفاصيل الطلب", "customerInfo": "معلومات العميل", "shippingInfo": "معلومات الشحن", "paymentInfo": "معلومات الدفع", "orderItems": "عناصر الطلب", "updateOrderStatus": "تحديث حالة الطلب", "orderStatusPending": "في الانتظار", "orderStatusConfirmed": "مؤكد", "orderStatusPreparing": "قيد التحضير", "orderStatusReady": "جا<PERSON>ز", "orderStatusShipped": "تم الشحن", "orderStatusDelivered": "تم التسليم", "orderStatusCancelled": "ملغي", "orderNotFound": "الطلب غير موجود", "errorFetchingOrder": "خطأ في جلب الطلب", "orderStatusUpdated": "تم تحديث حالة الطلب بنجاح", "errorUpdatingOrder": "خطأ في تحديث الطلب", "backToOrders": "العودة للطلبات", "orderPlacedOn": "تم الطلب في", "updatingStatus": "جاري التحديث", "unitPrice": "سعر الوحدة", "paid": "مدفوع", "failed": "فشل", "orderProgress": "تقدم الطلب", "orderTracking": "تتبع الطلب", "orderStatusPendingDesc": "تم استلام طلبك وهو قيد المراجعة", "orderStatusConfirmedDesc": "تم تأكيد طلبك وسيتم تحضيره قريباً", "orderStatusPreparingDesc": "يتم تحضير طلبك الآن", "orderStatusReadyDesc": "طلبك جاهز للشحن", "orderStatusShippedDesc": "تم شحن طلبك وهو في الطريق إليك", "orderStatusDeliveredDesc": "تم تسليم طلبك بنجاح", "orderCancelledDesc": "تم إلغاء هذا الطلب", "current": "الحالي", "orderCompletedInfo": "تم تسليم طلبك بنجاح. شكراً لك!", "orderTrackingInfo": "سيتم تحديث حالة طلبك تلقائياً", "estimatedDelivery": "التسليم المتوقع", "estimatedTime24Hours": "خلال 24 ساعة", "estimatedTime12Hours": "خلال 12 ساعة", "estimatedTime6Hours": "خلال 6 ساعات", "estimatedTime2Hours": "خلال ساعتين", "estimatedTime1Hour": "خلال ساعة واحدة", "unknown": "غير معروف", "reports": "التقارير", "salesReport": "تقرير المبيعات", "productsReport": "تقرير المنتجات", "orderManagementSubtitle": "متابعة وإدارة جميع طلبات متجرك", "reportsSubtitle": "تقارير وتحليلات شاملة لأداء متجرك", "reportsComingSoonTitle": "التقارير والتحليلات قيد التطوير", "reportsComingSoonDescription": "نعمل على تطوير نظام تقارير متقدم سيوفر لك رؤى شاملة حول أداء متجرك:", "reportFeature1": "تقارير المبيعات اليومية والشهرية", "reportFeature2": "تحليل أداء المنتجات الأكثر مبيعاً", "reportFeature3": "إحصائيات العملاء وسلوك الشراء", "reportFeature4": "تقارير الأرباح والإيرادات", "reportFeature5": "رسوم بيانية تفاعلية", "reportFeature6": "تصدير التقارير بصيغ مختلفة", "newMerchantWelcome": "مرحباً بك كتاجر جديد! يمكنك البدء بإعداد متجرك وإضافة منتجاتك الأولى.", "customersReport": "تقرير العملاء", "revenueReport": "تقرير الإيرادات", "dailyReport": "تقرير يومي", "weeklyReport": "تقرير أسبوعي", "monthlyReport": "تقرير شهري", "inventory": "المخزون", "inventoryManagement": "إدارة المخزون", "stockLevel": "مستوى المخزون", "stockAlert": "تنبيه المخزون", "updateStock": "تحديث المخزون", "navigation": "التنقل", "analytics": "التحليلات", "settings": "الإعدادات", "emailAlreadyInUse": "هذا البريد الإلكتروني مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر أو تسجيل الدخول.", "usernameAlreadyInUse": "اسم المستخدم هذا مستخدم بالفعل. يرجى اختيار اسم مستخدم آخر.", "checkingEmail": "جاري التحقق من البريد الإلكتروني...", "checkingUsername": "جاري التحقق من اسم المستخدم...", "warning": "تحذير", "signupFailed": "فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.", "continueWithGoogle": "المتابعة مع Google", "googleSignInFailed": "فشل تسجيل الدخول بـ Google. يرجى المحاولة مرة أخرى.", "googleSignInCancelled": "تم إلغاء تسجيل الدخول بـ Google.", "googleSignInPopupBlocked": "تم حظر نافذة تسجيل الدخول. يرجى السماح بالنوافذ المنبثقة والمحاولة مرة أخرى.", "orContinueWith": "أو المتابعة مع", "orSigninManually": "أو تسجيل الدخول يدوياً", "orCreateManually": "أو إنشاء حساب يدوياً", "selectUserType": "اختر نوع الحساب", "selectUserTypeSubtitle": "لبدأ بإنشاء حسابك، أولاً، أخبرنا عن نوع الحساب الذي تريده", "welcomeToMikhla": "مرحباً بك في مخلاة", "continueAsCustomer": "متابعة كعميل", "continueAsMerchant": "متابعة كتاجر", "continueAsRepresentative": "متابعة كمندوب", "chooseAccountType": "اختر نوع حسابك", "accountTypeRequired": "يجب اختيار نوع الحساب", "userTypeSelectionTitle": "مرحباً بك في مِخْلاة", "userTypeSelectionSubtitle": "لنبدأ بإنشاء حسابك. أولاً، أخبرنا عن نوع الحساب الذي تريده", "connectionTimeout": "انتهت مهلة الاتصال", "retryingConnection": "جاري إعادة المحاولة...", "googleSignInCustomersOnly": "التسجيل عبر Google متاح للعملاء فقط.", "merchantGoogleSignupNotice": "التسجيل عبر Google غير متاح للتجار", "representativeGoogleSignupNotice": "التسجيل عبر Google غير متاح للمندوبين", "merchantManualSignupRequired": "يتطلب تسجيل التجار تعبئة نموذج مفصل ورفع الوثائق المطلوبة. يرجى استخدام التسجيل اليدوي.", "representativeManualSignupRequired": "يتطلب تسجيل المندوبين تعبئة نموذج مفصل ورفع الوثائق المطلوبة. يرجى استخدام التسجيل اليدوي.", "signupManually": "التسجيل يدوياً", "redirectingPleaseWait": "جاري إعادة التوجيه، يرجى الانتظار...", "authenticationInProgress": "جاري التحقق من المصادقة...", "processingRequest": "جاري معالجة طلبك...", "advancedSearch": "البحث المتقدم", "searchProductsAndStores": "ابحث في المنتجات والمتاجر...", "searchResultsFor": "نتائج البحث عن", "searchError": "حد<PERSON> خطأ أثناء البحث", "startSearching": "اب<PERSON><PERSON> البحث", "enterSearchTermToFindProducts": "أدخل كلمة البحث للعثور على المنتجات والمتاجر", "noSearchResults": "لا توجد نتائج", "tryDifferentSearchTerms": "جرب كلمات بحث مختلفة أو قم بتعديل الفلاتر", "clearFiltersAndTryAgain": "مسح الفلاتر والمحاولة مرة أخرى", "noProductsFound": "لا توجد منتجات", "tryAdjustingFilters": "جرب تعديل الفلاتر للحصول على نتائج أفضل", "relevance": "الصلة", "priceLowToHigh": "السعر: من الأقل للأعلى", "priceHighToLow": "السعر: من الأعلى للأقل", "highestRated": "الأعلى تقييماً", "newest": "الأحدث", "mostPopular": "الأكثر شعبية", "browseByCategories": "تصف<PERSON> حسب الفئات", "discoverProductsInDifferentCategories": "اكتشف المنتجات في فئات مختلفة", "allCategories": "جميع الفئات", "productsFound": "منتج موجود", "productsInCategory": "المنتجات في الفئة", "noProductsInThisCategory": "لا توجد منتجات في هذه الفئة", "trySelectingDifferentCategory": "جرب اختيار فئة مختلفة أو تصفح جميع الفئات", "checkBackLaterForNewProducts": "تحقق لاحقاً للحصول على منتجات جديدة", "browseAllCategories": "تصفح جميع الفئات", "food": "الطعام والمشروبات", "groceries": "البقالة والمواد الغذائية", "fashion": "الأزياء والملابس", "electronics": "الإلكترونيات", "homeAndGarden": "المنزل والحديقة", "beautyAndHealth": "الجمال والصحة", "sportsAndFitness": "الرياضة واللياقة", "automotive": "السيارات", "booksAndMedia": "الكتب والوسائط", "artsAndCrafts": "الفنون والحرف", "handicrafts": "الحرف اليدوية والتراثية", "toys": "الألعاب والترفيه", "pets": "الحيوانات الأليفة", "babyKids": "الأطفال والرضع", "jewelry": "المجوهرات والإكسسوارات", "services": "الخدمات المحلية", "plants": "النباتات والزراعة", "appliances": "الأجهزة المنزلية", "waterSports": "الرياضات المائية والشاطئ", "perfumes": "العطور والبخور", "tools": "الأدوات والمعدات", "other": "أ<PERSON><PERSON><PERSON>", "discoverTopRatedStores": "اكتشف المتاجر الأعلى تقييماً في منطقتك", "viewAllStores": "عرض جميع المتاجر", "noFeaturedStores": "لا توجد متاجر مميزة حالياً", "checkBackLaterForFeaturedStores": "تحقق لاحقاً لرؤية المتاجر المميزة", "popularProducts": "المنتجات الأكثر مبيعاً", "discoverBestSellingProducts": "اكتشف المنتجات الأكثر شعبية ومبيعاً", "viewAllProducts": "عرض جميع المنتجات", "noPopularProducts": "لا توجد منتجات شائعة حالياً", "checkBackLaterForPopularProducts": "تحقق لاحقاً لرؤية المنتجات الشائعة", "customizeProfile": "تخصيص الملف الشخصي", "user": "المستخدم", "notLoggedIn": "غير مسجل الدخول", "profileUpdateSuccessTitle": "تم تحديث الملف الشخصي بنجاح", "profileUpdateSuccessMessage": "تم تحديث ملفك الشخصي بنجاح", "fourStarsAndUp": "4 نجوم فأكثر", "threeStarsAndUp": "3 نجوم فأكثر", "twoStarsAndUp": "نجمتان فأكثر", "oneStarAndUp": "نجمة فأكثر", "oneKm": "1 كم", "fiveKm": "5 كم", "tenKm": "10 كم", "twentyFiveKm": "25 كم", "fiftyKm": "50 كم", "adminDashboard": "لوحة التحكم الإدارية", "adminDashboardSubtitle": "نظرة شاملة على أداء المنصة", "platformOverview": "نظرة عامة على المنصة", "totalUsers": "إجمالي المستخدمين", "activeUsers": "المستخدمون النشطون", "totalCustomers": "إجمالي العملاء", "totalMerchants": "إجمالي التجار", "totalRepresentatives": "إجمالي المندوبين", "totalStores": "إجمالي المتاجر", "activeStores": "المتاجر النشطة", "todayOrders": "طلبات اليوم", "pendingOrders": "الطلبات المعلقة", "completedOrders": "الطلبات المكتملة", "todayRevenue": "إيرادات اليوم", "monthlyRevenue": "الإيرادات الشهرية", "totalCommissions": "إجمالي العمولات", "monthlyCommissions": "العمولات الشهرية", "customerRetentionRate": "معدل الاحتفاظ بالعملاء", "systemAlerts": "تنبيهات النظام", "topPerformers": "أفضل المؤدين", "topMerchants": "أفضل التجار", "topProducts": "أفضل المنتجات", "topRepresentatives": "أفضل المندوبين", "geographicDistribution": "التوزيع الجغرافي", "salesChart": "مخطط المبيعات", "revenueChart": "مخطط الإيرادات", "ordersChart": "مخط<PERSON> الطلبات", "usersChart": "مخطط المستخدمين", "last7Days": "آخر 7 أيام", "last30Days": "<PERSON><PERSON><PERSON> 30 يوماً", "last3Months": "آخر 3 أشهر", "lastYear": "العام الماضي", "noAlertsTitle": "لا توجد تنبيهات", "noAlertsDescription": "جميع الأنظمة تعمل بشكل طبيعي", "criticalAlert": "تنبيه حرج", "warningAlert": "تحذير", "infoAlert": "معلومات", "newUserRegistration": "تسجيل مستخدم جديد", "newOrderPlaced": "<PERSON><PERSON><PERSON> جديد", "newStoreRegistration": "تسجيل متجر جديد", "newRepresentativeApplication": "طلب مندوب جديد", "orderCompleted": "ط<PERSON><PERSON> مكتمل", "orderCancelled": "<PERSON><PERSON><PERSON> ملغي", "paymentReceived": "دفعة مستلمة", "systemMaintenance": "صيانة النظام", "highServerLoad": "حمولة خادم عالية", "lowInventory": "مخزون منخفض", "suspiciousActivity": "نشاط مشبوه", "dismissAlert": "إزالة التنبيه", "markAsRead": "تحديد كمقروء", "refreshData": "تحديث البيانات", "exportReport": "تصدير التقرير", "viewFullReport": "عرض التقرير الكامل", "dataLastUpdated": "آخر تحديث للبيانات", "realTimeData": "بيانات في الوقت الفعلي", "errorLoadingData": "خطأ في تحميل البيانات", "noDataAvailable": "لا توجد بيانات متاحة", "dataUnavailable": "البيانات غير متاحة", "comingSoon": "قريباً", "featureInDevelopment": "هذه الميزة قيد التطوير", "performanceMetrics": "مقاييس الأداء", "growthRate": "معدل النمو", "userEngagement": "تفاعل المستخدمين", "orderTrends": "اتجاهات الطلبات", "revenueTrends": "اتجاهات الإيرادات", "topSellingProducts": "المنتجات الأكثر مبيعاً", "topRatedStores": "المتاجر الأعلى تقييماً", "mostActiveRepresentatives": "المندوبين الأكثر نشاطاً", "regionPerformance": "أداء المناطق", "cityDistribution": "توزيع المدن", "ordersByRegion": "الطلبات حسب المنطقة", "revenueByRegion": "الإيرادات حسب المنطقة", "usersByRegion": "المستخدمين حسب المنطقة", "systemHealth": "صحة النظام", "serverStatus": "حالة الخادم", "usersManagement": "إدارة المستخدمين", "usersManagementSubtitle": "إدارة جميع المستخدمين والحسابات في المنصة", "allUsers": "جميع المستخدمين", "inactiveUsers": "المستخدمون غير النشطين", "searchUsers": "البحث في المستخدمين", "searchByNameEmailPhone": "البحث بالاسم أو البريد الإلكتروني أو رقم الهاتف", "filterByUserType": "تصفية حسب نوع المستخدم", "filterByStatus": "تصفية حسب الحالة", "filterByRegistrationDate": "تصفية حسب تاريخ التسجيل", "userDetails": "تفاصيل المستخدم", "userActivity": "نشاط المستخدم", "userStats": "إحصائيات المستخدم", "editUser": "تعديل المستخدم", "deleteUser": "<PERSON>ذ<PERSON> المستخدم", "activateUser": "تفعيل المستخدم", "deactivateUser": "إلغاء تفعيل المستخدم", "sendNotification": "إرسال إشعار", "exportUsers": "تصدير المستخدمين", "bulkActions": "إجراءات جماعية", "selectAll": "تحدي<PERSON> الكل", "selectedUsers": "المستخدمون المحددون", "userPermissions": "صلاحيات المستخدم", "lastLogin": "آخر تسجيل دخول", "registrationDate": "تاريخ التسجيل", "totalSpent": "إجمالي المبلغ المنفق", "userComplaints": "شكاوى المستخدم", "userReports": "تقارير المستخدم", "userNotes": "ملاحظات المستخدم", "accountStatus": "حالة الحساب", "verificationStatus": "حالة التحقق", "joinDate": "تاريخ الانضمام", "userRole": "دور المستخدم", "accessLevel": "مستوى الوصول", "customNotification": "إشعار مخصص", "notificationTitle": "عنوان الإشعار", "notificationMessage": "رسالة الإشعار", "sendToSelected": "إرسال للمحددين", "sendToAll": "إرسال للجميع", "userActivityLog": "سجل نشاط المستخدم", "loginHistory": "تاريخ تسجيل الدخول", "paymentHistory": "تاريخ المدفوعات", "categoriesManagement": "إدارة الفئات والمنتجات", "categoriesManagementSubtitle": "إدارة فئات المنتجات ومراجعة المحتوى", "mainCategories": "الفئات الرئيسية", "subCategories": "الفئات الفرعية", "addCategory": "إضافة فئة", "editCategory": "تعديل الفئة", "deleteCategory": "حذ<PERSON> الفئة", "categoryName": "اسم الفئة", "categoryDescription": "وصف الفئة", "categoryIcon": "أيقونة الفئة", "categoryImage": "صورة الفئة", "parentCategory": "الفئة الأب", "categoryOrder": "ترتيب الفئة", "categoryStatus": "حالة الفئة", "categoryStats": "إحصائيات الفئة", "dragToReorder": "اسحب لإعادة الترتيب", "productModeration": "مراجعة المنتجات", "pendingProducts": "المنتجات المعلقة", "approvedProducts": "المنتجات المعتمدة", "rejectedProducts": "المنتجات المرفوضة", "reportedProducts": "المنتجات المبلغ عنها", "approveProduct": "اعتماد المنتج", "rejectProduct": "<PERSON><PERSON><PERSON> المنتج", "productQuality": "جودة المنتج", "contentQuality": "جودة المحتوى", "imageQuality": "جودة الصور", "brandManagement": "إدارة العلامات التجارية", "addBrand": "إضافة علامة تجارية", "brandName": "اسم العلامة التجارية", "brandLogo": "شعار العلامة التجارية", "brandDescription": "وصف العلامة التجارية", "featuredBrands": "العلامات التجارية المميزة", "qualityScore": "نقاط الجودة", "contentAnalysis": "تحليل المحتوى", "automaticModeration": "المراجعة التلقائية", "manualReview": "المراجعة اليدوية", "systemSettings": "إعدادات النظام", "systemSettingsSubtitle": "إدارة الإعدادات العامة للمنصة", "generalSettings": "الإعدادات العامة", "platformSettings": "إعدادات المنصة", "platformName": "اسم المنصة", "platformLogo": "شعار المنصة", "platformDescription": "وصف المنصة", "platformColors": "ألوان المنصة", "platformFonts": "خطوط المنصة", "commissionSettings": "إعدادات العمولة", "merchantCommission": "عمولة التجار", "representativeCommission": "عمولة المندوبين", "platformCommission": "عمولة المنصة", "commissionType": "نوع العمولة", "fixedCommission": "عمولة ثابتة", "percentageCommission": "عمولة نسبية", "emailNotifications": "إشعارات البريد الإلكتروني", "pushNotifications": "الإشعارات الفورية", "smsNotifications": "إشعارات الرسائل النصية", "notificationTemplates": "قوالب الإشعارات", "subscriptionPlans": "خطط الاشتراك", "addPlan": "إضا<PERSON>ة خطة", "editPlan": "تعديل الخطة", "planName": "اسم الخطة", "planPrice": "سعر الخطة", "planDuration": "مدة الخطة", "shippingSettings": "إعدادات الشحن", "shippingZones": "مناطق الشحن", "shippingRates": "أسعار الشحن", "deliveryTimes": "أوقات التوصيل", "paymentSettings": "إعدادات الدفع", "paymentMethods": "طرق الدفع", "paymentGateways": "بوابات الدفع", "paymentConfiguration": "تكوين الدفع", "securitySettings": "إعدادات الأمان", "passwordPolicy": "سياسة كلمة المرور", "twoFactorAuth": "المصادقة الثنائية", "sessionTimeout": "انتهاء الجلسة", "ipWhitelist": "قائمة IP المسموحة", "backupSettings": "إعدادات النسخ الاحتياطية", "automaticBackup": "النسخ الاحتياطي التلقائي", "backupFrequency": "تكرار النسخ الاحتياطي", "backupLocation": "موقع النسخ الاحتياطية", "restoreBackup": "استعادة النسخة الاحتياطية", "seoSettings": "إعدادات SEO", "metaTags": "علا<PERSON><PERSON><PERSON> Meta", "sitemap": "خريطة الموقع", "robotsTxt": "ملف robots.txt", "integrationSettings": "إعدادات التكاملات", "apiKeys": "مفاتيح API", "webhooks": "Webhooks", "thirdPartyServices": "الخدمات الخارجية", "maintenanceMode": "وضع الصيانة", "emergencyMode": "وضع الطوارئ", "systemLogs": "سجلات النظام", "changeLog": "سجل التغييرات", "systemUpdates": "تحديثات النظام", "databaseStatus": "حالة قاعدة البيانات", "apiStatus": "حالة API", "uptime": "وقت التشغيل", "responseTime": "وقت الاستجابة", "errorRate": "معدل الأخطاء", "activeConnections": "الاتصالات النشطة", "memoryUsage": "استخدام الذاكرة", "cpuUsage": "استخدام المعالج", "diskUsage": "استخدام القرص الصلب", "networkTraffic": "حركة الشبكة", "securityAlerts": "تنبيهات الأمان", "failedLoginAttempts": "محاولات تسجيل دخول فاشلة", "suspiciousTransactions": "معاملات مشبوهة", "dataBreachAttempts": "محاولات اختراق البيانات", "malwareDetection": "اكتشاف البرا<PERSON>ج الضارة", "ddosAttacks": "هجمات DDoS", "unauthorizedAccess": "وصول غير مصرح", "securityIncidents": "حو<PERSON>ث أمنية", "complianceStatus": "حالة الامتثال", "dataProtection": "حماية البيانات", "privacyCompliance": "امتثال الخصوصية", "auditLogs": "سجلات التدقيق", "backupStatus": "حالة النسخ الاحتياطية", "lastBackup": "آخر نسخة احتياطية", "backupSize": "حجم النسخة الاحتياطية", "recoveryTime": "وقت الاستعادة", "dataIntegrity": "سلامة البيانات", "storageUsage": "استخدام التخزين", "bandwidthUsage": "استخدام النطاق الترددي", "apiCalls": "استدعاءات API", "cacheHitRate": "معدل إصابة التخزين المؤقت", "databaseQueries": "استعلامات قاعدة البيانات", "slowQueries": "الاستعلامات البطيئة", "indexOptimization": "تحسين الفهارس", "queryPerformance": "أداء الاستعلامات", "searchFilters": "فلاتر البحث", "filterByCategory": "تصفية حسب الفئة", "profileUpdateFailed": "فشل تحديث الملف الشخصي", "logoutFailed": "فشل تسجيل الخروج", "yourCurrentLocation": "موقعك الحالي", "latitude": "<PERSON><PERSON> العرض", "longitude": "<PERSON><PERSON> الطول", "addReview": "إضافة مراجعة", "writeReview": "كتابة مراجعة", "rateProduct": "قيم المنتج", "addToWishlist": "إضافة لقائمة الأمنيات", "removeFromCart": "إزالة من السلة", "updateQuantity": "تحديث الكمية", "notifications": "الإشعارات", "faq": "الأسئلة الشائعة", "reportIssue": "الإبلاغ عن مشكلة", "locationPermissionPrompt": "يرجى السماح بالوصول للموقع", "loadingMap": "جاري تحميل الخريطة...", "yourLocation": "موقعك", "representative.dashboard.welcome": "مرحباً بك، {{name}}!", "representative.dashboard.subtitle": "إدارة طلبات التوصيل والأرباح من هنا", "representative.dashboard.completed": "مكتملة", "representative.dashboard.onTimeRate": "معدل التوصيل في الوقت المحدد", "representative.dashboard.avgDeliveryTime": "متوسط وقت التوصيل", "representative.dashboard.loading": "جاري التحميل...", "representative.dashboard.representative": "مندوب", "representative.dashboard.active": "نشط", "representative.dashboard.inactive": "غير نشط", "representative.dashboard.available": "متاح", "representative.dashboard.unavailable": "<PERSON>ير متاح", "representative.dashboard.totalDeliveries": "إجمالي التوصيلات", "representative.dashboard.monthlyEarnings": "الأرباح الشهرية", "representative.dashboard.totalEarnings": "إجمالي الأرباح", "representative.dashboard.averageRating": "متوسط التقييم", "representative.dashboard.reviews": "تقييمات", "representative.dashboard.minutes": "دقيقة", "representative.dashboard.mustBeLoggedIn": "يجب أن تكون مسجل دخول كمندوب للوصول إلى هذا القسم", "representative.nav.dashboard": "لوحة التحكم", "representative.nav.orders": "الطلبات", "representative.nav.earnings": "الأرباح", "representative.nav.profile": "الملف الشخصي", "auth.login": "تسجيل الدخول", "unknownStatus": "حالة غير معروفة", "unknownStatusDesc": "حدث خطأ في تحديد حالة طلبك", "previous": "السابق", "next": "التالي", "reviews.noReviews": "لا توجد تقييمات", "reviews.beFirstToReview": "كن أول من يقيم", "reviews.helpful": "م<PERSON><PERSON><PERSON>", "reviews.report": "إب<PERSON><PERSON>غ", "reviews.confirmDelete": "تأكيد الحذف", "reviews.viewImages": "عرض الصور", "reviews.loginToReview": "سجل دخولك للتقييم", "reviews.loginToReviewDescription": "يجب تسجيل الدخول لإضافة تقييم", "reviews.alreadyReviewed": "تم التقييم مسبقاً", "reviews.alreadyReviewedDescription": "لقد قمت بتقييم هذا المنتج مسبقاً", "reviews.cannotReview": "لا يمكن التقييم", "reviews.cannotReviewDescription": "لا يمكنك تقييم هذا العنصر", "reviews.addStoreReview": "إضافة تقييم للمتجر", "reviews.addProductReview": "إضافة تقييم للمنتج", "reviews.ratingRequired": "التقييم مطلوب", "reviews.comment": "تعليق", "reviews.commentRequired": "التعليق مطلوب", "reviews.commentTooShort": "التعليق قصير جداً", "reviews.commentPlaceholder": "شارك تجربتك...", "reviews.images": "الصور", "reviews.addImages": "إضافة صور", "reviews.maxImages": "ح<PERSON> {{count}} صور", "reviews.submitReview": "إرسال التقييم", "reviews.editReview": "تعديل التقييم", "reviews.deleteReview": "<PERSON>ذ<PERSON> التقييم", "reviews.reviewSubmitted": "تم إرسال التقييم", "reviews.reviewUpdated": "تم تحديث التقييم", "reviews.reviewDeleted": "تم حذف التقييم", "reviews.loadingReviews": "جاري تحميل التقييمات...", "reviews.noReviewsYet": "لا توجد تقييمات بعد", "reviews.writeFirstReview": "اكتب أول تقييم", "map.loading": "جاري تحميل الخريطة...", "map.error": "خطأ في الخريطة", "map.noLocation": "لا يوجد موقع", "map.enableLocation": "تمكين الموقع", "map.locationDenied": "تم رفض الوصول للموقع", "map.locationUnavailable": "الموقع غير متاح", "map.findStores": "البحث عن المتاجر", "map.nearbyStores": "المتاجر القريبة", "map.storeDetails": "تفاصيل المتجر", "map.getDirections": "الحصول على الاتجاهات", "map.distance": "المسافة", "map.estimatedTime": "الوقت المقدر", "stores.featured": "المتاجر المميزة", "stores.popular": "المتاجر الشائعة", "stores.newest": "أ<PERSON><PERSON><PERSON> المتاجر", "stores.topRated": "الأعلى تقييماً", "stores.openNow": "م<PERSON><PERSON><PERSON><PERSON> الآن", "stores.closedNow": "م<PERSON><PERSON><PERSON> الآن", "stores.deliveryAvailable": "التوصيل متاح", "stores.pickupOnly": "الاستلام فقط", "stores.freeDelivery": "توصيل مجاني", "stores.fastDelivery": "توصيل سريع", "stores.viewMenu": "عرض القائمة", "stores.orderOnline": "اطلب أونلاين", "stores.callStore": "اتصل بالمتجر", "stores.storeInfo": "معلومات المتجر", "stores.workingHours": "ساعات العمل", "stores.contactInfo": "معلومات التواصل", "stores.socialMedia": "وسائل التواصل الاجتماعي", "products.featured": "المنتجات المميزة", "products.bestsellers": "الأكثر مبيعاً", "products.newArrivals": "وصل حديثاً", "products.onSale": "في التخفيضات", "products.recommended": "موصى به", "products.relatedProducts": "منتجات ذات صلة", "products.productDetails": "تفاصيل المنتج", "products.specifications": "المواصفات", "products.ingredients": "المكونات", "products.nutritionFacts": "القيم الغذائية", "products.allergenInfo": "معلومات المواد المسببة للحساسية", "products.storageInstructions": "تعليمات التخزين", "products.usageInstructions": "تعليمات الاستخدام", "products.warranty": "الضمان", "products.returnPolicy": "سياسة الإرجاع", "orders.orderHistory": "تاريخ الطلبات", "orders.currentOrders": "الطلبات الحالية", "orders.pastOrders": "الطلبات السابقة", "orders.orderDetails": "تفاصيل الطلب", "orders.orderSummary": "ملخص الطلب", "orders.orderItems": "عناصر الطلب", "orders.orderTotal": "إجمالي الطلب", "orders.orderStatus": "حالة الطلب", "orders.trackOrder": "تتبع الطلب", "orders.cancelOrder": "إلغاء الطلب", "orders.reorder": "إعادة الطلب", "orders.orderConfirmation": "تأ<PERSON>يد الطلب", "orders.estimatedDelivery": "التوصيل المقدر", "orders.deliveryAddress": "عنوان التوصيل", "orders.paymentMethod": "طريقة الدفع", "orders.orderNotes": "ملاحظات الطلب", "payment.paymentMethods": "طرق الدفع", "payment.creditCard": "بطاقة ائتمان", "payment.debitCard": "بطاقة خصم", "payment.cashOnDelivery": "الدفع عند التسليم", "payment.digitalWallet": "المحفظة الرقمية", "payment.bankTransfer": "تحويل بنكي", "payment.paymentProcessing": "جاري معالجة الدفع...", "payment.paymentSuccess": "تم الدفع بنجاح", "payment.paymentFailed": "فشل الدفع", "payment.paymentCancelled": "تم إلغاء الدفع", "payment.refundProcessing": "جاري معالجة الاسترداد...", "payment.refundCompleted": "تم الاسترداد", "payment.billingAddress": "عنوان الفوترة", "payment.securePayment": "د<PERSON><PERSON> آمن", "delivery.deliveryOptions": "خيارات التوصيل", "delivery.standardDelivery": "توصيل عادي", "delivery.expressDelivery": "توصيل سريع", "delivery.sameDay": "توصيل في نفس اليوم", "delivery.nextDay": "توصيل في اليوم التالي", "delivery.scheduled": "توصيل مجدول", "delivery.pickup": "الاستلام", "delivery.deliveryFee": "رسوم التوصيل", "delivery.freeDelivery": "توصيل مجاني", "delivery.deliveryTime": "وقت التوصيل", "delivery.deliveryAddress": "عنوان التوصيل", "delivery.deliveryInstructions": "تعليمات التوصيل", "delivery.contactlessDelivery": "توصيل بدون تلامس", "delivery.trackDelivery": "تتبع التوصيل", "delivery.deliveryStatus": "حالة التوصيل", "delivery.outForDelivery": "في الطريق للتوصيل", "delivery.delivered": "تم التوصيل", "delivery.deliveryConfirmation": "تأكيد التوصيل", "reviews.optional": "اختياري", "reviews.uploadImages": "رفع الصور", "reviews.uploadingImages": "جاري رفع الصور...", "reviews.imagesUploaded": "تم رفع الصور بنجاح", "reviews.imageUploadFailed": "فشل في رفع الصور", "reviews.maxImagesExceeded": "تم تجاوز الحد الأقصى للصور", "reviews.minCharacters": "الح<PERSON> الأدنى للأحرف", "reviews.submitting": "جاري الإرسال...", "reviews.ratingLabels.1": "سيء جداً", "reviews.ratingLabels.2": "سيء", "reviews.ratingLabels.3": "متوسط", "reviews.ratingLabels.4": "<PERSON>ي<PERSON>", "reviews.ratingLabels.5": "مم<PERSON><PERSON><PERSON>", "reviews.reportReview": "الإبلاغ عن المراجعة", "reviews.reportReviewDescription": "إذا كانت هذه المراجعة تنتهك قواعد المجتمع، يرجى الإبلاغ عنها", "reviews.reportReason": "سب<PERSON> الإبلاغ", "reviews.additionalDetails": "تفاصيل إضافية", "reviews.reportDescriptionPlaceholder": "اكتب تفاصيل إضافية حول سبب الإبلاغ...", "reviews.reportWarningTitle": "تحذير", "reviews.reportWarningDescription": "الإبلاغ الكاذب قد يؤدي إلى تعليق حسابك", "reviews.submittingReport": "جاري إرسال البلاغ...", "reviews.submitReport": "إرسال البلاغ", "reviews.reportReasons.spam": "رسائل مزعجة", "reviews.reportReasons.inappropriate": "محتوى غير مناسب", "reviews.reportReasons.fake": "مراجعة مزيفة", "reviews.reportReasons.offensive": "محتوى مسيء", "reviews.reportReasons.other": "أ<PERSON><PERSON><PERSON>", "profile.editProfile": "تعديل الملف الشخصي", "profile.changePassword": "تغيير كلمة المرور", "profile.deleteAccount": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "profile.accountVerification": "التحقق من الحساب", "profile.verificationPending": "التحقق قيد الانتظار", "profile.verificationCompleted": "تم التحقق بنجاح", "location.currentLocation": "الموقع الحالي", "location.detectLocation": "تحديد الموقع", "location.locationPermission": "إذن الوصول للموقع", "location.locationNotAvailable": "الموقع غير متاح", "location.nearbyStores": "المتاجر القريبة", "notifications.markAllAsRead": "تحديد الكل كمقروء", "notifications.deleteAll": "حذ<PERSON> ج<PERSON>يع الإشعارات", "notifications.notificationSettings": "إعدادات الإشعارات", "notifications.pushNotifications": "الإشعارات الفورية", "notifications.emailNotifications": "إشعارات البريد الإلكتروني", "support.contactSupport": "اتصل بالدعم", "support.helpCenter": "مركز المساعدة", "support.faq": "الأسئلة الشائعة", "support.reportIssue": "الإبلاغ عن مشكلة", "support.technicalSupport": "الدعم التقني", "security.twoFactorAuth": "المصادقة الثنائية", "security.loginHistory": "تاريخ تسجيل الدخول", "security.securitySettings": "إعدادات الأمان", "security.changePassword": "تغيير كلمة المرور", "security.logoutAllDevices": "تسجيل الخروج من جميع الأجهزة", "integrations.title": "إعدادات التكامل", "integrations.description": "ربط متجرك مع أنظمة ERP و POS الخارجية", "integrations.erp.title": "أنظمة ERP", "integrations.pos.title": "أنظمة POS", "integrations.addErp": "إضافة تكامل ERP", "integrations.addPos": "إضافة تكامل POS", "integrations.testConnection": "اختبار الاتصال", "integrations.sync": "مزامنة", "integrations.delete": "<PERSON><PERSON><PERSON>", "integrations.status.connected": "متصل", "integrations.status.disconnected": "<PERSON>ير متصل", "integrations.status.error": "خطأ", "integrations.status.syncing": "جاري المزامنة", "integrations.lastSync": "آخر مزامنة", "integrations.neverSynced": "لم تتم بعد", "integrations.syncInterval": "فترة المزامنة", "integrations.minutes": "دقيقة", "integrations.enabled": "م<PERSON>عل", "integrations.disabled": "معطل", "integrations.products": "المنتجات", "integrations.inventory": "المخزون", "integrations.orders": "الطلبات", "integrations.customers": "العملاء", "integrations.sales": "المبيعات", "integrations.payments": "المدفوعات", "integrations.accounting": "المحاسبة", "integrations.noErpIntegrations": "لا توجد تكاملات ERP", "integrations.noPosIntegrations": "لا توجد تكاملات POS", "integrations.addFirstErp": "ابدأ بإضافة تكامل مع نظام ERP لمزامنة بياناتك", "integrations.addFirstPos": "ابدأ بإضافة تكامل مع نظام POS لمزامنة مبيعاتك", "integrations.form.systemType": "نوع النظام", "integrations.form.systemName": "اسم النظام", "integrations.form.apiUrl": "رابط API", "integrations.form.apiKey": "مفتاح API", "integrations.form.username": "اسم المستخدم", "integrations.form.password": "كلمة المرور", "integrations.form.database": "قاعدة البيانات", "integrations.form.accessToken": "ر<PERSON>ز الوصول", "integrations.form.storeId": "معر<PERSON> المت<PERSON>ر", "integrations.form.locationId": "معرف الموقع", "integrations.form.environment": "البيئة", "integrations.form.sandbox": "تجريبي", "integrations.form.production": "إنتاج", "integrations.form.syncSettings": "إعدادات المزامنة", "integrations.form.autoSync": "مزامنة تلقائية", "integrations.form.create": "إنشاء التكامل", "integrations.form.cancel": "إلغاء", "integrations.errors.createFailed": "فشل في إنشاء التكامل", "integrations.errors.testFailed": "فشل في اختبار الاتصال", "integrations.errors.syncFailed": "فشل في مزامنة البيانات", "integrations.errors.deleteFailed": "فشل في حذف التكامل", "integrations.errors.loadFailed": "فشل في تحميل بيانات التكامل", "integrations.confirmDelete": "هل أنت متأكد من حذف هذا التكامل؟"}, "en": {"appName": "<PERSON><PERSON><PERSON>", "tagline": "Connecting local stores with customers through seamless e-commerce.", "home": "Home", "pricing": "Subscription Plans", "login": "<PERSON><PERSON>", "signup": "Sign Up", "profile": "Profile", "logout": "Logout", "language": "Language", "english": "English", "arabic": "Arabic", "homeHeroTitle": "<PERSON><PERSON><PERSON>: Your City's Treasures, Delivered.", "homeHeroSubtitle": "Explore a vibrant marketplace of unique goods from local stores and artisans. Fresh produce, handmade crafts, local fashion, and more – support your neighbors and discover quality.", "getStarted": "Get Started", "getStartedFree": "Get Started Free", "browseStores": "Browse Stores", "copyright": "© {{year}} <PERSON><PERSON><PERSON>. All rights reserved.", "merchants": "Merchants", "customers": "Customers", "merchantPlansTitle": "Plans for Merchants", "customerPlansTitle": "Plans for Customers", "plan_basic_name": "Basic Plan", "plan_premium_name": "Premium Plan", "plan_business_name": "Business Plan", "plan_customer_basic_name": "Basic Customer", "plan_customer_premium_name": "Premium Customer", "SAR": "SAR", "monthly": "monthly", "month": "month", "free": "Free", "pricePerPeriod": "{{price}} {{currency}} / {{period}}", "commission": "{{value}}% commission on sales", "basicStoreManagement": "Basic store management", "addManageProducts": "Add and manage products", "emailSupport": "Email support", "simpleDashboard": "Simple dashboard", "basicStats": "Basic statistics", "basicOrderManagement": "Basic order management", "standardStorePage": "Standard store page", "productImages3": "Up to 3 images per product", "basicInventory": "Basic inventory management", "simpleMonthlyReports": "Simple monthly reports", "basicPaymentIntegration": "Basic payment integration", "basicRatingSystem": "Basic rating system", "basicEmailNotifications": "Basic email notifications", "basicDataExport": "Basic data export", "manualDataBackup": "Manual data backup", "unlimitedProducts": "Unlimited products", "prioritySupport247": "24/7 priority support", "advancedDashboard": "Advanced dashboard", "fullStoreCustomization": "Full store page customization", "advancedSalesAnalytics": "Advanced sales analytics", "advancedInventorySystem": "Advanced inventory system", "socialMediaIntegration": "Social media integration", "customerLoyaltySystem": "Customer loyalty system", "detailedWeeklyReports": "Detailed weekly reports", "advancedMarketingTools": "Advanced marketing tools", "multipleShippingOptions": "Multiple shipping options", "couponsDiscountsSystem": "Coupons and discounts system", "multiFormatReportExport": "Export reports in multiple formats", "autoDailyBackup": "Automatic daily backup", "vipSupport": "VIP support with dedicated account manager", "smartAnalyticsPredictions": "Smart analytics with future predictions", "erpPosIntegration": "Integration with ERP & POS systems", "crmSystem": "CRM system", "fullSystemCustomization": "Full system customization", "processMarketingAutomation": "Process and marketing automation", "realtimeAdvancedAnalytics": "Real-time advanced analytical reports", "multiBranchManagement": "Multi-branch management", "advancedPerformanceMonitoring": "Advanced performance monitoring system", "advancedFraudProtection": "Advanced fraud protection", "accountingIntegration": "Integration with accounting systems", "autoHourlyBackup": "Automatic hourly backup", "businessConsultingServices": "Business consulting services", "customTeamTraining": "Custom team training", "freeAccountCreation": "Free account creation", "browseProducts": "Browse products", "addToCartWishlist": "Add to cart and wishlist", "basicSearch": "Basic search", "basicOrderTracking": "Basic order tracking", "ePayment": "E-payment", "ratingsReviews": "Ratings and reviews", "saveAddresses": "Save addresses", "orderHistory": "Order history", "socialSharing": "Social sharing", "profileManagement": "Profile management", "passwordRecovery": "Password recovery", "basicAlerts": "Basic alerts", "unlimitedFreeShipping": "Unlimited free shipping", "discount20all": "20% discount on all purchases", "priorityOrders": "Priority Orders", "exclusiveOffers": "Exclusive offers", "doubleRewardPoints": "Double reward points", "freeOrderCancellation": "Free order cancellation", "earlyAccessSales": "Early access to sales", "vipCustomerService": "VIP customer service", "advancedOrderTracking": "Advanced order tracking", "instantNotifications": "Instant notifications", "monthlyVouchers": "Monthly purchase vouchers", "cashbackPurchases": "Cashback on purchases", "premiumClubMembership": "Premium Club membership", "loginToAccount": "Login to your account", "emailAddress": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "needAccount": "Need an account?", "createAnAccount": "Create an account", "username": "Username", "confirmPassword": "Confirm Password", "changePassword": "Change Password", "changePasswordDescription": "Update your password. Your new password must be at least 8 characters long.", "currentPassword": "Current Password", "newPassword": "New Password", "enterCurrentPassword": "Enter your current password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm your new password", "updatePassword": "Update Password", "passwordChangeSuccessTitle": "Password Changed", "passwordChangeSuccessMessage": "Your password has been updated successfully.", "wrongCurrentPassword": "Current password is incorrect", "weakPassword": "New password is too weak. It must be at least 8 characters long.", "recentLoginRequired": "Please sign in again before changing your password", "passwordChangeFailed": "Password change failed. Please try again.", "alreadyHaveAccount": "Already have an account?", "myProfile": "My Profile", "customizeProfile": "Customize Profile", "uploadImage": "Upload Image", "generateAvatar": "Generate Avatar", "saveChanges": "Save Changes", "selectPlan": "Select Plan", "popular": "Popular", "passwordsDoNotMatch": "Passwords do not match.", "errorTitle": "Error", "signupSuccessTitle": "Signup Successful", "signupSuccessMessage": "Welcome! Your account has been created. You are being redirected...", "signupFailed": "Signup failed. Please try again.", "loginSuccessTitle": "Login Successful", "loginSuccessMessage": "Welcome back!", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "user": "User", "alreadyLoggedIn": "You are already logged in", "redirectingToYourDashboard": "You are being redirected to your dashboard...", "authenticationError": "Authentication Error", "authenticationErrorMessage": "An error occurred while verifying your identity. Please try again.", "securityRecommendation": "For your security, we recommend logging out and signing in again.", "secureLogout": "Se<PERSON>", "securityLogoutMessage": "You have been logged out for security reasons.", "retry": "Retry", "loggingOut": "Logging out...", "notLoggedIn": "Not logged in", "profileUpdateSuccessTitle": "Profile Updated Successfully", "profileUpdateSuccessMessage": "Your profile has been updated successfully", "profileUpdateFailed": "Failed to update profile.", "logoutSuccessTitle": "Logged Out", "logoutSuccessMessage": "You have been successfully logged out.", "logoutFailed": "<PERSON><PERSON><PERSON> failed. Please try again.", "invalidCredentials": "Invalid email or password. Please try again.", "accountDisabled": "This account has been disabled. Please contact support.", "tooManyRequests": "Too many failed attempts. Please try again later.", "networkError": "Network connection error. Please check your internet connection.", "emailAlreadyInUse": "This email is already in use. Please try a different email or log in.", "usernameAlreadyInUse": "This username is already taken. Please choose a different username.", "checkingEmail": "Checking email availability...", "passwordTooShort": "Password should be at least 6 characters.", "passwordLengthHint": "Password must be at least 6 characters long.", "loadingProfile": "Loading...", "loadingTerms": "Loading terms and conditions...", "userType": "User Type", "customer": "Customer", "merchant": "Merchant", "termsAndConditions": "Terms and Conditions", "iAgreeToThe": "I agree to the", "commercialRegistration": "Commercial Registration", "otherLicenses": "Other Licenses (Optional)", "freelanceDocument": "Freelance Work Document", "errorUserTypeRequired": "Please select a user type.", "errorTermsRequired": "You must accept the Terms and Conditions to continue.", "errorCommercialRegistrationRequired": "Please upload your Commercial Registration certificate.", "errorFreelanceDocumentRequired": "Please upload your freelance work document from the freelance platform.", "fileFormatsAllowed": "Allowed formats: PDF, JPG, PNG.", "termsAndConditionsTitle": "Terms and Conditions", "termsAndConditionsForCustomers": "Terms and Conditions for Customers", "termsAndConditionsForMerchants": "Terms and Conditions for Merchants", "termsAndConditionsForRepresentatives": "Terms and Conditions for Representatives", "customerTermsText": "Welcome to Mikhla!\n\nBy registering and using the Mikhla platform as a customer, you agree to be bound by the following terms and conditions:\n\n1.  **Account and Personal Responsibility**: You are fully responsible for maintaining the confidentiality of your account information (username and password) and for all activities that occur under your account. All information provided by you must be accurate and up-to-date. Notify us immediately of any unauthorized use of your account.\n2.  **Legal Eligibility**: You must be legally eligible to make purchases under the laws applicable in the Kingdom of Saudi Arabia.\n3.  **Purchases and Payments**: When making a purchase, you agree to provide correct, complete, and accurate payment information. All sales are considered final unless otherwise stated by the store you are transacting with in its return and exchange policy.\n4.  **Role of Mikhla Platform**: You acknowledge and agree that the Mikhla platform acts as an intermediary between you (the Customer) and the Merchants. Mikhla does not directly hold or escrow funds; payments are processed through approved payment gateways according to established mechanisms.\n5.  **Platform Usage**: You undertake to use the Mikhla platform legally, ethically, and for its intended purposes only. Any use intended for fraud, harm to others, dissemination of illegal or offensive content, or disruption of platform services is strictly prohibited.\n6.  **Privacy and Data Protection**: Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your personal information. Please review it carefully to understand our practices.\n7.  **Product and Store Reviews**: We encourage you to provide honest, constructive, and helpful reviews for the community. Mikhla reserves the right to remove any content deemed inappropriate, offensive, misleading, infringing on intellectual property rights, or violating these terms.\n8.  **Communication and Notifications**: By using the platform, you consent to receive emails or in-app notifications related to your account, orders, promotions, or platform updates. You can manage your communication preferences through your account settings where applicable.\n9.  **Intellectual Property**: All content displayed on the platform (excluding user and store content) is the property of Mikhla or licensed to it and is protected by copyright and trademark laws. You may not use any of these materials without prior written permission.\n10. **Modification of Terms and Conditions**: Mikhla reserves the right to modify these terms and conditions at any time. You will be notified of any material changes via email or through a prominent notice on the platform. Your continued use of the platform after the posting of modifications constitutes your acceptance of the new terms.\n11. **Governing Law and Dispute Resolution**: These terms and conditions shall be governed by and construed in accordance with the laws of the Kingdom of Saudi Arabia. Any disputes arising from these terms will be resolved amicably, and if not possible, through competent courts in the Kingdom of Saudi Arabia.\n12. **Governing Language**: Both Arabic and English are the approved languages for these terms. In the event of any conflict between the Arabic version and the English version, the Arabic version shall prevail unless local law requires otherwise.\n\nThank you for your trust and use of the Mikhla platform. We wish you an enjoyable and safe shopping experience!", "merchantTermsText": "Welcome to the Mikhla Merchant Platform!\n\nThis agreement ('Terms', 'Agreement') sets forth the legally binding rules and regulations for your use of the Mikhla platform ('Platform', 'Service') as a merchant ('You', 'Merchant'). By registering and using the Platform, you acknowledge that you have read, understood, and agree to be bound by all of these Terms. If you do not agree with any part of these Terms, you must not use the Platform.\n\n1.  **Merchant Account Registration and Verification**:\n    *   1.1. **Accurate Information**: You must provide complete, accurate, and current business information during registration and thereafter, including legal business name, address, contact details (phone number and email), bank account details for payouts, and tax identification numbers. You commit to promptly updating this information should any changes occur.\n    *   1.2. **Required Documentation**: You agree to submit all necessary documentation for identity and business verification promptly upon request, such as a valid copy of the Commercial Registration, relevant business licenses (e.g., municipal licenses, safety certificates, etc.), national ID of the owner or authorized signatory, and any other documents Mi<PERSON><PERSON> may require for verification or compliance purposes. Mikhla reserves the right to suspend or terminate your account for failure to provide these documents or if they are found to be invalid, expired, or insufficient.\n    *   1.3. **Account Confidentiality and Security**: You are solely responsible for maintaining the confidentiality of your account credentials (username and password) and for all activities that occur under your account. <PERSON><PERSON><PERSON> must be notified immediately of any unauthorized use of your account or any other security breach. Mikhla is not liable for any loss or damage arising from your failure to comply with this requirement.\n    *   1.4. **Merchant Eligibility**: You must be a legally registered business entity in the Kingdom of Saudi Arabia or an individual authorized to conduct business according to applicable regulations.\n\n2.  **Store Management and Product Listings**:\n    *   2.1. **Product Description and Quality**: All product descriptions, images, prices, availability information, and specifications (such as size, color, material, country of origin, expiry date if applicable) must be accurate, complete, and not misleading. You are solely responsible for your store's content and product listings, ensuring that products offered are of acceptable commercial quality, conform to the descriptions provided, are free from defects, and safe for their intended use.\n    *   2.2. **Prohibited Products and Legal Compliance**: It is strictly forbidden to list or sell any products that are illegal under the laws of the Kingdom of Saudi Arabia (including products contrary to Islamic Sharia), counterfeit or fraudulent items, or products that infringe on the intellectual property rights of third parties (including copyrights, trademarks, and patents), or violate Mikhla's published policies, public morals, or consumer protection regulations. Mikhla reserves the right to remove any offending listings and/or suspend or terminate your account without prior notice, with the possibility of imposing fines or taking legal action.\n    *   2.3. **Pricing and Taxes**: You determine your product prices. All prices displayed to customers must be in Saudi Riyals (SAR) and inclusive of Value Added Tax (VAT) and any other applicable taxes, unless clearly stated otherwise and permitted by regulation. You are responsible for determining, collecting, and remitting all applicable taxes to the relevant authorities and issuing tax-compliant invoices.\n    *   2.4. **Inventory and Availability Management**: You must maintain accurate and up-to-date inventory levels for all listed products to ensure availability accuracy for buyers and avoid selling out-of-stock items. Unavailable products must be promptly removed or clearly marked as such.\n    *   2.5. **Intellectual Property Rights for Content**: You represent and warrant that you have all necessary rights, licenses, and permissions to use and display all content you upload to your store (including text, images, videos, logos, and trademarks).\n\n3.  **Order Fulfillment and Shipping**:\n    *   3.1. **Timely Order Processing**: You agree to process and fulfill customer orders received through the platform within a period not exceeding 2-3 business days, unless a different timeframe is clearly communicated to the customer before purchase (e.g., for products requiring special preparation, custom-made items, or pre-orders). This includes order confirmation, product preparation, and handover to the approved shipping carrier or the one chosen by the customer if this feature is available.\n    *   3.2. **Secure Shipping and Delivery**: You are responsible for securely and appropriately packaging products to prevent damage during transit and for selecting a reliable shipping method. You must provide accurate and correct tracking information to customers where possible and in a timely manner. Shipping policies and associated costs (if any) must be clear to customers before they complete their purchase.\n    *   3.3. **Shipping Responsibility and Insurance**: Unless explicitly agreed otherwise with the customer or Mikhla, you bear the responsibility for the product until it is successfully delivered to the customer. Obtaining adequate shipping insurance to cover loss or damage, especially for high-value shipments, is strongly recommended, and the platform may require proof of insurance for certain product categories.\n    *   3.4. **Communication Regarding Delays**: In the event of any anticipated delay in order processing or shipping beyond the specified period, you must immediately inform the concerned customer, providing the reason for the delay and an updated, realistic delivery date.\n    *   3.5. **Delivery and Verification**: You are responsible for ensuring products are delivered to the correct address provided by the customer and verifying the recipient's identity if necessary, according to the shipping company's policies.\n\n4.  **Fees, Commissions, and Payments**:\n    *   4.1. **Fee Structure**: You agree to pay the applicable subscription fees for your chosen merchant plan (if any) in addition to any agreed-upon commissions on sales made through the platform. The fee and commission structure will be clarified to you upon registration or when changing plans. Mikhla reserves the right to modify the fee structure with at least 30 days prior notice to you.\n    *   4.2. **Payment Processing and Role of Mikhla Platform**: Mikhla (or its payment processing partner) will process customer payments. You acknowledge and agree that the Mikhla platform acts as an intermediary platform facilitating transactions between you (the Merchant) and the Customers. Mikhla does not directly hold or escrow funds; payments are processed via approved payment gateways according to established mechanisms. The currency for all transactions is Saudi Riyal.\n    *   4.3. **Merchant Payouts**: Net sales (after deducting commissions, payment processing fees, any other applicable charges, refunds, or chargebacks, or fines) will be transferred to your registered bank account according to the payment schedule set by Mikhla (e.g., weekly, bi-weekly, monthly), and after any minimum transfer threshold is met (if applicable).\n    *   4.4. **Disputes, Refunds, and Chargebacks**: Mikhla reserves the right to withhold payments or make deductions from your future payouts to cover customer refunds (in accordance with your return policy or consumer protection system requirements), confirmed fraud cases, or other violations of these terms. Mikhla will work with you in good faith to resolve any payment disputes, but Mikhla's final decision will be binding in disputes related to the platform.\n    *   4.5. **Financial Reports**: Mikhla will provide you with access to financial reports detailing sales, commissions, and payouts.\n\n5.  **Returns, Exchanges, and Customer Service**:\n    *   5.1. **Return and Exchange Policy**: You must define and display a clear, detailed, and fair return and exchange policy for your customers, compliant with the consumer protection system in the Kingdom of Saudi Arabia and any Mikhla platform requirements. This policy must include, at a minimum, a 7-day return period for unused products in their original packaging and condition, unless the nature of the product dictates otherwise (e.g., perishable goods, personal items, software, or custom-made products). You must clearly state any exceptions to this rule in your own policy. The policy should clarify return conditions, the return process, and who bears shipping costs in different return scenarios.\n    *   5.2. **High-Quality Customer Service**: You are responsible for providing prompt, effective, and courteous customer service, responding to customer inquiries and complaints related to your products or orders in a timely manner (e.g., within a maximum of 24-48 business hours).\n    *   5.3. **Handling Returns and Warranties**: You must process return, exchange, and defect repair (warranty) requests according to your stated policy and applicable regulations. You commit to providing clear information about the warranties offered on your products.\n\n6.  **Data, Privacy, and Intellectual Property**:\n    *   6.1. **Customer Data**: You may access customer data (such as name, shipping address, contact information) solely for the purpose of fulfilling orders, related communication, providing customer service, and complying with legal requirements. All customer data must be handled with strict confidentiality and in accordance with Mikhla's privacy policy and all applicable data protection laws in the Kingdom of Saudi Arabia (such as the Personal Data Protection Law - PDPL). Using customer data for any marketing purposes not authorized by the customer or the platform is prohibited.\n    *   6.2. **Merchant's Intellectual Property**: You retain all intellectual property rights to your content (such as brand logo, original product images, descriptions). However, you grant Mikhla a worldwide, non-exclusive, royalty-free, sublicensable license to use, display, copy, modify (for formatting and optimal display on the platform), and distribute your content for the purpose of operating, developing, and promoting the platform and related services, including its use in platform marketing campaigns.\n    *   6.3. **Mikhla's Intellectual Property**: All intellectual property rights related to the platform itself (such as software, design, interfaces, the 'Mikhla' trademark, and Mikhla-owned materials) are the exclusive property of Mikhla or its licensors. You may not use any of these materials without prior written permission from Mikhla.\n    *   6.4. **Non-Infringement**: You warrant that your content and your use of the platform do not infringe the intellectual property rights or any other rights of any third party. You bear full responsibility for any claims arising from the infringement of these rights.\n\n7.  **Legal Obligations and Taxes**:\n    *   7.1. **Compliance with Laws and Regulations**: You are fully responsible for complying with all local and national laws, regulations, and systems related to your business activity, including (but not limited to) the e-commerce system, consumer protection system, product safety, commercial licensing requirements, labor laws, and the system for combating commercial fraud and deception.\n    *   7.2. **Taxes and Invoices**: You are solely responsible for determining, collecting, submitting, issuing correct tax invoices, and paying all applicable taxes on your sales (including Value Added Tax) to the competent tax authorities. You must be registered with the Zakat, Tax and Customs Authority (ZATCA) if you meet the registration criteria, and provide Mikhla with your tax registration number.\n    *   7.3. **Commercial Records**: You commit to maintaining accurate and up-to-date commercial records for all transactions conducted via the platform according to regulatory requirements.\n\n8.  **Ratings and Reviews**: The merchant acknowledges that customers have the right to rate products and services provided. The merchant must not manipulate ratings or attempt to influence them unfairly. Mikhla reserves the right to remove ratings that violate its policies.\n\n9.  **Termination and Suspension**:\n    *   9.1. **By Merchant**: You may request termination of your account according to the procedures specified by Mikhla, subject to settling any outstanding obligations (such as pending orders, due fees, or pending refunds).\n    *   9.2. **By Mikhla**: Mikhla reserves the right to suspend or terminate your account and access to the platform, temporarily or permanently, at its sole discretion and for any reason, including (but not limited to) violation of these terms, receiving multiple and documented customer complaints, suspicion of fraudulent or illegal activity, non-payment of due fees, damaging the platform's reputation, or failure to comply with quality and service standards set by the platform. Mikhla will make a reasonable effort to provide prior notice of suspension or termination unless the situation requires immediate action.\n    *   9.3. **Effects of Termination**: Upon termination, your access to your merchant account will be disabled, and your store and products will be removed from the platform. Any outstanding financial obligations to either party remain. Mikhla may retain some of your account data for legal, accounting, or internal analytical purposes, in accordance with its privacy policy and applicable laws.\n\n10. **Limitation of Liability and Disclaimer**:\n    *   10.1. **Use of Platform at Your Own Risk**: The platform and services are provided 'as is' and 'as available' without any warranties of any kind, whether express or implied, including but not limited to, implied warranties of merchantability, fitness for a particular purpose, or non-infringement. Mikhla does not warrant that the platform will be error-free, uninterrupted, secure, or permanently available.\n    *   10.2. **Mikhla as an Intermediary Platform**: You acknowledge that Mikhla is a technology platform that facilitates transactions between merchants and customers. Mikhla is not a direct party in any sale or purchase transaction conducted between you and the customer (unless explicitly stated otherwise), and does not assume any responsibility for the quality of products, or their safety, or legality, or accuracy of listings, or the ability of customers to pay, or your ability to complete the sale. Any disputes between you and the customer must be resolved directly between yourselves, with Mikhla's potential intervention as a mediator at its discretion.\n    *   10.3. **Limitation of Financial Liability**: To the maximum extent permitted by law, Mikhla, its officials, employees, or agents will not be liable for any direct, indirect, incidental, special, consequential, or punitive damages (including loss of profits, data, reputation, or business opportunities) arising from or related to your use or inability to use the platform or any products or services obtained through it, even if Mikhla has been advised of the possibility of such damages. In all cases, Mikhla's total liability to you concerning any claim arising from this agreement shall not exceed the total amount of commissions you actually paid to Mikhla during the three months preceding the claim that led to the liability (if any).\n\n11. **Governing Law and Dispute Resolution**: These terms and conditions shall be governed by and construed in accordance with the laws of the Kingdom of Saudi Arabia. Any disputes arising from this agreement will be resolved amicably first, and if that fails, through binding arbitration in the Kingdom of Saudi Arabia in accordance with the rules of the Saudi Center for Commercial Arbitration (SCCA).\n\n12. **Amendments to Terms**: Mikhla reserves the right to amend these terms at any time at its sole discretion. You will be notified of material changes via your registered email or through a prominent notice on the platform at least 30 days before their effective date. Your continued use of the platform after the effective date of these amendments constitutes your binding acceptance of the new terms. If you do not agree to the amended terms, you must stop using the platform and close your account.\n\n13. **General Provisions**:\n    *   13.1. **Entire Agreement**: These terms, in addition to any other policies or guidelines published on the platform and forming an integral part thereof (such as the Privacy Policy, Prohibited Products Policy), constitute the entire and final agreement between you and Mikhla regarding its subject matter, and supersede all prior or contemporaneous agreements or understandings, whether oral or written.\n    *   13.2. **Severability**: If any part of these terms is deemed invalid or unenforceable by a competent court or arbitration panel, that part will be modified to the minimum extent necessary to make it valid and enforceable, and the remaining parts of the terms will remain in full force and effect.\n    *   13.3. **No Waiver**: Mikhla's failure to exercise or enforce any right or provision of these terms does not constitute a waiver of such right or provision. Any waiver must be in writing and signed by an authorized representative of Mikhla.\n    *   1.3.4. **Force Majeure**: Mikhla will not be liable for any failure or delay in performance due to circumstances beyond its reasonable control, including natural disasters, wars, terrorism, riots, labor strikes, epidemics, governmental decisions, or failure of internet or telecommunications infrastructure.\n    *   13.5. **Notices**: Any notices or other communications required or permitted under this agreement can be provided electronically via email to your registered address or through notices on the platform or merchant dashboard. A notice is considered delivered when sent.\n    *   13.6. **Relationship of Parties**: The relationship between Mikhla and the Merchant is that of an independent contractor. Nothing in this Agreement shall be construed as creating a partnership, joint venture, agency, or employment relationship between the parties.\n    *   13.7. **Governing Language**: Both Arabic and English are the approved languages for these terms. In the event of any conflict between the Arabic version and the English version, the Arabic version shall prevail unless local law requires otherwise.\n\nThank you for using the Mikhla merchant platform!", "generalTermsText": "Please select a user type (Customer or Merchant) to view the specific terms and conditions.", "orSignupManually": "or signup manually", "popularCategoriesTitle": "Explore Popular Categories", "popularCategoriesSubtitle": "Dive into a wide array of authentic local products and discover new favorites waiting around every corner. From everyday essentials to unique handcrafted items, <PERSON><PERSON><PERSON> connects you with the heart of your community.", "categoryGroceries": "Groceries & Foodstuff", "categoryGroceriesDesc": "Order daily groceries, farm-fresh fruits and vegetables, artisanal bread, and delicious local delicacies. Get everything you need for your kitchen delivered straight to your doorstep.", "categoryHandicrafts": "Handicrafts & Gifts", "categoryHandicraftsDesc": "Explore a unique collection of handmade gifts, traditional crafts, and artistic creations that bear the unique touch of creative local artisans. Find the perfect, one-of-a-kind present.", "categoryFashion": "Local Fashion & Apparel", "categoryFashionDesc": "Discover the latest local fashion designs, traditional attire, and contemporary clothing that reflects your region's culture. Support promising local designers and unique boutiques.", "categoryHealthBeauty": "Health & Beauty", "categoryHealthBeautyDesc": "Pamper yourself with natural and organic health and beauty products. Find locally sourced skincare, soaps, essential oils, and wellness items, carefully selected for your well-being.", "merchantCtaTitle": "Are you a local merchant?", "merchantCtaSubtitle": "Are you a shop owner, artisan, or local producer? Join <PERSON> today to expand your reach, showcase your unique products to a wider community, and grow your business easily and effectively through our dedicated platform.", "listYourStore": "List Your Store", "merchantBenefitsTitle": "Key Benefits for Merchants:", "merchantBenefit1": "Reach more customers in your city.", "merchantBenefit2": "Easy-to-use store management tools.", "merchantBenefit3": "Secure payment processing.", "merchantBenefit4": "Marketing and promotional opportunities.", "yourCurrentLocation": "Your Current Location", "latitude": "Latitude", "longitude": "Longitude", "fetchingLocation": "Fetching your location...", "locationPermissionDenied": "Location permission denied. Please enable location services in your browser/system to see nearby stores.", "locationUnavailable": "Location information is unavailable.", "locationRequestTimeout": "The request to get user location timed out.", "locationErrorUnknown": "An unknown error occurred while trying to get your location.", "geolocationNotSupported": "Geolocation is not supported by your browser.", "locationPermissionPrompt": "Please allow location access to find nearby stores.", "discoverNearbyTitle": "Discover Stores Near You", "discoverNearbySubtitle": "Allow location access to find local gems and support businesses in your neighborhood. We'll show you stores closest to you first!", "loadingMap": "Loading Map...", "yourLocation": "You are here", "errorFileUploadFailed": "Upload failed for: {{file}}. Please try again or contact support.", "profileAvatarUploadSuccess": "Avatar Uploaded Successfully", "profileAvatarUploadFailed": "Cloudinary avatar upload failed. Please try again.", "profileAvatarGenerateSuccess": "Avatar generated.", "profileOnlyMerchantsCanUploadAvatar": "Only merchants can upload or generate an avatar.", "addNewProduct": "Add New Product", "addNewProductSubtitle": "Fill in the details below to add a new product to your store.", "productName": "Product Name", "productDescription": "Product Description", "productPrice": "Price", "productCategory": "Product Category", "productStockQuantity": "Stock Quantity", "productImages": "Product Images", "uploadFiles": "Upload files", "switchToEnglish": "Switch to English", "switchToArabic": "Switch to Arabic", "orDragAndDrop": "or drag and drop", "imageTypesAndMaxSize": "Images (PNG, JPG, GIF) up to {{size}}MB each. Max {{count}} allowed.", "productIsActive": "Product is active (visible for sale)", "saveProduct": "Save Product", "savingProduct": "Saving Product...", "productAddedSuccessTitle": "Product Added Successfully", "productAddedSuccessMessage": "Product '{{productName}}' has been added to your store.", "errorAddingProductFailed": "Failed to add product. Please try again.", "errorProductNameRequired": "Product name is required.", "errorPriceInvalid": "Price must be a positive number.", "errorCategoryRequired": "Product category is required.", "errorStockInvalid": "Stock quantity must be a non-negative integer.", "errorMinOneImage": "At least one image is required for the product.", "productImagePreview": "Product image preview", "removeImage": "Remove image", "errorMaxImagesReached": "You have reached the maximum number of allowed images ({{count}}).", "errorImageSizeExceeded": "Image size exceeds the allowed limit ({{size}}MB).", "errorFileTypeInvalid": "Invalid file type. Please upload an image file.", "errorImageUploadFailedSummary": "Failed to upload one or more images.", "errorPartialImageUpload": "Some images failed to upload. Product not saved.", "currencySAR": "SAR", "productCategoryPlaceholder": "e.g., Electronics, Apparel, Books", "locationPermissionLoginRequired": "Please log in to enable location features and discover nearby stores.", "merchantDashboard": "Merchant Dashboard", "welcomeMerchant": "Welcome, {{name}}!", "storeNameLabel": "Store Name:", "manageYourStore": "Manage your store and products from here.", "addNewProductCta": "Add New Product", "errorFetchingStoreData": "Error fetching your store data. Please try again.", "storeNotActive": "Your store is not yet active. Please complete all required information or contact support.", "loadingStoreData": "Loading store data...", "loadingProducts": "Loading products...", "myProducts": "My Products", "errorFetchingProducts": "Error fetching your products. Please try again.", "noProductsFoundTitle": "No Products Found", "noProductsFoundDesc": "You haven't added any products yet. Start by adding your first product.", "addProductNow": "Add Product Now", "productList": "Product List", "manageYourProductsDesc": "Here you can view, edit, or delete your products.", "image": "Image", "productNameTable": "Name", "editProduct": "Edit Product", "deleteProduct": "Delete Product", "noImage": "No Image", "mustBeLoggedInAsMerchant": "You must be logged in as a merchant to access this page.", "notAllowedToAccessMerchantPage": "You are not authorized to access this merchant page.", "loadingMerchantSection": "Loading merchant section...", "accessDenied": "Access Denied", "mustBeLoggedInToAccessMerchant": "You must be logged in as a merchant to access this section.", "mustBeMerchantToAccess": "You must be a merchant to access this page.", "errorStoreDataNotFound": "Store data not found. Please try again or contact support.", "errorFetchingPlan": "Error fetching subscription plan data.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteProductMessage": "Are you sure you want to delete the product \"{{productName}}\"? This action cannot be undone.", "deletingProduct": "Deleting product...", "productDeletedSuccessTitle": "Product Deleted", "productDeletedSuccessMessage": "Product \"{{productName}}\" has been successfully deleted.", "errorDeletingProduct": "Failed to delete product \"{{productName}}\".", "errorDeletingProductImages": "Failed to delete one or more images for product \"{{productName}}\". The product document was still deleted.", "unauthorizedMerchantAction": "Unauthorized: You are not allowed to perform this action.", "storeNotFoundOrNoAccess": "Store not found or you do not have permission to access it.", "customerAvatarInfo": "Customer avatar is automatically generated based on your name.", "uploadingAvatarTitle": "Uploading Avatar...", "uploadingAvatarDesc": "Please wait while your new avatar is being uploaded to Cloudinary...", "profileAvatarUploadSuccessDesc": "Your new avatar has been set using Cloudinary.", "uploadingFilesTitle": "Uploading Files to Cloudinary...", "uploadingFilesDesc": "Your documents are being uploaded. This may take a moment...", "fileUploadSuccessTitle": "Files Uploaded to Cloudinary", "fileUploadSuccessDesc": "Your documents have been successfully uploaded and linked to your store.", "crUploadSuccessDescCloudinary": "Commercial registration uploaded successfully to Cloudinary.", "otherLicensesUploadSuccessDescCloudinary": "Other licenses uploaded successfully to Cloudinary.", "freelanceDocumentUploadSuccessDescCloudinary": "Freelance document uploaded successfully to Cloudinary.", "uploadingCRDescCloudinary": "Uploading commercial registration to Cloudinary...", "uploadingOtherLicensesDescCloudinary": "Uploading other licenses to Cloudinary...", "uploadingFreelanceDocumentDescCloudinary": "Uploading freelance document to Cloudinary...", "signupInProgressTitle": "Signup in Progress", "signupInProgressMessage": "Creating your account, please wait...", "notSet": "Not Set", "editProfile": "Edit Profile", "errorInvalidEmailFormat": "Please enter a valid email address format.", "loginRedirecting": "Redirecting you to your dashboard...", "loadingDashboard": "Loading dashboard...", "customerDashboardTitle": "Customer Dashboard", "customerDashboardSubtitle": "Manage your orders, profile, and more.", "welcomeUser": "Welcome, {{name}}!", "quickActions": "Quick Actions", "quickActionsSubtitle": "Navigate easily through your account.", "browseProductsDashboard": "Browse Products", "myOrders": "My Orders", "manageProfile": "Manage Profile", "accountOverview": "Account Overview", "accountOverviewSubtitle": "A summary of your recent activity.", "accountOverviewPlaceholder": "Your recent orders and activities will appear here soon.", "recommendationsTitle": "Recommendations", "recommendationsSubtitle": "Products you might like.", "recommendationsPlaceholder": "We'll have some recommendations for you shortly!", "goToHomepage": "Go to Homepage", "errorFetchingUserData": "An error occurred while fetching your user data. Please try again.", "redirectingTitle": "Redirecting", "defaultStoreName": "My Store", "cloudinaryNotConfiguredError": "Cloudinary is not configured correctly. Please check environment variables.", "storeNameNotLoadedError": "Store name could not be loaded. Please try again or contact support.", "savingProductTitle": "Saving Product", "savingProductDesc": "Please wait while your product is being saved...", "uploadingProductImagesTitle": "Uploading Product Images", "uploadingProductImagesDesc": "Please wait while product images are uploaded to Cloudinary...", "imageUploadProgressTitle": "Image Upload Progress", "imageUploadProgressDesc": "Uploaded {{current}} of {{total}} images: {{fileName}}", "errorIndividualImageUploadFailed": "Failed to upload image '{{fileName}}': {{message}}", "browseAllProductsTitle": "Browse All Products", "noProductsAvailableTitle": "No Products Available Yet", "noProductsAvailableDesc": "It looks like there are no products listed at the moment. Check back soon!", "errorFetchingProductsGeneral": "An error occurred while fetching products. Please try again later.", "byStore": "By: {{storeName}}", "unknownStore": "Unknown Store", "viewDetails": "View Details", "addToCart": "Add to Cart", "noImageAvailable": "No Image", "signingIn": "Signing in...", "profileSettings": "Profile Settings", "manageYourAccountSettings": "Manage your personal account settings", "subscription": "Subscription", "orders": "orders", "subscriptionInfo": "Subscription Information", "subscriptionNotFound": "Subscription information not found", "subscriptionError": "Subscription Error", "subscriptionErrorMessage": "An error occurred while loading subscription information. Please try again.", "contactSupportForHelp": "Contact support for assistance", "userDocumentNotFound": "User data not found", "accountSetupComplete": "Account setup completed successfully", "customerAccountCreated": "Customer account created and free plan activated automatically", "failedToCreateUserDocument": "Failed to create user data", "retryLoading": "Retry", "contactSupport": "Contact Support", "planFeatures": "Plan Features", "andMoreFeatures": "and {{count}} more features", "viewAllPlans": "View All Plans", "upgradeForMoreFeatures": "Upgrade for more features", "upgradePlan": "Upgrade Plan", "customerUpgradeBenefits": "Get free shipping and exclusive discounts", "merchantUpgradeBenefits": "Get advanced features and comprehensive analytics", "currentPlan": "Current Plan", "availableFeatures": "Available Features", "activePlan": "Active Plan", "defaultPlanInfo": "Default Plan Information", "defaultCustomerPlanMessage": "You are registered as a customer with the basic free plan", "defaultMerchantPlanMessage": "You are registered as a merchant with the basic free plan", "features": "Features", "featuresPageTitle": "Platform Features", "featuresPageSubtitle": "Discover all available features for each type of user", "customerFeaturesTitle": "Customer Features", "merchantFeaturesTitle": "Merchant Features", "allFeatures": "All Features", "featureIncluded": "Included", "featureNotIncluded": "Not Included", "totalFeatures": "Total Features", "loginRequired": "<PERSON><PERSON> Required", "loginToViewSubscription": "Please log in to view subscription information", "loadingSubscriptionInfo": "Loading subscription information...", "recentOrders": "Recent Orders", "recentOrdersDescription": "View your latest orders", "noOrdersYet": "No orders yet", "startShoppingMessage": "Start shopping and enjoy the Mikhla experience", "orderNumber": "Order Number", "date": "Date", "items": "Items", "total": "Total", "viewAllOrders": "View All Orders", "pending": "Pending", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "merchantAccount": "Merchant Account", "customerAccount": "Customer Account", "pleaseWait": "Please wait...", "securePayment": "Secure Payment", "premiumSupport": "Premium Support", "allOrders": "All Orders", "ordersFound": "Found {{count}} orders", "noOrdersDescription": "You haven't received any orders yet. Orders will appear here when customers start ordering from your store.", "backToDashboard": "Back to Dashboard", "representatives": "Representatives", "representativeDashboard": "Representative Dash<PERSON>", "welcomeRepresentative": "Welcome, {{representativeName}}!", "representativeAccount": "Representative Account", "becomeRepresentative": "Become a Representative", "representativeSignup": "Representative <PERSON><PERSON>", "representativeLogin": "Representative <PERSON><PERSON>", "representativeProfile": "Representative Profile", "representativeOrders": "Delivery Orders", "representativeEarnings": "Earnings", "representativeStats": "Representative <PERSON><PERSON>", "representativeSettings": "Representative <PERSON><PERSON><PERSON>", "plan_representative_basic_name": "Basic Plan", "plan_representative_premium_name": "Premium Plan", "unlimitedDeliveries": "Unlimited Deliveries", "basicSupport": "Basic Support", "advancedStats": "Advanced Statistics", "detailedReports": "Detailed Reports", "advancedGpsTracking": "Advanced GPS Tracking", "advancedNotifications": "Advanced Notifications", "dailyPayouts": "Daily Payouts", "weeklyPayouts": "Weekly Payouts", "commission5Percent": "5% Commission", "commission10Percent": "10% Commission", "bonusIncentives": "Bonus Incentives", "flexibleSchedule": "Flexible Schedule", "basicTraining": "Basic Training", "premiumTraining": "Premium Training", "standardPriority": "Standard Priority", "basicEarningsReports": "Basic Earnings Reports", "mobileApp": "Mobile App", "gpsTracking": "GPS Tracking", "customerRatings": "Customer Ratings", "basicNotifications": "Basic Notifications", "upgradeToPremium": "Upgrade to Premium", "orderManagementSubtitle": "Track and manage all your store orders", "reportsSubtitle": "Comprehensive reports and analytics for your store performance", "reportsComingSoonTitle": "Reports and Analytics Under Development", "reportsComingSoonDescription": "We're working on an advanced reporting system that will provide you with comprehensive insights about your store performance:", "reportFeature1": "Daily and monthly sales reports", "reportFeature2": "Best-selling products performance analysis", "reportFeature3": "Customer statistics and purchasing behavior", "reportFeature4": "Profit and revenue reports", "reportFeature5": "Interactive charts and graphs", "reportFeature6": "Export reports in various formats", "newMerchantWelcome": "Welcome as a new merchant! You can start by setting up your store and adding your first products.", "personalInformation": "Personal Information", "identityInformation": "Identity Information", "drivingLicense": "Driving License", "vehicleInformation": "Vehicle Information", "vehicleInspection": "Vehicle Inspection", "documentsUpload": "Documents Upload", "fullName": "Full Name", "phoneNumber": "Phone Number", "nationalId": "National ID", "nationalIdType": "ID Type", "nationalIdCard": "National ID", "residentId": "Resident ID", "licenseNumber": "License Number", "issueDate": "Issue Date", "expiryDate": "Expiry Date", "licenseImage": "License Image", "vehicleType": "Vehicle Type", "vehicleModel": "Vehicle Model", "vehicleYear": "Manufacturing Year", "plateNumber": "Plate Number", "vehicleColor": "Vehicle Color", "vehicleImage": "Vehicle Image", "car": "Car", "motorcycle": "Motorcycle", "bicycle": "Bicycle", "certificateNumber": "Certificate Number", "inspectionImage": "Inspection Certificate Image", "totalDeliveries": "Total Deliveries", "completedDeliveries": "Completed Deliveries", "cancelledDeliveries": "Cancelled Deliveries", "averageRating": "Average Rating", "totalEarnings": "Total Earnings", "monthlyEarnings": "Monthly Earnings", "weeklyEarnings": "Weekly Earnings", "dailyEarnings": "Daily Earnings", "averageDeliveryTime": "Average Delivery Time", "successRate": "Success Rate", "deliveryStatus": "Delivery Status", "assigned": "Assigned", "picked_up": "Picked Up", "in_transit": "In Transit", "pickupLocation": "Pickup Location", "deliveryLocation": "Delivery Location", "deliveryFee": "Delivery Fee", "representativeEarning": "Representative <PERSON><PERSON><PERSON>", "workingHours": "Working Hours", "workingDays": "Working Days", "maxDeliveryRadius": "Max Delivery Radius", "vehicleCapacity": "Vehicle Capacity", "autoAcceptOrders": "Auto Accept Orders", "notificationSettings": "Notification Settings", "newOrders": "New Orders", "orderUpdates": "Order Updates", "earnings": "Earnings", "promotions": "Promotions", "enterYourPersonalInformation": "Enter your personal information", "enterFullName": "Enter full name", "enterPhoneNumber": "Enter phone number", "enterEmail": "Enter email address", "enterYourIdentityInformation": "Enter your identity information", "enterNationalId": "Enter national ID number", "uploadYourDrivingLicense": "Upload your driving license", "enterLicenseNumber": "Enter license number", "enterVehicleDetails": "Enter vehicle details", "enterVehicleModel": "Enter vehicle model", "enterPlateNumber": "Enter plate number", "enterVehicleColor": "Enter vehicle color", "enterCertificateNumber": "Enter certificate number", "reviewAndAcceptTerms": "Review and accept terms and conditions", "representativeTermsTitle": "Representative Terms and Conditions", "representativeTermsIntro": "Welcome to <PERSON><PERSON><PERSON>'s delivery representative team", "eligibilityRequirements": "Eligibility Requirements", "vehicleRequirements": "Vehicle Requirements", "deliveryResponsibilities": "Delivery Responsibilities", "commissionStructure": "Commission Structure", "conductStandards": "Conduct Standards", "terminationConditions": "Termination Conditions", "joinOurDeliveryTeam": "Join our delivery team", "step": "Step", "of": "of", "submitApplication": "Submit Application", "pleaseCompleteAllRequiredFields": "Please complete all required fields", "representativeSignupSuccess": "Registration application submitted successfully! It will be reviewed soon.", "representativeSignupError": "An error occurred during registration. Please try again.", "advancedSearch": "Advanced Search", "searchProductsAndStores": "Search products and stores...", "searchResultsFor": "Search results for", "searchError": "An error occurred while searching", "startSearching": "Start Searching", "enterSearchTermToFindProducts": "Enter search term to find products and stores", "noSearchResults": "No search results", "tryDifferentSearchTerms": "Try different search terms or adjust filters", "clearFiltersAndTryAgain": "Clear filters and try again", "noProductsFound": "No products found", "tryAdjustingFilters": "Try adjusting filters for better results", "relevance": "Relevance", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "highestRated": "Highest Rated", "newest": "Newest", "mostPopular": "Most Popular", "browseByCategories": "Browse by Categories", "discoverProductsInDifferentCategories": "Discover products in different categories", "allCategories": "All Categories", "productsFound": "products found", "productsInCategory": "products in category", "totalProducts": "Total Products", "noProductsInThisCategory": "No products in this category", "trySelectingDifferentCategory": "Try selecting a different category or browse all categories", "checkBackLaterForNewProducts": "Check back later for new products", "browseAllCategories": "Browse All Categories", "food": "Food & Beverages", "groceries": "Groceries & Food Items", "fashion": "Fashion & Clothing", "electronics": "Electronics", "homeAndGarden": "Home & Garden", "beautyAndHealth": "Beauty & Health", "sportsAndFitness": "Sports & Fitness", "automotive": "Automotive", "booksAndMedia": "Books & Media", "artsAndCrafts": "Arts & Crafts", "handicrafts": "Handicrafts & Traditional Crafts", "toys": "Toys & Entertainment", "pets": "Pets", "babyKids": "Baby & Kids", "jewelry": "Jewelry & Accessories", "services": "Local Services", "plants": "Plants & Gardening", "appliances": "Home Appliances", "waterSports": "Water Sports & Beach", "perfumes": "Perfumes & Incense", "tools": "Tools & Equipment", "featuredStores": "Featured Stores", "discoverTopRatedStores": "Discover top-rated stores in your area", "discoverLocalStores": "Discover local stores in your area", "viewAllStores": "View All Stores", "noFeaturedStores": "No featured stores currently", "checkBackLaterForFeaturedStores": "Check back later to see featured stores", "basicInformation": "Basic Information", "enterBasicInformation": "Enter your basic information", "passwordSetup": "Password Setup", "createSecurePassword": "Create a secure password", "accountType": "Account Type", "selectAccountType": "Select your account type", "uploadRequiredDocuments": "Upload required documents", "joinOurCommunity": "Join our community", "createAccount": "Create Account", "enterUsername": "Enter username", "enterPassword": "Enter password", "enterConfirmPassword": "Confirm password", "confirmYourPassword": "Confirm your password", "optionalFileFormatsAllowed": "Optional formats allowed: PDF, JPG, PNG", "fileSelected": "File selected", "errorUsernameRequired": "Username is required", "errorEmailRequired": "Email address is required", "chooseYourAccountType": "Choose your account type", "customerDescription": "Shop from local stores and enjoy a unique shopping experience", "merchantDescription": "Create your online store and start selling your products", "representativeDescription": "Join as a delivery representative and earn extra income", "termsAndConditionsIntro": "Please read and review the following terms and conditions carefully", "userRights": "User Rights", "userResponsibilities": "User Responsibilities", "privacyPolicy": "Privacy Policy", "serviceTerms": "Service Terms", "paymentTerms": "Payment Terms", "readyToStart": "Ready to Start", "proceedToCreateAccount": "Proceed to create your account", "customerAccountSummary": "You'll be able to browse products and shop from local stores", "merchantAccountSummary": "You'll be able to create your store and sell products to customers", "representativeAccountSummary": "You'll be able to join our delivery team and earn additional income", "clickNextToProceed": "Click next to proceed to the detailed registration form", "completeYourRegistration": "Complete your registration", "startRegistration": "Start Registration", "finalStep": "Final Step", "reviewYourInformation": "Review your information", "uploaded": "Uploaded", "notUploaded": "Not uploaded", "clickCreateToFinish": "Click create account to finish registration", "loadingRepresentativeSection": "Loading representative section...", "mustBeLoggedInToAccessRepresentative": "You must be logged in to access the representative section", "loadingRepresentativeData": "Loading representative data...", "noRepresentativeDataFound": "No representative data found", "pleaseCompleteRegistration": "Please complete the registration process", "completeRegistration": "Complete Registration", "checkYourApplicationStatus": "Check your application status", "representativeName": "Representative Name", "reapply": "Reapply", "goToDashboard": "Go to Dashboard", "helpCenter": "Help Center", "suspended": "Suspended", "backToHome": "Back to Home", "representativeDashboardSubtitle": "Manage your delivery orders and earnings from here", "reviews": {"noReviews": "No reviews yet", "beFirstToReview": "Be the first to review", "helpful": "Helpful", "report": "Report", "confirmDelete": "Are you sure you want to delete this review?", "viewImages": "View Images", "loginToReview": "Login to add a review", "loginToReviewDescription": "You must be logged in to add a review or rating", "alreadyReviewed": "You've already reviewed this", "alreadyReviewedDescription": "You can only add one review per product or store", "cannotReview": "Cannot add review", "cannotReviewDescription": "You must purchase the product first to add a review", "addStoreReview": "Add Store Review", "addProductReview": "Add Product Review", "ratingRequired": "Rating is required", "comment": "Comment", "commentRequired": "Comment is required", "commentTooShort": "Comment is too short (minimum 10 characters)", "commentPlaceholder": "Share your experience with this product or store...", "images": "Images", "optional": "Optional", "uploadImages": "Upload Images", "uploadingImages": "Uploading images...", "imagesUploaded": "Images uploaded successfully", "imageUploadFailed": "Failed to upload images", "maxImagesExceeded": "Maximum number of images exceeded (5 images)", "maxImages": "You can upload {{count}} more images", "minCharacters": "Minimum {{count}} characters", "submitting": "Submitting...", "ratingLabels": {"1": "Very Poor", "2": "Poor", "3": "Average", "4": "Good", "5": "Excellent"}, "reportReview": "Report Review", "reportReviewDescription": "Help us maintain review quality by reporting inappropriate content", "reportReason": "Report Reason", "additionalDetails": "Additional Details", "reportDescriptionPlaceholder": "Explain why you're reporting this review...", "reportWarningTitle": "Important Warning", "reportWarningDescription": "False reporting may result in account suspension. Please ensure your report is accurate.", "submittingReport": "Submitting report...", "submitReport": "Submit Report", "reportReasons": {"spam": "Spam", "spamDescription": "Unwanted promotional or repetitive content", "inappropriate": "Inappropriate Content", "inappropriateDescription": "Offensive or inappropriate content that violates guidelines", "fake": "Fake Review", "fakeDescription": "Fake or fraudulent review from a fake account", "offensive": "Offensive Content", "offensiveDescription": "Offensive, inflammatory, or discriminatory language", "other": "Other", "otherDescription": "Another reason not listed above"}, "ratingsAndReviews": "Ratings & Reviews", "noRatings": "No ratings yet", "noRatingsDescription": "Be the first to rate this product or store", "totalReviews": "{{count}} reviews", "verifiedReviews": "{{count}} verified reviews", "recentReviews": "Recent Reviews", "ratingDistribution": "Rating Distribution", "verifiedPercentage": "Verified", "positivePercentage": "Positive", "qualityScore": "Quality Score", "qualityScoreDescription": "Comprehensive indicator of product or store quality", "excellent": "Excellent", "veryGood": "Very Good", "good": "Good", "average": "Average", "poor": "Poor", "reviewImages": "Review Images", "imageNavigationHint": "Use arrows or click thumbnails to navigate", "shareYourExperience": "Share Your Experience", "helpOthersWithYourReview": "Help others with your product review", "helpOthersWithYourStoreReview": "Help others with your store review", "customerReviews": "Customer Reviews", "reviewGuidelines": "Review Guidelines", "helpfulReviewTips": "Tips for helpful reviews", "tip1": "Be honest and detailed about your experience", "tip2": "Mention both positives and negatives", "tip3": "Add photos if possible", "tip4": "Be respectful in your comments", "helpfulStoreReviewTips": "Tips for store reviews", "storeTip1": "Rate service quality and products", "storeTip2": "Mention delivery speed and handling", "storeTip3": "Rate store cleanliness and organization", "storeTip4": "Share your customer service experience", "communityGuidelines": "Community Guidelines", "guidelinesDescription": "We are committed to maintaining a respectful and helpful community. Please follow community guidelines when writing reviews.", "viewAllReviews": "View All Reviews", "positiveReviews": "Positive Reviews", "stars": "stars"}, "minutes": "minutes", "currentStatus": "Current Status", "workingStatus": "Working Status", "commissionRate": "Commission Rate", "recentActivity": "Recent Activity", "recentActivitySubtitle": "Latest orders and activities", "noRecentActivity": "No recent activity", "startAcceptingOrders": "Start accepting orders to see activity here", "representativeApprovals": "Representative <PERSON><PERSON><PERSON><PERSON>", "reviewAndApproveRepresentatives": "Review and approve representative applications", "loadingRepresentatives": "Loading representatives...", "noPendingRepresentatives": "No pending applications", "allRepresentativesReviewed": "All representative applications have been reviewed", "submittedOn": "Submitted on", "showDetails": "Show Details", "hideDetails": "Hide Details", "viewLicense": "View License", "viewCertificate": "View Certificate", "reviewNotes": "Review Notes", "addReviewNotes": "Add review notes (optional)", "approve": "Approve", "reject": "Reject", "representativeApproved": "Representative approved successfully", "representativeRejected": "Representative rejected", "errorFetchingRepresentatives": "Error fetching representatives data", "errorUpdatingRepresentative": "Error updating representative status", "representativePlansTitle": "Representative Plans", "joinDeliveryTeamSubtitle": "Join our delivery team and earn flexible income", "representativeBenefitsTitle": "Benefits of working as a representative:", "flexibleWorkingHours": "Flexible working hours", "competitiveCommissions": "Competitive commissions", "weeklyPayments": "Weekly payments", "comprehensiveSupport": "Comprehensive ongoing support", "joinDeliveryTeam": "Join Delivery Team", "pleaseReadTermsCarefully": "Please read the terms and conditions carefully", "acceptTerms": "Accept Terms", "showing": "Showing", "continueWithGoogle": "Continue with Google", "googleSignInFailed": "Google sign-in failed. Please try again.", "googleSignInCancelled": "Google sign-in was cancelled.", "googleSignInPopupBlocked": "Sign-in popup was blocked. Please allow popups and try again.", "orContinueWith": "Or continue with", "orSigninManually": "Or sign in manually", "orCreateManually": "Or create account manually", "selectUserType": "Select Account Type", "selectUserTypeSubtitle": "To start creating your account, first, tell us about the type of account you want", "welcomeToMikhla": "Welcome to Mikhla", "continueAsCustomer": "Continue as Customer", "continueAsMerchant": "Continue as Merchant", "continueAsRepresentative": "Continue as Representative", "chooseAccountType": "Choose your account type", "accountTypeRequired": "Account type selection is required", "userTypeSelectionTitle": "Welcome to Mikhla", "userTypeSelectionSubtitle": "Let's start by creating your account. First, tell us about the type of account you want", "connectionTimeout": "Connection timeout", "retryingConnection": "Retrying connection...", "googleSignInCustomersOnly": "Google Sign-In is available for customers only.", "merchantGoogleSignupNotice": "Google Sign-Up is not available for merchants", "representativeGoogleSignupNotice": "Google Sign-Up is not available for representatives", "merchantManualSignupRequired": "Merchant registration requires filling out a detailed form and uploading required documents. Please use manual registration.", "representativeManualSignupRequired": "Representative registration requires filling out a detailed form and uploading required documents. Please use manual registration.", "signupManually": "Sign Up Manually", "redirectingPleaseWait": "Redirecting, please wait...", "authenticationInProgress": "Authentication in progress...", "processingRequest": "Processing your request...", "signupSuccessful": "Registration successful!", "redirectingToYourAccount": "Redirecting you to your account...", "errorLoadingTerms": "An error occurred while loading terms and conditions", "signupErrorTitle": "Account Creation Failed", "signupErrorMessage": "An error occurred while creating your account. Please try again later.", "merchantApproval": "Merchant Approval", "pendingApproval": "Pending Approval", "approvalPending": "Your application is under review", "approvalApproved": "Your application has been approved!", "approvalRejected": "Your application has been rejected", "approvalPendingDesc": "We are reviewing your merchant application. You will be notified of the result soon.", "approvalApprovedDesc": "Congratulations! Your application has been approved and your store is now active. You can now access the dashboard.", "approvalRejectedDesc": "Your application has been rejected. Please review the requirements and try again.", "submissionDate": "Submission Date", "reviewDate": "Review Date", "applicationInfo": "Application Information", "merchantName": "Merchant Name", "viewFile": "View File", "needHelp": "Need Help?", "submitNewApplication": "Submit New Application", "interactiveMap": "Interactive Map", "findNearbyStores": "Find nearby stores", "detectingLocation": "Detecting your location", "detectingLocationDesc": "We are detecting your location to show nearby stores", "enableLocation": "Enable Location", "locationDetected": "Location Detected", "locationError": "Location Error", "tryAgain": "Try Again", "searchStores": "Search stores...", "filters": "Filters", "clearFilters": "Clear Filters", "storesMap": "Stores Map", "storesFound": "Found {{count}} stores", "locationRequired": "Location Required", "enableLocationToSeeMap": "Enable location to see the map", "nearbyStores": "Nearby Stores", "noStoresFound": "No stores found", "tryDifferentFilters": "Try different filters", "open": "Open", "closed": "Closed", "km": "km", "today": "Today", "website": "Website", "visitStore": "Visit Store", "directions": "Directions", "distanceCalculator": "Distance Calculator", "distanceTo": "Distance to", "walking": "Walking", "cycling": "Cycling", "driving": "Driving", "getDirections": "Get Directions", "note": "Note", "estimatedTimesNote": "Estimated times are approximate and may vary based on road conditions and weather", "errorLoadingStores": "Error loading stores", "checkout": "Checkout", "completeYourOrder": "Complete Your Order", "customerInformation": "Customer Information", "shippingAddress": "Shipping Address", "streetAddress": "Street Address", "city": "City", "postalCode": "Postal Code", "deliveryNotes": "Delivery Notes", "deliveryNotesPlaceholder": "Special delivery instructions (optional)", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery", "creditCard": "Credit Card", "onlinePayment": "Online Payment", "orderNotes": "Order Notes", "orderNotesPlaceholder": "Additional notes for your order (optional)", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shippingFee": "Shipping Fee", "tax": "Tax", "discount": "Discount", "grandTotal": "Grand Total", "placeOrder": "Place Order", "processingOrder": "Processing Order", "orderPlaced": "Order Placed", "orderConfirmation": "Order Confirmation", "thankYouForOrder": "Thank you for your order!", "estimatedDelivery": "Estimated Delivery", "trackOrder": "Track Order", "continueShopping": "Continue Shopping", "orderDetails": "Order Details", "billingAddress": "Billing Address", "sameAsShipping": "Same as shipping address", "differentBillingAddress": "Use different billing address", "products": "Products", "allProducts": "All Products", "productDetails": "Product Details", "lowStock": "Low Stock", "cart": "<PERSON><PERSON>", "searchPlaceholder": "Search for products and stores...", "searching": "Searching...", "recentSearches": "Recent Searches", "clearAll": "Clear All", "trendingSearches": "Trending Searches", "noSuggestions": "No suggestions", "searchResults": "Search Results", "noStoresAvailable": "No stores available currently", "errorFetchingStores": "Error fetching stores", "noProductsInStore": "No products in store", "storeHasNoProducts": "This store currently has no products", "errorFetchingStore": "Error fetching store data", "allStores": "All Stores", "quantity": "Quantity", "freeDeliveryNotice": "Add {{amount}} SAR more for free delivery", "secureCheckoutNotice": "Your payment information is secure and encrypted", "orderSuccessTitle": "Order Placed Successfully", "orderSuccessMessage": "Your order has been placed successfully and will be processed soon", "viewMyOrders": "View My Orders", "orderCreationFailed": "Failed to create order. Please try again", "stores": "Stores", "map": "Map", "storeDetails": "Store Details", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "reviewsComingSoon": "Reviews Coming Soon", "reviewsFeatureWillBeAvailable": "The reviews feature will be available soon", "sortBy": "Sort By", "categories": "Categories", "priceRange": "Price Range", "rating": "Rating", "distance": "Distance", "storeStatus": "Store Status", "openNow": "Open Now", "closedNow": "Closed Now", "anyTime": "Any Time", "within1km": "Within 1 km", "within5km": "Within 5 km", "within10km": "Within 10 km", "within50km": "Within 50 km", "anyDistance": "Any Distance", "4starsAndUp": "4 stars and up", "3starsAndUp": "3 stars and up", "2starsAndUp": "2 stars and up", "1starAndUp": "1 star and up", "anyRating": "Any Rating", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters", "filtersApplied": "Filters Applied", "showingResults": "Showing {{start}}-{{end}} of {{total}} results", "loadMore": "Load More", "endOfResults": "End of results", "noResultsFound": "No results found", "adjustFiltersForBetterResults": "Try adjusting your filters for better results", "sortByRelevance": "Relevance", "sortByPriceLow": "Price: Low to High", "sortByPriceHigh": "Price: High to Low", "sortByRating": "Rating", "sortByDistance": "Distance", "sortByNewest": "Newest", "fourStarsAndUp": "4 stars and up", "threeStarsAndUp": "3 stars and up", "twoStarsAndUp": "2 stars and up", "oneStarAndUp": "1 star and up", "oneKm": "1 km", "fiveKm": "5 km", "tenKm": "10 km", "twentyFiveKm": "25 km", "fiftyKm": "50 km", "common": {"loading": "Loading...", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "price": "Price", "category": "Category", "store": "Store", "product": "Product", "stock": "Stock", "available": "Available", "outOfStock": "Out of Stock", "verified": "Verified", "active": "Active", "inactive": "Inactive", "status": "Status", "phone": "Phone", "email": "Email", "address": "Address", "businessHours": "Business Hours", "storeInfo": "Store Information", "productInfo": "Product Information", "storeStats": "Store Statistics", "totalOrders": "Total Orders", "totalSales": "Total Sales", "sar": "SAR", "productNotFound": "Product Not Found", "productNotFoundDescription": "The requested product was not found", "storeNotFound": "Store Not Found", "storeNotFoundDescription": "The requested store was not found", "backToProducts": "Back to Products", "backToStores": "Back to Stores", "backToProduct": "Back to Product", "backToStore": "Back to Store"}, "admin": {"reviewReports": "Review Reports", "reviewReportsDescription": "Review and handle reports submitted about reviews", "noReportsFound": "No pending reports", "noReportsFoundDescription": "All reports have been reviewed or there are no new reports", "pendingReports": "Pending Reports", "reporter": "Reporter", "reason": "Reason", "reviewContent": "Review Content", "reportDate": "Report Date", "actions": "Actions", "review": "Review", "by": "by", "reviewNotFound": "Review not found", "reviewReport": "Review Report", "reviewReportDescription": "Review the report and take appropriate action", "reportDetails": "Report Details", "description": "Description", "reportedReview": "Reported Review", "moderationNotes": "Moderation Notes", "moderationNotesPlaceholder": "Add notes about the moderation decision...", "moderationWarning": "Important Warning", "moderationWarningDescription": "Moderation decisions affect user experience. Make sure to make the right decision.", "processing": "Processing...", "approveReview": "Approve Review", "rejectReview": "Reject Review", "reportApproved": "Review approved and report closed", "reportRejected": "Review rejected and hidden", "errorFetchingReports": "Error fetching reports", "errorResolvingReport": "Error processing report"}, "popularProducts": "Popular Products", "discoverBestSellingProducts": "Discover the most popular and best-selling products", "viewAllProducts": "View All Products", "noPopularProducts": "No popular products currently", "checkBackLaterForPopularProducts": "Check back later to see popular products", "searchFilters": "Search Filters", "filterByCategory": "Filter by Category", "viewStore": "View Store", "contactAvailable": "Contact Available", "websiteAvailable": "Website Available", "addReview": "Add Review", "writeReview": "Write Review", "rateProduct": "Rate Product", "addToWishlist": "Add to Wishlist", "removeFromCart": "Remove from Cart", "updateQuantity": "Update Quantity", "notifications": "Notifications", "markAsRead": "<PERSON> <PERSON>", "faq": "FAQ", "reportIssue": "Report Issue", "representative.dashboard.welcome": "Welcome, {{name}}!", "representative.dashboard.subtitle": "Manage delivery orders and earnings from here", "representative.dashboard.completed": "Completed", "representative.dashboard.onTimeRate": "On-time delivery rate", "representative.dashboard.avgDeliveryTime": "Average delivery time", "representative.dashboard.loading": "Loading...", "representative.dashboard.representative": "Representative", "representative.dashboard.active": "Active", "representative.dashboard.inactive": "Inactive", "representative.dashboard.available": "Available", "representative.dashboard.unavailable": "Unavailable", "representative.dashboard.totalDeliveries": "Total Deliveries", "representative.dashboard.monthlyEarnings": "Monthly Earnings", "representative.dashboard.totalEarnings": "Total Earnings", "representative.dashboard.averageRating": "Average Rating", "representative.dashboard.reviews": "Reviews", "representative.dashboard.minutes": "Minutes", "representative.dashboard.mustBeLoggedIn": "You must be logged in as a representative to access this section", "representative.nav.dashboard": "Dashboard", "representative.nav.orders": "Orders", "representative.nav.earnings": "Earnings", "representative.nav.profile": "Profile", "auth.login": "<PERSON><PERSON>", "unknownStatus": "Unknown Status", "unknownStatusDesc": "An error occurred while determining your application status", "previous": "Previous", "next": "Next", "warning": "Warning", "closedToday": "Closed Today", "hoursNotAvailable": "Hours Not Available", "following": "Following", "reviews.noReviews": "No reviews", "reviews.beFirstToReview": "Be the first to review", "reviews.helpful": "Helpful", "reviews.report": "Report", "reviews.confirmDelete": "Confirm Delete", "reviews.viewImages": "View Images", "reviews.loginToReview": "Login to Review", "reviews.loginToReviewDescription": "You must be logged in to add a review", "reviews.alreadyReviewed": "Already Reviewed", "reviews.alreadyReviewedDescription": "You have already reviewed this product", "reviews.cannotReview": "Cannot Review", "reviews.cannotReviewDescription": "You cannot review this item", "reviews.addStoreReview": "Add Store Review", "reviews.addProductReview": "Add Product Review", "reviews.ratingRequired": "Rating is required", "reviews.comment": "Comment", "reviews.commentRequired": "Comment is required", "reviews.commentTooShort": "Comment is too short", "reviews.commentPlaceholder": "Share your experience...", "reviews.images": "Images", "reviews.addImages": "Add Images", "reviews.maxImages": "Maximum {{count}} images", "reviews.submitReview": "Submit Review", "reviews.editReview": "Edit Review", "reviews.deleteReview": "Delete Review", "reviews.reviewSubmitted": "Review Submitted", "reviews.reviewUpdated": "Review Updated", "reviews.reviewDeleted": "Review Deleted", "reviews.loadingReviews": "Loading Reviews...", "reviews.noReviewsYet": "No reviews yet", "reviews.writeFirstReview": "Write the first review", "map.loading": "Loading Map...", "map.error": "Map Error", "map.noLocation": "No Location", "map.enableLocation": "Enable Location", "map.locationDenied": "Location Access Denied", "map.locationUnavailable": "Location Unavailable", "map.findStores": "Find Stores", "map.nearbyStores": "Nearby Stores", "map.storeDetails": "Store Details", "map.getDirections": "Get Directions", "map.distance": "Distance", "map.estimatedTime": "Estimated Time", "stores.featured": "Featured Stores", "stores.popular": "Popular Stores", "stores.newest": "Newest Stores", "stores.topRated": "Top Rated Stores", "stores.openNow": "Open Now", "stores.closedNow": "Closed Now", "stores.deliveryAvailable": "Delivery Available", "stores.pickupOnly": "Pickup Only", "stores.freeDelivery": "Free Delivery", "stores.fastDelivery": "Fast Delivery", "stores.viewMenu": "View Menu", "stores.orderOnline": "Order Online", "stores.callStore": "Call Store", "stores.storeInfo": "Store Information", "stores.workingHours": "Working Hours", "stores.contactInfo": "Contact Information", "stores.socialMedia": "Social Media", "products.featured": "Featured Products", "products.bestsellers": "Bestsellers", "products.newArrivals": "New Arrivals", "products.onSale": "On Sale", "products.recommended": "Recommended", "products.relatedProducts": "Related Products", "products.productDetails": "Product Details", "products.specifications": "Specifications", "products.ingredients": "Ingredients", "products.nutritionFacts": "Nutrition Facts", "products.allergenInfo": "Allergen Information", "products.storageInstructions": "Storage Instructions", "products.usageInstructions": "Usage Instructions", "products.warranty": "Warranty", "products.returnPolicy": "Return Policy", "orders.orderHistory": "Order History", "orders.currentOrders": "Current Orders", "orders.pastOrders": "Past Orders", "orders.orderDetails": "Order Details", "orders.orderSummary": "Order Summary", "orders.orderItems": "Order Items", "orders.orderTotal": "Order Total", "orders.orderStatus": "Order Status", "orders.trackOrder": "Track Order", "orders.cancelOrder": "Cancel Order", "orders.reorder": "Reorder", "orders.orderConfirmation": "Order Confirmation", "orders.estimatedDelivery": "Estimated Delivery", "orders.deliveryAddress": "Delivery Address", "orders.paymentMethod": "Payment Method", "orders.orderNotes": "Order Notes", "payment.paymentMethods": "Payment Methods", "payment.creditCard": "Credit Card", "payment.debitCard": "Debit Card", "payment.cashOnDelivery": "Cash on Delivery", "payment.digitalWallet": "Digital Wallet", "payment.bankTransfer": "Bank Transfer", "payment.paymentProcessing": "Processing Payment...", "payment.paymentSuccess": "Payment Successful", "payment.paymentFailed": "Payment Failed", "payment.paymentCancelled": "Payment Cancelled", "payment.refundProcessing": "Processing Refund...", "payment.refundCompleted": "Refund Completed", "payment.billingAddress": "Billing Address", "payment.securePayment": "Secure Payment", "delivery.deliveryOptions": "Delivery Options", "delivery.standardDelivery": "Standard Delivery", "delivery.expressDelivery": "Express Delivery", "delivery.sameDay": "Same Day Delivery", "delivery.nextDay": "Next Day Delivery", "delivery.scheduled": "Scheduled Delivery", "delivery.pickup": "Pickup", "delivery.deliveryFee": "Delivery Fee", "delivery.freeDelivery": "Free Delivery", "delivery.deliveryTime": "Delivery Time", "delivery.deliveryAddress": "Delivery Address", "delivery.deliveryInstructions": "Delivery Instructions", "delivery.contactlessDelivery": "Contactless Delivery", "delivery.trackDelivery": "Track Delivery", "delivery.deliveryStatus": "Delivery Status", "delivery.outForDelivery": "Out for Delivery", "delivery.delivered": "Delivered", "delivery.deliveryConfirmation": "Delivery Confirmation", "follow": "Follow", "share": "Share", "aboutStore": "About Store", "noDescriptionAvailable": "No description available", "trending": "Trending", "location": "Location", "errorFetchingProduct": "Error fetching product", "inStock": "In Stock", "emptyCart": "Cart is empty", "emptyCartDescription": "You haven't added any products to your cart yet", "startShopping": "Start Shopping", "loadingCart": "Loading cart...", "proceedToCheckout": "Proceed to Checkout", "secureCheckout": "Secure and protected payment", "freeDeliveryThreshold": "Free delivery for orders over {{amount}}", "representative.dashboard": "Representative Dash<PERSON>", "representative.orders": "Orders", "representative.earnings": "Earnings", "representative.profile": "Profile", "representative.acceptOrder": "Accept Order", "representative.rejectOrder": "Reject Order", "representative.completeOrder": "Complete Order", "representative.totalEarnings": "Total Earnings", "representative.todayEarnings": "Today's Earnings", "representative.pendingOrders": "Pending Orders", "representative.completedOrders": "Completed Orders", "submitReview": "Submit Review", "reviewSubmitted": "Review Submitted", "thankYouForReview": "Thank you for your review", "orderStatus": "Order Status", "orderTotal": "Order Total", "orderDate": "Order Date", "payment": "Payment", "paymentStatus": "Payment Status", "paymentSuccessful": "Payment Successful", "paymentFailed": "Payment Failed", "shipping": "Shipping", "shippingMethod": "Shipping Method", "shippingCost": "Shipping Cost", "freeShipping": "Free Shipping", "settings": "Settings", "accountSettings": "Account <PERSON><PERSON>", "privacySettings": "Privacy Settings", "error": "Error", "errorOccurred": "An error occurred", "somethingWentWrong": "Something went wrong", "navigation": "Navigation", "menu": "<PERSON><PERSON>", "reports": "Reports", "analytics": "Analytics", "integrations.title": "Integration Settings", "integrations.description": "Connect your store with external ERP and POS systems", "integrations.erp.title": "ERP Systems", "integrations.pos.title": "POS Systems", "integrations.addErp": "Add ERP Integration", "integrations.addPos": "Add POS Integration", "integrations.testConnection": "Test Connection", "integrations.sync": "Sync", "integrations.delete": "Delete", "integrations.status.connected": "Connected", "integrations.status.disconnected": "Disconnected", "integrations.status.error": "Error", "integrations.status.syncing": "Syncing", "integrations.lastSync": "Last Sync", "integrations.neverSynced": "Never", "integrations.syncInterval": "Sync Interval", "integrations.minutes": "minutes", "integrations.enabled": "Enabled", "integrations.disabled": "Disabled", "integrations.products": "Products", "integrations.inventory": "Inventory", "integrations.orders": "Orders", "integrations.customers": "Customers", "integrations.sales": "Sales", "integrations.payments": "Payments", "integrations.accounting": "Accounting", "integrations.noErpIntegrations": "No ERP Integrations", "integrations.noPosIntegrations": "No POS Integrations", "integrations.addFirstErp": "Start by adding an ERP integration to sync your data", "integrations.addFirstPos": "Start by adding a POS integration to sync your sales", "integrations.form.systemType": "System Type", "integrations.form.systemName": "System Name", "integrations.form.apiUrl": "API URL", "integrations.form.apiKey": "API Key", "integrations.form.username": "Username", "integrations.form.password": "Password", "integrations.form.database": "Database", "integrations.form.accessToken": "Access Token", "integrations.form.storeId": "Store ID", "integrations.form.locationId": "Location ID", "integrations.form.environment": "Environment", "integrations.form.sandbox": "Sandbox", "integrations.form.production": "Production", "integrations.form.syncSettings": "Sync Settings", "integrations.form.autoSync": "Auto Sync", "integrations.form.create": "Create Integration", "integrations.form.cancel": "Cancel", "integrations.errors.createFailed": "Failed to create integration", "integrations.errors.testFailed": "Failed to test connection", "integrations.errors.syncFailed": "Failed to sync data", "integrations.errors.deleteFailed": "Failed to delete integration", "integrations.errors.loadFailed": "Failed to load integration data", "integrations.confirmDelete": "Are you sure you want to delete this integration?"}}