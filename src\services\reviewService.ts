import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { CustomerReview, ReviewReport, ReviewStats } from '@/types';

export class ReviewService {
  private reviewsCollection = collection(db, 'reviews');
  private reportsCollection = collection(db, 'review_reports');
  private storesCollection = collection(db, 'stores');
  private productsCollection = collection(db, 'products');

  // إضافة مراجعة جديدة
  async addReview(reviewData: Omit<CustomerReview, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const batch = writeBatch(db);
      
      // إضافة المراجعة
      const reviewRef = doc(this.reviewsCollection);
      const review: CustomerReview = {
        ...reviewData,
        id: reviewRef.id,
        helpfulCount: 0,
        reportCount: 0,
        isReported: false,
        isHidden: false,
        moderationStatus: 'approved', // موافقة تلقائية، يمكن تغييرها لاحقاً
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };
      
      batch.set(reviewRef, review);

      // تحديث إحصائيات المتجر أو المنتج
      if (reviewData.storeId) {
        const storeRef = doc(this.storesCollection, reviewData.storeId);
        batch.update(storeRef, {
          'stats.reviewCount': increment(1),
          'stats.totalRating': increment(reviewData.rating),
          updatedAt: Timestamp.now()
        });
      }

      if (reviewData.productId) {
        const productRef = doc(this.productsCollection, reviewData.productId);
        batch.update(productRef, {
          reviewCount: increment(1),
          totalRating: increment(reviewData.rating),
          updatedAt: Timestamp.now()
        });
      }

      await batch.commit();
      return reviewRef.id;
    } catch (error) {
      console.error('Error adding review:', error);
      throw new Error('فشل في إضافة المراجعة');
    }
  }

  // جلب مراجعات المنتج
  async getProductReviews(
    productId: string, 
    limitCount: number = 10,
    lastDoc?: any
  ): Promise<{ reviews: CustomerReview[], hasMore: boolean }> {
    try {
      let q = query(
        this.reviewsCollection,
        where('productId', '==', productId),
        where('isHidden', '==', false),
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1)
      );

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);
      const reviews = snapshot.docs.slice(0, limitCount).map(doc => ({
        ...doc.data(),
        id: doc.id
      })) as CustomerReview[];

      return {
        reviews,
        hasMore: snapshot.docs.length > limitCount
      };
    } catch (error) {
      console.error('Error fetching product reviews:', error);
      throw new Error('فشل في جلب مراجعات المنتج');
    }
  }

  // جلب مراجعات المتجر
  async getStoreReviews(
    storeId: string, 
    limitCount: number = 10,
    lastDoc?: any
  ): Promise<{ reviews: CustomerReview[], hasMore: boolean }> {
    try {
      let q = query(
        this.reviewsCollection,
        where('storeId', '==', storeId),
        where('isHidden', '==', false),
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1)
      );

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);
      const reviews = snapshot.docs.slice(0, limitCount).map(doc => ({
        ...doc.data(),
        id: doc.id
      })) as CustomerReview[];

      return {
        reviews,
        hasMore: snapshot.docs.length > limitCount
      };
    } catch (error) {
      console.error('Error fetching store reviews:', error);
      throw new Error('فشل في جلب مراجعات المتجر');
    }
  }

  // الإبلاغ عن مراجعة
  async reportReview(reportData: Omit<ReviewReport, 'id' | 'createdAt' | 'status'>): Promise<string> {
    try {
      const batch = writeBatch(db);

      // إضافة التقرير
      const reportRef = doc(this.reportsCollection);
      const report: ReviewReport = {
        ...reportData,
        id: reportRef.id,
        status: 'pending',
        createdAt: Timestamp.now()
      };
      
      batch.set(reportRef, report);

      // تحديث عداد التقارير في المراجعة
      const reviewRef = doc(this.reviewsCollection, reportData.reviewId);
      batch.update(reviewRef, {
        reportCount: increment(1),
        isReported: true,
        updatedAt: Timestamp.now()
      });

      await batch.commit();
      return reportRef.id;
    } catch (error) {
      console.error('Error reporting review:', error);
      throw new Error('فشل في الإبلاغ عن المراجعة');
    }
  }

  // تحديث مفيد للمراجعة
  async markReviewHelpful(reviewId: string): Promise<void> {
    try {
      const reviewRef = doc(this.reviewsCollection, reviewId);
      await updateDoc(reviewRef, {
        helpfulCount: increment(1),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error marking review helpful:', error);
      throw new Error('فشل في تحديث المراجعة');
    }
  }

  // حذف مراجعة (للمستخدم نفسه أو المدير)
  async deleteReview(reviewId: string, userId: string): Promise<void> {
    try {
      const reviewRef = doc(this.reviewsCollection, reviewId);
      const reviewDoc = await getDoc(reviewRef);
      
      if (!reviewDoc.exists()) {
        throw new Error('المراجعة غير موجودة');
      }

      const review = reviewDoc.data() as CustomerReview;
      
      // التحقق من الصلاحية (المستخدم نفسه أو مدير)
      if (review.customerId !== userId) {
        throw new Error('غير مصرح لك بحذف هذه المراجعة');
      }

      const batch = writeBatch(db);
      
      // حذف المراجعة
      batch.delete(reviewRef);

      // تحديث الإحصائيات
      if (review.storeId) {
        const storeRef = doc(this.storesCollection, review.storeId);
        batch.update(storeRef, {
          'stats.reviewCount': increment(-1),
          'stats.totalRating': increment(-review.rating),
          updatedAt: Timestamp.now()
        });
      }

      if (review.productId) {
        const productRef = doc(this.productsCollection, review.productId);
        batch.update(productRef, {
          reviewCount: increment(-1),
          totalRating: increment(-review.rating),
          updatedAt: Timestamp.now()
        });
      }

      await batch.commit();
    } catch (error) {
      console.error('Error deleting review:', error);
      throw new Error('فشل في حذف المراجعة');
    }
  }

  // حساب إحصائيات التقييمات
  async calculateReviewStats(targetId: string, type: 'store' | 'product'): Promise<ReviewStats> {
    try {
      const field = type === 'store' ? 'storeId' : 'productId';
      const q = query(
        this.reviewsCollection,
        where(field, '==', targetId),
        where('isHidden', '==', false)
      );

      const snapshot = await getDocs(q);
      const reviews = snapshot.docs.map(doc => doc.data() as CustomerReview);

      const stats: ReviewStats = {
        totalReviews: reviews.length,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        verifiedReviews: reviews.filter(r => r.isVerified).length,
        recentReviews: 0
      };

      if (reviews.length > 0) {
        // حساب المتوسط
        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        stats.averageRating = Math.round((totalRating / reviews.length) * 10) / 10;

        // توزيع التقييمات
        reviews.forEach(review => {
          stats.ratingDistribution[review.rating as keyof typeof stats.ratingDistribution]++;
        });

        // المراجعات الحديثة (آخر 30 يوم)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        stats.recentReviews = reviews.filter(review => 
          review.createdAt.toDate() > thirtyDaysAgo
        ).length;
      }

      return stats;
    } catch (error) {
      console.error('Error calculating review stats:', error);
      throw new Error('فشل في حساب إحصائيات التقييمات');
    }
  }
}

export const reviewService = new ReviewService();
