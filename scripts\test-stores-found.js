#!/usr/bin/env node

/**
 * اختبار بسيط للتأكد من أن مشكلة "storesFound" تم حلها
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * محاكاة دالة الترجمة
 */
function t(key, params = {}, translations) {
  let text = translations[key] || key;
  
  // استبدال المتغيرات
  Object.keys(params).forEach(param => {
    const placeholder = `{{${param}}}`;
    text = text.replace(new RegExp(placeholder, 'g'), params[param]);
  });
  
  return text;
}

/**
 * اختبار ترجمة storesFound
 */
function testStoresFound() {
  console.log('🧪 اختبار ترجمة "storesFound"...\n');
  
  // قراءة ملفات الترجمة
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    return false;
  }
  
  // اختبار الترجمة العربية
  console.log('📝 اختبار الترجمة العربية:');
  const arTests = [
    { count: 0, expected: 'تم العثور على 0 متجر' },
    { count: 1, expected: 'تم العثور على 1 متجر' },
    { count: 5, expected: 'تم العثور على 5 متجر' },
    { count: 10, expected: 'تم العثور على 10 متجر' }
  ];
  
  let arSuccess = true;
  arTests.forEach(test => {
    const result = t('storesFound', { count: test.count }, arTranslations);
    const success = result === test.expected;
    
    console.log(`   ${success ? '✅' : '❌'} العدد ${test.count}: "${result}"`);
    if (!success) {
      console.log(`      المتوقع: "${test.expected}"`);
      arSuccess = false;
    }
  });
  
  console.log('');
  
  // اختبار الترجمة الإنجليزية
  console.log('📝 اختبار الترجمة الإنجليزية:');
  const enTests = [
    { count: 0, expected: 'Found 0 stores' },
    { count: 1, expected: 'Found 1 stores' },
    { count: 5, expected: 'Found 5 stores' },
    { count: 10, expected: 'Found 10 stores' }
  ];
  
  let enSuccess = true;
  enTests.forEach(test => {
    const result = t('storesFound', { count: test.count }, enTranslations);
    const success = result === test.expected;
    
    console.log(`   ${success ? '✅' : '❌'} Count ${test.count}: "${result}"`);
    if (!success) {
      console.log(`      Expected: "${test.expected}"`);
      enSuccess = false;
    }
  });
  
  console.log('');
  
  // النتيجة النهائية
  const overallSuccess = arSuccess && enSuccess;
  
  if (overallSuccess) {
    console.log('🎉 ممتاز! جميع اختبارات "storesFound" نجحت');
    console.log('✅ المشكلة الأساسية تم حلها بنجاح');
  } else {
    console.log('❌ بعض اختبارات "storesFound" فشلت');
    console.log('⚠️  قد تحتاج إلى مراجعة ملفات الترجمة');
  }
  
  return overallSuccess;
}

/**
 * اختبار إضافي للتأكد من عدم وجود مفاتيح مكررة لـ storesFound
 */
function testNoDuplicateStoresFound() {
  console.log('\n🔍 التحقق من عدم وجود مفاتيح مكررة لـ "storesFound"...');
  
  const files = [
    { path: AR_TRANSLATIONS_PATH, name: 'العربي' },
    { path: EN_TRANSLATIONS_PATH, name: 'الإنجليزي' }
  ];
  
  let allClean = true;
  
  files.forEach(file => {
    try {
      const content = fs.readFileSync(file.path, 'utf8');
      const lines = content.split('\n');
      const storesFoundLines = [];
      
      lines.forEach((line, index) => {
        if (line.includes('"storesFound"')) {
          storesFoundLines.push(index + 1);
        }
      });
      
      if (storesFoundLines.length === 1) {
        console.log(`   ✅ الملف ${file.name}: مفتاح واحد فقط في السطر ${storesFoundLines[0]}`);
      } else if (storesFoundLines.length > 1) {
        console.log(`   ❌ الملف ${file.name}: ${storesFoundLines.length} مفاتيح مكررة في الأسطر: ${storesFoundLines.join(', ')}`);
        allClean = false;
      } else {
        console.log(`   ⚠️  الملف ${file.name}: لا يحتوي على مفتاح "storesFound"`);
        allClean = false;
      }
    } catch (error) {
      console.log(`   ❌ خطأ في فحص الملف ${file.name}: ${error.message}`);
      allClean = false;
    }
  });
  
  return allClean;
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 اختبار إصلاح مشكلة "storesFound"\n');
  
  const functionalTest = testStoresFound();
  const duplicateTest = testNoDuplicateStoresFound();
  
  console.log('\n📋 ملخص النتائج:');
  console.log(`   الاختبار الوظيفي: ${functionalTest ? '✅ نجح' : '❌ فشل'}`);
  console.log(`   اختبار المفاتيح المكررة: ${duplicateTest ? '✅ نجح' : '❌ فشل'}`);
  
  if (functionalTest && duplicateTest) {
    console.log('\n🎉 تم إصلاح مشكلة "storesFound" بنجاح!');
    console.log('💡 يمكنك الآن المتابعة لإصلاح المشاكل الأخرى');
  } else {
    console.log('\n⚠️  لا تزال هناك مشاكل تحتاج إلى إصلاح');
  }
}

// تشغيل الاختبار
if (require.main === module) {
  main();
}

module.exports = {
  testStoresFound,
  testNoDuplicateStoresFound,
  t
};
