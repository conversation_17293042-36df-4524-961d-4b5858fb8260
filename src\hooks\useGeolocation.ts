// src/hooks/useGeolocation.ts
"use client";

import { useState, useEffect, useCallback } from 'react';

export interface GeolocationState {
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  error: string | null;
  loading: boolean;
  permissionStatus: 'prompt' | 'granted' | 'denied' | 'loading';
}

export interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  watch?: boolean;
}

export const useGeolocation = (options: GeolocationOptions = {}) => {
  const {
    enableHighAccuracy = false,
    timeout = 10000,
    maximumAge = 300000, // 5 دقائق
    watch = false
  } = options;

  const [state, setState] = useState<GeolocationState>({
    latitude: null,
    longitude: null,
    accuracy: null,
    error: null,
    loading: false,
    permissionStatus: 'prompt'
  });

  const [watchId, setWatchId] = useState<number | null>(null);

  const handleSuccess = useCallback((position: GeolocationPosition) => {
    setState(prev => ({
      ...prev,
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      error: null,
      loading: false,
      permissionStatus: 'granted'
    }));
  }, []);

  const handleError = useCallback((error: GeolocationPositionError) => {
    let errorMessage = '';
    let permissionStatus: GeolocationState['permissionStatus'] = 'denied';

    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'تم رفض إذن الوصول للموقع';
        permissionStatus = 'denied';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'معلومات الموقع غير متاحة';
        break;
      case error.TIMEOUT:
        errorMessage = 'انتهت مهلة طلب الموقع';
        break;
      default:
        errorMessage = 'حدث خطأ غير معروف';
        break;
    }

    setState(prev => ({
      ...prev,
      error: errorMessage,
      loading: false,
      permissionStatus
    }));
  }, []);

  const getCurrentPosition = useCallback(() => {
    if (!navigator.geolocation) {
      setState(prev => ({
        ...prev,
        error: 'متصفحك لا يدعم خدمة تحديد الموقع',
        loading: false,
        permissionStatus: 'denied'
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    navigator.geolocation.getCurrentPosition(
      handleSuccess,
      handleError,
      {
        enableHighAccuracy,
        timeout,
        maximumAge
      }
    );
  }, [enableHighAccuracy, timeout, maximumAge, handleSuccess, handleError]);

  const startWatching = useCallback(() => {
    if (!navigator.geolocation || watchId !== null) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    const id = navigator.geolocation.watchPosition(
      handleSuccess,
      handleError,
      {
        enableHighAccuracy,
        timeout,
        maximumAge
      }
    );

    setWatchId(id);
  }, [enableHighAccuracy, timeout, maximumAge, handleSuccess, handleError, watchId]);

  const stopWatching = useCallback(() => {
    if (watchId !== null) {
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  }, [watchId]);

  const requestPermission = useCallback(async () => {
    if (!navigator.permissions) {
      getCurrentPosition();
      return;
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      
      setState(prev => ({
        ...prev,
        permissionStatus: permission.state as GeolocationState['permissionStatus']
      }));

      if (permission.state === 'granted') {
        getCurrentPosition();
      } else if (permission.state === 'prompt') {
        getCurrentPosition();
      }

      // مراقبة تغييرات الإذن
      permission.onchange = () => {
        setState(prev => ({
          ...prev,
          permissionStatus: permission.state as GeolocationState['permissionStatus']
        }));
      };
    } catch (error) {
      console.error('خطأ في فحص إذن الموقع:', error);
      getCurrentPosition();
    }
  }, [getCurrentPosition]);

  useEffect(() => {
    if (watch) {
      startWatching();
    }

    return () => {
      if (watch) {
        stopWatching();
      }
    };
  }, [watch, startWatching, stopWatching]);

  return {
    ...state,
    getCurrentPosition,
    requestPermission,
    startWatching,
    stopWatching,
    isWatching: watchId !== null
  };
};
