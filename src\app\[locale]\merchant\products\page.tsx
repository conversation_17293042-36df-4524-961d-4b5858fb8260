// src/app/[locale]/merchant/products/page.tsx
"use client";

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, orderBy, type Timestamp } from 'firebase/firestore';
import type { ProductDocument } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, PlusCircle, Edit, Trash2, PackageSearch } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';

interface EnrichedProductDocument extends ProductDocument {
  id: string; // Ensure id is present
  createdAt?: Timestamp; // Make sure Timestamp type is available
}

export default function MerchantProductsPage() {
  const { t, locale } = useLocale();
  const { user, initialLoadingCompleted } = useAuth();
  const [products, setProducts] = useState<EnrichedProductDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (initialLoadingCompleted && user) {
      const fetchProducts = async () => {
        setIsLoading(true);
        setError(null);
        try {
          const productsRef = collection(db, 'products');
          const q = query(
            productsRef,
            where('merchantUid', '==', user.uid),
            orderBy('createdAt', 'desc')
          );
          const querySnapshot = await getDocs(q);
          const fetchedProducts = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as EnrichedProductDocument[];
          setProducts(fetchedProducts);
        } catch (err) {
          console.error('Error fetching products:', err);
          setError(t('errorFetchingProducts'));
        } finally {
          setIsLoading(false);
        }
      };
      fetchProducts();
    } else if (initialLoadingCompleted && !user) {
      // User is not logged in, MerchantLayout should handle redirect.
      // Setting loading to false if user is definitively not logged in.
      setIsLoading(false);
    }
  }, [user, initialLoadingCompleted, t]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-16rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('loadingProducts')}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary">{t('myProducts')}</h1>
        <Button asChild>
          <Link href={`/${locale}/merchant/products/add`}>
            <PlusCircle className="me-2 h-5 w-5" />
            {t('addNewProductCta')}
          </Link>
        </Button>
      </div>

      {products.length === 0 ? (
        <Card className="shadow-lg">
          <CardHeader className="items-center text-center">
             <PackageSearch className="h-16 w-16 text-muted-foreground mb-4" />
            <CardTitle className="text-2xl">{t('noProductsFoundTitle')}</CardTitle>
            <CardDescription>{t('noProductsFoundDesc')}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button asChild size="lg">
              <Link href={`/${locale}/merchant/products/add`}>
                <PlusCircle className="me-2 h-5 w-5" />
                {t('addProductNow')}
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card className="shadow-xl">
          <CardHeader>
            <CardTitle>{t('productList')}</CardTitle>
            <CardDescription>{t('manageYourProductsDesc')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px] hidden sm:table-cell">{t('image')}</TableHead>
                  <TableHead>{t('productNameTable')}</TableHead>
                  <TableHead className="hidden md:table-cell">{t('category')}</TableHead>
                  <TableHead className="text-right">{t('price')}</TableHead>
                  <TableHead className="text-center hidden sm:table-cell">{t('stock')}</TableHead>
                  <TableHead className="text-center">{t('status')}</TableHead>
                  <TableHead className="text-right">{t('actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {products.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="hidden sm:table-cell">
                      {product.imageUrls && product.imageUrls.length > 0 ? (
                        <Image
                          src={product.imageUrls[0]}
                          alt={product.name}
                          width={64}
                          height={64}
                          className="rounded-md object-cover aspect-square"
                          data-ai-hint="product photo"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-muted rounded-md flex items-center justify-center text-xs text-muted-foreground">
                          {t('noImage')}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell className="hidden md:table-cell">{product.category}</TableCell>
                    <TableCell className="text-right">
                      {product.price.toLocaleString(locale === 'ar' ? 'ar-SA' : 'en-US', {
                        style: 'currency',
                        currency: product.currency || 'SAR',
                      })}
                    </TableCell>
                    <TableCell className="text-center hidden sm:table-cell">{product.stockQuantity}</TableCell>
                    <TableCell className="text-center">
                      <Badge variant={product.isActive ? 'default' : 'secondary'}>
                        {product.isActive ? t('active') : t('inactive')}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" className="me-1 hover:text-primary" aria-label={t('editProduct')}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="hover:text-destructive" aria-label={t('deleteProduct')}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
