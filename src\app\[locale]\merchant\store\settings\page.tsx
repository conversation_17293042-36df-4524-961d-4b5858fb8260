// src/app/[locale]/merchant/store/settings/page.tsx
"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Settings, Store, Clock, Phone, Mail, MapPin, Globe, Camera,
  Upload, Save, Loader2, CheckCircle, AlertCircle, Image as ImageIcon,
  Facebook, Instagram, Twitter, Youtube, Linkedin, ExternalLink, ArrowLeft
} from 'lucide-react';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { StoreDocument } from '@/types';
import Link from 'next/link';

interface WorkingHours {
  [key: string]: {
    isOpen: boolean;
    openTime: string;
    closeTime: string;
  };
}

interface SocialMedia {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;
  linkedin?: string;
  website?: string;
}

export default function StoreSettingsPage() {
  const { user } = useAuth();
  const { t, locale } = useLocale();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingCover, setUploadingCover] = useState(false);

  // بيانات المتجر
  const [storeData, setStoreData] = useState<StoreDocument | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    region: '',
    postalCode: '',
    logoUrl: '',
    coverImageUrl: '',
    isActive: true,
    allowOnlineOrders: true,
    allowDelivery: true,
    allowPickup: true,
    deliveryFee: 0,
    minOrderAmount: 0,
    maxDeliveryDistance: 10,
  });

  const [workingHours, setWorkingHours] = useState<WorkingHours>({
    sunday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
    monday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
    tuesday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
    wednesday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
    thursday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
    friday: { isOpen: true, openTime: '14:00', closeTime: '22:00' },
    saturday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  });

  const [socialMedia, setSocialMedia] = useState<SocialMedia>({});

  const daysOfWeek = [
    { key: 'sunday', label: 'الأحد' },
    { key: 'monday', label: 'الإثنين' },
    { key: 'tuesday', label: 'الثلاثاء' },
    { key: 'wednesday', label: 'الأربعاء' },
    { key: 'thursday', label: 'الخميس' },
    { key: 'friday', label: 'الجمعة' },
    { key: 'saturday', label: 'السبت' },
  ];

  // جلب بيانات المتجر
  useEffect(() => {
    const fetchStoreData = async () => {
      if (!user) return;

      try {
        const storeRef = doc(db, 'stores', user.uid);
        const storeSnap = await getDoc(storeRef);

        if (storeSnap.exists()) {
          const data = storeSnap.data() as StoreDocument;
          setStoreData(data);

          // تحديث بيانات النموذج
          setFormData({
            name: data.name || '',
            description: data.description || '',
            phone: data.phone || '',
            email: data.email || '',
            address: data.address || '',
            city: data.city || '',
            region: data.region || '',
            postalCode: data.postalCode || '',
            logoUrl: data.logoUrl || '',
            coverImageUrl: data.coverImageUrl || '',
            isActive: data.isActive ?? true,
            allowOnlineOrders: data.allowOnlineOrders ?? true,
            allowDelivery: data.allowDelivery ?? true,
            allowPickup: data.allowPickup ?? true,
            deliveryFee: data.deliveryFee || 0,
            minOrderAmount: data.minOrderAmount || 0,
            maxDeliveryDistance: data.maxDeliveryDistance || 10,
          });

          // تحديث ساعات العمل
          if (data.workingHours) {
            setWorkingHours(data.workingHours);
          }

          // تحديث وسائل التواصل
          if (data.socialMedia) {
            setSocialMedia(data.socialMedia);
          }
        }
      } catch (error) {
        console.error('Error fetching store data:', error);
        toast.error('حدث خطأ في جلب بيانات المتجر');
      } finally {
        setLoading(false);
      }
    };

    fetchStoreData();
  }, [user]);

  // رفع الصور
  const handleImageUpload = async (file: File, type: 'logo' | 'cover') => {
    if (!user) return;

    const setUploading = type === 'logo' ? setUploadingLogo : setUploadingCover;
    setUploading(true);

    try {
      // رفع الصورة إلى Cloudinary
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', 'mikhla_stores'); // يجب إنشاء upload preset في Cloudinary

      const response = await fetch(
        `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`,
        {
          method: 'POST',
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error('فشل في رفع الصورة إلى Cloudinary');
      }

      const data = await response.json();
      const downloadURL = data.secure_url;

      setFormData(prev => ({
        ...prev,
        [type === 'logo' ? 'logoUrl' : 'coverImageUrl']: downloadURL
      }));

      toast.success(`تم رفع ${type === 'logo' ? 'الشعار' : 'صورة الغلاف'} بنجاح`);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(`حدث خطأ في رفع ${type === 'logo' ? 'الشعار' : 'صورة الغلاف'}`);
    } finally {
      setUploading(false);
    }
  };

  // حفظ الإعدادات
  const handleSave = async () => {
    if (!user || !storeData) return;

    setSaving(true);
    try {
      const storeRef = doc(db, 'stores', user.uid);
      await updateDoc(storeRef, {
        ...formData,
        workingHours,
        socialMedia,
        updatedAt: new Date(),
      });

      toast.success('تم حفظ إعدادات المتجر بنجاح');
    } catch (error) {
      console.error('Error saving store settings:', error);
      toast.error('حدث خطأ في حفظ الإعدادات');
    } finally {
      setSaving(false);
    }
  };

  // تحديث ساعات العمل
  const updateWorkingHours = (day: string, field: string, value: any) => {
    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }));
  };

  // تحديث وسائل التواصل
  const updateSocialMedia = (platform: string, value: string) => {
    setSocialMedia(prev => ({
      ...prev,
      [platform]: value
    }));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[...Array(3)].map((_, j) => (
                    <div key={j} className="h-10 bg-gray-200 rounded animate-pulse" />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link href={`/${locale}/merchant/dashboard`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Settings className="h-8 w-8 text-primary" />
                إعدادات المتجر
              </h1>
              <p className="text-muted-foreground">
                إدارة معلومات وإعدادات متجرك
              </p>
            </div>
          </div>

          <Button onClick={handleSave} disabled={saving} size="lg">
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            حفظ التغييرات
          </Button>
        </div>

        <div className="space-y-6">
          {/* معلومات المتجر الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Store className="h-5 w-5" />
                معلومات المتجر الأساسية
              </CardTitle>
              <CardDescription>
                تحديث اسم ووصف ومعلومات الاتصال الخاصة بمتجرك
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">اسم المتجر *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="أدخل اسم المتجر"
                  />
                </div>
                <div>
                  <Label htmlFor="phone">رقم الهاتف *</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="+966 50 123 4567"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">وصف المتجر</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="اكتب وصفاً مختصراً عن متجرك ومنتجاتك"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
            </CardContent>
          </Card>

          {/* العنوان والموقع */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                العنوان والموقع
              </CardTitle>
              <CardDescription>
                تحديد موقع المتجر لتسهيل وصول العملاء
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="address">العنوان التفصيلي *</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="الشارع، الحي، رقم المبنى"
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">المدينة *</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    placeholder="الرياض"
                  />
                </div>
                <div>
                  <Label htmlFor="region">المنطقة</Label>
                  <Input
                    id="region"
                    value={formData.region}
                    onChange={(e) => setFormData(prev => ({ ...prev, region: e.target.value }))}
                    placeholder="منطقة الرياض"
                  />
                </div>
                <div>
                  <Label htmlFor="postalCode">الرمز البريدي</Label>
                  <Input
                    id="postalCode"
                    value={formData.postalCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                    placeholder="12345"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الصور والشعار */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5" />
                الصور والشعار
              </CardTitle>
              <CardDescription>
                رفع شعار المتجر وصورة الغلاف
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* شعار المتجر */}
              <div>
                <Label>شعار المتجر</Label>
                <div className="flex items-center gap-4 mt-2">
                  {formData.logoUrl ? (
                    <div className="relative">
                      <img
                        src={formData.logoUrl}
                        alt="شعار المتجر"
                        className="w-20 h-20 object-cover rounded-lg border"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                        onClick={() => setFormData(prev => ({ ...prev, logoUrl: '' }))}
                      >
                        ×
                      </Button>
                    </div>
                  ) : (
                    <div className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                      <ImageIcon className="h-8 w-8 text-gray-400" />
                    </div>
                  )}

                  <div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImageUpload(file, 'logo');
                      }}
                      className="hidden"
                      id="logo-upload"
                    />
                    <Label htmlFor="logo-upload" className="cursor-pointer">
                      <Button variant="outline" disabled={uploadingLogo} asChild>
                        <span>
                          {uploadingLogo ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Upload className="h-4 w-4 mr-2" />
                          )}
                          رفع شعار
                        </span>
                      </Button>
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      الحد الأقصى: 2MB، PNG/JPG
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* صورة الغلاف */}
              <div>
                <Label>صورة الغلاف</Label>
                <div className="mt-2">
                  {formData.coverImageUrl ? (
                    <div className="relative">
                      <img
                        src={formData.coverImageUrl}
                        alt="صورة الغلاف"
                        className="w-full h-48 object-cover rounded-lg border"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => setFormData(prev => ({ ...prev, coverImageUrl: '' }))}
                      >
                        حذف الصورة
                      </Button>
                    </div>
                  ) : (
                    <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center">
                      <ImageIcon className="h-12 w-12 text-gray-400 mb-2" />
                      <p className="text-gray-500">لا توجد صورة غلاف</p>
                    </div>
                  )}

                  <div className="mt-4">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImageUpload(file, 'cover');
                      }}
                      className="hidden"
                      id="cover-upload"
                    />
                    <Label htmlFor="cover-upload" className="cursor-pointer">
                      <Button variant="outline" disabled={uploadingCover} asChild>
                        <span>
                          {uploadingCover ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Upload className="h-4 w-4 mr-2" />
                          )}
                          رفع صورة الغلاف
                        </span>
                      </Button>
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      الحد الأقصى: 5MB، PNG/JPG، الأبعاد المثلى: 1200x400
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ساعات العمل */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                ساعات العمل
              </CardTitle>
              <CardDescription>
                تحديد أوقات فتح وإغلاق المتجر لكل يوم
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {daysOfWeek.map((day) => (
                  <div key={day.key} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="w-20">
                      <Label className="font-medium">{day.label}</Label>
                    </div>

                    <div className="flex items-center gap-2">
                      <Switch
                        checked={workingHours[day.key]?.isOpen || false}
                        onCheckedChange={(checked) => updateWorkingHours(day.key, 'isOpen', checked)}
                      />
                      <span className="text-sm">
                        {workingHours[day.key]?.isOpen ? 'مفتوح' : 'مغلق'}
                      </span>
                    </div>

                    {workingHours[day.key]?.isOpen && (
                      <div className="flex items-center gap-2 flex-1">
                        <div>
                          <Label className="text-xs">من</Label>
                          <Input
                            type="time"
                            value={workingHours[day.key]?.openTime || '09:00'}
                            onChange={(e) => updateWorkingHours(day.key, 'openTime', e.target.value)}
                            className="w-24"
                          />
                        </div>
                        <span className="text-muted-foreground">إلى</span>
                        <div>
                          <Label className="text-xs">إلى</Label>
                          <Input
                            type="time"
                            value={workingHours[day.key]?.closeTime || '22:00'}
                            onChange={(e) => updateWorkingHours(day.key, 'closeTime', e.target.value)}
                            className="w-24"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* إعدادات الطلبات والتوصيل */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات الطلبات والتوصيل
              </CardTitle>
              <CardDescription>
                تكوين خيارات الطلبات والتوصيل والأسعار
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* حالة المتجر */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label className="font-medium">حالة المتجر</Label>
                  <p className="text-sm text-muted-foreground">
                    تفعيل أو إيقاف استقبال الطلبات
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                  <Badge variant={formData.isActive ? "default" : "secondary"}>
                    {formData.isActive ? 'نشط' : 'غير نشط'}
                  </Badge>
                </div>
              </div>

              {/* خيارات الطلبات */}
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <Label className="font-medium">الطلبات الإلكترونية</Label>
                    <p className="text-sm text-muted-foreground">
                      السماح بالطلبات عبر الموقع
                    </p>
                  </div>
                  <Switch
                    checked={formData.allowOnlineOrders}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowOnlineOrders: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <Label className="font-medium">خدمة التوصيل</Label>
                    <p className="text-sm text-muted-foreground">
                      توصيل الطلبات للعملاء
                    </p>
                  </div>
                  <Switch
                    checked={formData.allowDelivery}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowDelivery: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <Label className="font-medium">الاستلام من المتجر</Label>
                    <p className="text-sm text-muted-foreground">
                      السماح للعملاء بالاستلام المباشر
                    </p>
                  </div>
                  <Switch
                    checked={formData.allowPickup}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowPickup: checked }))}
                  />
                </div>
              </div>

              {/* إعدادات التوصيل */}
              {formData.allowDelivery && (
                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium">إعدادات التوصيل</h4>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="deliveryFee">رسوم التوصيل (ريال)</Label>
                      <Input
                        id="deliveryFee"
                        type="number"
                        min="0"
                        step="0.5"
                        value={formData.deliveryFee}
                        onChange={(e) => setFormData(prev => ({ ...prev, deliveryFee: parseFloat(e.target.value) || 0 }))}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="minOrderAmount">الحد الأدنى للطلب (ريال)</Label>
                      <Input
                        id="minOrderAmount"
                        type="number"
                        min="0"
                        value={formData.minOrderAmount}
                        onChange={(e) => setFormData(prev => ({ ...prev, minOrderAmount: parseFloat(e.target.value) || 0 }))}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="maxDeliveryDistance">أقصى مسافة توصيل (كم)</Label>
                      <Input
                        id="maxDeliveryDistance"
                        type="number"
                        min="1"
                        max="50"
                        value={formData.maxDeliveryDistance}
                        onChange={(e) => setFormData(prev => ({ ...prev, maxDeliveryDistance: parseInt(e.target.value) || 10 }))}
                        placeholder="10"
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* وسائل التواصل الاجتماعي */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                وسائل التواصل الاجتماعي
              </CardTitle>
              <CardDescription>
                ربط حسابات التواصل الاجتماعي بمتجرك
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="website" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    الموقع الإلكتروني
                  </Label>
                  <Input
                    id="website"
                    value={socialMedia.website || ''}
                    onChange={(e) => updateSocialMedia('website', e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>

                <div>
                  <Label htmlFor="facebook" className="flex items-center gap-2">
                    <Facebook className="h-4 w-4" />
                    فيسبوك
                  </Label>
                  <Input
                    id="facebook"
                    value={socialMedia.facebook || ''}
                    onChange={(e) => updateSocialMedia('facebook', e.target.value)}
                    placeholder="https://facebook.com/yourstore"
                  />
                </div>

                <div>
                  <Label htmlFor="instagram" className="flex items-center gap-2">
                    <Instagram className="h-4 w-4" />
                    إنستغرام
                  </Label>
                  <Input
                    id="instagram"
                    value={socialMedia.instagram || ''}
                    onChange={(e) => updateSocialMedia('instagram', e.target.value)}
                    placeholder="https://instagram.com/yourstore"
                  />
                </div>

                <div>
                  <Label htmlFor="twitter" className="flex items-center gap-2">
                    <Twitter className="h-4 w-4" />
                    تويتر
                  </Label>
                  <Input
                    id="twitter"
                    value={socialMedia.twitter || ''}
                    onChange={(e) => updateSocialMedia('twitter', e.target.value)}
                    placeholder="https://twitter.com/yourstore"
                  />
                </div>

                <div>
                  <Label htmlFor="youtube" className="flex items-center gap-2">
                    <Youtube className="h-4 w-4" />
                    يوتيوب
                  </Label>
                  <Input
                    id="youtube"
                    value={socialMedia.youtube || ''}
                    onChange={(e) => updateSocialMedia('youtube', e.target.value)}
                    placeholder="https://youtube.com/yourstore"
                  />
                </div>

                <div>
                  <Label htmlFor="linkedin" className="flex items-center gap-2">
                    <Linkedin className="h-4 w-4" />
                    لينكد إن
                  </Label>
                  <Input
                    id="linkedin"
                    value={socialMedia.linkedin || ''}
                    onChange={(e) => updateSocialMedia('linkedin', e.target.value)}
                    placeholder="https://linkedin.com/company/yourstore"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* أزرار الحفظ */}
          <div className="flex justify-end gap-4 pt-6">
            <Link href={`/${locale}/merchant/dashboard`}>
              <Button variant="outline">
                إلغاء
              </Button>
            </Link>
            <Button onClick={handleSave} disabled={saving} size="lg">
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              حفظ جميع التغييرات
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
