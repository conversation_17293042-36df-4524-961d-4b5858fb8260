import React from 'react'
import { AuthProvider, useAuth } from './AuthContext'

// مكون اختبار لاستخدام الـ context
const TestComponent = () => {
  const { user, loading, signIn, signOut } = useAuth()
  
  return (
    <div>
      <div data-testid="loading-status">{loading ? 'جاري التحميل' : 'مكتمل'}</div>
      <div data-testid="user-status">{user ? 'مسجل الدخول' : 'غير مسجل'}</div>
      <button data-testid="signin-btn" onClick={() => signIn('<EMAIL>', 'password')}>
        تسجيل الدخول
      </button>
      <button data-testid="signout-btn" onClick={signOut}>
        تسجيل الخروج
      </button>
      {user && (
        <div data-testid="user-info">
          <span data-testid="user-email">{user.email}</span>
          <span data-testid="user-type">{user.userType}</span>
        </div>
      )}
    </div>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    // مسح localStorage قبل كل اختبار
    cy.window().then((win) => {
      win.localStorage.clear()
    })
  })

  it('يعرض الحالة الافتراضية بشكل صحيح', () => {
    cy.mount(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    cy.get('[data-testid="loading-status"]').should('contain.text', 'مكتمل')
    cy.get('[data-testid="user-status"]').should('contain.text', 'غير مسجل')
  })

  it('يتعامل مع تسجيل الدخول', () => {
    cy.mount(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    cy.get('[data-testid="signin-btn"]').click()
    
    // التحقق من تغيير الحالة
    cy.get('[data-testid="user-status"]').should('contain.text', 'مسجل الدخول')
    cy.get('[data-testid="user-email"]').should('contain.text', '<EMAIL>')
  })

  it('يتعامل مع تسجيل الخروج', () => {
    cy.mount(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    // تسجيل الدخول أولاً
    cy.get('[data-testid="signin-btn"]').click()
    cy.get('[data-testid="user-status"]').should('contain.text', 'مسجل الدخول')
    
    // تسجيل الخروج
    cy.get('[data-testid="signout-btn"]').click()
    cy.get('[data-testid="user-status"]').should('contain.text', 'غير مسجل')
  })

  it('يحافظ على حالة المستخدم في localStorage', () => {
    cy.mount(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    cy.get('[data-testid="signin-btn"]').click()
    
    // التحقق من حفظ البيانات في localStorage
    cy.window().its('localStorage').invoke('getItem', 'auth-user').should('exist')
  })

  it('يستعيد حالة المستخدم من localStorage', () => {
    // محاكاة وجود مستخدم في localStorage
    cy.window().then((win) => {
      win.localStorage.setItem('auth-user', JSON.stringify({
        id: '123',
        email: '<EMAIL>',
        userType: 'customer'
      }))
    })
    
    cy.mount(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    cy.get('[data-testid="user-status"]').should('contain.text', 'مسجل الدخول')
    cy.get('[data-testid="user-email"]').should('contain.text', '<EMAIL>')
  })

  it('يعرض حالة التحميل أثناء العمليات', () => {
    cy.mount(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    cy.get('[data-testid="signin-btn"]').click()
    
    // قد تظهر حالة التحميل لفترة قصيرة
    cy.get('[data-testid="loading-status"]').should('exist')
  })

  it('يتعامل مع أنواع المستخدمين المختلفة', () => {
    const UserTypeComponent = () => {
      const { user, signIn } = useAuth()
      
      return (
        <div>
          <button 
            data-testid="signin-customer" 
            onClick={() => signIn('<EMAIL>', 'password', 'customer')}
          >
            دخول كعميل
          </button>
          <button 
            data-testid="signin-merchant" 
            onClick={() => signIn('<EMAIL>', 'password', 'merchant')}
          >
            دخول كتاجر
          </button>
          <button 
            data-testid="signin-representative" 
            onClick={() => signIn('<EMAIL>', 'password', 'representative')}
          >
            دخول كمندوب
          </button>
          {user && <div data-testid="user-type">{user.userType}</div>}
        </div>
      )
    }
    
    cy.mount(
      <AuthProvider>
        <UserTypeComponent />
      </AuthProvider>
    )
    
    cy.get('[data-testid="signin-customer"]').click()
    cy.get('[data-testid="user-type"]').should('contain.text', 'customer')
    
    cy.get('[data-testid="signin-merchant"]').click()
    cy.get('[data-testid="user-type"]').should('contain.text', 'merchant')
    
    cy.get('[data-testid="signin-representative"]').click()
    cy.get('[data-testid="user-type"]').should('contain.text', 'representative')
  })
})
