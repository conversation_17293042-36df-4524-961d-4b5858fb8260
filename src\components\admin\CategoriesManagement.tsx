'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLocale } from '@/hooks/use-locale';
import { CategoryTree } from './CategoryTree';
import { ProductsModeration } from './ProductsModeration';
import { BrandManagement } from './BrandManagement';
import { CategoryEditor } from './CategoryEditor';
import { 
  FolderTree, 
  Package, 
  Award,
  Plus,
  Settings,
  BarChart3
} from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function CategoriesManagement() {
  const { t } = useLocale();
  const [activeTab, setActiveTab] = useState('categories');
  const [showCategoryEditor, setShowCategoryEditor] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);

  const tabs = [
    {
      id: 'categories',
      label: t('allCategories'),
      icon: FolderTree,
      description: 'إدارة شجرة الفئات والفئات الفرعية'
    },
    {
      id: 'products',
      label: t('productModeration'),
      icon: Package,
      description: 'مراجعة واعتماد المنتجات الجديدة'
    },
    {
      id: 'brands',
      label: t('brandManagement'),
      icon: Award,
      description: 'إدارة العلامات التجارية والماركات'
    }
  ];

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FolderTree className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">إجمالي الفئات</p>
                <p className="text-xl font-bold">24</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Package className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">المنتجات النشطة</p>
                <p className="text-xl font-bold">1,247</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Settings className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">في انتظار المراجعة</p>
                <p className="text-xl font-bold">15</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">العلامات التجارية</p>
                <p className="text-xl font-bold">89</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between mb-6">
          <TabsList className="grid w-full max-w-md grid-cols-3">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <TabsTrigger 
                  key={tab.id} 
                  value={tab.id}
                  className="flex items-center gap-2"
                >
                  <IconComponent className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* أزرار الإجراءات */}
          <div className="flex items-center gap-2">
            {activeTab === 'categories' && (
              <Button
                onClick={() => setShowCategoryEditor(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {t('addCategory')}
              </Button>
            )}
            
            {activeTab === 'brands' && (
              <Button
                onClick={() => {/* إضافة علامة تجارية */}}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {t('addBrand')}
              </Button>
            )}
          </div>
        </div>

        {/* وصف التبويب النشط */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              {(() => {
                const activeTabData = tabs.find(tab => tab.id === activeTab);
                const IconComponent = activeTabData?.icon || FolderTree;
                return (
                  <>
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <IconComponent className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {activeTabData?.label}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {activeTabData?.description}
                      </p>
                    </div>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        {/* محتوى التبويبات */}
        <TabsContent value="categories" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* شجرة الفئات */}
            <div className="lg:col-span-1">
              <CategoryTree
                onEditCategory={(category) => {
                  setEditingCategory(category);
                  setShowCategoryEditor(true);
                }}
              />
            </div>

            {/* تفاصيل الفئة المحددة */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>تفاصيل الفئة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-gray-500">
                    اختر فئة من الشجرة لعرض تفاصيلها
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <ProductsModeration />
        </TabsContent>

        <TabsContent value="brands" className="space-y-6">
          <BrandManagement />
        </TabsContent>
      </Tabs>

      {/* محرر الفئات */}
      {showCategoryEditor && (
        <CategoryEditor
          category={editingCategory}
          onClose={() => {
            setShowCategoryEditor(false);
            setEditingCategory(null);
          }}
          onSave={(category) => {
            // حفظ الفئة
            setShowCategoryEditor(false);
            setEditingCategory(null);
          }}
        />
      )}
    </div>
  );
}
