'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { CreditCard, DollarSign, Banknote } from 'lucide-react';
import { Switch } from '@/components/ui/switch';

interface PaymentSettingsProps {
  onSettingsChange: () => void;
}

export function PaymentSettings({ onSettingsChange }: PaymentSettingsProps) {
  const { t } = useLocale();
  const [settings, setSettings] = useState({
    paymentMethods: {
      cash: { enabled: true, name: 'الدفع عند الاستلام' },
      card: { enabled: true, name: 'البطاقات الائتمانية' },
      wallet: { enabled: false, name: 'المحافظ الرقمية' },
      bankTransfer: { enabled: false, name: 'التحويل البنكي' }
    },
    paymentGateways: {
      paypal: {
        enabled: true,
        clientId: 'your-paypal-client-id',
        clientSecret: 'your-paypal-client-secret',
        sandbox: true
      },
      stripe: {
        enabled: false,
        publishableKey: '',
        secretKey: '',
        sandbox: true
      },
      mada: {
        enabled: false,
        merchantId: '',
        terminalId: '',
        sandbox: true
      }
    },
    currency: {
      primary: 'SAR',
      symbol: 'ر.س',
      position: 'after'
    },
    minimumOrderAmount: 10,
    maximumOrderAmount: 10000
  });

  const handleMethodToggle = (method: string, enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        [method]: {
          ...prev.paymentMethods[method as keyof typeof prev.paymentMethods],
          enabled
        }
      }
    }));
    onSettingsChange();
  };

  const handleGatewayChange = (gateway: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      paymentGateways: {
        ...prev.paymentGateways,
        [gateway]: {
          ...prev.paymentGateways[gateway as keyof typeof prev.paymentGateways],
          [field]: value
        }
      }
    }));
    onSettingsChange();
  };

  const handleGeneralChange = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    onSettingsChange();
  };

  return (
    <div className="space-y-6">
      {/* طرق الدفع */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            طرق الدفع المتاحة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(settings.paymentMethods).map(([key, method]) => (
            <div key={key} className="flex items-center justify-between">
              <Label>{method.name}</Label>
              <Switch
                checked={method.enabled}
                onCheckedChange={(checked) => handleMethodToggle(key, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* بوابات الدفع */}
      <Card>
        <CardHeader>
          <CardTitle>بوابات الدفع الإلكتروني</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* PayPal */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">PayPal</Label>
              <Switch
                checked={settings.paymentGateways.paypal.enabled}
                onCheckedChange={(checked) => handleGatewayChange('paypal', 'enabled', checked)}
              />
            </div>
            
            {settings.paymentGateways.paypal.enabled && (
              <div className="space-y-3 pl-4 border-l-2 border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Client ID</Label>
                    <Input
                      value={settings.paymentGateways.paypal.clientId}
                      onChange={(e) => handleGatewayChange('paypal', 'clientId', e.target.value)}
                      placeholder="PayPal Client ID"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Client Secret</Label>
                    <Input
                      type="password"
                      value={settings.paymentGateways.paypal.clientSecret}
                      onChange={(e) => handleGatewayChange('paypal', 'clientSecret', e.target.value)}
                      placeholder="PayPal Client Secret"
                    />
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label>وضع التجربة (Sandbox)</Label>
                  <Switch
                    checked={settings.paymentGateways.paypal.sandbox}
                    onCheckedChange={(checked) => handleGatewayChange('paypal', 'sandbox', checked)}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Stripe */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Stripe</Label>
              <Switch
                checked={settings.paymentGateways.stripe.enabled}
                onCheckedChange={(checked) => handleGatewayChange('stripe', 'enabled', checked)}
              />
            </div>
            
            {settings.paymentGateways.stripe.enabled && (
              <div className="space-y-3 pl-4 border-l-2 border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Publishable Key</Label>
                    <Input
                      value={settings.paymentGateways.stripe.publishableKey}
                      onChange={(e) => handleGatewayChange('stripe', 'publishableKey', e.target.value)}
                      placeholder="pk_test_..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Secret Key</Label>
                    <Input
                      type="password"
                      value={settings.paymentGateways.stripe.secretKey}
                      onChange={(e) => handleGatewayChange('stripe', 'secretKey', e.target.value)}
                      placeholder="sk_test_..."
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* مدى */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">مدى (Mada)</Label>
              <Switch
                checked={settings.paymentGateways.mada.enabled}
                onCheckedChange={(checked) => handleGatewayChange('mada', 'enabled', checked)}
              />
            </div>
            
            {settings.paymentGateways.mada.enabled && (
              <div className="space-y-3 pl-4 border-l-2 border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Merchant ID</Label>
                    <Input
                      value={settings.paymentGateways.mada.merchantId}
                      onChange={(e) => handleGatewayChange('mada', 'merchantId', e.target.value)}
                      placeholder="Merchant ID"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Terminal ID</Label>
                    <Input
                      value={settings.paymentGateways.mada.terminalId}
                      onChange={(e) => handleGatewayChange('mada', 'terminalId', e.target.value)}
                      placeholder="Terminal ID"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* إعدادات العملة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            إعدادات العملة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>العملة الأساسية</Label>
              <select
                value={settings.currency.primary}
                onChange={(e) => handleGeneralChange('currency', { ...settings.currency, primary: e.target.value })}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="SAR">ريال سعودي (SAR)</option>
                <option value="USD">دولار أمريكي (USD)</option>
                <option value="EUR">يورو (EUR)</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label>رمز العملة</Label>
              <Input
                value={settings.currency.symbol}
                onChange={(e) => handleGeneralChange('currency', { ...settings.currency, symbol: e.target.value })}
                placeholder="ر.س"
              />
            </div>

            <div className="space-y-2">
              <Label>موضع الرمز</Label>
              <select
                value={settings.currency.position}
                onChange={(e) => handleGeneralChange('currency', { ...settings.currency, position: e.target.value })}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="before">قبل المبلغ</option>
                <option value="after">بعد المبلغ</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* حدود الطلبات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Banknote className="h-5 w-5" />
            حدود الطلبات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الحد الأدنى للطلب (ر.س)</Label>
              <Input
                type="number"
                value={settings.minimumOrderAmount}
                onChange={(e) => handleGeneralChange('minimumOrderAmount', parseFloat(e.target.value))}
                min="0"
                step="0.01"
              />
            </div>

            <div className="space-y-2">
              <Label>الحد الأقصى للطلب (ر.س)</Label>
              <Input
                type="number"
                value={settings.maximumOrderAmount}
                onChange={(e) => handleGeneralChange('maximumOrderAmount', parseFloat(e.target.value))}
                min="0"
                step="0.01"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
