describe('تتبع أخطاء Next.js في تدفق إنشاء الحساب', () => {
  beforeEach(() => {
    // مراقبة أخطاء الكونسول
    cy.window().then((win) => {
      cy.stub(win.console, 'error').as('consoleError')
      cy.stub(win.console, 'warn').as('consoleWarn')
    })
    
    // مراقبة أخطاء الشبكة
    cy.intercept('**', (req) => {
      req.continue((res) => {
        if (res.statusCode >= 400) {
          cy.log(`❌ خطأ شبكة: ${res.statusCode} - ${req.url}`)
        }
      })
    })
  })

  it('التدفق الكامل مع مراقبة الأخطاء: الصفحة الرئيسية → إنشاء حساب → اختيار تاجر → تبديل اللغة', () => {
    // الخطوة 1: زيارة الصفحة الرئيسية
    cy.log('🏠 زيارة الصفحة الرئيسية')
    cy.visit('http://localhost:9002')
    cy.wait(3000)
    
    // فحص الأخطاء في الكونسول
    cy.get('@consoleError').should('not.have.been.called')
    
    // الخطوة 2: البحث عن رابط إنشاء حساب
    cy.log('🔍 البحث عن رابط إنشاء حساب')
    cy.get('body').then(($body) => {
      const signupLinks = [
        'a:contains("إنشاء حساب")',
        'button:contains("إنشاء حساب")', 
        'a[href*="signup"]',
        'a:contains("تسجيل")',
        'a:contains("Sign up")',
        '[data-testid="signup-link"]'
      ]
      
      let found = false
      for (const selector of signupLinks) {
        if ($body.find(selector).length > 0) {
          cy.log(`✅ وجدت رابط إنشاء حساب: ${selector}`)
          cy.get(selector).first().click()
          found = true
          break
        }
      }
      
      if (!found) {
        cy.log('❌ لم أجد رابط إنشاء حساب، سأنتقل مباشرة')
        cy.visit('http://localhost:9002/ar/user-type-selection')
      }
    })
    
    cy.wait(3000)
    
    // فحص الأخطاء بعد النقر
    cy.get('@consoleError').should('not.have.been.called')
    
    // الخطوة 3: التأكد من وصولنا للصفحة الصحيحة
    cy.url().then((url) => {
      cy.log(`📍 الرابط الحالي: ${url}`)
      
      if (url.includes('user-type-selection')) {
        cy.log('✅ وصلنا لصفحة اختيار نوع المستخدم')
      } else if (url.includes('signup')) {
        cy.log('✅ وصلنا لصفحة التسجيل')
      } else {
        cy.log('⚠️ وصلنا لصفحة غير متوقعة')
        // الانتقال لصفحة اختيار نوع المستخدم
        cy.visit('http://localhost:9002/ar/user-type-selection')
        cy.wait(3000)
      }
    })
    
    // الخطوة 4: اختيار نوع التاجر
    cy.log('👨‍💼 اختيار نوع التاجر')
    cy.get('body').then(($body) => {
      const merchantSelectors = [
        'button:contains("تاجر")',
        'input[value="merchant"]',
        '[data-testid*="merchant"]',
        '[data-cy*="merchant"]',
        'label:contains("تاجر")',
        '.merchant-option'
      ]
      
      let found = false
      for (const selector of merchantSelectors) {
        if ($body.find(selector).length > 0) {
          cy.log(`✅ وجدت خيار التاجر: ${selector}`)
          cy.get(selector).first().click()
          found = true
          break
        }
      }
      
      if (!found) {
        cy.log('❌ لم أجد خيار التاجر')
      }
    })
    
    cy.wait(2000)
    
    // فحص الأخطاء بعد اختيار التاجر
    cy.get('@consoleError').should('not.have.been.called')
    
    // الخطوة 5: تبديل اللغة إلى الإنجليزية
    cy.log('🌐 تبديل اللغة إلى الإنجليزية')
    cy.get('body').then(($body) => {
      const languageSelectors = [
        'button:contains("EN")',
        '[data-testid="language-switcher"]',
        'button[aria-label*="language"]',
        'button[aria-label*="English"]',
        '.language-switcher',
        'button:contains("English")'
      ]
      
      let found = false
      for (const selector of languageSelectors) {
        if ($body.find(selector).length > 0) {
          cy.log(`✅ وجدت زر تبديل اللغة: ${selector}`)
          cy.get(selector).first().click()
          found = true
          break
        }
      }
      
      if (!found) {
        cy.log('❌ لم أجد زر تبديل اللغة')
      }
    })
    
    // مراقبة التغييرات أثناء تبديل اللغة
    cy.wait(1000)
    cy.log('⏱️ ثانية واحدة بعد تبديل اللغة')
    
    cy.wait(2000) 
    cy.log('⏱️ 3 ثوانٍ بعد تبديل اللغة')
    
    cy.wait(3000)
    cy.log('⏱️ 6 ثوانٍ بعد تبديل اللغة')
    
    // فحص الأخطاء بعد تبديل اللغة
    cy.get('@consoleError').then((stub) => {
      if (stub.callCount > 0) {
        cy.log(`❌ تم العثور على ${stub.callCount} أخطاء في الكونسول`)
        stub.getCalls().forEach((call, index) => {
          cy.log(`خطأ ${index + 1}: ${call.args.join(' ')}`)
        })
      } else {
        cy.log('✅ لا توجد أخطاء في الكونسول')
      }
    })
    
    // التحقق من تغيير اللغة
    cy.url().then((url) => {
      if (url.includes('/en/')) {
        cy.log('✅ تم تبديل اللغة بنجاح إلى الإنجليزية')
      } else {
        cy.log('❌ لم يتم تبديل اللغة')
      }
    })
  })

  it('اختبار مبسط لتبديل اللغة في صفحة اختيار نوع المستخدم', () => {
    // الانتقال مباشرة لصفحة اختيار نوع المستخدم
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(5000)
    
    // اختيار تاجر
    cy.contains('تاجر').click()
    cy.wait(1000)
    
    // تبديل اللغة
    cy.get('button:contains("EN")').click()
    
    // مراقبة الأخطاء
    cy.get('@consoleError').then((stub) => {
      if (stub.callCount > 0) {
        cy.log('❌ أخطاء موجودة:')
        stub.getCalls().forEach((call) => {
          cy.log(call.args.join(' '))
        })
      }
    })
    
    cy.wait(5000)
    cy.url().should('include', '/en/')
  })
})
