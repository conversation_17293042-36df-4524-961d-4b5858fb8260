// src/app/[locale]/admin/ai-dashboard/page.tsx
"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { collection, query, where, getDocs, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { AISystemDashboard } from '@/components/admin/AISystemDashboard';
import { DocumentAnalysisViewer } from '@/components/admin/DocumentAnalysisViewer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  BarChart3, 
  FileText, 
  Settings, 
  TrendingUp,
  Users,
  Truck,
  Shield,
  AlertTriangle
} from 'lucide-react';

export default function AIDashboardPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recentAnalyses, setRecentAnalyses] = useState([]);

  useEffect(() => {
    const checkAdminAccess = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // التحقق من صلاحيات المدير
        const userDocSnap = await getDocs(query(
          collection(db, 'users'),
          where('uid', '==', user.uid),
          where('userType', '==', 'admin')
        ));

        if (userDocSnap.empty) {
          console.log('User is not admin, redirecting...');
          router.push('/');
          return;
        }

        // جلب البيانات
        await fetchDashboardData();
      } catch (error) {
        console.error('Error checking admin access:', error);
        setError('خطأ في التحقق من الصلاحيات');
        router.push('/');
      }
    };

    checkAdminAccess();
  }, [user, router]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // جلب التحليلات الحديثة (محاكاة)
      // في التطبيق الحقيقي، ستجلب من قاعدة البيانات
      const mockRecentAnalyses = [
        {
          documentType: 'commercial_registration',
          extractedData: {
            businessName: 'متجر الأحلام للتجارة',
            ownerName: 'أحمد محمد السعيد',
            registrationNumber: 'CR-**********'
          },
          confidence: 96.2,
          isValid: true,
          issues: [],
          ocrText: 'نص مستخرج من السجل التجاري...',
          processingTime: 28.5,
          timestamp: new Date()
        },
        {
          documentType: 'driving_license',
          extractedData: {
            ownerName: 'محمد أحمد السعيد',
            documentNumber: 'DL-123456789',
            licenseClass: 'خاص'
          },
          confidence: 94.8,
          isValid: true,
          issues: [],
          ocrText: 'نص مستخرج من رخصة القيادة...',
          processingTime: 31.2,
          timestamp: new Date()
        }
      ];

      setRecentAnalyses(mockRecentAnalyses);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('فشل في تحميل بيانات لوحة المراقبة');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <Skeleton className="h-12 w-64" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <Skeleton className="h-8 w-8 mb-2" />
                  <Skeleton className="h-6 w-16 mb-1" />
                  <Skeleton className="h-4 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* العنوان الرئيسي */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-3">
          <Brain className="h-8 w-8 text-purple-500" />
          لوحة مراقبة النظام الذكي
        </h1>
        <p className="text-muted-foreground">
          مراقبة وإدارة أداء نظام الذكاء الاصطناعي للموافقة التلقائية
        </p>
      </div>

      {/* الإحصائيات السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-blue-600">156</div>
            <p className="text-sm text-muted-foreground">طلبات معالجة اليوم</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
            <div className="text-2xl font-bold text-green-600">96.2%</div>
            <p className="text-sm text-muted-foreground">دقة النظام</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Shield className="h-8 w-8 text-purple-500" />
            </div>
            <div className="text-2xl font-bold text-purple-600">78%</div>
            <p className="text-sm text-muted-foreground">موافقة تلقائية</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <FileText className="h-8 w-8 text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-orange-600">30s</div>
            <p className="text-sm text-muted-foreground">متوسط وقت المعالجة</p>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="merchants" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            التجار
          </TabsTrigger>
          <TabsTrigger value="representatives" className="flex items-center gap-2">
            <Truck className="h-4 w-4" />
            المندوبين
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            تحليل المستندات
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <AISystemDashboard />
        </TabsContent>

        <TabsContent value="merchants">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                إحصائيات التجار
              </CardTitle>
              <CardDescription>
                أداء النظام الذكي في معالجة طلبات التجار
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AISystemDashboard className="mt-0" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="representatives">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                إحصائيات المندوبين
              </CardTitle>
              <CardDescription>
                أداء النظام الذكي في معالجة طلبات المندوبين
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AISystemDashboard className="mt-0" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                تحليل المستندات الحديثة
              </CardTitle>
              <CardDescription>
                عرض نتائج تحليل المستندات بواسطة الذكاء الاصطناعي
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DocumentAnalysisViewer 
                analyses={recentAnalyses}
                entityType="merchant"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* أزرار الإجراءات السريعة */}
      <div className="mt-8 flex flex-wrap gap-4 justify-center">
        <Button 
          onClick={() => router.push('/admin/merchant-approvals')}
          className="flex items-center gap-2"
        >
          <Users className="h-4 w-4" />
          موافقات التجار
        </Button>
        
        <Button 
          onClick={() => router.push('/admin/representative-approvals')}
          className="flex items-center gap-2"
        >
          <Truck className="h-4 w-4" />
          موافقات المندوبين
        </Button>
        
        <Button 
          variant="outline"
          onClick={() => fetchDashboardData()}
          className="flex items-center gap-2"
        >
          <TrendingUp className="h-4 w-4" />
          تحديث البيانات
        </Button>
        
        <Button 
          variant="outline"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          إعدادات النظام
        </Button>
      </div>
    </div>
  );
}
