"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MapPin, Star, Clock, Phone, Globe, Users } from "lucide-react";
import Link from "next/link";
import { useLocale } from "@/hooks/use-locale";
import type { StoreWithDistance } from "@/types";

interface StoreCardProps {
  store: StoreWithDistance;
  showDistance?: boolean;
  variant?: "default" | "compact" | "featured";
  className?: string;
}

export default function StoreCard({ 
  store, 
  showDistance = false, 
  variant = "default",
  className = "" 
}: StoreCardProps) {
  const { t, locale } = useLocale();

  const getStoreInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDistance = (distance?: number) => {
    if (!distance) return '';
    if (distance < 1) {
      return `${Math.round(distance * 1000)} م`;
    }
    return `${distance.toFixed(1)} كم`;
  };

  const getStoreStatus = () => {
    if (store.isOpen === undefined) return null;
    return store.isOpen ? (
      <Badge variant="default" className="bg-green-500 hover:bg-green-600">
        <Clock className="w-3 h-3 mr-1" />
        {t('openNow')}
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-700">
        <Clock className="w-3 h-3 mr-1" />
        {t('closedNow')}
      </Badge>
    );
  };

  if (variant === "compact") {
    return (
      <Card className={`hover:shadow-lg transition-all duration-300 cursor-pointer ${className}`}>
        <Link href={`/${locale}/stores/${store.merchantUid}`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Avatar className="h-12 w-12">
                <AvatarImage src={store.logoUrl} alt={store.storeName} />
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {getStoreInitials(store.storeName)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-sm truncate">{store.storeName}</h3>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-muted-foreground">
                  {store.stats?.averageRating && (
                    <div className="flex items-center">
                      <Star className="w-3 h-3 fill-yellow-400 text-yellow-400 mr-1" />
                      <span>{store.stats.averageRating.toFixed(1)}</span>
                    </div>
                  )}
                  {showDistance && store.distance && (
                    <div className="flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      <span>{formatDistance(store.distance)}</span>
                    </div>
                  )}
                </div>
              </div>
              {getStoreStatus()}
            </div>
          </CardContent>
        </Link>
      </Card>
    );
  }

  if (variant === "featured") {
    return (
      <Card className={`hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${className}`}>
        <Link href={`/${locale}/stores/${store.merchantUid}`}>
          {store.bannerUrl && (
            <div className="relative h-32 overflow-hidden rounded-t-lg">
              <img 
                src={store.bannerUrl} 
                alt={store.storeName}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-2 right-2">
                {getStoreStatus()}
              </div>
            </div>
          )}
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Avatar className="h-16 w-16">
                <AvatarImage src={store.logoUrl} alt={store.storeName} />
                <AvatarFallback className="bg-primary text-primary-foreground text-lg">
                  {getStoreInitials(store.storeName)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="font-bold text-lg">{store.storeName}</h3>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {store.storeDescription}
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-muted-foreground">
                {store.stats?.averageRating && (
                  <div className="flex items-center">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="font-medium">{store.stats.averageRating.toFixed(1)}</span>
                    <span className="ml-1">({store.stats.reviewCount || 0})</span>
                  </div>
                )}
                {showDistance && store.distance && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{formatDistance(store.distance)}</span>
                  </div>
                )}
              </div>
            </div>
            
            {store.categories && store.categories.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {store.categories.slice(0, 3).map((category, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {category}
                  </Badge>
                ))}
                {store.categories.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{store.categories.length - 3}
                  </Badge>
                )}
              </div>
            )}

            <Button className="w-full" size="sm">
              {t('visitStore')}
            </Button>
          </CardContent>
        </Link>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className={`hover:shadow-lg transition-all duration-300 cursor-pointer ${className}`}>
      <Link href={`/${locale}/stores/${store.merchantUid}`}>
        <CardHeader className="pb-3">
          <div className="flex items-start space-x-3 rtl:space-x-reverse">
            <Avatar className="h-14 w-14">
              <AvatarImage src={store.logoUrl} alt={store.storeName} />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {getStoreInitials(store.storeName)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className="font-semibold text-lg truncate">{store.storeName}</h3>
                {getStoreStatus()}
              </div>
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                {store.storeDescription}
              </p>
              <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-muted-foreground">
                {store.stats?.averageRating && (
                  <div className="flex items-center">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="font-medium">{store.stats.averageRating.toFixed(1)}</span>
                    <span className="ml-1">({store.stats.reviewCount || 0})</span>
                  </div>
                )}
                {showDistance && store.distance && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{formatDistance(store.distance)}</span>
                  </div>
                )}
                {store.address?.city && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{store.address.city}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {store.categories && store.categories.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {store.categories.slice(0, 4).map((category, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {category}
                </Badge>
              ))}
              {store.categories.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{store.categories.length - 4}
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 rtl:space-x-reverse text-xs text-muted-foreground">
              {store.phoneNumber && (
                <div className="flex items-center">
                  <Phone className="w-3 h-3 mr-1" />
                  <span>{t('contactAvailable')}</span>
                </div>
              )}
              {store.website && (
                <div className="flex items-center">
                  <Globe className="w-3 h-3 mr-1" />
                  <span>{t('websiteAvailable')}</span>
                </div>
              )}
            </div>
            <Button variant="outline" size="sm">
              {t('viewStore')}
            </Button>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}
