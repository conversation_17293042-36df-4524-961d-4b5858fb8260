/// <reference types="cypress" />

describe('اختبار أداء الترجمات', () => {
  
  describe('اختبار سرعة تحميل الترجمات', () => {
    it('يجب أن تحمل الترجمات بسرعة مقبولة', () => {
      const startTime = Date.now();
      
      cy.visit('/ar').then(() => {
        const loadTime = Date.now() - startTime;
        expect(loadTime).to.be.lessThan(3000); // أقل من 3 ثوانٍ
      });
      
      // التحقق من ظهور المحتوى المترجم
      cy.contains('مرحباً بك').should('be.visible');
    });

    it('يجب أن يكون تبديل اللغة سريعاً', () => {
      cy.visit('/ar');
      
      const startTime = Date.now();
      
      // النقر على زر تبديل اللغة
      cy.get('[aria-label*="switch"], [aria-label*="تبديل"], button').contains(/EN|ع/).click();
      
      // التحقق من تغيير اللغة بسرعة
      cy.url().should('include', '/en').then(() => {
        const switchTime = Date.now() - startTime;
        expect(switchTime).to.be.lessThan(2000); // أقل من ثانيتين
      });
      
      cy.contains('Welcome').should('be.visible');
    });
  });

  describe('اختبار استقرار الترجمات', () => {
    it('يجب أن تبقى الترجمات مستقرة عند التنقل', () => {
      const pages = [
        '/ar',
        '/ar/search', 
        '/ar/categories',
        '/ar/products',
        '/ar/stores'
      ];
      
      pages.forEach(page => {
        cy.visit(page);
        
        // التحقق من وجود محتوى مترجم
        cy.get('body').should('not.be.empty');
        cy.get('html').should('have.attr', 'lang', 'ar');
        
        // التحقق من عدم وجود نصوص إنجليزية غير مترجمة
        cy.get('body').should('not.contain.text', 'undefined');
        cy.get('body').should('not.contain.text', 'null');
      });
    });

    it('يجب أن تعمل الترجمات مع إعادة التحميل', () => {
      cy.visit('/ar/search');
      cy.contains('البحث').should('be.visible');
      
      // إعادة تحميل الصفحة
      cy.reload();
      
      // التحقق من بقاء الترجمات
      cy.contains('البحث').should('be.visible');
      cy.get('html').should('have.attr', 'lang', 'ar');
    });
  });

  describe('اختبار الترجمات مع البيانات الديناميكية', () => {
    it('يجب أن تعمل الترجمات مع نتائج البحث', () => {
      cy.visit('/ar/search');
      
      // إجراء بحث
      cy.get('input[placeholder*="ابحث"]').type('منتج');
      cy.get('button').contains('البحث').click();
      
      // التحقق من ترجمة النتائج الديناميكية
      cy.get('body').should('contain.text', 'نتائج')
        .or('contain.text', 'منتج')
        .or('contain.text', 'لا توجد');
    });

    it('يجب أن تعمل الترجمات مع الفلاتر', () => {
      cy.visit('/ar/products');
      
      // اختبار الفلاتر
      cy.get('select, [role="combobox"]').first().select(0);
      
      // التحقق من ترجمة النتائج المفلترة
      cy.get('body').should('contain.text', 'منتج')
        .or('contain.text', 'فئة')
        .or('contain.text', 'نتيجة');
    });
  });

  describe('اختبار الترجمات مع الأخطاء', () => {
    it('يجب أن تظهر رسائل الخطأ مترجمة', () => {
      // محاولة الوصول لصفحة محمية
      cy.visit('/ar/merchant/dashboard', { failOnStatusCode: false });
      
      // التحقق من ترجمة رسائل الخطأ
      cy.get('body').should('contain.text', 'خطأ')
        .or('contain.text', 'تسجيل')
        .or('contain.text', 'مصادقة');
    });

    it('يجب أن تعمل الترجمات مع أخطاء الشبكة', () => {
      // محاكاة خطأ في الشبكة
      cy.intercept('GET', '/api/**', { forceNetworkError: true });
      
      cy.visit('/ar/products');
      
      // التحقق من رسائل خطأ الشبكة
      cy.get('body').should('contain.text', 'خطأ')
        .or('contain.text', 'تحميل')
        .or('contain.text', 'اتصال');
    });
  });

  describe('اختبار الترجمات على أجهزة مختلفة', () => {
    it('يجب أن تعمل الترجمات على الهاتف المحمول', () => {
      cy.viewport('iphone-x');
      cy.visit('/ar');
      
      // التحقق من ظهور الترجمات على الهاتف
      cy.contains('مرحباً').should('be.visible');
      cy.get('html').should('have.attr', 'lang', 'ar');
    });

    it('يجب أن تعمل الترجمات على التابلت', () => {
      cy.viewport('ipad-2');
      cy.visit('/ar/search');
      
      // التحقق من ظهور الترجمات على التابلت
      cy.contains('البحث').should('be.visible');
      cy.get('input[placeholder*="ابحث"]').should('be.visible');
    });
  });

  describe('اختبار ذاكرة التخزين المؤقت للترجمات', () => {
    it('يجب أن تحفظ الترجمات في الذاكرة المؤقتة', () => {
      cy.visit('/ar');
      
      // التنقل لصفحة أخرى والعودة
      cy.visit('/ar/search');
      cy.go('back');
      
      // التحقق من سرعة التحميل من الذاكرة المؤقتة
      cy.contains('مرحباً').should('be.visible');
    });
  });

  describe('اختبار الترجمات مع المحتوى الكثيف', () => {
    it('يجب أن تعمل الترجمات مع قوائم طويلة', () => {
      cy.visit('/ar/products');
      
      // التمرير لأسفل لتحميل المزيد من المحتوى
      cy.scrollTo('bottom');
      
      // التحقق من ترجمة المحتوى المحمل ديناميكياً
      cy.get('body').should('contain.text', 'منتج')
        .or('contain.text', 'تحميل')
        .or('contain.text', 'المزيد');
    });
  });

  describe('اختبار الترجمات مع التفاعل المكثف', () => {
    it('يجب أن تبقى الترجمات مستقرة مع التفاعل المكثف', () => {
      cy.visit('/ar/search');
      
      // تفاعل مكثف مع الصفحة
      for (let i = 0; i < 5; i++) {
        cy.get('input[placeholder*="ابحث"]').clear().type(`بحث ${i}`);
        cy.get('button').contains('البحث').click();
        cy.wait(500);
      }
      
      // التحقق من استقرار الترجمات
      cy.contains('البحث').should('be.visible');
      cy.get('html').should('have.attr', 'lang', 'ar');
    });
  });
});
