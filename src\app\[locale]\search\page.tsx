"use client";

import { useState, useEffect, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Filter, 
  SlidersHorizontal, 
  Grid3X3, 
  List, 
  MapPin,
  Star,
  Package,
  Store,
  AlertCircle,
  X
} from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import ProductCard from "@/components/common/ProductCard";
import StoreCard from "@/components/customer/StoreCard";
import FilterSidebar from "@/components/customer/FilterSidebar";
import type { ProductDocument, StoreDocument, SearchFilter } from "@/types";
import { collection, query, where, getDocs, orderBy, limit } from "firebase/firestore";
import { db, checkFirebaseConnection } from "@/lib/firebase";

interface SearchResults {
  products: ProductDocument[];
  stores: StoreDocument[];
  totalProducts: number;
  totalStores: number;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const { t } = useLocale();
  
  const [searchQuery, setSearchQuery] = useState(searchParams?.get('q') || '');
  const [searchResults, setSearchResults] = useState<SearchResults>({
    products: [],
    stores: [],
    totalProducts: 0,
    totalStores: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'products' | 'stores'>('products');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('relevance');
  const [filters, setFilters] = useState<SearchFilter>({
    categories: [],
    priceRange: [0, 1000],
    rating: 0,
    location: '',
    inStock: false
  });
  const [showFilters, setShowFilters] = useState(false);

  // Search function
  const performSearch = async (searchQuery: string, searchFilters: SearchFilter) => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      // Check Firebase connection first
      const connectionStatus = await checkFirebaseConnection();
      if (!connectionStatus.connected) {
        throw new Error(connectionStatus.isOffline ? 'offline' : connectionStatus.error);
      }
      // Search products
      const productsRef = collection(db, 'products');
      let productsQuery = query(
        productsRef,
        where('isActive', '==', true)
      );

      // Apply filters
      if (searchFilters.categories.length > 0) {
        productsQuery = query(
          productsRef,
          where('category', 'in', searchFilters.categories),
          where('isActive', '==', true)
        );
      }

      if (searchFilters.inStock) {
        productsQuery = query(
          productsRef,
          where('stock', '>', 0),
          where('isActive', '==', true)
        );
      }

      // Add sorting
      if (sortBy === 'price_low') {
        productsQuery = query(productsQuery, orderBy('price', 'asc'));
      } else if (sortBy === 'price_high') {
        productsQuery = query(productsQuery, orderBy('price', 'desc'));
      } else if (sortBy === 'rating') {
        productsQuery = query(productsQuery, orderBy('averageRating', 'desc'));
      } else if (sortBy === 'newest') {
        productsQuery = query(productsQuery, orderBy('createdAt', 'desc'));
      }

      const productsSnapshot = await getDocs(productsQuery);
      const productsData: ProductDocument[] = [];

      productsSnapshot.forEach((doc) => {
        const product = { ...doc.data(), id: doc.id } as ProductDocument;

        // Text search in product name and description
        const searchText = searchQuery.toLowerCase();
        const productText = `${product.name} ${product.description}`.toLowerCase();

        if (productText.includes(searchText)) {
          // Apply price filter
          if (product.price >= searchFilters.priceRange[0] &&
              product.price <= searchFilters.priceRange[1]) {
            productsData.push(product);
          }
        }
      });

      // Search stores
      const storesRef = collection(db, 'stores');
      const storesQuery = query(
        storesRef,
        where('isActive', '==', true),
        where('approvalStatus', '==', 'approved')
      );

      const storesSnapshot = await getDocs(storesQuery);
      const storesData: StoreDocument[] = [];

      storesSnapshot.forEach((doc) => {
        const store = { ...doc.data(), id: doc.id } as StoreDocument;

        // Text search in store name and description
        const searchText = searchQuery.toLowerCase();
        const storeText = `${store.storeName} ${store.storeDescription}`.toLowerCase();

        if (storeText.includes(searchText)) {
          // Apply location filter
          if (!searchFilters.location ||
              store.address?.city?.toLowerCase().includes(searchFilters.location.toLowerCase())) {
            storesData.push(store);
          }
        }
      });

      setSearchResults({
        products: productsData,
        stores: storesData,
        totalProducts: productsData.length,
        totalStores: storesData.length
      });

    } catch (err: any) {
      console.error('Search error:', err);

      // Provide more specific error messages
      let errorMessage = t('searchError');
      if (err?.code === 'unavailable') {
        errorMessage = 'خدمة البحث غير متاحة حالياً. يرجى المحاولة لاحقاً.';
      } else if (err?.code === 'permission-denied') {
        errorMessage = 'ليس لديك صلاحية للوصول إلى البيانات.';
      } else if (err?.message?.includes('offline')) {
        errorMessage = 'لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال.';
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect for initial search
  useEffect(() => {
    const initialQuery = searchParams?.get('q');
    if (initialQuery) {
      setSearchQuery(initialQuery);
      performSearch(initialQuery, filters);
    }
  }, [searchParams?.get('q')]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      performSearch(searchQuery, filters);
    }
  };

  // Handle filter change
  const handleFilterChange = (newFilters: SearchFilter) => {
    setFilters(newFilters);
    if (searchQuery.trim()) {
      performSearch(searchQuery, newFilters);
    }
  };

  // Clear filters
  const clearFilters = () => {
    const defaultFilters: SearchFilter = {
      categories: [],
      priceRange: [0, 1000],
      rating: 0,
      location: '',
      inStock: false
    };
    setFilters(defaultFilters);
    if (searchQuery.trim()) {
      performSearch(searchQuery, defaultFilters);
    }
  };

  // Get active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.categories.length > 0) count++;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 1000) count++;
    if (filters.rating > 0) count++;
    if (filters.location) count++;
    if (filters.inStock) count++;
    return count;
  }, [filters]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Search Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">{t('advancedSearch')}</h1>
        
        {/* Search Form */}
        <form onSubmit={handleSearch} className="flex gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              type="text"
              placeholder={t('searchProductsAndStores')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? t('searching') : t('search')}
          </Button>
        </form>

        {/* Search Stats and Controls */}
        {searchQuery && (
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="text-sm text-muted-foreground">
              {t('searchResultsFor')} "<strong>{searchQuery}</strong>" - 
              {' '}{searchResults.totalProducts} {t('products')}, {searchResults.totalStores} {t('stores')}
            </div>
            
            <div className="flex items-center gap-4">
              {/* Filters Toggle */}
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="relative"
              >
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                {t('filters')}
                {activeFiltersCount > 0 && (
                  <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>

              {/* Clear Filters */}
              {activeFiltersCount > 0 && (
                <Button variant="ghost" onClick={clearFilters} size="sm">
                  <X className="w-4 h-4 mr-1" />
                  {t('clearFilters')}
                </Button>
              )}

              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">{t('relevance')}</SelectItem>
                  <SelectItem value="newest">{t('newest')}</SelectItem>
                  <SelectItem value="price_low">{t('priceLowToHigh')}</SelectItem>
                  <SelectItem value="price_high">{t('priceHighToLow')}</SelectItem>
                  <SelectItem value="rating">{t('highestRated')}</SelectItem>
                </SelectContent>
              </Select>

              {/* View Mode */}
              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex gap-8">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="w-80 flex-shrink-0">
            <FilterSidebar
              filters={filters}
              onFiltersChange={handleFilterChange}
              className="sticky top-4"
            />
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1">
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {!searchQuery ? (
            // Empty state
            <Card>
              <CardContent className="text-center py-16">
                <Search className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">{t('startSearching')}</h3>
                <p className="text-muted-foreground">
                  {t('enterSearchTermToFindProducts')}
                </p>
              </CardContent>
            </Card>
          ) : isLoading ? (
            // Loading state
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i}>
                    <Skeleton className="h-40 w-full" />
                    <CardContent className="p-4 space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                      <Skeleton className="h-8 w-full" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : searchResults.totalProducts === 0 && searchResults.totalStores === 0 ? (
            // No results
            <Card>
              <CardContent className="text-center py-16">
                <Package className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">{t('noSearchResults')}</h3>
                <p className="text-muted-foreground mb-4">
                  {t('tryDifferentSearchTerms')}
                </p>
                <Button variant="outline" onClick={clearFilters}>
                  {t('clearFiltersAndTryAgain')}
                </Button>
              </CardContent>
            </Card>
          ) : (
            // Results
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'products' | 'stores')}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="products">
                  <Package className="w-4 h-4 mr-2" />
                  {t('products')} ({searchResults.totalProducts})
                </TabsTrigger>
                <TabsTrigger value="stores">
                  <Store className="w-4 h-4 mr-2" />
                  {t('stores')} ({searchResults.totalStores})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="products">
                {searchResults.products.length === 0 ? (
                  <Card>
                    <CardContent className="text-center py-12">
                      <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">{t('noProductsFound')}</h3>
                      <p className="text-muted-foreground">
                        {t('tryAdjustingFilters')}
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className={`grid gap-6 ${
                    viewMode === 'grid'
                      ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                      : 'grid-cols-1'
                  }`}>
                    {searchResults.products.map((product) => (
                      <ProductCard
                        key={product.id}
                        product={product}
                        variant={viewMode === 'list' ? 'compact' : 'default'}
                        showStore={true}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="stores">
                {searchResults.stores.length === 0 ? (
                  <Card>
                    <CardContent className="text-center py-12">
                      <Store className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">{t('noStoresFound')}</h3>
                      <p className="text-muted-foreground">
                        {t('tryAdjustingFilters')}
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className={`grid gap-6 ${
                    viewMode === 'grid'
                      ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                      : 'grid-cols-1'
                  }`}>
                    {searchResults.stores.map((store) => (
                      <StoreCard
                        key={store.id}
                        store={store}
                        variant={viewMode === 'list' ? 'compact' : 'default'}
                        showDistance={false}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </div>
  );
}
