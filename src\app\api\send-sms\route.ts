import { NextRequest, NextResponse } from 'next/server';

/**
 * API لإرسال الرسائل النصية
 * يدعم عدة مزودي خدمة الرسائل النصية
 */

interface SMSRequest {
  to: string | string[];
  message: string;
  from?: string;
  provider?: 'native' | 'webhook' | 'firebase';
  priority?: 'low' | 'normal' | 'high';
}

interface SMSResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  provider?: string;
  cost?: number;
}

export async function POST(request: NextRequest): Promise<NextResponse<SMSResponse>> {
  try {
    const body: SMSRequest = await request.json();
    
    // التحقق من صحة البيانات
    if (!body.to || !body.message) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة: to, message'
      }, { status: 400 });
    }

    // التحقق من صحة رقم الهاتف
    const phoneNumbers = Array.isArray(body.to) ? body.to : [body.to];
    for (const phone of phoneNumbers) {
      if (!isValidPhoneNumber(phone)) {
        return NextResponse.json({
          success: false,
          error: `رقم هاتف غير صحيح: ${phone}`
        }, { status: 400 });
      }
    }

    const provider = body.provider || 'native';
    
    console.log('📱 إرسال رسالة نصية:', {
      to: body.to,
      message: body.message.substring(0, 50) + '...',
      provider
    });

    let result: SMSResponse;

    switch (provider) {
      case 'native':
        result = await sendSMSNative(body);
        break;
      case 'webhook':
        result = await sendSMSWebhook(body);
        break;
      case 'firebase':
        result = await sendSMSFirebase(body);
        break;
      default:
        result = await sendSMSNative(body);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ خطأ في إرسال الرسالة النصية:', error);
    
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في إرسال الرسالة النصية'
    }, { status: 500 });
  }
}

/**
 * إرسال الرسالة باستخدام النظام المحلي
 */
async function sendSMSNative(smsData: SMSRequest): Promise<SMSResponse> {
  try {
    // محاكاة إرسال الرسالة النصية
    // في التطبيق الحقيقي، يمكن استخدام مزود محلي أو API
    
    const messageId = `sms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const phoneNumbers = Array.isArray(smsData.to) ? smsData.to : [smsData.to];
    
    // حساب التكلفة التقديرية
    const cost = calculateSMSCost(smsData.message, phoneNumbers.length);
    
    // تسجيل الرسالة في قاعدة البيانات للمتابعة
    await logSMSToDatabase({
      messageId,
      to: phoneNumbers,
      message: smsData.message,
      provider: 'native',
      status: 'sent',
      cost,
      sentAt: new Date()
    });

    console.log('✅ تم إرسال الرسالة النصية بنجاح:', messageId);

    return {
      success: true,
      messageId,
      provider: 'native',
      cost
    };

  } catch (error) {
    console.error('❌ خطأ في الإرسال المحلي:', error);
    throw error;
  }
}

/**
 * إرسال الرسالة باستخدام Webhook خارجي
 */
async function sendSMSWebhook(smsData: SMSRequest): Promise<SMSResponse> {
  try {
    // يمكن تكوين webhook URL من متغيرات البيئة
    const webhookUrl = process.env.SMS_WEBHOOK_URL;
    
    if (!webhookUrl) {
      throw new Error('SMS_WEBHOOK_URL غير مكون');
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SMS_WEBHOOK_TOKEN || ''}`
      },
      body: JSON.stringify({
        to: smsData.to,
        message: smsData.message,
        from: smsData.from || process.env.DEFAULT_SMS_FROM,
        priority: smsData.priority || 'normal'
      })
    });

    if (!response.ok) {
      throw new Error(`SMS Webhook failed: ${response.status}`);
    }

    const result = await response.json();
    
    return {
      success: true,
      messageId: result.messageId || `webhook_sms_${Date.now()}`,
      provider: 'webhook',
      cost: result.cost || 0
    };

  } catch (error) {
    console.error('❌ خطأ في SMS Webhook:', error);
    throw error;
  }
}

/**
 * إرسال الرسالة باستخدام Firebase Functions
 */
async function sendSMSFirebase(smsData: SMSRequest): Promise<SMSResponse> {
  try {
    // استخدام Firebase Functions لإرسال الرسالة
    const { db } = await import('@/lib/firebase');
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    // إضافة الرسالة إلى قائمة انتظار Firebase
    const smsQueueRef = collection(db, 'sms_queue');
    const docRef = await addDoc(smsQueueRef, {
      to: smsData.to,
      message: smsData.message,
      from: smsData.from,
      priority: smsData.priority || 'normal',
      status: 'pending',
      createdAt: serverTimestamp(),
      attempts: 0
    });

    console.log('✅ تم إضافة الرسالة لقائمة Firebase:', docRef.id);

    return {
      success: true,
      messageId: docRef.id,
      provider: 'firebase'
    };

  } catch (error) {
    console.error('❌ خطأ في Firebase SMS:', error);
    throw error;
  }
}

/**
 * التحقق من صحة رقم الهاتف
 */
function isValidPhoneNumber(phone: string): boolean {
  // تنظيف الرقم من المسافات والرموز
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // التحقق من الأرقام السعودية والدولية
  const saudiPattern = /^(\+966|966|0)?[5][0-9]{8}$/;
  const internationalPattern = /^\+?[1-9]\d{1,14}$/;
  
  return saudiPattern.test(cleanPhone) || internationalPattern.test(cleanPhone);
}

/**
 * حساب تكلفة الرسالة النصية
 */
function calculateSMSCost(message: string, recipientCount: number): number {
  // حساب عدد الرسائل بناءً على طول النص
  const messageLength = message.length;
  let messageCount = 1;
  
  if (messageLength > 160) {
    messageCount = Math.ceil(messageLength / 153); // 153 حرف للرسائل المتعددة
  }
  
  // تكلفة تقديرية: 0.05 ريال لكل رسالة
  const costPerMessage = 0.05;
  
  return messageCount * recipientCount * costPerMessage;
}

/**
 * تسجيل الرسالة في قاعدة البيانات
 */
async function logSMSToDatabase(smsLog: {
  messageId: string;
  to: string[];
  message: string;
  provider: string;
  status: string;
  cost: number;
  sentAt: Date;
}): Promise<void> {
  try {
    const { db } = await import('@/lib/firebase');
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    const smsLogsRef = collection(db, 'sms_logs');
    await addDoc(smsLogsRef, {
      ...smsLog,
      sentAt: serverTimestamp()
    });

  } catch (error) {
    console.error('❌ خطأ في تسجيل الرسالة:', error);
    // لا نرمي خطأ هنا لأن الإرسال نجح
  }
}

// دعم GET للاختبار
export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    service: 'SMS API',
    status: 'active',
    providers: ['native', 'webhook', 'firebase'],
    supportedCountries: ['SA', 'AE', 'KW', 'QA', 'BH', 'OM'],
    version: '1.0.0'
  });
}
