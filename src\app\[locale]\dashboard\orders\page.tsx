// src/app/[locale]/dashboard/orders/page.tsx
"use client";

import { useLocale } from '@/hooks/use-locale';
import { useAuth } from '@/context/AuthContext';
import { useOrders } from '@/hooks/useOrders';
import { Loader2, ShoppingBag, Clock, Package, ArrowLeft } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from 'next/link';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

export default function OrdersPage() {
  const { t, locale } = useLocale();
  const { user } = useAuth();
  const { orders, loading, error } = useOrders({
    customerId: user?.uid,
    realtime: true
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: t('orderStatusPending'), variant: 'outline' },
      confirmed: { label: t('orderStatusConfirmed'), variant: 'secondary' },
      preparing: { label: t('orderStatusPreparing'), variant: 'secondary' },
      ready: { label: t('orderStatusReady'), variant: 'default' },
      shipped: { label: t('orderStatusShipped'), variant: 'default' },
      delivered: { label: t('orderStatusDelivered'), variant: 'default' },
      cancelled: { label: t('orderStatusCancelled'), variant: 'destructive' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'outline' };

    return (
      <Badge variant={config.variant as any}>{config.label}</Badge>
    );
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp || !timestamp.toDate) return '';
    const date = timestamp.toDate();
    return format(date, 'PPP', {
      locale: locale === 'ar' ? ar : enUS
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">{t('loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/${locale}/dashboard`}>
              <ArrowLeft className="h-4 w-4 me-2" />
              {t('backToDashboard')}
            </Link>
          </Button>
        </div>
        <div className="flex items-center gap-3">
          <Package className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">{t('myOrders')}</h1>
            <p className="text-muted-foreground">{t('recentOrdersDescription')}</p>
          </div>
        </div>
      </div>

      {/* Orders Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            {t('allOrders')}
          </CardTitle>
          <CardDescription>
            {orders.length > 0 
              ? t('ordersFound', { count: orders.length })
              : t('noOrdersDescription')
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-16 w-16 mx-auto text-muted-foreground mb-6" />
              <h3 className="text-xl font-medium mb-2">{t('noOrdersYet')}</h3>
              <p className="text-muted-foreground mb-6">{t('startShoppingMessage')}</p>
              <Button asChild>
                <Link href={`/${locale}/products`}>
                  <ShoppingBag className="me-2 h-4 w-4" />
                  {t('browseProducts')}
                </Link>
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('orderNumber')}</TableHead>
                    <TableHead>{t('date')}</TableHead>
                    <TableHead>{t('items')}</TableHead>
                    <TableHead>{t('total')}</TableHead>
                    <TableHead>{t('status')}</TableHead>
                    <TableHead className="text-center">{t('actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">{order.orderNumber}</TableCell>
                      <TableCell>{formatDate(order.createdAt)}</TableCell>
                      <TableCell>{order.items.length}</TableCell>
                      <TableCell>{(order.finalTotal || order.totalAmount).toFixed(2)} {t('SAR')}</TableCell>
                      <TableCell>{getStatusBadge(order.status)}</TableCell>
                      <TableCell className="text-center">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/${locale}/dashboard/orders/${order.id}`}>
                            {t('viewDetails')}
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
