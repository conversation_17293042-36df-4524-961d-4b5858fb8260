// cypress/e2e/categories-colored-icons.cy.ts
describe('Categories Colored Icons', () => {
  beforeEach(() => {
    cy.visit('/ar/categories');
  });

  it('should display all category cards with colored icons', () => {
    // التحقق من وجود شبكة الفئات
    cy.get('[data-testid="categories-grid"]').should('be.visible');
    
    // التحقق من وجود بطاقة "جميع الفئات"
    cy.contains('جميع الفئات').should('be.visible');
    
    // التحقق من وجود جميع الفئات
    const categories = [
      'الطعام والمشروبات',
      'البقالة والمواد الغذائية',
      'الأزياء والملابس',
      'الإلكترونيات',
      'المنزل والحديقة',
      'الجمال والصحة',
      'الرياضة واللياقة',
      'السيارات',
      'الكتب والوسائط',
      'الفنون والحرف',
      'الحرف اليدوية والتراثية',
      'الألعاب والترفيه',
      'الحيوانات الأليفة',
      'الأطفال والرضع',
      'المجوهرات والإكسسوارات',
      'الخدمات المحلية',
      'النباتات والزراعة',
      'الأجهزة المنزلية',
      'الرياضات المائية والشاطئ',
      'العطور والبخور',
      'الأدوات والمعدات',
      'أخرى'
    ];

    categories.forEach(category => {
      cy.contains(category).should('be.visible');
    });
  });

  it('should display colored icons for each category', () => {
    // التحقق من وجود أيقونات SVG ملونة
    cy.get('.category-icon svg').should('have.length.at.least', 22);
    
    // التحقق من وجود gradients في الأيقونات
    cy.get('.category-icon svg defs linearGradient').should('exist');
    cy.get('.category-icon svg defs radialGradient').should('exist');
    
    // التحقق من أن الأيقونات تحتوي على ألوان
    cy.get('.category-icon svg path[fill*="url(#"]').should('exist');
    cy.get('.category-icon svg circle[fill*="url(#"]').should('exist');
  });

  it('should apply hover effects on category cards', () => {
    // التحقق من تطبيق فئة CSS للبطاقات
    cy.get('.category-card').should('exist');
    
    // التحقق من تأثير الهوفر
    cy.get('.category-card').first().trigger('mouseover');
    cy.get('.category-card').first().should('have.class', 'category-card');
  });

  it('should select category and show selected state', () => {
    // النقر على فئة الطعام
    cy.contains('الطعام والمشروبات').click();
    
    // التحقق من تحديث URL
    cy.url().should('include', 'category=food');
    
    // التحقق من عرض رأس الفئة المحددة
    cy.get('h2').contains('الطعام والمشروبات').should('be.visible');
    
    // التحقق من تطبيق حالة التحديد
    cy.get('.category-card.selected').should('exist');
  });

  it('should display category icon in selected category header', () => {
    // النقر على فئة الإلكترونيات
    cy.contains('الإلكترونيات').click();
    
    // التحقق من وجود أيقونة في رأس الفئة المحددة
    cy.get('h2').contains('الإلكترونيات').parent().parent()
      .find('.category-icon svg').should('be.visible');
  });

  it('should show products count for selected category', () => {
    // النقر على فئة
    cy.contains('الأزياء والملابس').click();
    
    // التحقق من عرض عدد المنتجات
    cy.contains('منتج').should('be.visible');
  });

  it('should return to all categories when clicking "جميع الفئات"', () => {
    // النقر على فئة معينة أولاً
    cy.contains('الجمال والصحة').click();
    cy.url().should('include', 'category=beauty');
    
    // النقر على "جميع الفئات"
    cy.contains('جميع الفئات').click();
    
    // التحقق من إزالة فلتر الفئة من URL
    cy.url().should('not.include', 'category=');
  });

  it('should have responsive design for category icons', () => {
    // اختبار على شاشة الهاتف المحمول
    cy.viewport(375, 667);
    
    // التحقق من أن الأيقونات لا تزال مرئية
    cy.get('.category-icon svg').should('be.visible');
    
    // التحقق من أن البطاقات تتكيف مع الشاشة الصغيرة
    cy.get('.category-card').should('be.visible');
  });

  it('should maintain accessibility for colored icons', () => {
    // التحقق من وجود نصوص بديلة
    cy.get('.category-card').each(($card) => {
      cy.wrap($card).find('h3').should('not.be.empty');
    });
    
    // التحقق من إمكانية التنقل بالكيبورد
    cy.get('.category-card').first().focus();
    cy.focused().should('have.class', 'category-card');
  });

  it('should load category icons without errors', () => {
    // التحقق من عدم وجود أخطاء في وحدة التحكم
    cy.window().then((win) => {
      cy.stub(win.console, 'error').as('consoleError');
    });
    
    // إعادة تحميل الصفحة
    cy.reload();
    
    // التحقق من عدم وجود أخطاء
    cy.get('@consoleError').should('not.have.been.called');
    
    // التحقق من تحميل جميع الأيقونات
    cy.get('.category-icon svg').should('have.length.at.least', 22);
  });
});
