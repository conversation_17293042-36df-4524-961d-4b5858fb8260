"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useLocale } from '@/hooks/use-locale';
import AdvancedNotificationService, { UserNotificationPreferences } from '@/services/advancedNotificationService';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Bell,
  Mail,
  MessageSquare,
  Smartphone,
  Clock,
  Settings,
  Save,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

interface NotificationPreferencesProps {
  className?: string;
}

export default function NotificationPreferences({ className }: NotificationPreferencesProps) {
  const { user } = useAuth();
  const { t } = useLocale();
  const [preferences, setPreferences] = useState<UserNotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // تحميل التفضيلات عند تحميل المكون
  useEffect(() => {
    if (user?.uid) {
      loadPreferences();
    }
  }, [user]);

  const loadPreferences = async () => {
    try {
      setLoading(true);
      // هنا يجب استدعاء دالة للحصول على التفضيلات من قاعدة البيانات
      // مؤقتاً سنستخدم تفضيلات افتراضية
      const defaultPrefs: UserNotificationPreferences = {
        userId: user?.uid || '',
        pushEnabled: true,
        emailEnabled: true,
        smsEnabled: false,
        marketingEnabled: true,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00'
        },
        frequency: 'immediate',
        categories: {
          orders: true,
          payments: true,
          marketing: true,
          system: true,
          security: true
        }
      };
      
      setPreferences(defaultPrefs);
    } catch (error) {
      console.error('خطأ في تحميل التفضيلات:', error);
      toast.error('فشل في تحميل تفضيلات الإشعارات');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    if (!preferences || !user?.uid) return;

    try {
      setSaving(true);
      await AdvancedNotificationService.updateUserNotificationPreferences(
        user.uid,
        preferences
      );
      toast.success('تم حفظ التفضيلات بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ التفضيلات:', error);
      toast.error('فشل في حفظ التفضيلات');
    } finally {
      setSaving(false);
    }
  };

  const updatePreference = (key: keyof UserNotificationPreferences, value: any) => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      [key]: value
    });
  };

  const updateQuietHours = (key: 'enabled' | 'startTime' | 'endTime', value: any) => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      quietHours: {
        ...preferences.quietHours,
        [key]: value
      }
    });
  };

  const updateCategory = (category: string, enabled: boolean) => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      categories: {
        ...preferences.categories,
        [category]: enabled
      }
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className="text-center p-8">
        <p className="text-muted-foreground">فشل في تحميل التفضيلات</p>
        <Button onClick={loadPreferences} className="mt-4">
          إعادة المحاولة
        </Button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* إعدادات القنوات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            قنوات الإشعارات
          </CardTitle>
          <CardDescription>
            اختر كيف تريد استلام الإشعارات
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Smartphone className="w-5 h-5 text-blue-500" />
              <div>
                <Label htmlFor="push-notifications">الإشعارات المباشرة</Label>
                <p className="text-sm text-muted-foreground">
                  إشعارات فورية على جهازك
                </p>
              </div>
            </div>
            <Switch
              id="push-notifications"
              checked={preferences.pushEnabled}
              onCheckedChange={(checked) => updatePreference('pushEnabled', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-green-500" />
              <div>
                <Label htmlFor="email-notifications">البريد الإلكتروني</Label>
                <p className="text-sm text-muted-foreground">
                  إشعارات عبر البريد الإلكتروني
                </p>
              </div>
            </div>
            <Switch
              id="email-notifications"
              checked={preferences.emailEnabled}
              onCheckedChange={(checked) => updatePreference('emailEnabled', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <MessageSquare className="w-5 h-5 text-orange-500" />
              <div>
                <Label htmlFor="sms-notifications">الرسائل النصية</Label>
                <p className="text-sm text-muted-foreground">
                  إشعارات عبر الرسائل النصية
                </p>
              </div>
            </div>
            <Switch
              id="sms-notifications"
              checked={preferences.smsEnabled}
              onCheckedChange={(checked) => updatePreference('smsEnabled', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* تكرار الإشعارات */}
      <Card>
        <CardHeader>
          <CardTitle>تكرار الإشعارات</CardTitle>
          <CardDescription>
            كم مرة تريد استلام الإشعارات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={preferences.frequency}
            onValueChange={(value) => updatePreference('frequency', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="immediate">فوري</SelectItem>
              <SelectItem value="hourly">كل ساعة</SelectItem>
              <SelectItem value="daily">يومي</SelectItem>
              <SelectItem value="weekly">أسبوعي</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* الساعات الهادئة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            الساعات الهادئة
          </CardTitle>
          <CardDescription>
            تعطيل الإشعارات خلال ساعات معينة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="quiet-hours">تفعيل الساعات الهادئة</Label>
            <Switch
              id="quiet-hours"
              checked={preferences.quietHours.enabled}
              onCheckedChange={(checked) => updateQuietHours('enabled', checked)}
            />
          </div>

          {preferences.quietHours.enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start-time">من الساعة</Label>
                <Input
                  id="start-time"
                  type="time"
                  value={preferences.quietHours.startTime}
                  onChange={(e) => updateQuietHours('startTime', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="end-time">إلى الساعة</Label>
                <Input
                  id="end-time"
                  type="time"
                  value={preferences.quietHours.endTime}
                  onChange={(e) => updateQuietHours('endTime', e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* فئات الإشعارات */}
      <Card>
        <CardHeader>
          <CardTitle>فئات الإشعارات</CardTitle>
          <CardDescription>
            اختر أنواع الإشعارات التي تريد استلامها
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(preferences.categories).map(([category, enabled]) => (
            <div key={category} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge variant={enabled ? "default" : "secondary"}>
                  {getCategoryLabel(category)}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {getCategoryDescription(category)}
                </span>
              </div>
              <Switch
                checked={enabled}
                onCheckedChange={(checked) => updateCategory(category, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* إعدادات التسويق */}
      <Card>
        <CardHeader>
          <CardTitle>الإشعارات التسويقية</CardTitle>
          <CardDescription>
            استلام عروض وأخبار المنصة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="marketing-notifications">الإشعارات التسويقية</Label>
              <p className="text-sm text-muted-foreground">
                عروض خاصة وأخبار المنصة
              </p>
            </div>
            <Switch
              id="marketing-notifications"
              checked={preferences.marketingEnabled}
              onCheckedChange={(checked) => updatePreference('marketingEnabled', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* زر الحفظ */}
      <div className="flex justify-end">
        <Button onClick={savePreferences} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              جاري الحفظ...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              حفظ التفضيلات
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

// دوال مساعدة
function getCategoryLabel(category: string): string {
  const labels: Record<string, string> = {
    orders: 'الطلبات',
    payments: 'المدفوعات',
    marketing: 'التسويق',
    system: 'النظام',
    security: 'الأمان'
  };
  return labels[category] || category;
}

function getCategoryDescription(category: string): string {
  const descriptions: Record<string, string> = {
    orders: 'إشعارات الطلبات الجديدة وتحديثات الحالة',
    payments: 'إشعارات المدفوعات والفواتير',
    marketing: 'العروض الخاصة والحملات التسويقية',
    system: 'تحديثات النظام والصيانة',
    security: 'تنبيهات الأمان وتسجيل الدخول'
  };
  return descriptions[category] || '';
}
