'use client';

import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { ArrowLeft, Star, MapPin, Clock } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ReviewsList, AddReviewForm, ReviewStats } from '@/components/reviews';
import { useAuth } from '@/context/AuthContext';
import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { StoreDocument } from '@/types';

export default function StoreReviewsPage() {
  const params = useParams();
  const t = useTranslations();
  const { user } = useAuth();
  const storeId = params.storeId as string;
  
  const [store, setStore] = useState<StoreDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAddReview, setShowAddReview] = useState(false);

  useEffect(() => {
    const fetchStore = async () => {
      try {
        const storeRef = doc(db, 'stores', storeId);
        const storeSnap = await getDoc(storeRef);
        
        if (storeSnap.exists()) {
          setStore({ id: storeSnap.id, ...storeSnap.data() } as StoreDocument);
        }
      } catch (error) {
        console.error('Error fetching store:', error);
      } finally {
        setLoading(false);
      }
    };

    if (storeId) {
      fetchStore();
    }
  }, [storeId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3" />
          <div className="h-64 bg-gray-200 rounded" />
          <div className="h-96 bg-gray-200 rounded" />
        </div>
      </div>
    );
  }

  if (!store) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t('common.storeNotFound')}
            </h1>
            <p className="text-gray-600 mb-6">
              {t('common.storeNotFoundDescription')}
            </p>
            <Link href="/stores">
              <Button>
                <ArrowLeft className="h-4 w-4 me-2" />
                {t('common.backToStores')}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* رأس الصفحة */}
      <div className="mb-8">
        <Link 
          href={`/stores/${storeId}`}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="h-4 w-4 me-2" />
          {t('common.backToStore')}
        </Link>
        
        <div className="flex items-start space-x-4 rtl:space-x-reverse">
          <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
            {store.logoUrl ? (
              <img
                src={store.logoUrl}
                alt={store.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Star className="h-10 w-10 text-gray-400" />
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
              <h1 className="text-3xl font-bold text-gray-900">
                {store.name}
              </h1>
              {store.isVerified && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {t('common.verified')}
                </Badge>
              )}
            </div>
            
            <p className="text-gray-600 mb-3">
              {store.description}
            </p>
            
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
              {store.address && (
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <MapPin className="h-4 w-4" />
                  <span>{store.address}</span>
                </div>
              )}
              
              {store.businessHours && (
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <Clock className="h-4 w-4" />
                  <span>{t('common.businessHours')}</span>
                </div>
              )}
              
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                <span>{t('common.category')}: {store.category}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* العمود الرئيسي - قائمة المراجعات */}
        <div className="lg:col-span-2 space-y-6">
          {/* إضافة مراجعة جديدة */}
          {user && (
            <div className="space-y-4">
              {!showAddReview ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Star className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t('reviews.shareYourExperience')}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {t('reviews.helpOthersWithYourStoreReview')}
                    </p>
                    <Button onClick={() => setShowAddReview(true)}>
                      <Star className="h-4 w-4 me-2" />
                      {t('reviews.addStoreReview')}
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <AddReviewForm
                  targetId={storeId}
                  type="store"
                  targetName={store.name}
                  onSuccess={() => setShowAddReview(false)}
                />
              )}
              
              {showAddReview && (
                <div className="text-center">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddReview(false)}
                  >
                    {t('common.cancel')}
                  </Button>
                </div>
              )}
            </div>
          )}

          <Separator />

          {/* قائمة المراجعات */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {t('reviews.customerReviews')}
            </h2>
            <ReviewsList
              targetId={storeId}
              type="store"
            />
          </div>
        </div>

        {/* الشريط الجانبي - الإحصائيات */}
        <div className="space-y-6">
          <ReviewStats
            targetId={storeId}
            type="store"
          />

          {/* معلومات المتجر */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t('common.storeInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">{t('common.category')}:</span>
                <span className="font-medium">{store.category}</span>
              </div>
              
              {store.phone && (
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('common.phone')}:</span>
                  <span className="font-medium">{store.phone}</span>
                </div>
              )}
              
              {store.email && (
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('common.email')}:</span>
                  <span className="font-medium">{store.email}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-gray-600">{t('common.status')}:</span>
                <Badge variant={store.isActive ? "default" : "secondary"}>
                  {store.isActive ? t('common.active') : t('common.inactive')}
                </Badge>
              </div>
              
              {store.stats && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">
                      {t('common.storeStats')}
                    </h4>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">{t('common.totalOrders')}:</span>
                      <span>{store.stats.totalOrders || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">{t('common.totalSales')}:</span>
                      <span>{store.stats.totalSales || 0} {t('common.sar')}</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
          {/* إرشادات المراجعة */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t('reviews.reviewGuidelines')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-sm text-gray-600">
                <h4 className="font-medium text-gray-900 mb-2">
                  {t('reviews.helpfulStoreReviewTips')}
                </h4>
                <ul className="space-y-1 list-disc list-inside">
                  <li>{t('reviews.storeTip1')}</li>
                  <li>{t('reviews.storeTip2')}</li>
                  <li>{t('reviews.storeTip3')}</li>
                  <li>{t('reviews.storeTip4')}</li>
                </ul>
              </div>

              <Separator />

              <div className="text-sm text-gray-600">
                <h4 className="font-medium text-gray-900 mb-2">
                  {t('reviews.communityGuidelines')}
                </h4>
                <p>
                  {t('reviews.guidelinesDescription')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
