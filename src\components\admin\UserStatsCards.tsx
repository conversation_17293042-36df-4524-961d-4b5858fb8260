'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useLocale } from '@/hooks/use-locale';
import { UserStats } from '@/hooks/useUsersManagement';
import { 
  Users, 
  UserCheck, 
  ShoppingBag, 
  Truck,
  TrendingUp,
  Calendar,
  Clock
} from 'lucide-react';

interface UserStatsCardsProps {
  stats: UserStats | null;
  loading: boolean;
}

export function UserStatsCards({ stats, loading }: UserStatsCardsProps) {
  const { t } = useLocale();

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-10 w-10 rounded-lg" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) return null;

  const statsCards = [
    {
      title: t('totalUsers'),
      value: stats.totalUsers.toLocaleString('ar-SA'),
      description: 'إجمالي المستخدمين المسجلين',
      icon: Users,
      color: 'bg-blue-100 text-blue-600',
      trend: '+12%'
    },
    {
      title: t('activeUsers'),
      value: stats.activeUsers.toLocaleString('ar-SA'),
      description: 'المستخدمون النشطون',
      icon: UserCheck,
      color: 'bg-green-100 text-green-600',
      trend: '+8%'
    },
    {
      title: t('totalCustomers'),
      value: stats.totalCustomers.toLocaleString('ar-SA'),
      description: 'إجمالي العملاء',
      icon: ShoppingBag,
      color: 'bg-purple-100 text-purple-600',
      trend: '+15%'
    },
    {
      title: t('totalMerchants'),
      value: stats.totalMerchants.toLocaleString('ar-SA'),
      description: 'إجمالي التجار',
      icon: Users,
      color: 'bg-orange-100 text-orange-600',
      trend: '+5%'
    },
    {
      title: t('totalRepresentatives'),
      value: stats.totalRepresentatives.toLocaleString('ar-SA'),
      description: 'إجمالي المندوبين',
      icon: Truck,
      color: 'bg-indigo-100 text-indigo-600',
      trend: '+20%'
    },
    {
      title: 'مستخدمون جدد اليوم',
      value: stats.newUsersToday.toLocaleString('ar-SA'),
      description: 'انضموا اليوم',
      icon: Calendar,
      color: 'bg-cyan-100 text-cyan-600',
      trend: '+3'
    },
    {
      title: 'مستخدمون جدد هذا الأسبوع',
      value: stats.newUsersThisWeek.toLocaleString('ar-SA'),
      description: 'انضموا هذا الأسبوع',
      icon: Clock,
      color: 'bg-pink-100 text-pink-600',
      trend: '+25'
    },
    {
      title: 'مستخدمون جدد هذا الشهر',
      value: stats.newUsersThisMonth.toLocaleString('ar-SA'),
      description: 'انضموا هذا الشهر',
      icon: TrendingUp,
      color: 'bg-emerald-100 text-emerald-600',
      trend: '+120'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsCards.map((stat, index) => {
        const IconComponent = stat.icon;
        
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-green-600 font-medium">
                      {stat.trend}
                    </span>
                    <span className="text-xs text-gray-500">
                      {stat.description}
                    </span>
                  </div>
                </div>
                
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <IconComponent className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
