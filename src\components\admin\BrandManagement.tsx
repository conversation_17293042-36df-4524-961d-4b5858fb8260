'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useLocale } from '@/hooks/use-locale';
import { 
  Award, 
  Plus, 
  Search,
  Edit,
  Trash2,
  Star,
  Eye
} from 'lucide-react';

interface Brand {
  id: string;
  name: string;
  logoUrl?: string;
  description?: string;
  isActive: boolean;
  isFeatured: boolean;
  productCount: number;
  createdAt: Date;
}

export function BrandManagement() {
  const { t } = useLocale();
  const [searchQuery, setSearchQuery] = useState('');

  // بيانات تجريبية للعلامات التجارية
  const mockBrands: Brand[] = [
    {
      id: '1',
      name: 'تقنية المستقبل',
      logoUrl: '/api/placeholder/60/60',
      description: 'علامة تجارية رائدة في مجال التقنية',
      isActive: true,
      isFeatured: true,
      productCount: 45,
      createdAt: new Date('2024-01-01')
    },
    {
      id: '2',
      name: 'أناقة العصر',
      logoUrl: '/api/placeholder/60/60',
      description: 'أزياء عصرية وأنيقة',
      isActive: true,
      isFeatured: false,
      productCount: 78,
      createdAt: new Date('2024-01-05')
    },
    {
      id: '3',
      name: 'منزل الأحلام',
      logoUrl: '/api/placeholder/60/60',
      description: 'أدوات منزلية عالية الجودة',
      isActive: false,
      isFeatured: false,
      productCount: 23,
      createdAt: new Date('2024-01-10')
    }
  ];

  const filteredBrands = mockBrands.filter(brand =>
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">إجمالي العلامات</p>
                <p className="text-xl font-bold">{mockBrands.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">العلامات المميزة</p>
                <p className="text-xl font-bold">
                  {mockBrands.filter(b => b.isFeatured).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Award className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">العلامات النشطة</p>
                <p className="text-xl font-bold">
                  {mockBrands.filter(b => b.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والإضافة */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في العلامات التجارية..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              إضافة علامة تجارية
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* قائمة العلامات التجارية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredBrands.map((brand) => (
          <Card key={brand.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {/* شعار العلامة التجارية */}
                <div className="flex-shrink-0">
                  {brand.logoUrl ? (
                    <img
                      src={brand.logoUrl}
                      alt={brand.name}
                      className="w-16 h-16 object-cover rounded-lg border"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                      <Award className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* معلومات العلامة التجارية */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium text-gray-900 truncate">
                      {brand.name}
                    </h3>
                    <div className="flex gap-1">
                      {brand.isFeatured && (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <Star className="h-3 w-3 mr-1" />
                          مميزة
                        </Badge>
                      )}
                      {brand.isActive ? (
                        <Badge className="bg-green-100 text-green-800">
                          نشطة
                        </Badge>
                      ) : (
                        <Badge className="bg-gray-100 text-gray-800">
                          غير نشطة
                        </Badge>
                      )}
                    </div>
                  </div>

                  {brand.description && (
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {brand.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{brand.productCount} منتج</span>
                    <span>{brand.createdAt.toLocaleDateString('ar-SA')}</span>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex items-center gap-2 mt-4">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      عرض
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-1" />
                      تعديل
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* رسالة عدم وجود نتائج */}
      {filteredBrands.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد علامات تجارية
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery 
                ? 'لم يتم العثور على علامات تجارية تطابق البحث'
                : 'لم يتم إضافة أي علامات تجارية بعد'
              }
            </p>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              إضافة علامة تجارية جديدة
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
