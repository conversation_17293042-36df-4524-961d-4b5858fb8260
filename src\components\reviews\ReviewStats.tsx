'use client';

import { useTranslations } from 'next-intl';
import { Star, TrendingUp, Users, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useReviews } from '@/hooks/useReviews';
import type { ReviewStats as ReviewStatsType } from '@/types';

interface ReviewStatsProps {
  targetId: string;
  type: 'store' | 'product';
  className?: string;
}

export function ReviewStats({ targetId, type, className }: ReviewStatsProps) {
  const t = useTranslations();
  const { stats, loading } = useReviews({ targetId, type, autoFetch: true });

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="animate-pulse space-y-2">
            <div className="h-6 bg-gray-200 rounded w-1/3" />
            <div className="h-4 bg-gray-200 rounded w-1/2" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-16 bg-gray-200 rounded" />
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats || stats.totalReviews === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('reviews.noRatings')}
          </h3>
          <p className="text-gray-500">
            {t('reviews.noRatingsDescription')}
          </p>
        </CardContent>
      </Card>
    );
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-5 w-5 ${
          i < Math.floor(rating) 
            ? 'fill-yellow-400 text-yellow-400' 
            : i < rating
            ? 'fill-yellow-200 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const getRatingLabel = (rating: number) => {
    if (rating >= 4.5) return { label: t('reviews.excellent'), color: 'bg-green-500' };
    if (rating >= 4.0) return { label: t('reviews.veryGood'), color: 'bg-blue-500' };
    if (rating >= 3.5) return { label: t('reviews.good'), color: 'bg-yellow-500' };
    if (rating >= 3.0) return { label: t('reviews.average'), color: 'bg-orange-500' };
    return { label: t('reviews.poor'), color: 'bg-red-500' };
  };

  const ratingInfo = getRatingLabel(stats.averageRating);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <Star className="h-5 w-5 text-yellow-500" />
          <span>{t('reviews.ratingsAndReviews')}</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* نظرة عامة على التقييم */}
        <div className="flex items-center space-x-6 rtl:space-x-reverse">
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-1">
              {stats.averageRating.toFixed(1)}
            </div>
            <div className="flex items-center justify-center mb-2">
              {renderStars(stats.averageRating)}
            </div>
            <Badge variant="secondary" className={`${ratingInfo.color} text-white`}>
              {ratingInfo.label}
            </Badge>
          </div>
          
          <div className="flex-1 space-y-3">
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
              <Users className="h-4 w-4" />
              <span>
                {t('reviews.totalReviews', { count: stats.totalReviews })}
              </span>
            </div>
            
            {stats.verifiedReviews > 0 && (
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>
                  {t('reviews.verifiedReviews', { count: stats.verifiedReviews })}
                </span>
              </div>
            )}
            
            {stats.recentReviews > 0 && (
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
                <TrendingUp className="h-4 w-4 text-blue-500" />
                <span>
                  {t('reviews.recentReviews', { count: stats.recentReviews })}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* توزيع التقييمات */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">
            {t('reviews.ratingDistribution')}
          </h4>
          
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution];
            const percentage = stats.totalReviews > 0 ? (count / stats.totalReviews) * 100 : 0;
            
            return (
              <div key={rating} className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="flex items-center space-x-1 rtl:space-x-reverse w-16">
                  <span className="text-sm font-medium">{rating}</span>
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                </div>
                
                <div className="flex-1">
                  <Progress 
                    value={percentage} 
                    className="h-2"
                  />
                </div>
                
                <div className="w-12 text-sm text-gray-600 text-right rtl:text-left">
                  {count}
                </div>
                
                <div className="w-12 text-xs text-gray-500 text-right rtl:text-left">
                  {percentage.toFixed(0)}%
                </div>
              </div>
            );
          })}
        </div>

        {/* إحصائيات إضافية */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round((stats.verifiedReviews / stats.totalReviews) * 100)}%
            </div>
            <div className="text-xs text-gray-500">
              {t('reviews.verifiedPercentage')}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Math.round(((stats.ratingDistribution[4] + stats.ratingDistribution[5]) / stats.totalReviews) * 100)}%
            </div>
            <div className="text-xs text-gray-500">
              {t('reviews.positivePercentage')}
            </div>
          </div>
        </div>

        {/* مؤشر الجودة */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              {t('reviews.qualityScore')}
            </span>
            <span className="text-sm font-bold text-gray-900">
              {Math.round(stats.averageRating * 20)}%
            </span>
          </div>
          <Progress 
            value={stats.averageRating * 20} 
            className="h-2"
          />
          <p className="text-xs text-gray-500 mt-2">
            {t('reviews.qualityScoreDescription')}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
