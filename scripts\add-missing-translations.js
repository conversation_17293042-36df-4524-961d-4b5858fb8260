#!/usr/bin/env node

/**
 * سكريبت متقدم لإضافة الترجمات المفقودة الأساسية
 * يركز على الترجمات الأكثر أهمية للمستخدمين
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  fs.copyFileSync(filePath, backupPath);
  console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
  return backupPath;
}

/**
 * الترجمات الأساسية المفقودة (أولوية عالية)
 */
const essentialTranslations = {
  // ترجمات الدفع والشراء (أولوية قصوى)
  checkout: {
    ar: "الدفع",
    en: "Checkout"
  },
  customerInformation: {
    ar: "معلومات العميل",
    en: "Customer Information"
  },
  shippingAddress: {
    ar: "عنوان الشحن",
    en: "Shipping Address"
  },
  paymentMethod: {
    ar: "طريقة الدفع",
    en: "Payment Method"
  },
  orderSummary: {
    ar: "ملخص الطلب",
    en: "Order Summary"
  },
  placeOrder: {
    ar: "تأكيد الطلب",
    en: "Place Order"
  },
  
  // ترجمات البحث المتقدم
  sortBy: {
    ar: "ترتيب حسب",
    en: "Sort By"
  },
  priceRange: {
    ar: "نطاق السعر",
    en: "Price Range"
  },
  storesFound: {
    ar: "تم العثور على {{count}} متجر",
    en: "Found {{count}} stores"
  },
  
  // ترجمات أساسية مفقودة
  reviews: {
    ar: "المراجعات",
    en: "Reviews"
  },
  viewStore: {
    ar: "عرض المتجر",
    en: "View Store"
  },
  contactAvailable: {
    ar: "التواصل متاح",
    en: "Contact Available"
  },
  websiteAvailable: {
    ar: "الموقع متاح",
    en: "Website Available"
  },
  
  // ترجمات الملف الشخصي
  profileUpdateFailed: {
    ar: "فشل تحديث الملف الشخصي",
    en: "Profile Update Failed"
  },
  logoutFailed: {
    ar: "فشل تسجيل الخروج",
    en: "Logout Failed"
  },
  
  // ترجمات الموقع
  yourCurrentLocation: {
    ar: "موقعك الحالي",
    en: "Your Current Location"
  },
  latitude: {
    ar: "خط العرض",
    en: "Latitude"
  },
  longitude: {
    ar: "خط الطول",
    en: "Longitude"
  },
  
  // ترجمات التقييم والمراجعات
  addReview: {
    ar: "إضافة مراجعة",
    en: "Add Review"
  },
  writeReview: {
    ar: "كتابة مراجعة",
    en: "Write Review"
  },
  rateProduct: {
    ar: "قيم المنتج",
    en: "Rate Product"
  },
  
  // ترجمات السلة والطلبات
  addToWishlist: {
    ar: "إضافة لقائمة الأمنيات",
    en: "Add to Wishlist"
  },
  removeFromCart: {
    ar: "إزالة من السلة",
    en: "Remove from Cart"
  },
  updateQuantity: {
    ar: "تحديث الكمية",
    en: "Update Quantity"
  },
  
  // ترجمات الإشعارات
  notifications: {
    ar: "الإشعارات",
    en: "Notifications"
  },
  markAsRead: {
    ar: "تحديد كمقروء",
    en: "Mark as Read"
  },
  clearAll: {
    ar: "مسح الكل",
    en: "Clear All"
  },
  
  // ترجمات الدعم
  helpCenter: {
    ar: "مركز المساعدة",
    en: "Help Center"
  },
  faq: {
    ar: "الأسئلة الشائعة",
    en: "FAQ"
  },
  reportIssue: {
    ar: "الإبلاغ عن مشكلة",
    en: "Report Issue"
  }
};

/**
 * إضافة الترجمات المفقودة لملف معين
 */
function addMissingTranslations(filePath, language) {
  console.log(`\n🔧 إضافة الترجمات المفقودة للملف: ${path.basename(filePath)}`);
  
  // إنشاء نسخة احتياطية
  createBackup(filePath);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(content);
    
    let addedCount = 0;
    const addedTranslations = [];
    
    // إضافة كل ترجمة مفقودة
    Object.entries(essentialTranslations).forEach(([key, values]) => {
      if (!translations[key]) {
        translations[key] = values[language];
        addedCount++;
        addedTranslations.push({
          key,
          value: values[language]
        });
        console.log(`✅ أضيف: "${key}" = "${values[language]}"`);
      } else {
        console.log(`⏭️ موجود بالفعل: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      // كتابة الملف المحدث
      const updatedContent = JSON.stringify(translations, null, 2);
      fs.writeFileSync(filePath, updatedContent, 'utf8');
      
      console.log(`\n✅ تم إضافة ${addedCount} ترجمة جديدة للغة ${language === 'ar' ? 'العربية' : 'الإنجليزية'}`);
    } else {
      console.log(`\n✨ جميع الترجمات الأساسية موجودة بالفعل في ${language === 'ar' ? 'العربية' : 'الإنجليزية'}`);
    }
    
    return addedTranslations;
    
  } catch (error) {
    console.error(`❌ خطأ في إضافة الترجمات للملف ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * إنشاء تقرير مفصل عن الترجمات المضافة
 */
function generateReport(arAdded, enAdded) {
  const reportPath = path.join(__dirname, '../docs/missing-translations-report.md');
  const timestamp = new Date().toLocaleString('ar-SA');
  
  let report = `# تقرير إضافة الترجمات المفقودة\n\n`;
  report += `**تاريخ الإضافة**: ${timestamp}\n\n`;
  
  report += `## ملخص الإضافات\n\n`;
  report += `- **الترجمات العربية المضافة**: ${arAdded.length}\n`;
  report += `- **الترجمات الإنجليزية المضافة**: ${enAdded.length}\n`;
  report += `- **إجمالي الترجمات المضافة**: ${arAdded.length + enAdded.length}\n\n`;
  
  if (arAdded.length > 0) {
    report += `## الترجمات العربية المضافة\n\n`;
    arAdded.forEach((translation, index) => {
      report += `${index + 1}. **${translation.key}**: "${translation.value}"\n`;
    });
    report += `\n`;
  }
  
  if (enAdded.length > 0) {
    report += `## الترجمات الإنجليزية المضافة\n\n`;
    enAdded.forEach((translation, index) => {
      report += `${index + 1}. **${translation.key}**: "${translation.value}"\n`;
    });
    report += `\n`;
  }
  
  report += `## الفئات المضافة\n\n`;
  report += `- **ترجمات الدفع والشراء**: checkout, customerInformation, shippingAddress, paymentMethod, orderSummary, placeOrder\n`;
  report += `- **ترجمات البحث**: sortBy, priceRange, storesFound\n`;
  report += `- **ترجمات أساسية**: reviews, viewStore, contactAvailable, websiteAvailable\n`;
  report += `- **ترجمات الملف الشخصي**: profileUpdateFailed, logoutFailed\n`;
  report += `- **ترجمات الموقع**: yourCurrentLocation, latitude, longitude\n`;
  report += `- **ترجمات التقييم**: addReview, writeReview, rateProduct\n`;
  report += `- **ترجمات السلة**: addToWishlist, removeFromCart, updateQuantity\n`;
  report += `- **ترجمات الإشعارات**: notifications, markAsRead, clearAll\n`;
  report += `- **ترجمات الدعم**: helpCenter, faq, reportIssue\n\n`;
  
  report += `## التوصيات\n\n`;
  report += `1. تشغيل سكريبت التحقق للتأكد من الإضافات: \`node scripts/validate-translations.js\`\n`;
  report += `2. اختبار التطبيق للتأكد من ظهور الترجمات الجديدة\n`;
  report += `3. مراجعة الترجمات المضافة للتأكد من دقتها\n`;
  report += `4. إضافة المزيد من الترجمات المفقودة حسب الحاجة\n\n`;
  
  fs.writeFileSync(reportPath, report, 'utf8');
  console.log(`📄 تم إنشاء تقرير مفصل: ${path.basename(reportPath)}`);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء إضافة الترجمات المفقودة الأساسية...\n');
  
  try {
    // إضافة الترجمات للملفين
    const arAdded = addMissingTranslations(AR_TRANSLATIONS_PATH, 'ar');
    const enAdded = addMissingTranslations(EN_TRANSLATIONS_PATH, 'en');
    
    // إنشاء تقرير مفصل
    generateReport(arAdded, enAdded);
    
    // ملخص النتائج
    console.log('\n📋 ملخص الإضافات:');
    console.log(`✅ الترجمات العربية: ${arAdded.length} ترجمة جديدة`);
    console.log(`✅ الترجمات الإنجليزية: ${enAdded.length} ترجمة جديدة`);
    console.log(`✅ إجمالي الترجمات المضافة: ${arAdded.length + enAdded.length}`);
    
    if (arAdded.length > 0 || enAdded.length > 0) {
      console.log('\n🎉 تم الانتهاء من إضافة الترجمات المفقودة بنجاح!');
      console.log('💡 يُنصح بتشغيل سكريبت التحقق للتأكد من النتائج:');
      console.log('   node scripts/validate-translations.js');
    } else {
      console.log('\n✨ جميع الترجمات الأساسية موجودة بالفعل!');
    }
    
  } catch (error) {
    console.error('\n❌ فشل في إضافة الترجمات المفقودة:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  addMissingTranslations,
  essentialTranslations,
  generateReport
};
