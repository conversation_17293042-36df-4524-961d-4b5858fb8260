// src/app/[locale]/merchant/crm/page.tsx
'use client';

import React from 'react';
import { CRMDashboard } from '@/components/merchant/crm/CRMDashboard';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShieldAlert, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface CRMPageProps {
  params: {
    locale: string;
  };
}

export default function CRMPage({ params: { locale } }: CRMPageProps) {
  const { user, loading, userType } = useAuth();
  const router = useRouter();

  // التحقق من صلاحية الوصول
  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push(`/${locale}/login?redirect=${encodeURIComponent(`/${locale}/merchant/crm`)}`);
        return;
      }

      if (userType !== 'merchant') {
        router.push(`/${locale}/unauthorized`);
        return;
      }
    }
  }, [user, loading, userType, router, locale]);

  // عرض شاشة التحميل
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري التحميل...</p>
          </div>
        </div>
      </div>
    );
  }

  // عرض رسالة عدم الصلاحية
  if (!user || userType !== 'merchant') {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <ShieldAlert className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">غير مصرح بالوصول</h2>
              <p className="text-muted-foreground mb-4">
                هذه الصفحة متاحة للتجار فقط
              </p>
              <div className="flex gap-2 justify-center">
                <Button asChild variant="outline">
                  <Link href={`/${locale}`}>
                    <ArrowLeft className="h-4 w-4 me-2" />
                    العودة للرئيسية
                  </Link>
                </Button>
                <Button asChild>
                  <Link href={`/${locale}/login`}>
                    تسجيل الدخول
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <CRMDashboard />
    </div>
  );
}
