/**
 * خدمة البحث الذكي المتقدم
 * تدعم البحث الدلالي، التصحيح التلقائي، والاقتراحات الذكية
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  addDoc,
  updateDoc,
  doc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';

interface SearchQuery {
  query: string;
  category?: string;
  location?: string;
  priceRange?: { min: number; max: number };
  rating?: number;
  deliveryTime?: string;
  storeType?: string[];
  sortBy?: string;
  availability?: boolean;
}

interface SearchResult {
  id: string;
  type: 'product' | 'store' | 'category';
  title: string;
  description: string;
  price?: number;
  rating?: number;
  imageUrl?: string;
  storeId?: string;
  storeName?: string;
  location?: string;
  deliveryTime?: number;
  availability?: boolean;
  relevanceScore: number;
}

interface SearchSuggestion {
  text: string;
  type: 'query' | 'product' | 'store' | 'category';
  frequency: number;
  lastUsed: Date;
}

interface SearchAnalytics {
  query: string;
  userId?: string;
  resultsCount: number;
  clickedResults: string[];
  timestamp: Date;
  filters: SearchQuery;
}

class SmartSearchService {
  private static readonly SEARCH_INDEX_COLLECTION = 'search_index';
  private static readonly SEARCH_ANALYTICS_COLLECTION = 'search_analytics';
  private static readonly SEARCH_SUGGESTIONS_COLLECTION = 'search_suggestions';

  /**
   * البحث الذكي الرئيسي
   */
  static async smartSearch(searchQuery: SearchQuery, userId?: string): Promise<{
    results: SearchResult[];
    suggestions: string[];
    correctedQuery?: string;
    totalCount: number;
  }> {
    try {
      console.log('🔍 بدء البحث الذكي:', searchQuery);

      // 1. تصحيح الاستعلام وتحسينه
      const correctedQuery = await this.correctAndEnhanceQuery(searchQuery.query);

      // 2. البحث في المنتجات
      const productResults = await this.searchProducts(searchQuery);

      // 3. البحث في المتاجر
      const storeResults = await this.searchStores(searchQuery);

      // 4. البحث في الفئات
      const categoryResults = await this.searchCategories(searchQuery);

      // 5. دمج النتائج وترتيبها
      const allResults = [...productResults, ...storeResults, ...categoryResults];
      const sortedResults = this.sortResults(allResults, searchQuery.sortBy || 'relevance');

      // 6. إنشاء اقتراحات ذكية
      const suggestions = await this.generateSmartSuggestions(searchQuery.query);

      // 7. تسجيل التحليلات
      await this.logSearchAnalytics({
        query: searchQuery.query,
        userId,
        resultsCount: sortedResults.length,
        clickedResults: [],
        timestamp: new Date(),
        filters: searchQuery
      });

      // 8. تحديث اقتراحات البحث
      await this.updateSearchSuggestions(searchQuery.query);

      return {
        results: sortedResults,
        suggestions,
        correctedQuery: correctedQuery !== searchQuery.query ? correctedQuery : undefined,
        totalCount: sortedResults.length
      };

    } catch (error) {
      console.error('❌ خطأ في البحث الذكي:', error);
      throw new Error('فشل في تنفيذ البحث الذكي');
    }
  }

  /**
   * البحث في المنتجات
   */
  private static async searchProducts(searchQuery: SearchQuery): Promise<SearchResult[]> {
    try {
      const productsRef = collection(db, 'products');
      let q = query(productsRef, where('isActive', '==', true));

      // فلترة حسب الفئة
      if (searchQuery.category && searchQuery.category !== 'الكل') {
        q = query(q, where('category', '==', searchQuery.category));
      }

      // فلترة حسب السعر
      if (searchQuery.priceRange) {
        q = query(q, 
          where('price', '>=', searchQuery.priceRange.min),
          where('price', '<=', searchQuery.priceRange.max)
        );
      }

      // فلترة حسب التوفر
      if (searchQuery.availability) {
        q = query(q, where('inStock', '==', true));
      }

      const snapshot = await getDocs(q);
      const products: SearchResult[] = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        
        // حساب درجة الصلة
        const relevanceScore = this.calculateRelevanceScore(
          searchQuery.query,
          data.name,
          data.description,
          data.tags || []
        );

        // فلترة حسب التقييم
        if (searchQuery.rating && data.rating < searchQuery.rating) {
          return;
        }

        // فلترة حسب البحث النصي
        if (searchQuery.query && relevanceScore < 0.3) {
          return;
        }

        products.push({
          id: doc.id,
          type: 'product',
          title: data.name,
          description: data.description,
          price: data.price,
          rating: data.rating,
          imageUrl: data.imageUrl,
          storeId: data.storeId,
          storeName: data.storeName,
          availability: data.inStock,
          relevanceScore
        });
      });

      return products;
    } catch (error) {
      console.error('❌ خطأ في البحث في المنتجات:', error);
      return [];
    }
  }

  /**
   * البحث في المتاجر
   */
  private static async searchStores(searchQuery: SearchQuery): Promise<SearchResult[]> {
    try {
      const storesRef = collection(db, 'stores');
      let q = query(
        storesRef, 
        where('isActive', '==', true),
        where('approvalStatus', '==', 'approved')
      );

      // فلترة حسب نوع المتجر
      if (searchQuery.storeType && searchQuery.storeType.length > 0) {
        q = query(q, where('storeType', 'in', searchQuery.storeType));
      }

      const snapshot = await getDocs(q);
      const stores: SearchResult[] = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        
        // حساب درجة الصلة
        const relevanceScore = this.calculateRelevanceScore(
          searchQuery.query,
          data.storeName,
          data.storeDescription,
          data.tags || []
        );

        // فلترة حسب التقييم
        if (searchQuery.rating && data.rating < searchQuery.rating) {
          return;
        }

        // فلترة حسب الموقع
        if (searchQuery.location && !data.address?.city?.includes(searchQuery.location)) {
          return;
        }

        // فلترة حسب البحث النصي
        if (searchQuery.query && relevanceScore < 0.3) {
          return;
        }

        stores.push({
          id: doc.id,
          type: 'store',
          title: data.storeName,
          description: data.storeDescription,
          rating: data.rating,
          imageUrl: data.logoUrl,
          location: data.address?.city,
          deliveryTime: data.averageDeliveryTime,
          relevanceScore
        });
      });

      return stores;
    } catch (error) {
      console.error('❌ خطأ في البحث في المتاجر:', error);
      return [];
    }
  }

  /**
   * البحث في الفئات
   */
  private static async searchCategories(searchQuery: SearchQuery): Promise<SearchResult[]> {
    try {
      const categoriesRef = collection(db, 'categories');
      const q = query(categoriesRef, where('isActive', '==', true));

      const snapshot = await getDocs(q);
      const categories: SearchResult[] = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        
        // حساب درجة الصلة
        const relevanceScore = this.calculateRelevanceScore(
          searchQuery.query,
          data.name,
          data.description,
          data.keywords || []
        );

        // فلترة حسب البحث النصي
        if (searchQuery.query && relevanceScore < 0.4) {
          return;
        }

        categories.push({
          id: doc.id,
          type: 'category',
          title: data.name,
          description: data.description,
          imageUrl: data.icon,
          relevanceScore
        });
      });

      return categories;
    } catch (error) {
      console.error('❌ خطأ في البحث في الفئات:', error);
      return [];
    }
  }

  /**
   * حساب درجة الصلة
   */
  private static calculateRelevanceScore(
    query: string,
    title: string,
    description: string,
    tags: string[]
  ): number {
    if (!query) return 1;

    const queryLower = query.toLowerCase();
    const titleLower = title.toLowerCase();
    const descriptionLower = description.toLowerCase();
    const tagsLower = tags.map(tag => tag.toLowerCase());

    let score = 0;

    // تطابق العنوان (وزن عالي)
    if (titleLower.includes(queryLower)) {
      score += 0.8;
    } else if (titleLower.split(' ').some(word => queryLower.includes(word))) {
      score += 0.6;
    }

    // تطابق الوصف (وزن متوسط)
    if (descriptionLower.includes(queryLower)) {
      score += 0.4;
    } else if (descriptionLower.split(' ').some(word => queryLower.includes(word))) {
      score += 0.2;
    }

    // تطابق العلامات (وزن متوسط)
    if (tagsLower.some(tag => tag.includes(queryLower))) {
      score += 0.5;
    }

    // تطابق جزئي للكلمات
    const queryWords = queryLower.split(' ');
    const titleWords = titleLower.split(' ');
    const matchingWords = queryWords.filter(qWord => 
      titleWords.some(tWord => tWord.includes(qWord) || qWord.includes(tWord))
    );
    score += (matchingWords.length / queryWords.length) * 0.3;

    return Math.min(score, 1);
  }

  /**
   * ترتيب النتائج
   */
  private static sortResults(results: SearchResult[], sortBy: string): SearchResult[] {
    switch (sortBy) {
      case 'price_low':
        return results.sort((a, b) => (a.price || 0) - (b.price || 0));
      case 'price_high':
        return results.sort((a, b) => (b.price || 0) - (a.price || 0));
      case 'rating':
        return results.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      case 'delivery_time':
        return results.sort((a, b) => (a.deliveryTime || 999) - (b.deliveryTime || 999));
      case 'newest':
        return results; // يحتاج إلى تاريخ الإنشاء
      default: // relevance
        return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }
  }

  /**
   * تصحيح وتحسين الاستعلام
   */
  private static async correctAndEnhanceQuery(query: string): Promise<string> {
    try {
      // تنظيف الاستعلام
      let correctedQuery = query.trim();
      
      // إزالة الأحرف الخاصة
      correctedQuery = correctedQuery.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]/g, '');
      
      // تصحيح الأخطاء الإملائية الشائعة
      const corrections: Record<string, string> = {
        'مطعام': 'مطعم',
        'بقاله': 'بقالة',
        'صيدليه': 'صيدلية',
        'الكترونيات': 'إلكترونيات',
        'ملابس': 'ملابس'
      };

      Object.entries(corrections).forEach(([wrong, correct]) => {
        correctedQuery = correctedQuery.replace(new RegExp(wrong, 'gi'), correct);
      });

      return correctedQuery;
    } catch (error) {
      console.error('❌ خطأ في تصحيح الاستعلام:', error);
      return query;
    }
  }

  /**
   * إنشاء اقتراحات ذكية
   */
  private static async generateSmartSuggestions(query: string): Promise<string[]> {
    try {
      const suggestionsRef = collection(db, this.SEARCH_SUGGESTIONS_COLLECTION);
      const q = query.length > 0 
        ? query(suggestionsRef, 
            where('text', '>=', query.toLowerCase()),
            where('text', '<=', query.toLowerCase() + '\uf8ff'),
            orderBy('frequency', 'desc'),
            limit(5)
          )
        : query(suggestionsRef, orderBy('frequency', 'desc'), limit(5));

      const snapshot = await getDocs(q);
      const suggestions: string[] = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        suggestions.push(data.text);
      });

      return suggestions;
    } catch (error) {
      console.error('❌ خطأ في إنشاء الاقتراحات:', error);
      return [];
    }
  }

  /**
   * تحديث اقتراحات البحث
   */
  private static async updateSearchSuggestions(query: string): Promise<void> {
    try {
      if (!query.trim()) return;

      const suggestionsRef = collection(db, this.SEARCH_SUGGESTIONS_COLLECTION);
      const q = query(suggestionsRef, where('text', '==', query.toLowerCase()));
      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        // إضافة اقتراح جديد
        await addDoc(suggestionsRef, {
          text: query.toLowerCase(),
          type: 'query',
          frequency: 1,
          lastUsed: serverTimestamp()
        });
      } else {
        // تحديث التكرار
        const docRef = doc(db, this.SEARCH_SUGGESTIONS_COLLECTION, snapshot.docs[0].id);
        const currentData = snapshot.docs[0].data();
        await updateDoc(docRef, {
          frequency: currentData.frequency + 1,
          lastUsed: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث اقتراحات البحث:', error);
    }
  }

  /**
   * تسجيل تحليلات البحث
   */
  private static async logSearchAnalytics(analytics: SearchAnalytics): Promise<void> {
    try {
      const analyticsRef = collection(db, this.SEARCH_ANALYTICS_COLLECTION);
      await addDoc(analyticsRef, {
        ...analytics,
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('❌ خطأ في تسجيل تحليلات البحث:', error);
    }
  }

  /**
   * الحصول على الاتجاهات الشائعة
   */
  static async getTrendingSearches(limit: number = 10): Promise<string[]> {
    try {
      const analyticsRef = collection(db, this.SEARCH_ANALYTICS_COLLECTION);
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const q = query(
        analyticsRef,
        where('timestamp', '>=', Timestamp.fromDate(oneDayAgo)),
        orderBy('timestamp', 'desc'),
        limit(100)
      );

      const snapshot = await getDocs(q);
      const queryFrequency: Record<string, number> = {};

      snapshot.forEach(doc => {
        const data = doc.data();
        const query = data.query.toLowerCase();
        queryFrequency[query] = (queryFrequency[query] || 0) + 1;
      });

      return Object.entries(queryFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, limit)
        .map(([query]) => query);

    } catch (error) {
      console.error('❌ خطأ في الحصول على الاتجاهات الشائعة:', error);
      return [];
    }
  }

  /**
   * تسجيل النقر على نتيجة البحث
   */
  static async logSearchClick(
    query: string,
    resultId: string,
    resultType: string,
    userId?: string
  ): Promise<void> {
    try {
      const analyticsRef = collection(db, 'search_clicks');
      await addDoc(analyticsRef, {
        query: query.toLowerCase(),
        resultId,
        resultType,
        userId,
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('❌ خطأ في تسجيل النقر:', error);
    }
  }
}

export default SmartSearchService;
export type { SearchQuery, SearchResult, SearchSuggestion, SearchAnalytics };
