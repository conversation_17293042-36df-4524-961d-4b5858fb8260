#!/usr/bin/env node

/**
 * سكريبت تشغيل الاختبارات الشاملة
 * يقوم بتشغيل جميع اختبارات Cypress للميزات المكتملة
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ألوان للطباعة
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// طباعة ملونة
function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// طباعة عنوان
function printHeader(title) {
  const border = '='.repeat(60);
  colorLog(border, 'cyan');
  colorLog(`🧪 ${title}`, 'bright');
  colorLog(border, 'cyan');
}

// طباعة قسم
function printSection(title) {
  colorLog(`\n📋 ${title}`, 'yellow');
  colorLog('-'.repeat(40), 'yellow');
}

// تشغيل أمر مع معالجة الأخطاء
function runCommand(command, description) {
  try {
    colorLog(`\n⚡ ${description}...`, 'blue');
    execSync(command, { stdio: 'inherit' });
    colorLog(`✅ ${description} - مكتمل`, 'green');
    return true;
  } catch (error) {
    colorLog(`❌ ${description} - فشل`, 'red');
    colorLog(`خطأ: ${error.message}`, 'red');
    return false;
  }
}

// التحقق من وجود الملفات المطلوبة
function checkRequiredFiles() {
  const requiredFiles = [
    'cypress.config.ts',
    'cypress/support/commands.ts',
    'cypress/support/e2e.ts',
    'package.json'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    colorLog('❌ ملفات مطلوبة مفقودة:', 'red');
    missingFiles.forEach(file => colorLog(`   - ${file}`, 'red'));
    return false;
  }
  
  colorLog('✅ جميع الملفات المطلوبة موجودة', 'green');
  return true;
}

// الحصول على قائمة اختبارات Cypress
function getCypressTests() {
  const testsDir = 'cypress/e2e';
  if (!fs.existsSync(testsDir)) {
    colorLog('❌ مجلد الاختبارات غير موجود', 'red');
    return [];
  }

  const testFiles = fs.readdirSync(testsDir)
    .filter(file => file.endsWith('.cy.ts') || file.endsWith('.cy.js'))
    .map(file => path.join(testsDir, file));

  return testFiles;
}

// تشغيل اختبارات محددة
function runSpecificTests(testFiles, mode = 'run') {
  const results = [];
  
  for (const testFile of testFiles) {
    const testName = path.basename(testFile, path.extname(testFile));
    const command = mode === 'run' 
      ? `npx cypress run --spec "${testFile}"` 
      : `npx cypress open --e2e --spec "${testFile}"`;
    
    const success = runCommand(command, `تشغيل اختبار ${testName}`);
    results.push({ test: testName, success });
  }
  
  return results;
}

// طباعة تقرير النتائج
function printResults(results) {
  printSection('📊 تقرير النتائج');
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => r.success === false).length;
  const total = results.length;
  
  colorLog(`\n📈 الإحصائيات:`, 'bright');
  colorLog(`   إجمالي الاختبارات: ${total}`, 'blue');
  colorLog(`   نجح: ${passed}`, 'green');
  colorLog(`   فشل: ${failed}`, 'red');
  colorLog(`   معدل النجاح: ${((passed / total) * 100).toFixed(1)}%`, 'cyan');
  
  if (failed > 0) {
    colorLog(`\n❌ الاختبارات الفاشلة:`, 'red');
    results.filter(r => !r.success).forEach(r => {
      colorLog(`   - ${r.test}`, 'red');
    });
  }
  
  if (passed === total) {
    colorLog(`\n🎉 جميع الاختبارات نجحت!`, 'green');
  }
}

// الوظيفة الرئيسية
async function main() {
  printHeader('اختبارات Cypress الشاملة لمشروع مخلة');
  
  // التحقق من المتطلبات
  printSection('🔍 التحقق من المتطلبات');
  
  if (!checkRequiredFiles()) {
    process.exit(1);
  }
  
  // التحقق من تثبيت Cypress
  try {
    execSync('npx cypress version', { stdio: 'pipe' });
    colorLog('✅ Cypress مثبت ومتاح', 'green');
  } catch (error) {
    colorLog('❌ Cypress غير مثبت أو غير متاح', 'red');
    colorLog('قم بتشغيل: npm install cypress', 'yellow');
    process.exit(1);
  }
  
  // الحصول على قائمة الاختبارات
  const testFiles = getCypressTests();
  
  if (testFiles.length === 0) {
    colorLog('❌ لم يتم العثور على ملفات اختبار', 'red');
    process.exit(1);
  }
  
  colorLog(`✅ تم العثور على ${testFiles.length} ملف اختبار`, 'green');
  
  // عرض قائمة الاختبارات
  printSection('📝 قائمة الاختبارات');
  testFiles.forEach((file, index) => {
    const testName = path.basename(file, path.extname(file));
    colorLog(`   ${index + 1}. ${testName}`, 'blue');
  });
  
  // تشغيل الاختبارات
  printSection('🚀 تشغيل الاختبارات');
  
  // التحقق من وضع التشغيل
  const mode = process.argv.includes('--open') ? 'open' : 'run';
  
  if (mode === 'open') {
    colorLog('🖥️  فتح واجهة Cypress التفاعلية...', 'cyan');
    runCommand('npx cypress open', 'فتح Cypress');
  } else {
    colorLog('⚡ تشغيل الاختبارات في وضع headless...', 'cyan');
    
    // تشغيل جميع الاختبارات
    const results = runSpecificTests(testFiles, 'run');
    
    // طباعة النتائج
    printResults(results);
    
    // إنهاء البرنامج بحالة الخطأ إذا فشل أي اختبار
    const hasFailures = results.some(r => !r.success);
    if (hasFailures) {
      process.exit(1);
    }
  }
  
  colorLog('\n🎯 انتهى تشغيل الاختبارات', 'bright');
}

// تشغيل البرنامج
if (require.main === module) {
  main().catch(error => {
    colorLog(`❌ خطأ في تشغيل الاختبارات: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { main, runSpecificTests, getCypressTests };
