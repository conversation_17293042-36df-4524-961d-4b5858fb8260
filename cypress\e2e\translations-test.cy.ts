/// <reference types="cypress" />

describe('اختبار الترجمات المضافة', () => {
  beforeEach(() => {
    // زيارة الصفحة الرئيسية
    cy.visit('/');
  });

  describe('اختبار تبديل اللغات', () => {
    it('يجب أن يعمل تبديل اللغة بين العربية والإنجليزية', () => {
      // التحقق من وجود زر تبديل اللغة
      cy.get('[aria-label*="switch"], [aria-label*="تبديل"], button').contains(/EN|ع/).should('exist');
      
      // النقر على زر تبديل اللغة
      cy.get('[aria-label*="switch"], [aria-label*="تبديل"], button').contains(/EN|ع/).click();
      
      // التحقق من تغيير اللغة
      cy.url().should('match', /(\/ar\/|\/en\/)/);
    });
  });

  describe('اختبار ترجمات البحث المتقدم', () => {
    it('يجب أن تظهر ترجمات البحث بشكل صحيح', () => {
      // الانتقال لصفحة البحث
      cy.visit('/ar/search');
      
      // التحقق من وجود العناصر المترجمة
      cy.contains('البحث المتقدم').should('be.visible');
      cy.get('input[placeholder*="ابحث"]').should('exist');
      cy.contains('الفلاتر').should('be.visible');
      
      // تبديل للإنجليزية
      cy.visit('/en/search');
      cy.contains('Advanced Search').should('be.visible');
      cy.get('input[placeholder*="Search"]').should('exist');
      cy.contains('Filters').should('be.visible');
    });

    it('يجب أن تعمل ترجمات نتائج البحث', () => {
      // اختبار البحث بالعربية
      cy.visit('/ar/search');
      cy.get('input[placeholder*="ابحث"]').type('منتج');
      cy.get('button').contains('البحث').click();
      
      // التحقق من ترجمات النتائج
      cy.contains('نتائج البحث').should('be.visible');
      
      // اختبار البحث بالإنجليزية
      cy.visit('/en/search');
      cy.get('input[placeholder*="Search"]').type('product');
      cy.get('button').contains('Search').click();
      
      // التحقق من ترجمات النتائج
      cy.contains('Search results').should('be.visible');
    });
  });

  describe('اختبار ترجمات الفئات', () => {
    it('يجب أن تظهر أسماء الفئات مترجمة', () => {
      // اختبار الفئات بالعربية
      cy.visit('/ar/categories');
      
      const arabicCategories = [
        'الطعام والمشروبات',
        'الأزياء والملابس', 
        'الإلكترونيات',
        'المنزل والحديقة',
        'الجمال والصحة'
      ];
      
      arabicCategories.forEach(category => {
        cy.contains(category).should('be.visible');
      });
      
      // اختبار الفئات بالإنجليزية
      cy.visit('/en/categories');
      
      const englishCategories = [
        'Food & Beverages',
        'Fashion & Clothing',
        'Electronics', 
        'Home & Garden',
        'Beauty & Health'
      ];
      
      englishCategories.forEach(category => {
        cy.contains(category).should('be.visible');
      });
    });
  });

  describe('اختبار ترجمات المتاجر', () => {
    it('يجب أن تظهر ترجمات صفحة المتاجر', () => {
      // اختبار بالعربية
      cy.visit('/ar/stores');
      cy.contains('المتاجر').should('be.visible');
      cy.contains('المتاجر القريبة').should('be.visible');
      
      // اختبار بالإنجليزية  
      cy.visit('/en/stores');
      cy.contains('Stores').should('be.visible');
      cy.contains('Nearby Stores').should('be.visible');
    });
  });

  describe('اختبار ترجمات المنتجات', () => {
    it('يجب أن تظهر ترجمات صفحة المنتجات', () => {
      // اختبار بالعربية
      cy.visit('/ar/products');
      cy.contains('جميع المنتجات').should('be.visible');
      
      // اختبار بالإنجليزية
      cy.visit('/en/products');  
      cy.contains('Browse All Products').should('be.visible');
    });
  });

  describe('اختبار ترجمات التسجيل', () => {
    it('يجب أن تظهر ترجمات صفحة اختيار نوع المستخدم', () => {
      // اختبار بالعربية
      cy.visit('/ar/user-type-selection');
      cy.contains('مرحباً بك في مِخْلاة').should('be.visible');
      cy.contains('عميل').should('be.visible');
      cy.contains('تاجر').should('be.visible');
      cy.contains('مندوب').should('be.visible');
      
      // اختبار بالإنجليزية
      cy.visit('/en/user-type-selection');
      cy.contains('Welcome to Mikhla').should('be.visible');
      cy.contains('Customer').should('be.visible');
      cy.contains('Merchant').should('be.visible');
      cy.contains('Representative').should('be.visible');
    });
  });

  describe('اختبار ترجمات الأخطاء والحالات', () => {
    it('يجب أن تظهر رسائل الخطأ مترجمة', () => {
      // اختبار صفحة غير موجودة
      cy.visit('/ar/non-existent-page', { failOnStatusCode: false });
      // التحقق من وجود رسالة خطأ مترجمة
      cy.get('body').should('contain.text', 'خطأ');
      
      cy.visit('/en/non-existent-page', { failOnStatusCode: false });
      cy.get('body').should('contain.text', 'Error');
    });
  });

  describe('اختبار ترجمات الأزرار والعناصر التفاعلية', () => {
    it('يجب أن تكون الأزرار مترجمة بشكل صحيح', () => {
      // اختبار الصفحة الرئيسية بالعربية
      cy.visit('/ar');
      cy.contains('ابدأ الآن').should('be.visible');
      cy.contains('تصفح المتاجر').should('be.visible');

      // اختبار الصفحة الرئيسية بالإنجليزية
      cy.visit('/en');
      cy.contains('Get Started').should('be.visible');
      cy.contains('Browse Stores').should('be.visible');
    });
  });

  describe('اختبار ترجمات صفحة المتاجر', () => {
    it('يجب أن تظهر ترجمة عدد المتاجر بشكل صحيح', () => {
      // اختبار بالعربية
      cy.visit('/ar/stores');
      cy.contains('المتاجر').should('be.visible');
      cy.contains('اكتشف المتاجر المحلية').should('be.visible');

      // التحقق من وجود نص عدد المتاجر (قد يكون 0 إذا لم تكن هناك متاجر)
      cy.get('[data-testid="stores-count"]', { timeout: 10000 }).should('exist');

      // اختبار بالإنجليزية
      cy.visit('/en/stores');
      cy.contains('Stores').should('be.visible');
      cy.contains('Discover local stores').should('be.visible');

      // التحقق من وجود نص عدد المتاجر بالإنجليزية
      cy.get('[data-testid="stores-count"]', { timeout: 10000 }).should('exist');
    });
  });

  describe('اختبار ترجمات التنقل', () => {
    it('يجب أن تكون عناصر التنقل مترجمة', () => {
      // اختبار التنقل بالعربية
      cy.visit('/ar');
      cy.contains('الرئيسية').should('be.visible');
      cy.contains('خطط الاشتراك').should('be.visible');
      
      // اختبار التنقل بالإنجليزية
      cy.visit('/en');
      cy.contains('Home').should('be.visible');
      cy.contains('Subscription Plans').should('be.visible');
    });
  });
});
