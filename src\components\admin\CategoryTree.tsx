'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLocale } from '@/hooks/use-locale';
import { 
  FolderTree, 
  Folder, 
  FolderOpen,
  MoreHorizontal,
  Edit,
  Trash2,
  Plus,
  GripVertical,
  Eye
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Category {
  id: string;
  name: string;
  nameAr: string;
  nameEn: string;
  description?: string;
  icon?: string;
  imageUrl?: string;
  parentId?: string;
  order: number;
  isActive: boolean;
  productCount: number;
  children?: Category[];
}

interface CategoryTreeProps {
  onEditCategory: (category: Category) => void;
}

export function CategoryTree({ onEditCategory }: CategoryTreeProps) {
  const { t } = useLocale();
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // بيانات تجريبية للفئات
  const mockCategories: Category[] = [
    {
      id: '1',
      name: 'الإلكترونيات',
      nameAr: 'الإلكترونيات',
      nameEn: 'Electronics',
      description: 'جميع المنتجات الإلكترونية',
      order: 1,
      isActive: true,
      productCount: 245,
      children: [
        {
          id: '1-1',
          name: 'الهواتف الذكية',
          nameAr: 'الهواتف الذكية',
          nameEn: 'Smartphones',
          parentId: '1',
          order: 1,
          isActive: true,
          productCount: 89
        },
        {
          id: '1-2',
          name: 'أجهزة الكمبيوتر',
          nameAr: 'أجهزة الكمبيوتر',
          nameEn: 'Computers',
          parentId: '1',
          order: 2,
          isActive: true,
          productCount: 156
        }
      ]
    },
    {
      id: '2',
      name: 'الأزياء',
      nameAr: 'الأزياء',
      nameEn: 'Fashion',
      description: 'ملابس وإكسسوارات',
      order: 2,
      isActive: true,
      productCount: 432,
      children: [
        {
          id: '2-1',
          name: 'ملابس رجالية',
          nameAr: 'ملابس رجالية',
          nameEn: 'Men\'s Clothing',
          parentId: '2',
          order: 1,
          isActive: true,
          productCount: 198
        },
        {
          id: '2-2',
          name: 'ملابس نسائية',
          nameAr: 'ملابس نسائية',
          nameEn: 'Women\'s Clothing',
          parentId: '2',
          order: 2,
          isActive: true,
          productCount: 234
        }
      ]
    },
    {
      id: '3',
      name: 'المنزل والحديقة',
      nameAr: 'المنزل والحديقة',
      nameEn: 'Home & Garden',
      description: 'أدوات منزلية ونباتات',
      order: 3,
      isActive: true,
      productCount: 178
    },
    {
      id: '4',
      name: 'الرياضة',
      nameAr: 'الرياضة',
      nameEn: 'Sports',
      description: 'معدات رياضية',
      order: 4,
      isActive: false,
      productCount: 67
    }
  ];

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const renderCategory = (category: Category, level: number = 0) => {
    const isExpanded = expandedCategories.has(category.id);
    const isSelected = selectedCategory === category.id;
    const hasChildren = category.children && category.children.length > 0;

    return (
      <div key={category.id} className="space-y-1">
        <div
          className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-colors ${
            isSelected 
              ? 'bg-blue-100 border border-blue-200' 
              : 'hover:bg-gray-50'
          }`}
          style={{ paddingLeft: `${level * 20 + 8}px` }}
          onClick={() => setSelectedCategory(category.id)}
        >
          {/* مؤشر السحب */}
          <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />

          {/* أيقونة التوسيع/الطي */}
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(category.id);
              }}
            >
              {isExpanded ? (
                <FolderOpen className="h-4 w-4" />
              ) : (
                <Folder className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <div className="w-6 h-6 flex items-center justify-center">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
          )}

          {/* اسم الفئة */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 truncate">
                {category.name}
              </span>
              {!category.isActive && (
                <Badge variant="secondary" className="text-xs">
                  غير نشط
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>{category.productCount} منتج</span>
              {category.description && (
                <>
                  <span>•</span>
                  <span className="truncate">{category.description}</span>
                </>
              )}
            </div>
          </div>

          {/* قائمة الإجراءات */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEditCategory(category)}>
                <Edit className="h-4 w-4 mr-2" />
                تعديل
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <Plus className="h-4 w-4 mr-2" />
                إضافة فئة فرعية
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                عرض المنتجات
              </DropdownMenuItem>
              
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                حذف
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* الفئات الفرعية */}
        {hasChildren && isExpanded && (
          <div className="space-y-1">
            {category.children!.map(child => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FolderTree className="h-5 w-5" />
            شجرة الفئات
          </CardTitle>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setExpandedCategories(new Set(mockCategories.map(c => c.id)))}
          >
            توسيع الكل
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="p-4">
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {mockCategories.map(category => renderCategory(category))}
        </div>

        {/* إضافة فئة جديدة */}
        <div className="mt-4 pt-4 border-t">
          <Button
            variant="outline"
            className="w-full flex items-center gap-2"
            onClick={() => onEditCategory({} as Category)}
          >
            <Plus className="h-4 w-4" />
            إضافة فئة رئيسية جديدة
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
