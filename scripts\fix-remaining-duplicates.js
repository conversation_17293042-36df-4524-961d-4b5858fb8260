#!/usr/bin/env node

/**
 * سكريبت إصلاح التكرارات المتبقية في ملف الترجمة الإنجليزي
 */

const fs = require('fs');
const path = require('path');

const EN_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'en.json');

/**
 * إصلاح التكرارات المتبقية في الملف الإنجليزي
 */
function fixRemainingEnglishDuplicates() {
  console.log('🔧 إصلاح التكرارات المتبقية في الملف الإنجليزي...');
  
  try {
    // قراءة الملف
    const content = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const data = JSON.parse(content);
    
    // إنشاء نسخة احتياطية
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${EN_TRANSLATIONS_PATH}.backup.${timestamp}`;
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    
    let fixes = 0;
    
    // إصلاح التكرارات المحددة
    const duplicatesToFix = [
      'profile', 'representative', 'dashboard', 'orders', 'earnings',
      'loading', 'active', 'inactive', 'available', 'unavailable',
      'totalDeliveries', 'monthlyEarnings', 'totalEarnings', 
      'averageRating', 'reviews', 'minutes'
    ];
    
    duplicatesToFix.forEach(key => {
      if (data[key] && data.representative && data.representative[key]) {
        console.log(`   ❌ إزالة تكرار: representative.${key}`);
        delete data.representative[key];
        fixes++;
      }
    });
    
    // إصلاح تكرار representative.representative
    if (data.representative && data.representative.representative) {
      console.log(`   ❌ إزالة تكرار: representative.representative`);
      Object.assign(data.representative, data.representative.representative);
      delete data.representative.representative;
      fixes++;
    }
    
    // كتابة الملف المصلح
    const cleanedContent = JSON.stringify(data, null, 2);
    fs.writeFileSync(EN_TRANSLATIONS_PATH, cleanedContent);
    
    console.log(`   ✅ تم إصلاح ${fixes} تكرار`);
    
    // التحقق من صحة JSON
    JSON.parse(fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8'));
    console.log(`   ✅ الملف صحيح بعد الإصلاح`);
    
    return { success: true, fixesApplied: fixes };
    
  } catch (error) {
    console.error(`   ❌ خطأ في الإصلاح:`, error.message);
    return { success: false, fixesApplied: 0 };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧹 بدء إصلاح التكرارات المتبقية...\n');
  
  const result = fixRemainingEnglishDuplicates();
  
  console.log('');
  
  if (result.success) {
    console.log('🎉 تم إصلاح التكرارات المتبقية بنجاح!');
    console.log(`📊 إجمالي الإصلاحات: ${result.fixesApplied}`);
    
    // تشغيل التحقق النهائي
    console.log('\n🔍 تشغيل التحقق النهائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في إصلاح التكرارات المتبقية');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  fixRemainingEnglishDuplicates
};
