#!/usr/bin/env node

/**
 * التحقق النهائي من إصلاح مشكلة الترجمات
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * محاكاة دالة الترجمة
 */
function t(key, params = {}, translations) {
  let text = translations[key] || key;
  
  // استبدال المتغيرات
  Object.keys(params).forEach(param => {
    const placeholder = `{{${param}}}`;
    text = text.replace(new RegExp(placeholder, 'g'), params[param]);
  });
  
  return text;
}

/**
 * التحقق من مشكلة storesFound المحددة
 */
function verifyStoresFoundFix() {
  console.log('🔍 التحقق النهائي من إصلاح مشكلة "storesFound"...\n');
  
  // قراءة ملفات الترجمة
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    return false;
  }
  
  let allTestsPassed = true;
  
  // اختبار 1: التحقق من وجود المفتاح
  console.log('📝 اختبار 1: التحقق من وجود مفتاح "storesFound"');
  
  if (!arTranslations.storesFound) {
    console.log('❌ مفتاح "storesFound" غير موجود في الترجمة العربية');
    allTestsPassed = false;
  } else {
    console.log('✅ مفتاح "storesFound" موجود في الترجمة العربية');
  }
  
  if (!enTranslations.storesFound) {
    console.log('❌ مفتاح "storesFound" غير موجود في الترجمة الإنجليزية');
    allTestsPassed = false;
  } else {
    console.log('✅ مفتاح "storesFound" موجود في الترجمة الإنجليزية');
  }
  
  console.log('');
  
  // اختبار 2: التحقق من صحة النص العربي
  console.log('📝 اختبار 2: التحقق من صحة النص العربي');
  const arText = arTranslations.storesFound;
  
  if (arText.includes('{{count}}')) {
    console.log(`✅ النص العربي يحتوي على متغير {{count}}: "${arText}"`);
  } else {
    console.log(`❌ النص العربي لا يحتوي على متغير {{count}}: "${arText}"`);
    allTestsPassed = false;
  }
  
  // اختبار 3: التحقق من صحة النص الإنجليزي
  console.log('📝 اختبار 3: التحقق من صحة النص الإنجليزي');
  const enText = enTranslations.storesFound;
  
  if (enText.includes('{{count}}')) {
    console.log(`✅ النص الإنجليزي يحتوي على متغير {{count}}: "${enText}"`);
  } else {
    console.log(`❌ النص الإنجليزي لا يحتوي على متغير {{count}}: "${enText}"`);
    allTestsPassed = false;
  }
  
  console.log('');
  
  // اختبار 4: محاكاة الاستخدام الفعلي
  console.log('📝 اختبار 4: محاكاة الاستخدام الفعلي');
  
  const testCases = [
    { count: 0, expectedAr: 'تم العثور على 0 متجر', expectedEn: 'Found 0 stores' },
    { count: 1, expectedAr: 'تم العثور على 1 متجر', expectedEn: 'Found 1 stores' },
    { count: 5, expectedAr: 'تم العثور على 5 متجر', expectedEn: 'Found 5 stores' },
    { count: 25, expectedAr: 'تم العثور على 25 متجر', expectedEn: 'Found 25 stores' }
  ];
  
  testCases.forEach(testCase => {
    const arResult = t('storesFound', { count: testCase.count }, arTranslations);
    const enResult = t('storesFound', { count: testCase.count }, enTranslations);
    
    const arPassed = arResult === testCase.expectedAr;
    const enPassed = enResult === testCase.expectedEn;
    
    console.log(`   العدد ${testCase.count}:`);
    console.log(`     ${arPassed ? '✅' : '❌'} العربية: "${arResult}"`);
    console.log(`     ${enPassed ? '✅' : '❌'} الإنجليزية: "${enResult}"`);
    
    if (!arPassed || !enPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log('');
  
  // اختبار 5: التحقق من عدم وجود مفاتيح مكررة
  console.log('📝 اختبار 5: التحقق من عدم وجود مفاتيح مكررة لـ "storesFound"');
  
  const files = [
    { path: AR_TRANSLATIONS_PATH, name: 'العربي' },
    { path: EN_TRANSLATIONS_PATH, name: 'الإنجليزي' }
  ];
  
  files.forEach(file => {
    try {
      const content = fs.readFileSync(file.path, 'utf8');
      const lines = content.split('\n');
      const storesFoundLines = [];
      
      lines.forEach((line, index) => {
        if (line.includes('"storesFound"')) {
          storesFoundLines.push(index + 1);
        }
      });
      
      if (storesFoundLines.length === 1) {
        console.log(`   ✅ الملف ${file.name}: مفتاح واحد فقط في السطر ${storesFoundLines[0]}`);
      } else if (storesFoundLines.length > 1) {
        console.log(`   ❌ الملف ${file.name}: ${storesFoundLines.length} مفاتيح مكررة في الأسطر: ${storesFoundLines.join(', ')}`);
        allTestsPassed = false;
      } else {
        console.log(`   ❌ الملف ${file.name}: لا يحتوي على مفتاح "storesFound"`);
        allTestsPassed = false;
      }
    } catch (error) {
      console.log(`   ❌ خطأ في فحص الملف ${file.name}: ${error.message}`);
      allTestsPassed = false;
    }
  });
  
  return allTestsPassed;
}

/**
 * التحقق من حالة البناء
 */
function verifyBuildStatus() {
  console.log('\n🏗️  التحقق من حالة البناء...');
  
  const nextDir = path.join(__dirname, '../.next');
  
  if (fs.existsSync(nextDir)) {
    console.log('✅ مجلد .next موجود - البناء تم بنجاح');
    
    // التحقق من وجود ملفات البناء الأساسية
    const buildManifest = path.join(nextDir, 'build-manifest.json');
    if (fs.existsSync(buildManifest)) {
      console.log('✅ ملف build-manifest.json موجود');
      return true;
    } else {
      console.log('❌ ملف build-manifest.json غير موجود');
      return false;
    }
  } else {
    console.log('❌ مجلد .next غير موجود - البناء لم يتم');
    return false;
  }
}

/**
 * إنشاء تقرير نهائي
 */
function generateFinalReport(storesFoundPassed, buildPassed) {
  console.log('\n📋 التقرير النهائي:');
  console.log('=====================================');
  
  console.log(`🎯 إصلاح مشكلة "storesFound": ${storesFoundPassed ? '✅ نجح' : '❌ فشل'}`);
  console.log(`🏗️  حالة البناء: ${buildPassed ? '✅ نجح' : '❌ فشل'}`);
  
  if (storesFoundPassed && buildPassed) {
    console.log('\n🎉 ممتاز! تم إصلاح المشكلة بنجاح!');
    console.log('\n📝 الخطوات التالية:');
    console.log('1. تشغيل خادم التطوير: npm run dev');
    console.log('2. اختبار الصفحة في المتصفح');
    console.log('3. مسح cache المتصفح (Ctrl+Shift+R)');
    console.log('4. التحقق من أن النص يظهر بشكل صحيح');
    
    console.log('\n💡 نصائح إضافية:');
    console.log('- استخدم أدوات المطور للتحقق من الترجمات');
    console.log('- اختبر تبديل اللغات');
    console.log('- تحقق من أن جميع المتغيرات تعمل');
    
  } else {
    console.log('\n⚠️  لا تزال هناك مشاكل تحتاج إلى إصلاح');
    
    if (!storesFoundPassed) {
      console.log('\n🔧 مشاكل الترجمة:');
      console.log('- تحقق من ملفات الترجمة يدوياً');
      console.log('- تأكد من عدم وجود مفاتيح مكررة');
      console.log('- تحقق من صحة المتغيرات');
    }
    
    if (!buildPassed) {
      console.log('\n🔧 مشاكل البناء:');
      console.log('- شغل: npm run build');
      console.log('- تحقق من رسائل الخطأ');
      console.log('- تأكد من صحة ملفات الترجمة JSON');
    }
  }
  
  console.log('\n📞 للمساعدة:');
  console.log('- شغل: node scripts/validate-translations.js');
  console.log('- شغل: node scripts/test-stores-found.js');
  console.log('- راجع: docs/translation-fix-summary.md');
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔍 التحقق النهائي من إصلاح مشكلة الترجمات');
  console.log('=============================================\n');
  
  const storesFoundPassed = verifyStoresFoundFix();
  const buildPassed = verifyBuildStatus();
  
  generateFinalReport(storesFoundPassed, buildPassed);
  
  // إرجاع كود الخروج المناسب
  process.exit(storesFoundPassed && buildPassed ? 0 : 1);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  verifyStoresFoundFix,
  verifyBuildStatus,
  generateFinalReport,
  t
};
