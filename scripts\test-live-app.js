#!/usr/bin/env node

/**
 * اختبار التطبيق المباشر للتحقق من عمل الترجمات
 */

const puppeteer = require('puppeteer');

const APP_URL = 'http://localhost:9002';

/**
 * اختبار الصفحة الرئيسية
 */
async function testHomePage(page) {
  console.log('🏠 اختبار الصفحة الرئيسية...');
  
  try {
    await page.goto(`${APP_URL}/ar`);
    await page.waitForSelector('body', { timeout: 10000 });
    
    // التحقق من وجود النصوص العربية
    const arabicTexts = await page.evaluate(() => {
      const body = document.body.innerText;
      return {
        hasArabic: /[\u0600-\u06FF]/.test(body),
        hasWelcome: body.includes('مرحباً') || body.includes('أهلاً'),
        hasDirection: document.documentElement.dir === 'rtl'
      };
    });
    
    if (arabicTexts.hasArabic && arabicTexts.hasDirection) {
      console.log('✅ الصفحة الرئيسية العربية تعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في الصفحة الرئيسية العربية');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في اختبار الصفحة الرئيسية:', error.message);
    return false;
  }
}

/**
 * اختبار صفحة البحث
 */
async function testSearchPage(page) {
  console.log('🔍 اختبار صفحة البحث...');
  
  try {
    await page.goto(`${APP_URL}/ar/search`);
    await page.waitForSelector('body', { timeout: 10000 });
    
    // البحث عن عناصر البحث
    const searchElements = await page.evaluate(() => {
      const body = document.body.innerText;
      return {
        hasSearchText: body.includes('البحث') || body.includes('ابحث'),
        hasSearchInput: document.querySelector('input[type="search"], input[placeholder*="بحث"], input[placeholder*="ابحث"]') !== null,
        hasFilters: body.includes('فلتر') || body.includes('تصفية')
      };
    });
    
    if (searchElements.hasSearchText) {
      console.log('✅ صفحة البحث العربية تعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في صفحة البحث العربية');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في اختبار صفحة البحث:', error.message);
    return false;
  }
}

/**
 * اختبار تبديل اللغة
 */
async function testLanguageSwitch(page) {
  console.log('🌐 اختبار تبديل اللغة...');
  
  try {
    await page.goto(`${APP_URL}/ar`);
    await page.waitForSelector('body', { timeout: 10000 });
    
    // البحث عن زر تبديل اللغة
    const languageButton = await page.$('button:contains("EN"), a[href*="/en"], [aria-label*="English"]');
    
    if (languageButton) {
      await languageButton.click();
      await page.waitForNavigation({ timeout: 5000 });
      
      const currentUrl = page.url();
      if (currentUrl.includes('/en')) {
        console.log('✅ تبديل اللغة يعمل بشكل صحيح');
        return true;
      }
    }
    
    console.log('❌ مشكلة في تبديل اللغة');
    return false;
  } catch (error) {
    console.log('❌ خطأ في اختبار تبديل اللغة:', error.message);
    return false;
  }
}

/**
 * اختبار الترجمات الإنجليزية
 */
async function testEnglishTranslations(page) {
  console.log('🇺🇸 اختبار الترجمات الإنجليزية...');
  
  try {
    await page.goto(`${APP_URL}/en`);
    await page.waitForSelector('body', { timeout: 10000 });
    
    const englishTexts = await page.evaluate(() => {
      const body = document.body.innerText;
      return {
        hasEnglish: /[a-zA-Z]/.test(body),
        hasWelcome: body.includes('Welcome') || body.includes('Hello'),
        hasDirection: document.documentElement.dir === 'ltr' || !document.documentElement.dir
      };
    });
    
    if (englishTexts.hasEnglish && englishTexts.hasDirection) {
      console.log('✅ الترجمات الإنجليزية تعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في الترجمات الإنجليزية');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في اختبار الترجمات الإنجليزية:', error.message);
    return false;
  }
}

/**
 * اختبار صفحة البحث الإنجليزية
 */
async function testEnglishSearchPage(page) {
  console.log('🔍 اختبار صفحة البحث الإنجليزية...');
  
  try {
    await page.goto(`${APP_URL}/en/search`);
    await page.waitForSelector('body', { timeout: 10000 });
    
    const searchElements = await page.evaluate(() => {
      const body = document.body.innerText;
      return {
        hasAdvancedSearch: body.includes('Advanced Search'),
        hasSearchPlaceholder: body.includes('Search products'),
        hasFilters: body.includes('Filters') || body.includes('Filter')
      };
    });
    
    if (searchElements.hasAdvancedSearch || searchElements.hasSearchPlaceholder) {
      console.log('✅ صفحة البحث الإنجليزية تعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في صفحة البحث الإنجليزية');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في اختبار صفحة البحث الإنجليزية:', error.message);
    return false;
  }
}

/**
 * اختبار صفحة الفئات
 */
async function testCategoriesPage(page) {
  console.log('🏷️ اختبار صفحة الفئات...');
  
  try {
    await page.goto(`${APP_URL}/en/categories`);
    await page.waitForSelector('body', { timeout: 10000 });
    
    const categoryElements = await page.evaluate(() => {
      const body = document.body.innerText;
      return {
        hasBrowseCategories: body.includes('Browse by Categories'),
        hasFoodBeverages: body.includes('Food & Beverages'),
        hasFashion: body.includes('Fashion & Clothing'),
        hasElectronics: body.includes('Electronics')
      };
    });
    
    const categoriesFound = Object.values(categoryElements).filter(Boolean).length;
    
    if (categoriesFound >= 2) {
      console.log('✅ صفحة الفئات تعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في صفحة الفئات');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في اختبار صفحة الفئات:', error.message);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🚀 بدء اختبار التطبيق المباشر...\n');
  console.log('=' .repeat(50));
  
  let browser;
  try {
    // تشغيل المتصفح
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    
    // تشغيل الاختبارات
    const tests = [
      () => testHomePage(page),
      () => testSearchPage(page),
      () => testLanguageSwitch(page),
      () => testEnglishTranslations(page),
      () => testEnglishSearchPage(page),
      () => testCategoriesPage(page)
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (let i = 0; i < tests.length; i++) {
      try {
        if (await tests[i]()) {
          passedTests++;
        }
      } catch (error) {
        console.log(`❌ خطأ في الاختبار ${i + 1}:`, error.message);
      }
      console.log(''); // سطر فارغ بين الاختبارات
    }
    
    // النتائج النهائية
    console.log('=' .repeat(50));
    console.log('📋 ملخص نتائج اختبار التطبيق المباشر:');
    console.log(`   🧪 الاختبارات المكتملة: ${passedTests}/${totalTests}`);
    console.log(`   📊 معدل نجاح الاختبارات: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 جميع اختبارات التطبيق المباشر نجحت!');
      console.log('✨ الترجمات تعمل بشكل مثالي في التطبيق الفعلي.');
    } else {
      console.log(`\n⚠️  ${totalTests - passedTests} اختبار فشل في التطبيق المباشر.`);
      console.log('🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها.');
    }
    
  } catch (error) {
    console.log('❌ خطأ عام في اختبار التطبيق:', error.message);
    console.log('💡 تأكد من أن التطبيق يعمل على http://localhost:9002');
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// تشغيل الاختبار
if (require.main === module) {
  main();
}

module.exports = {
  testHomePage,
  testSearchPage,
  testLanguageSwitch,
  testEnglishTranslations
};
