"use client";

import { memo } from 'react';
import { Marker } from 'pigeon-maps';
import { Store, MapPin, ShoppingBag, Coffee, Pill, Car, Utensils, Shirt } from 'lucide-react';
import type { StoreDocument } from '@/types';

interface StoreWithDistance extends StoreDocument {
  distance: number;
  isOpen: boolean;
  estimatedTime: number;
}

interface StoreMarkerProps {
  store: StoreWithDistance;
  isSelected: boolean;
  onClick: () => void;
}

const StoreMarker = memo<StoreMarkerProps>(({ store, isSelected, onClick }) => {
  if (!store.location?.latitude || !store.location?.longitude) {
    return null;
  }

  // Get category icon
  const getCategoryIcon = (category: string) => {
    const iconClass = "w-4 h-4 text-white";
    
    switch (category?.toLowerCase()) {
      case 'بقالة':
      case 'grocery':
      case 'supermarket':
        return <ShoppingBag className={iconClass} />;
      case 'مطعم':
      case 'restaurant':
      case 'food':
        return <Utensils className={iconClass} />;
      case 'مقهى':
      case 'cafe':
      case 'coffee':
        return <Coffee className={iconClass} />;
      case 'صيدلية':
      case 'pharmacy':
        return <Pill className={iconClass} />;
      case 'ملابس':
      case 'clothing':
      case 'fashion':
        return <Shirt className={iconClass} />;
      case 'سيارات':
      case 'automotive':
        return <Car className={iconClass} />;
      default:
        return <Store className={iconClass} />;
    }
  };

  // Get marker color based on store status and selection
  const getMarkerColor = () => {
    if (isSelected) {
      return "hsl(var(--primary))";
    }
    
    if (!store.isOpen) {
      return "hsl(var(--muted-foreground))";
    }
    
    // Color by category
    switch (store.category?.toLowerCase()) {
      case 'بقالة':
      case 'grocery':
        return "#10b981"; // green
      case 'مطعم':
      case 'restaurant':
        return "#f59e0b"; // amber
      case 'مقهى':
      case 'cafe':
        return "#8b5cf6"; // violet
      case 'صيدلية':
      case 'pharmacy':
        return "#ef4444"; // red
      case 'ملابس':
      case 'clothing':
        return "#ec4899"; // pink
      case 'سيارات':
      case 'automotive':
        return "#6b7280"; // gray
      default:
        return "hsl(var(--secondary))";
    }
  };

  // Custom marker component
  const CustomMarker = () => (
    <div
      className={`
        relative cursor-pointer transform transition-all duration-200 hover:scale-110
        ${isSelected ? 'scale-125 z-50' : 'z-10'}
      `}
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
    >
      {/* Main marker circle */}
      <div
        className={`
          w-10 h-10 rounded-full border-2 border-white shadow-lg flex items-center justify-center
          ${isSelected ? 'ring-2 ring-primary ring-offset-2' : ''}
          ${!store.isOpen ? 'opacity-60' : ''}
        `}
        style={{ backgroundColor: getMarkerColor() }}
      >
        {getCategoryIcon(store.category || '')}
      </div>

      {/* Store status indicator */}
      {!store.isOpen && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border border-white"></div>
      )}

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
          <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-primary"></div>
        </div>
      )}

      {/* Hover tooltip */}
      <div
        className={`
          absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 
          bg-black/80 text-white text-xs rounded whitespace-nowrap pointer-events-none
          transition-opacity duration-200 z-50
          ${isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
        `}
      >
        <div className="font-medium">{store.name}</div>
        {store.distance > 0 && (
          <div className="text-xs opacity-80">
            {store.distance.toFixed(1)} كم • {store.estimatedTime} دقيقة
          </div>
        )}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2">
          <div className="w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-black/80"></div>
        </div>
      </div>
    </div>
  );

  return (
    <Marker
      width={40}
      anchor={[store.location.latitude, store.location.longitude]}
      onClick={onClick}
    >
      <CustomMarker />
    </Marker>
  );
});

StoreMarker.displayName = 'StoreMarker';

export default StoreMarker;
