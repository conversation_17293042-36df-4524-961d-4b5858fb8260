import React from 'react'
import { Logo } from './Logo'

describe('Logo Component', () => {
  it('يعرض الشعار الافتراضي بشكل صحيح', () => {
    cy.mount(<Logo />)
    cy.get('svg').should('be.visible')
    cy.get('svg').should('have.attr', 'viewBox', '0 0 200 60')
  })

  it('يعرض النص عند تمرير showText', () => {
    cy.mount(<Logo showText />)
    cy.get('svg').should('be.visible')
    cy.contains('مِخْلاة').should('be.visible')
  })

  it('يطبق المتغيرات المختلفة بشكل صحيح', () => {
    cy.mount(
      <div className="space-y-4">
        <Logo variant="simple" data-testid="simple" />
        <Logo variant="animated" data-testid="animated" />
        <Logo variant="brand" data-testid="brand" />
      </div>
    )

    cy.get('[data-testid="simple"]').should('be.visible')
    cy.get('[data-testid="animated"]').should('be.visible')
    cy.get('[data-testid="brand"]').should('be.visible')
  })

  it('يطبق الأحجام المختلفة بشكل صحيح', () => {
    cy.mount(
      <div className="space-y-4">
        <Logo size="sm" data-testid="small" />
        <Logo size="md" data-testid="medium" />
        <Logo size="lg" data-testid="large" />
      </div>
    )

    cy.get('[data-testid="small"] svg').should('have.class', 'h-8')
    cy.get('[data-testid="medium"] svg').should('have.class', 'h-12')
    cy.get('[data-testid="large"] svg').should('have.class', 'h-16')
  })

  it('يدعم الفئات المخصصة', () => {
    cy.mount(<Logo className="custom-logo-class" />)
    cy.get('svg').should('have.class', 'custom-logo-class')
  })

  it('يعمل كرابط عند تمرير href', () => {
    cy.mount(<Logo href="/" />)
    cy.get('a').should('have.attr', 'href', '/')
    cy.get('a svg').should('be.visible')
  })

  it('يدعم الألوان المختلفة', () => {
    cy.mount(
      <div className="space-y-4">
        <Logo color="primary" data-testid="primary" />
        <Logo color="white" data-testid="white" />
        <Logo color="dark" data-testid="dark" />
      </div>
    )

    cy.get('[data-testid="primary"]').should('be.visible')
    cy.get('[data-testid="white"]').should('be.visible')
    cy.get('[data-testid="dark"]').should('be.visible')
  })

  it('يعرض الحركة عند استخدام variant="animated"', () => {
    cy.mount(<Logo variant="animated" />)
    cy.get('svg').should('be.visible')
    // التحقق من وجود عناصر الحركة
    cy.get('svg').within(() => {
      cy.get('*').should('exist')
    })
  })

  it('يستجيب للنقر عند استخدامه كرابط', () => {
    const onClickSpy = cy.spy().as('onClickSpy')
    cy.mount(<Logo href="/" onClick={onClickSpy} />)
    cy.get('a').click()
    cy.get('@onClickSpy').should('have.been.called')
  })

  it('يدعم خصائص SVG المخصصة', () => {
    cy.mount(<Logo width={100} height={50} />)
    cy.get('svg').should('have.attr', 'width', '100')
    cy.get('svg').should('have.attr', 'height', '50')
  })
})
