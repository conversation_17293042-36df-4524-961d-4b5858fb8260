/**
 * خدمة إعدادات التطابق الديناميكية - المرحلة الأولى
 * تعديل معايير التطابق حسب نوع المستند والسياق
 */

export interface DocumentTypeConfig {
  nameWeight: number;
  businessNameWeight: number;
  registrationNumberWeight: number;
  addressWeight: number;
  minThreshold: number;
  maxThreshold: number;
  preferredAlgorithms: string[];
  riskFactors: string[];
}

export interface DynamicMatchingConfig {
  documentTypes: {
    [key: string]: DocumentTypeConfig;
  };
  contextualRules: ContextualRule[];
  adaptiveThresholds: AdaptiveThreshold[];
  learningPatterns: LearningPattern[];
}

export interface ContextualRule {
  id: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
}

export interface AdaptiveThreshold {
  field: string;
  baseThreshold: number;
  adjustments: ThresholdAdjustment[];
}

export interface ThresholdAdjustment {
  condition: string;
  modifier: number;
  reason: string;
}

export interface LearningPattern {
  pattern: string;
  successRate: number;
  confidence: number;
  lastUpdated: Date;
  usageCount: number;
}

export class DynamicMatchingConfigService {
  
  private static config: DynamicMatchingConfig = {
    documentTypes: {
      'commercial_registration': {
        nameWeight: 0.4,
        businessNameWeight: 0.35,
        registrationNumberWeight: 0.2,
        addressWeight: 0.05,
        minThreshold: 80,
        maxThreshold: 95,
        preferredAlgorithms: ['fuzzy', 'jaro-winkler', 'levenshtein'],
        riskFactors: ['expired_document', 'name_mismatch', 'duplicate_registration']
      },
      'freelance_document': {
        nameWeight: 0.5,
        businessNameWeight: 0.25,
        registrationNumberWeight: 0.15,
        addressWeight: 0.1,
        minThreshold: 75,
        maxThreshold: 90,
        preferredAlgorithms: ['jaro-winkler', 'fuzzy', 'soundex'],
        riskFactors: ['expired_document', 'name_mismatch', 'unclear_activity']
      },
      'driving_license': {
        nameWeight: 0.6,
        businessNameWeight: 0,
        registrationNumberWeight: 0.3,
        addressWeight: 0.1,
        minThreshold: 85,
        maxThreshold: 95,
        preferredAlgorithms: ['levenshtein', 'jaro-winkler'],
        riskFactors: ['expired_license', 'name_mismatch', 'invalid_category']
      },
      'vehicle_inspection': {
        nameWeight: 0.4,
        businessNameWeight: 0,
        registrationNumberWeight: 0.5,
        addressWeight: 0.1,
        minThreshold: 80,
        maxThreshold: 90,
        preferredAlgorithms: ['fuzzy', 'levenshtein'],
        riskFactors: ['expired_inspection', 'vehicle_mismatch']
      }
    },
    contextualRules: [],
    adaptiveThresholds: [],
    learningPatterns: []
  };
  
  /**
   * الحصول على إعدادات نوع المستند
   */
  static getDocumentTypeConfig(documentType: string): DocumentTypeConfig {
    return this.config.documentTypes[documentType] || this.getDefaultConfig();
  }
  
  /**
   * الإعدادات الافتراضية
   */
  private static getDefaultConfig(): DocumentTypeConfig {
    return {
      nameWeight: 0.4,
      businessNameWeight: 0.3,
      registrationNumberWeight: 0.2,
      addressWeight: 0.1,
      minThreshold: 80,
      maxThreshold: 95,
      preferredAlgorithms: ['fuzzy', 'jaro-winkler', 'levenshtein'],
      riskFactors: ['general_mismatch']
    };
  }
  
  /**
   * حساب العتبة الديناميكية
   */
  static calculateDynamicThreshold(
    documentType: string,
    context: {
      documentQuality?: number;
      userHistory?: number;
      timeOfDay?: number;
      systemLoad?: number;
    }
  ): number {
    const config = this.getDocumentTypeConfig(documentType);
    let threshold = config.minThreshold;
    
    // تعديل العتبة حسب جودة المستند
    if (context.documentQuality !== undefined) {
      if (context.documentQuality > 90) {
        threshold -= 5; // تخفيف العتبة للمستندات عالية الجودة
      } else if (context.documentQuality < 70) {
        threshold += 10; // رفع العتبة للمستندات منخفضة الجودة
      }
    }
    
    // تعديل العتبة حسب تاريخ المستخدم
    if (context.userHistory !== undefined) {
      if (context.userHistory > 80) {
        threshold -= 3; // تخفيف للمستخدمين ذوي التاريخ الجيد
      } else if (context.userHistory < 50) {
        threshold += 5; // رفع للمستخدمين ذوي التاريخ السيء
      }
    }
    
    // تعديل العتبة حسب وقت اليوم (ساعات الذروة)
    if (context.timeOfDay !== undefined) {
      const hour = context.timeOfDay;
      if ((hour >= 9 && hour <= 12) || (hour >= 14 && hour <= 17)) {
        threshold += 2; // رفع العتبة في ساعات الذروة
      }
    }
    
    // تعديل العتبة حسب حمولة النظام
    if (context.systemLoad !== undefined) {
      if (context.systemLoad > 80) {
        threshold += 5; // رفع العتبة عند الحمولة العالية
      }
    }
    
    // التأكد من البقاء ضمن الحدود
    return Math.max(config.minThreshold, Math.min(threshold, config.maxThreshold));
  }
  
  /**
   * اختيار أفضل خوارزمية للسياق
   */
  static selectBestAlgorithm(
    documentType: string,
    textLength: number,
    complexity: number
  ): string[] {
    const config = this.getDocumentTypeConfig(documentType);
    let algorithms = [...config.preferredAlgorithms];
    
    // تعديل الخوارزميات حسب طول النص
    if (textLength < 10) {
      // للنصوص القصيرة، فضل الخوارزميات الدقيقة
      algorithms = ['levenshtein', 'jaro-winkler', 'fuzzy'];
    } else if (textLength > 50) {
      // للنصوص الطويلة، فضل الخوارزميات السريعة
      algorithms = ['fuzzy', 'soundex', 'jaro-winkler'];
    }
    
    // تعديل حسب التعقيد
    if (complexity > 0.7) {
      // للنصوص المعقدة، استخدم خوارزميات متقدمة
      algorithms.unshift('fuzzy');
    }
    
    return algorithms;
  }
  
  /**
   * حساب الأوزان الديناميكية
   */
  static calculateDynamicWeights(
    documentType: string,
    fieldConfidences: { [field: string]: number }
  ): { [field: string]: number } {
    const config = this.getDocumentTypeConfig(documentType);
    
    const weights = {
      nameWeight: config.nameWeight,
      businessNameWeight: config.businessNameWeight,
      registrationNumberWeight: config.registrationNumberWeight,
      addressWeight: config.addressWeight
    };
    
    // تعديل الأوزان حسب ثقة الحقول
    const totalConfidence = Object.values(fieldConfidences).reduce((sum, conf) => sum + conf, 0);
    
    if (totalConfidence > 0) {
      for (const [field, confidence] of Object.entries(fieldConfidences)) {
        const fieldWeight = field + 'Weight';
        if (weights[fieldWeight as keyof typeof weights] !== undefined) {
          // زيادة وزن الحقول عالية الثقة
          if (confidence > 90) {
            weights[fieldWeight as keyof typeof weights] *= 1.2;
          } else if (confidence < 70) {
            // تقليل وزن الحقول منخفضة الثقة
            weights[fieldWeight as keyof typeof weights] *= 0.8;
          }
        }
      }
      
      // إعادة تطبيع الأوزان
      const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
      for (const key in weights) {
        weights[key as keyof typeof weights] /= totalWeight;
      }
    }
    
    return weights;
  }
  
  /**
   * تقييم عوامل المخاطر
   */
  static assessRiskFactors(
    documentType: string,
    extractedData: any,
    userData: any
  ): { riskScore: number; factors: string[] } {
    const config = this.getDocumentTypeConfig(documentType);
    let riskScore = 0;
    const factors: string[] = [];
    
    // فحص عوامل المخاطر المحددة لنوع المستند
    for (const riskFactor of config.riskFactors) {
      switch (riskFactor) {
        case 'expired_document':
          if (this.isDocumentExpired(extractedData)) {
            riskScore += 30;
            factors.push('مستند منتهي الصلاحية');
          }
          break;
          
        case 'name_mismatch':
          if (this.hasNameMismatch(extractedData, userData)) {
            riskScore += 25;
            factors.push('عدم تطابق الأسماء');
          }
          break;
          
        case 'duplicate_registration':
          // سيتم فحص هذا في خدمة منفصلة
          break;
          
        case 'unclear_activity':
          if (this.hasUnclearActivity(extractedData)) {
            riskScore += 15;
            factors.push('نشاط غير واضح');
          }
          break;
          
        case 'invalid_category':
          if (this.hasInvalidCategory(extractedData)) {
            riskScore += 20;
            factors.push('فئة غير صالحة');
          }
          break;
      }
    }
    
    // عوامل مخاطر إضافية
    if (this.hasInconsistentData(extractedData)) {
      riskScore += 15;
      factors.push('بيانات غير متسقة');
    }
    
    if (this.hasPoorDocumentQuality(extractedData)) {
      riskScore += 10;
      factors.push('جودة مستند منخفضة');
    }
    
    return { riskScore: Math.min(riskScore, 100), factors };
  }
  
  /**
   * فحص انتهاء صلاحية المستند
   */
  private static isDocumentExpired(extractedData: any): boolean {
    if (!extractedData.expiryDate) return false;
    
    try {
      const expiryDate = new Date(extractedData.expiryDate);
      return expiryDate < new Date();
    } catch {
      return false;
    }
  }
  
  /**
   * فحص عدم تطابق الأسماء
   */
  private static hasNameMismatch(extractedData: any, userData: any): boolean {
    if (!extractedData.ownerName || !userData.displayName) return true;
    
    // استخدام خوارزمية بسيطة للفحص السريع
    const similarity = this.calculateQuickSimilarity(
      extractedData.ownerName.toLowerCase(),
      userData.displayName.toLowerCase()
    );
    
    return similarity < 70;
  }
  
  /**
   * فحص النشاط غير الواضح
   */
  private static hasUnclearActivity(extractedData: any): boolean {
    if (!extractedData.businessActivity) return true;
    
    const activity = extractedData.businessActivity.toLowerCase();
    const unclearKeywords = ['غير محدد', 'متنوع', 'عام', 'مختلف', 'أخرى'];
    
    return unclearKeywords.some(keyword => activity.includes(keyword));
  }
  
  /**
   * فحص الفئة غير الصالحة
   */
  private static hasInvalidCategory(extractedData: any): boolean {
    if (!extractedData.licenseCategory) return false;
    
    const validCategories = ['خاص', 'عام', 'نقل', 'دراجة نارية', 'معدات ثقيلة'];
    return !validCategories.includes(extractedData.licenseCategory);
  }
  
  /**
   * فحص البيانات غير المتسقة
   */
  private static hasInconsistentData(extractedData: any): boolean {
    // فحص تناسق التواريخ
    if (extractedData.issueDate && extractedData.expiryDate) {
      try {
        const issueDate = new Date(extractedData.issueDate);
        const expiryDate = new Date(extractedData.expiryDate);
        if (issueDate >= expiryDate) return true;
      } catch {
        return true;
      }
    }
    
    // فحص تناسق الأرقام
    if (extractedData.registrationNumber && extractedData.registrationNumber.length < 5) {
      return true;
    }
    
    return false;
  }
  
  /**
   * فحص جودة المستند المنخفضة
   */
  private static hasPoorDocumentQuality(extractedData: any): boolean {
    return extractedData.confidence && extractedData.confidence < 80;
  }
  
  /**
   * حساب التشابه السريع
   */
  private static calculateQuickSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 100;
    
    let matches = 0;
    for (let i = 0; i < shorter.length; i++) {
      if (longer.includes(shorter[i])) matches++;
    }
    
    return (matches / longer.length) * 100;
  }
  
  /**
   * تحديث إعدادات التطابق بناءً على النتائج
   */
  static updateConfigBasedOnResults(
    documentType: string,
    success: boolean,
    similarity: number,
    context: any
  ): void {
    // تحديث أنماط التعلم
    const pattern = `${documentType}_${Math.floor(similarity / 10) * 10}`;
    const existingPattern = this.config.learningPatterns.find(p => p.pattern === pattern);
    
    if (existingPattern) {
      existingPattern.usageCount++;
      existingPattern.successRate = (existingPattern.successRate + (success ? 100 : 0)) / 2;
      existingPattern.lastUpdated = new Date();
    } else {
      this.config.learningPatterns.push({
        pattern,
        successRate: success ? 100 : 0,
        confidence: similarity,
        lastUpdated: new Date(),
        usageCount: 1
      });
    }
    
    // تحديث العتبات التكيفية إذا لزم الأمر
    if (!success && similarity > 80) {
      // رفع العتبة إذا فشل قرار كان يبدو جيداً
      const config = this.config.documentTypes[documentType];
      if (config) {
        config.minThreshold = Math.min(config.minThreshold + 2, config.maxThreshold);
      }
    }
  }
  
  /**
   * الحصول على الإعدادات الكاملة
   */
  static getFullConfig(): DynamicMatchingConfig {
    return { ...this.config };
  }
  
  /**
   * تحديث الإعدادات
   */
  static updateConfig(newConfig: Partial<DynamicMatchingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
