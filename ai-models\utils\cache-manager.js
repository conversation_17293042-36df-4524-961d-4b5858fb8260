// مدير التخزين المؤقت الذكي - تحسين الأداء
export class CacheManager {
  constructor() {
    this.cache = new Map();
    this.accessTimes = new Map();
    this.config = null;
    this.maxSize = 256 * 1024 * 1024; // 256MB افتراضي
    this.currentSize = 0;
    this.ttl = 3600000; // ساعة واحدة افتراضي
    this.cleanupInterval = null;
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0
    };
  }

  /**
   * تهيئة مدير التخزين المؤقت
   */
  async initialize(config) {
    try {
      console.log('💾 تهيئة مدير التخزين المؤقت...');
      
      this.config = config;
      this.maxSize = this.parseSize(config.maxSize);
      this.ttl = config.ttl;
      
      // بدء التنظيف الدوري
      this.startPeriodicCleanup();
      
      console.log('✅ تم تهيئة مدير التخزين المؤقت بنجاح');
      console.log(`📊 الحد الأقصى للتخزين: ${this.formatSize(this.maxSize)}`);
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة مدير التخزين المؤقت:', error);
      throw error;
    }
  }

  /**
   * تحويل حجم من نص إلى رقم
   */
  parseSize(sizeStr) {
    const units = {
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };
    
    const match = sizeStr.match(/^(\d+)\s*(KB|MB|GB)$/i);
    if (!match) {
      throw new Error(`تنسيق حجم غير صحيح: ${sizeStr}`);
    }
    
    const [, size, unit] = match;
    return parseInt(size) * units[unit.toUpperCase()];
  }

  /**
   * تنسيق الحجم للعرض
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * حفظ عنصر في التخزين المؤقت
   */
  async set(key, value, customTTL = null) {
    try {
      const ttl = customTTL || this.ttl;
      const serializedValue = await this.serializeValue(value);
      const size = this.calculateSize(serializedValue);
      
      // التحقق من المساحة المتاحة
      await this.ensureSpace(size);
      
      const cacheEntry = {
        value: serializedValue,
        size: size,
        createdAt: Date.now(),
        expiresAt: Date.now() + ttl,
        accessCount: 0,
        lastAccessed: Date.now()
      };
      
      // إزالة العنصر القديم إذا كان موجوداً
      if (this.cache.has(key)) {
        const oldEntry = this.cache.get(key);
        this.currentSize -= oldEntry.size;
      }
      
      this.cache.set(key, cacheEntry);
      this.accessTimes.set(key, Date.now());
      this.currentSize += size;
      
      console.log(`💾 حفظ في التخزين المؤقت: ${key} (${this.formatSize(size)})`);
      
    } catch (error) {
      console.error(`❌ خطأ في حفظ ${key} في التخزين المؤقت:`, error);
      throw error;
    }
  }

  /**
   * استرجاع عنصر من التخزين المؤقت
   */
  async get(key) {
    this.stats.totalRequests++;
    
    if (!this.cache.has(key)) {
      this.stats.misses++;
      return null;
    }
    
    const entry = this.cache.get(key);
    
    // التحقق من انتهاء الصلاحية
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      this.stats.misses++;
      return null;
    }
    
    // تحديث إحصائيات الوصول
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.accessTimes.set(key, Date.now());
    
    this.stats.hits++;
    
    try {
      const value = await this.deserializeValue(entry.value);
      console.log(`💾 استرجاع من التخزين المؤقت: ${key}`);
      return value;
    } catch (error) {
      console.error(`❌ خطأ في استرجاع ${key} من التخزين المؤقت:`, error);
      this.delete(key);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * حذف عنصر من التخزين المؤقت
   */
  delete(key) {
    if (this.cache.has(key)) {
      const entry = this.cache.get(key);
      this.currentSize -= entry.size;
      this.cache.delete(key);
      this.accessTimes.delete(key);
      
      console.log(`🗑️ حذف من التخزين المؤقت: ${key}`);
      return true;
    }
    
    return false;
  }

  /**
   * التحقق من وجود عنصر
   */
  has(key) {
    if (!this.cache.has(key)) {
      return false;
    }
    
    const entry = this.cache.get(key);
    
    // التحقق من انتهاء الصلاحية
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * ضمان توفر المساحة
   */
  async ensureSpace(requiredSize) {
    if (this.currentSize + requiredSize <= this.maxSize) {
      return;
    }
    
    console.log(`🧹 تحرير مساحة في التخزين المؤقت: ${this.formatSize(requiredSize)}`);
    
    // تنظيف العناصر المنتهية الصلاحية أولاً
    this.cleanupExpired();
    
    // إذا لم تكن المساحة كافية، استخدم LRU eviction
    while (this.currentSize + requiredSize > this.maxSize && this.cache.size > 0) {
      this.evictLRU();
    }
    
    if (this.currentSize + requiredSize > this.maxSize) {
      throw new Error(`لا يمكن توفير مساحة كافية في التخزين المؤقت. مطلوب: ${this.formatSize(requiredSize)}, متاح: ${this.formatSize(this.maxSize - this.currentSize)}`);
    }
  }

  /**
   * إزالة العنصر الأقل استخداماً (LRU)
   */
  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();
    
    for (const [key, time] of this.accessTimes.entries()) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.delete(oldestKey);
      this.stats.evictions++;
      console.log(`🗑️ إزالة LRU: ${oldestKey}`);
    }
  }

  /**
   * تنظيف العناصر المنتهية الصلاحية
   */
  cleanupExpired() {
    const now = Date.now();
    const expiredKeys = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }
    
    for (const key of expiredKeys) {
      this.delete(key);
    }
    
    if (expiredKeys.length > 0) {
      console.log(`🧹 تنظيف العناصر المنتهية الصلاحية: ${expiredKeys.length} عنصر`);
    }
  }

  /**
   * بدء التنظيف الدوري
   */
  startPeriodicCleanup() {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpired();
      this.logStats();
    }, 300000); // كل 5 دقائق
    
    console.log('🔄 بدء التنظيف الدوري للتخزين المؤقت');
  }

  /**
   * تسلسل القيمة
   */
  async serializeValue(value) {
    if (value instanceof ArrayBuffer) {
      return {
        type: 'ArrayBuffer',
        data: Array.from(new Uint8Array(value))
      };
    } else if (value instanceof Uint8Array) {
      return {
        type: 'Uint8Array',
        data: Array.from(value)
      };
    } else {
      return {
        type: 'JSON',
        data: JSON.stringify(value)
      };
    }
  }

  /**
   * إلغاء تسلسل القيمة
   */
  async deserializeValue(serialized) {
    switch (serialized.type) {
      case 'ArrayBuffer':
        return new Uint8Array(serialized.data).buffer;
      case 'Uint8Array':
        return new Uint8Array(serialized.data);
      case 'JSON':
        return JSON.parse(serialized.data);
      default:
        throw new Error(`نوع تسلسل غير مدعوم: ${serialized.type}`);
    }
  }

  /**
   * حساب حجم القيمة
   */
  calculateSize(serializedValue) {
    const jsonString = JSON.stringify(serializedValue);
    return new Blob([jsonString]).size;
  }

  /**
   * مسح جميع العناصر
   */
  async clear() {
    this.cache.clear();
    this.accessTimes.clear();
    this.currentSize = 0;
    
    // إعادة تعيين الإحصائيات
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0
    };
    
    console.log('🧹 تم مسح جميع عناصر التخزين المؤقت');
  }

  /**
   * الحصول على الإحصائيات
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0;
    
    return {
      ...this.stats,
      hitRate: hitRate.toFixed(2) + '%',
      currentSize: this.currentSize,
      maxSize: this.maxSize,
      utilization: ((this.currentSize / this.maxSize) * 100).toFixed(2) + '%',
      itemCount: this.cache.size,
      formatted: {
        currentSize: this.formatSize(this.currentSize),
        maxSize: this.formatSize(this.maxSize)
      }
    };
  }

  /**
   * تسجيل الإحصائيات
   */
  logStats() {
    const stats = this.getStats();
    console.log(`📊 إحصائيات التخزين المؤقت:`, {
      'معدل الإصابة': stats.hitRate,
      'الاستخدام': stats.utilization,
      'عدد العناصر': stats.itemCount,
      'الحجم الحالي': stats.formatted.currentSize
    });
  }

  /**
   * الحصول على معلومات عنصر
   */
  getItemInfo(key) {
    if (!this.cache.has(key)) {
      return null;
    }
    
    const entry = this.cache.get(key);
    const now = Date.now();
    
    return {
      key,
      size: this.formatSize(entry.size),
      createdAt: new Date(entry.createdAt).toLocaleString(),
      expiresAt: new Date(entry.expiresAt).toLocaleString(),
      lastAccessed: new Date(entry.lastAccessed).toLocaleString(),
      accessCount: entry.accessCount,
      timeToExpiry: Math.max(0, entry.expiresAt - now),
      isExpired: now > entry.expiresAt
    };
  }

  /**
   * الحصول على قائمة جميع العناصر
   */
  getAllItems() {
    return Array.from(this.cache.keys()).map(key => this.getItemInfo(key));
  }

  /**
   * تنظيف شامل
   */
  async cleanup() {
    console.log('🧹 تنظيف شامل للتخزين المؤقت...');
    
    // إيقاف التنظيف الدوري
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    // مسح جميع العناصر
    await this.clear();
    
    console.log('✅ تم التنظيف الشامل للتخزين المؤقت');
  }
}
