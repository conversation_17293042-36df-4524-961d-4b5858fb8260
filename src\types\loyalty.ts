import type { Timestamp } from 'firebase/firestore';

// مستويات العضوية في برنامج الولاء
export type LoyaltyTier = 'bronze' | 'silver' | 'gold' | 'platinum';

// أنواع معاملات النقاط
export type PointTransactionType = 'earned' | 'redeemed' | 'expired' | 'bonus' | 'penalty';

// حالة برنامج الولاء
export type LoyaltyProgramStatus = 'active' | 'inactive' | 'suspended';

// نموذج بيانات برنامج الولاء للتاجر
export interface LoyaltyProgram {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  isActive: boolean;
  status: LoyaltyProgramStatus;
  settings: LoyaltySettings;
  tiers: LoyaltyTierConfig[];
  rewards: LoyaltyReward[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
}

// إعدادات برنامج الولاء
export interface LoyaltySettings {
  pointsPerSAR: number; // نقاط لكل ريال
  minimumOrderForPoints: number; // الحد الأدنى للطلب لكسب النقاط
  pointsExpiryDays: number; // انتهاء صلاحية النقاط بالأيام
  welcomeBonusPoints: number; // نقاط ترحيب للعضو الجديد
  birthdayBonusPoints: number; // نقاط عيد ميلاد
  referralBonusPoints: number; // نقاط إحالة صديق
  maxPointsPerOrder: number; // أقصى نقاط لكل طلب
  allowPartialRedemption: boolean; // السماح بالاستبدال الجزئي
  autoTierUpgrade: boolean; // الترقية التلقائية للمستوى
  notificationSettings: {
    pointsEarned: boolean;
    pointsRedeemed: boolean;
    pointsExpiring: boolean;
    tierUpgrade: boolean;
    rewardAvailable: boolean;
  };
}

// تكوين مستويات العضوية
export interface LoyaltyTierConfig {
  tier: LoyaltyTier;
  name: string;
  description: string;
  minSpent: number; // الحد الأدنى للإنفاق للوصول لهذا المستوى
  pointsMultiplier: number; // مضاعف النقاط (1.0 = عادي، 1.5 = 50% إضافي)
  benefits: string[]; // المزايا النصية
  color: string; // لون المستوى للعرض
  icon: string; // أيقونة المستوى
  minOrdersCount?: number; // الحد الأدنى لعدد الطلبات
  validityDays?: number; // مدة صلاحية المستوى
}

// مكافآت برنامج الولاء
export interface LoyaltyReward {
  id: string;
  name: string;
  description: string;
  type: 'discount' | 'free_product' | 'free_shipping' | 'gift_card' | 'experience';
  pointsCost: number;
  value: number; // قيمة المكافأة (خصم، مبلغ، إلخ)
  isActive: boolean;
  availableQuantity?: number; // الكمية المتاحة (اختياري)
  usedQuantity: number;
  validFrom: Timestamp;
  validUntil: Timestamp;
  tierRestrictions?: LoyaltyTier[]; // مقيدة لمستويات معينة
  conditions?: {
    minOrderAmount?: number;
    applicableCategories?: string[];
    excludedProducts?: string[];
    maxUsagePerCustomer?: number;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// ملف العضو في برنامج الولاء
export interface CustomerLoyalty {
  id: string;
  customerId: string;
  merchantId: string;
  programId: string;
  currentTier: LoyaltyTier;
  totalPoints: number;
  availablePoints: number;
  lifetimeSpent: number;
  lifetimeOrders: number;
  joinedAt: Timestamp;
  lastActivityAt: Timestamp;
  tierAchievedAt: Timestamp;
  nextTierProgress: {
    nextTier: LoyaltyTier | null;
    currentSpent: number;
    requiredSpent: number;
    progressPercentage: number;
  };
  statistics: {
    totalPointsEarned: number;
    totalPointsRedeemed: number;
    totalPointsExpired: number;
    averageOrderValue: number;
    favoriteCategories: string[];
    lastPurchaseDate?: Timestamp;
  };
}

// معاملة نقاط الولاء
export interface PointTransaction {
  id: string;
  customerId: string;
  merchantId: string;
  programId: string;
  type: PointTransactionType;
  points: number;
  description: string;
  orderId?: string;
  rewardId?: string;
  expiryDate?: Timestamp;
  createdAt: Timestamp;
  metadata?: {
    orderAmount?: number;
    tierAtTime?: LoyaltyTier;
    multiplierUsed?: number;
    bonusReason?: string;
  };
}

// استبدال المكافآت
export interface RewardRedemption {
  id: string;
  customerId: string;
  merchantId: string;
  rewardId: string;
  pointsUsed: number;
  status: 'pending' | 'approved' | 'redeemed' | 'cancelled' | 'expired';
  redemptionCode?: string;
  orderId?: string;
  redeemedAt: Timestamp;
  expiresAt?: Timestamp;
  usedAt?: Timestamp;
  notes?: string;
}

// إحصائيات برنامج الولاء
export interface LoyaltyAnalytics {
  programId: string;
  totalMembers: number;
  activeMembers: number;
  membersByTier: Record<LoyaltyTier, number>;
  totalPointsIssued: number;
  totalPointsRedeemed: number;
  totalPointsExpired: number;
  averagePointsPerMember: number;
  redemptionRate: number; // نسبة الاستبدال
  memberRetentionRate: number;
  averageOrderValueIncrease: number;
  topRewards: {
    rewardId: string;
    name: string;
    redemptionCount: number;
    pointsUsed: number;
  }[];
  monthlyStats: {
    month: string;
    year: number;
    newMembers: number;
    pointsEarned: number;
    pointsRedeemed: number;
    redemptions: number;
  }[];
  tierDistribution: {
    tier: LoyaltyTier;
    count: number;
    percentage: number;
    averageSpent: number;
  }[];
}

// بيانات إنشاء برنامج ولاء جديد
export interface CreateLoyaltyProgramData {
  name: string;
  description: string;
  settings: LoyaltySettings;
  tiers: Omit<LoyaltyTierConfig, 'tier'>[];
  rewards: Omit<LoyaltyReward, 'id' | 'createdAt' | 'updatedAt' | 'usedQuantity'>[];
}

// بيانات تحديث برنامج الولاء
export interface UpdateLoyaltyProgramData extends Partial<CreateLoyaltyProgramData> {
  isActive?: boolean;
  status?: LoyaltyProgramStatus;
}

// فلاتر البحث في أعضاء برنامج الولاء
export interface LoyaltyMemberFilters {
  tier?: LoyaltyTier[];
  pointsRange?: {
    min: number;
    max: number;
  };
  spentRange?: {
    min: number;
    max: number;
  };
  joinDateRange?: {
    start: Date;
    end: Date;
  };
  lastActivityRange?: {
    start: Date;
    end: Date;
  };
  searchTerm?: string;
}

// ترتيب أعضاء برنامج الولاء
export interface LoyaltyMemberSortOptions {
  field: 'joinedAt' | 'lastActivityAt' | 'totalPoints' | 'lifetimeSpent' | 'lifetimeOrders';
  direction: 'asc' | 'desc';
}

// استجابة API لأعضاء برنامج الولاء
export interface LoyaltyMembersResponse {
  members: CustomerLoyalty[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// تقرير أداء برنامج الولاء
export interface LoyaltyPerformanceReport {
  period: {
    start: Date;
    end: Date;
  };
  overview: {
    totalMembers: number;
    newMembers: number;
    activeMembers: number;
    churnedMembers: number;
    totalPointsIssued: number;
    totalPointsRedeemed: number;
    redemptionValue: number;
    programROI: number;
  };
  membershipGrowth: {
    date: string;
    newMembers: number;
    totalMembers: number;
  }[];
  tierPerformance: {
    tier: LoyaltyTier;
    memberCount: number;
    averageSpent: number;
    pointsEarned: number;
    pointsRedeemed: number;
    retentionRate: number;
  }[];
  rewardPerformance: {
    rewardId: string;
    name: string;
    redemptions: number;
    pointsUsed: number;
    conversionRate: number;
  }[];
  customerSegments: {
    segment: string;
    memberCount: number;
    averageOrderValue: number;
    frequency: number;
    lifetimeValue: number;
  }[];
}

// إعدادات إشعارات برنامج الولاء
export interface LoyaltyNotificationSettings {
  emailNotifications: {
    welcome: boolean;
    pointsEarned: boolean;
    pointsExpiring: boolean;
    tierUpgrade: boolean;
    rewardAvailable: boolean;
    birthdayBonus: boolean;
  };
  smsNotifications: {
    pointsEarned: boolean;
    tierUpgrade: boolean;
    pointsExpiring: boolean;
  };
  pushNotifications: {
    pointsEarned: boolean;
    rewardAvailable: boolean;
    tierUpgrade: boolean;
  };
}

// قالب برنامج ولاء للإنشاء السريع
export interface LoyaltyProgramTemplate {
  id: string;
  name: string;
  description: string;
  category: 'retail' | 'restaurant' | 'service' | 'ecommerce';
  settings: LoyaltySettings;
  tiers: LoyaltyTierConfig[];
  rewards: Omit<LoyaltyReward, 'id' | 'createdAt' | 'updatedAt' | 'usedQuantity'>[];
  isDefault: boolean;
}

// حدث برنامج الولاء للتتبع
export interface LoyaltyEvent {
  id: string;
  type: 'points_earned' | 'points_redeemed' | 'tier_upgraded' | 'reward_claimed' | 'member_joined';
  customerId: string;
  merchantId: string;
  programId: string;
  data: any;
  timestamp: Timestamp;
  processed: boolean;
}
