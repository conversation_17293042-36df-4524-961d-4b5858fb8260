"use client";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart } from "lucide-react";
import { useCart } from "./CartProvider";
import { useState } from "react";

interface CartIconProps {
  onClick?: () => void;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
  showBadge?: boolean;
  className?: string;
}

export default function CartIcon({
  onClick,
  variant = "ghost",
  size = "default",
  showBadge = true,
  className = ""
}: CartIconProps) {
  const { totalItems, isLoading } = useCart();
  const [isAnimating, setIsAnimating] = useState(false);

  // Trigger animation when items are added
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
    
    // Add bounce animation
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const getIconSize = () => {
    switch (size) {
      case "sm":
        return "w-4 h-4";
      case "lg":
        return "w-6 h-6";
      default:
        return "w-5 h-5";
    }
  };

  const getBadgeSize = () => {
    switch (size) {
      case "sm":
        return "h-4 w-4 text-xs";
      case "lg":
        return "h-6 w-6 text-sm";
      default:
        return "h-5 w-5 text-xs";
    }
  };

  return (
    <div className="relative">
      <Button
        variant={variant}
        size={size}
        onClick={handleClick}
        className={`relative ${isAnimating ? 'animate-bounce' : ''} ${className}`}
        disabled={isLoading}
      >
        <ShoppingCart className={getIconSize()} />
        {size !== "sm" && (
          <span className="sr-only">
            Shopping cart with {totalItems} items
          </span>
        )}
      </Button>
      
      {showBadge && totalItems > 0 && (
        <Badge
          variant="destructive"
          className={`absolute -top-1 -right-1 ${getBadgeSize()} rounded-full p-0 flex items-center justify-center font-bold transition-all duration-200 ${
            isAnimating ? 'scale-110' : 'scale-100'
          }`}
        >
          {totalItems > 99 ? '99+' : totalItems}
        </Badge>
      )}
      
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
}
