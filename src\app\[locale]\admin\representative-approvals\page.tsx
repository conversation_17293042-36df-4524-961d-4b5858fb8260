'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { collection, query, where, getDocs, doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  CheckCircle,
  XCircle,
  Clock,
  User,
  Car,
  FileText,
  Phone,
  Mail,
  Calendar,
  Loader2,
  Eye,
  Brain,
  Zap
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import type { RepresentativeDocument } from '@/types';
import { RepresentativeAIApprovalService } from '@/services/representativeAIApprovalService';

export default function RepresentativeApprovalsPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const { toast } = useToast();
  const [representatives, setRepresentatives] = useState<RepresentativeDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [aiProcessingId, setAiProcessingId] = useState<string | null>(null);

  useEffect(() => {
    const fetchPendingRepresentatives = async () => {
      try {
        const q = query(
          collection(db, 'representatives'),
          where('approvalStatus', '==', 'pending')
        );
        const querySnapshot = await getDocs(q);
        const representativesList: RepresentativeDocument[] = [];
        
        querySnapshot.forEach((doc) => {
          representativesList.push({ ...doc.data() } as RepresentativeDocument);
        });
        
        setRepresentatives(representativesList);
      } catch (error) {
        console.error('Error fetching representatives:', error);
        toast({
          title: t('error'),
          description: t('errorFetchingRepresentatives'),
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchPendingRepresentatives();
    }
  }, [user, t, toast]);

  const handleApproval = async (representativeId: string, status: 'approved' | 'rejected', notes?: string) => {
    if (!user) return;

    setProcessingId(representativeId);
    try {
      const representativeRef = doc(db, 'representatives', representativeId);
      await updateDoc(representativeRef, {
        approvalStatus: status,
        approvalDate: serverTimestamp(),
        approvalNotes: notes || '',
        reviewedBy: user.uid,
        isActive: status === 'approved',
        updatedAt: serverTimestamp(),
      });

      // Remove from local state
      setRepresentatives(prev => prev.filter(rep => rep.uid !== representativeId));

      toast({
        title: t('success'),
        description: status === 'approved' ? t('representativeApproved') : t('representativeRejected'),
      });
    } catch (error) {
      console.error('Error updating representative status:', error);
      toast({
        title: t('error'),
        description: t('errorUpdatingRepresentative'),
        variant: 'destructive',
      });
    } finally {
      setProcessingId(null);
    }
  };

  // الموافقة الذكية بالذكاء الاصطناعي للمندوبين
  const handleAIApproval = async (representativeId: string) => {
    if (!user) return;

    setAiProcessingId(representativeId);
    try {
      // استدعاء API الموافقة التلقائية
      const response = await fetch('/api/ai/auto-approve-representative', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ representativeUid: representativeId })
      });

      if (!response.ok) {
        throw new Error('فشل في الموافقة الذكية');
      }

      const result = await response.json();

      // إزالة المندوب من القائمة إذا تم اتخاذ قرار نهائي
      if (result.decision === 'approve' || result.decision === 'reject') {
        setRepresentatives(prev => prev.filter(rep => rep.uid !== representativeId));
      }

      // إظهار النتيجة للمدير
      if (result.decision === 'approve') {
        toast({
          title: 'تم القبول تلقائياً',
          description: `تم قبول المندوب بالذكاء الاصطناعي (ثقة: ${result.confidence}%)`,
        });
      } else if (result.decision === 'reject') {
        toast({
          title: 'تم الرفض تلقائياً',
          description: `تم رفض المندوب: ${result.reasons.join(', ')}`,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'يتطلب مراجعة يدوية',
          description: `${result.reasons.join(', ')}`,
          variant: 'default',
        });
      }

    } catch (error) {
      console.error('خطأ في الموافقة الذكية:', error);
      toast({
        title: 'خطأ في النظام',
        description: 'حدث خطأ في الموافقة الذكية',
        variant: 'destructive',
      });
    } finally {
      setAiProcessingId(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('loadingRepresentatives')}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('representativeApprovals')}</h1>
        <p className="text-muted-foreground">{t('reviewAndApproveRepresentatives')}</p>
      </div>

      {representatives.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Clock className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">{t('noPendingRepresentatives')}</h3>
            <p className="text-muted-foreground">{t('allRepresentativesReviewed')}</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {representatives.map((representative) => (
            <RepresentativeCard
              key={representative.uid}
              representative={representative}
              onApprove={(notes) => handleApproval(representative.uid, 'approved', notes)}
              onReject={(notes) => handleApproval(representative.uid, 'rejected', notes)}
              onAIApproval={() => handleAIApproval(representative.uid)}
              isProcessing={processingId === representative.uid}
              isAIProcessing={aiProcessingId === representative.uid}
              t={t}
            />
          ))}
        </div>
      )}
    </div>
  );
}

interface RepresentativeCardProps {
  representative: RepresentativeDocument;
  onApprove: (notes?: string) => void;
  onReject: (notes?: string) => void;
  onAIApproval: () => void;
  isProcessing: boolean;
  isAIProcessing: boolean;
  t: (key: string) => string;
}

function RepresentativeCard({ representative, onApprove, onReject, onAIApproval, isProcessing, isAIProcessing, t }: RepresentativeCardProps) {
  const [notes, setNotes] = useState('');
  const [showDetails, setShowDetails] = useState(false);

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {representative.displayName}
            </CardTitle>
            <CardDescription>
              {t('submittedOn')}: {representative.submittedAt?.toDate?.()?.toLocaleDateString('ar-SA')}
            </CardDescription>
          </div>
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            {t('pending')}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm text-muted-foreground">{t('phoneNumber')}</p>
              <p className="font-medium">{representative.phoneNumber}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm text-muted-foreground">{t('email')}</p>
              <p className="font-medium">{representative.email}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm text-muted-foreground">{t('nationalId')}</p>
              <p className="font-medium">{representative.nationalId} ({t(representative.nationalIdType)})</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Car className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm text-muted-foreground">{t('vehicleType')}</p>
              <p className="font-medium">{t(representative.vehicle.type)} - {representative.vehicle.model}</p>
            </div>
          </div>
        </div>

        {/* Toggle Details */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="w-full"
        >
          <Eye className="h-4 w-4 mr-2" />
          {showDetails ? t('hideDetails') : t('showDetails')}
        </Button>

        {/* Detailed Information */}
        {showDetails && (
          <div className="space-y-4 border-t pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">{t('drivingLicense')}</h4>
                <p className="text-sm">{t('licenseNumber')}: {representative.drivingLicense.number}</p>
                <p className="text-sm">{t('expiryDate')}: {representative.drivingLicense.expiryDate?.toDate?.()?.toLocaleDateString('ar-SA')}</p>
                {representative.drivingLicense.imageURL && (
                  <Button variant="link" size="sm" asChild>
                    <a href={representative.drivingLicense.imageURL} target="_blank" rel="noopener noreferrer">
                      {t('viewLicense')}
                    </a>
                  </Button>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">{t('vehicleInspection')}</h4>
                <p className="text-sm">{t('certificateNumber')}: {representative.vehicleInspection.certificateNumber}</p>
                <p className="text-sm">{t('expiryDate')}: {representative.vehicleInspection.expiryDate?.toDate?.()?.toLocaleDateString('ar-SA')}</p>
                {representative.vehicleInspection.imageURL && (
                  <Button variant="link" size="sm" asChild>
                    <a href={representative.vehicleInspection.imageURL} target="_blank" rel="noopener noreferrer">
                      {t('viewCertificate')}
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Notes */}
        <div>
          <Label htmlFor={`notes-${representative.uid}`}>{t('reviewNotes')}</Label>
          <Textarea
            id={`notes-${representative.uid}`}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder={t('addReviewNotes')}
            className="mt-1"
          />
        </div>

        {/* AI Approval Button */}
        <div className="mb-4">
          <Button
            onClick={onAIApproval}
            disabled={isProcessing || isAIProcessing}
            className="w-full bg-purple-600 hover:bg-purple-700"
            data-testid="ai-approval-button"
          >
            {isAIProcessing ? (
              <>
                <Zap className="h-4 w-4 mr-2 animate-spin" />
                جاري التحليل الذكي...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-2" />
                موافقة ذكية بالـ AI
              </>
            )}
          </Button>
        </div>

        {/* Manual Action Buttons */}
        <div className="flex gap-4">
          <Button
            onClick={() => onApprove(notes)}
            disabled={isProcessing || isAIProcessing}
            className="flex-1"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            {t('approve')}
          </Button>

          <Button
            variant="destructive"
            onClick={() => onReject(notes)}
            disabled={isProcessing || isAIProcessing}
            className="flex-1"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <XCircle className="h-4 w-4 mr-2" />
            )}
            {t('reject')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
