#!/usr/bin/env node

/**
 * سكريبت اختبار الملف المدموج للترجمات
 * يتحقق من أن الملف المدموج يعمل بشكل صحيح
 */

const fs = require('fs');
const path = require('path');

// مسارات الملفات
const MERGED_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/translations.json');
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * قراءة ملف JSON
 */
function loadJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ الملف غير موجود: ${path.basename(filePath)}`);
      return null;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${path.basename(filePath)}:`, error.message);
    return null;
  }
}

/**
 * اختبار بنية الملف المدموج
 */
function testMergedFileStructure() {
  console.log('🔍 اختبار بنية الملف المدموج...');
  
  const mergedData = loadJsonFile(MERGED_TRANSLATIONS_PATH);
  if (!mergedData) {
    console.error('❌ فشل في تحميل الملف المدموج');
    return false;
  }
  
  // التحقق من وجود المفاتيح الأساسية
  if (!mergedData.ar || !mergedData.en) {
    console.error('❌ الملف المدموج لا يحتوي على المفاتيح المطلوبة (ar, en)');
    return false;
  }
  
  // التحقق من أن كل قسم يحتوي على ترجمات
  const arKeys = Object.keys(mergedData.ar);
  const enKeys = Object.keys(mergedData.en);
  
  if (arKeys.length === 0) {
    console.error('❌ القسم العربي فارغ');
    return false;
  }
  
  if (enKeys.length === 0) {
    console.error('❌ القسم الإنجليزي فارغ');
    return false;
  }
  
  console.log(`✅ بنية الملف صحيحة:`);
  console.log(`   🇸🇦 الترجمات العربية: ${arKeys.length} مفتاح`);
  console.log(`   🇺🇸 الترجمات الإنجليزية: ${enKeys.length} مفتاح`);
  
  return true;
}

/**
 * مقارنة الملف المدموج مع الملفات الأصلية
 */
function compareWithOriginalFiles() {
  console.log('\n🔍 مقارنة مع الملفات الأصلية...');
  
  const mergedData = loadJsonFile(MERGED_TRANSLATIONS_PATH);
  const originalAr = loadJsonFile(AR_TRANSLATIONS_PATH);
  const originalEn = loadJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!mergedData || !originalAr || !originalEn) {
    console.error('❌ فشل في تحميل أحد الملفات للمقارنة');
    return false;
  }
  
  let allMatch = true;
  
  // مقارنة الترجمات العربية
  const arKeys = Object.keys(originalAr);
  const mergedArKeys = Object.keys(mergedData.ar);
  
  if (arKeys.length !== mergedArKeys.length) {
    console.warn(`⚠️ عدد المفاتيح العربية مختلف: أصلي=${arKeys.length}, مدموج=${mergedArKeys.length}`);
  }
  
  let arMismatches = 0;
  arKeys.forEach(key => {
    if (originalAr[key] !== mergedData.ar[key]) {
      arMismatches++;
      if (arMismatches <= 3) { // عرض أول 3 اختلافات فقط
        console.warn(`⚠️ اختلاف في المفتاح العربي "${key}"`);
      }
    }
  });
  
  // مقارنة الترجمات الإنجليزية
  const enKeys = Object.keys(originalEn);
  const mergedEnKeys = Object.keys(mergedData.en);
  
  if (enKeys.length !== mergedEnKeys.length) {
    console.warn(`⚠️ عدد المفاتيح الإنجليزية مختلف: أصلي=${enKeys.length}, مدموج=${mergedEnKeys.length}`);
  }
  
  let enMismatches = 0;
  enKeys.forEach(key => {
    if (originalEn[key] !== mergedData.en[key]) {
      enMismatches++;
      if (enMismatches <= 3) { // عرض أول 3 اختلافات فقط
        console.warn(`⚠️ اختلاف في المفتاح الإنجليزي "${key}"`);
      }
    }
  });
  
  if (arMismatches === 0 && enMismatches === 0) {
    console.log('✅ جميع الترجمات متطابقة مع الملفات الأصلية');
  } else {
    console.warn(`⚠️ تم العثور على ${arMismatches} اختلاف عربي و ${enMismatches} اختلاف إنجليزي`);
    allMatch = false;
  }
  
  return allMatch;
}

/**
 * اختبار عينة من الترجمات
 */
function testSampleTranslations() {
  console.log('\n🔍 اختبار عينة من الترجمات...');
  
  const mergedData = loadJsonFile(MERGED_TRANSLATIONS_PATH);
  if (!mergedData) {
    return false;
  }
  
  // مفاتيح للاختبار
  const testKeys = [
    'appName',
    'home',
    'login',
    'signup',
    'merchants',
    'customers'
  ];
  
  let allFound = true;
  
  testKeys.forEach(key => {
    const arValue = mergedData.ar[key];
    const enValue = mergedData.en[key];
    
    if (!arValue || !enValue) {
      console.error(`❌ مفتاح مفقود: "${key}"`);
      allFound = false;
    } else {
      console.log(`✅ ${key}: "${arValue}" / "${enValue}"`);
    }
  });
  
  return allFound;
}

/**
 * اختبار حجم الملف
 */
function testFileSize() {
  console.log('\n📊 معلومات حجم الملف...');
  
  try {
    const stats = fs.statSync(MERGED_TRANSLATIONS_PATH);
    const sizeKB = Math.round(stats.size / 1024);
    const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    console.log(`📁 حجم الملف المدموج: ${sizeKB} KB (${sizeMB} MB)`);
    
    if (sizeKB > 500) {
      console.warn('⚠️ الملف كبير نسبياً، قد يؤثر على أداء التحميل');
    } else {
      console.log('✅ حجم الملف مناسب');
    }
    
    return true;
  } catch (error) {
    console.error('❌ خطأ في قراءة معلومات الملف:', error.message);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧪 بدء اختبار الملف المدموج للترجمات...\n');
  
  let allTestsPassed = true;
  
  // اختبار بنية الملف
  if (!testMergedFileStructure()) {
    allTestsPassed = false;
  }
  
  // مقارنة مع الملفات الأصلية
  if (!compareWithOriginalFiles()) {
    allTestsPassed = false;
  }
  
  // اختبار عينة من الترجمات
  if (!testSampleTranslations()) {
    allTestsPassed = false;
  }
  
  // اختبار حجم الملف
  if (!testFileSize()) {
    allTestsPassed = false;
  }
  
  // النتيجة النهائية
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('✅ جميع الاختبارات نجحت! الملف المدموج جاهز للاستخدام');
    console.log('\n💡 الخطوات التالية:');
    console.log('   1. تأكد من أن ملف i18n.ts محدث');
    console.log('   2. اختبر التطبيق للتأكد من عمل الترجمات');
    console.log('   3. يمكنك حذف ar.json و en.json إذا كنت تريد استخدام الملف المدموج فقط');
  } else {
    console.log('❌ بعض الاختبارات فشلت! يرجى مراجعة الأخطاء أعلاه');
  }
  console.log('='.repeat(50));
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  testMergedFileStructure,
  compareWithOriginalFiles,
  testSampleTranslations,
  testFileSize
};
