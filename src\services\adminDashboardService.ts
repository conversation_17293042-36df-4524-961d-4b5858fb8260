// src/services/adminDashboardService.ts
import { 
  collection, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  getCountFromServer
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  UserDocument, 
  StoreDocument, 
  ProductDocument, 
  OrderDocument,
  RepresentativeDocument 
} from '@/types';

export interface AdminStats {
  // إحصائيات المستخدمين
  totalUsers: number;
  activeUsers: number;
  totalCustomers: number;
  totalMerchants: number;
  totalRepresentatives: number;
  
  // إحصائيات المتاجر والمنتجات
  totalStores: number;
  activeStores: number;
  totalProducts: number;
  activeProducts: number;
  
  // إحصائيات الطلبات
  totalOrders: number;
  todayOrders: number;
  pendingOrders: number;
  completedOrders: number;
  
  // إحصائيات مالية
  totalRevenue: number;
  todayRevenue: number;
  monthlyRevenue: number;
  totalCommissions: number;
  monthlyCommissions: number;
  averageOrderValue: number;
  
  // مقاييس الأداء
  conversionRate: number;
  customerRetentionRate: number;
  growthRate: number;
}

export interface ActivityItem {
  id: string;
  type: 'user_registration' | 'order_placed' | 'store_registration' | 'representative_application' | 'order_completed' | 'order_cancelled' | 'payment_received';
  title: string;
  description: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
  metadata?: Record<string, any>;
}

export interface SystemAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionRequired: boolean;
  metadata?: Record<string, any>;
}

export interface TopPerformer {
  id: string;
  name: string;
  type: 'merchant' | 'product' | 'representative';
  value: number;
  metric: string;
  change: number;
  avatar?: string;
}

export interface ChartData {
  date: string;
  value: number;
  label?: string;
}

export interface GeographicData {
  region: string;
  city: string;
  orders: number;
  revenue: number;
  users: number;
  coordinates?: [number, number];
}

class AdminDashboardService {
  // جلب الإحصائيات الأساسية
  async getAdminStats(): Promise<AdminStats> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

      // إحصائيات المستخدمين
      const usersSnapshot = await getCountFromServer(collection(db, 'users'));
      const totalUsers = usersSnapshot.data().count;

      const customersSnapshot = await getCountFromServer(
        query(collection(db, 'users'), where('userType', '==', 'customer'))
      );
      const totalCustomers = customersSnapshot.data().count;

      const merchantsSnapshot = await getCountFromServer(
        query(collection(db, 'users'), where('userType', '==', 'merchant'))
      );
      const totalMerchants = merchantsSnapshot.data().count;

      const representativesSnapshot = await getCountFromServer(
        query(collection(db, 'representatives'))
      );
      const totalRepresentatives = representativesSnapshot.data().count;

      // إحصائيات المتاجر
      const storesSnapshot = await getCountFromServer(collection(db, 'stores'));
      const totalStores = storesSnapshot.data().count;

      const activeStoresSnapshot = await getCountFromServer(
        query(collection(db, 'stores'), where('isActive', '==', true))
      );
      const activeStores = activeStoresSnapshot.data().count;

      // إحصائيات المنتجات
      const productsSnapshot = await getCountFromServer(collection(db, 'products'));
      const totalProducts = productsSnapshot.data().count;

      const activeProductsSnapshot = await getCountFromServer(
        query(collection(db, 'products'), where('isActive', '==', true))
      );
      const activeProducts = activeProductsSnapshot.data().count;

      // إحصائيات الطلبات
      const ordersSnapshot = await getCountFromServer(collection(db, 'orders'));
      const totalOrders = ordersSnapshot.data().count;

      const todayOrdersSnapshot = await getCountFromServer(
        query(collection(db, 'orders'), where('createdAt', '>=', Timestamp.fromDate(today)))
      );
      const todayOrders = todayOrdersSnapshot.data().count;

      const pendingOrdersSnapshot = await getCountFromServer(
        query(collection(db, 'orders'), where('status', '==', 'pending'))
      );
      const pendingOrders = pendingOrdersSnapshot.data().count;

      const completedOrdersSnapshot = await getCountFromServer(
        query(collection(db, 'orders'), where('status', '==', 'completed'))
      );
      const completedOrders = completedOrdersSnapshot.data().count;

      // حساب الإيرادات
      const ordersQuery = query(collection(db, 'orders'));
      const ordersData = await getDocs(ordersQuery);
      
      let totalRevenue = 0;
      let todayRevenue = 0;
      let monthlyRevenue = 0;
      let totalCommissions = 0;
      let monthlyCommissions = 0;

      ordersData.docs.forEach(doc => {
        const order = doc.data() as OrderDocument;
        if (order.status !== 'cancelled') {
          totalRevenue += order.totalAmount;
          totalCommissions += order.commission || 0;

          const orderDate = order.createdAt.toDate();
          if (orderDate >= today) {
            todayRevenue += order.totalAmount;
          }
          if (orderDate >= thisMonth) {
            monthlyRevenue += order.totalAmount;
            monthlyCommissions += order.commission || 0;
          }
        }
      });

      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // حساب معدلات الأداء (قيم تقديرية)
      const conversionRate = totalUsers > 0 ? (totalOrders / totalUsers) * 100 : 0;
      const customerRetentionRate = 85; // قيمة تقديرية
      const growthRate = 12; // قيمة تقديرية

      return {
        totalUsers,
        activeUsers: Math.floor(totalUsers * 0.7), // تقدير 70% نشطين
        totalCustomers,
        totalMerchants,
        totalRepresentatives,
        totalStores,
        activeStores,
        totalProducts,
        activeProducts,
        totalOrders,
        todayOrders,
        pendingOrders,
        completedOrders,
        totalRevenue,
        todayRevenue,
        monthlyRevenue,
        totalCommissions,
        monthlyCommissions,
        averageOrderValue,
        conversionRate,
        customerRetentionRate,
        growthRate,
      };
    } catch (error) {
      console.error('Error fetching admin stats:', error);
      throw new Error('Failed to fetch admin statistics');
    }
  }

  // جلب النشاط الحديث
  async getRecentActivity(limitCount: number = 10): Promise<ActivityItem[]> {
    try {
      const activities: ActivityItem[] = [];

      // جلب آخر المستخدمين المسجلين
      const usersQuery = query(
        collection(db, 'users'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      const usersSnapshot = await getDocs(usersQuery);
      
      usersSnapshot.docs.forEach(doc => {
        const user = doc.data() as UserDocument;
        activities.push({
          id: `user_${doc.id}`,
          type: 'user_registration',
          title: 'تسجيل مستخدم جديد',
          description: `انضم ${user.displayName} كـ${user.userType === 'customer' ? 'عميل' : user.userType === 'merchant' ? 'تاجر' : 'مندوب'}`,
          timestamp: user.createdAt.toDate(),
          userId: doc.id,
          userName: user.displayName,
        });
      });

      // جلب آخر الطلبات
      const ordersQuery = query(
        collection(db, 'orders'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      const ordersSnapshot = await getDocs(ordersQuery);
      
      ordersSnapshot.docs.forEach(doc => {
        const order = doc.data() as OrderDocument;
        activities.push({
          id: `order_${doc.id}`,
          type: order.status === 'completed' ? 'order_completed' : 'order_placed',
          title: order.status === 'completed' ? 'طلب مكتمل' : 'طلب جديد',
          description: `طلب بقيمة ${order.totalAmount} ريال`,
          timestamp: order.createdAt.toDate(),
          metadata: { orderId: doc.id, amount: order.totalAmount },
        });
      });

      // ترتيب النشاطات حسب التاريخ
      activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      return activities.slice(0, limitCount);
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      return [];
    }
  }

  // جلب تنبيهات النظام
  async getSystemAlerts(): Promise<SystemAlert[]> {
    try {
      // في التطبيق الحقيقي، هذه ستأتي من قاعدة البيانات
      // هنا سنقوم بإنشاء تنبيهات تجريبية
      const alerts: SystemAlert[] = [];

      // فحص الطلبات المعلقة
      const pendingOrdersSnapshot = await getCountFromServer(
        query(collection(db, 'orders'), where('status', '==', 'pending'))
      );
      const pendingCount = pendingOrdersSnapshot.data().count;

      if (pendingCount > 10) {
        alerts.push({
          id: 'pending_orders',
          type: 'warning',
          title: 'طلبات معلقة كثيرة',
          message: `يوجد ${pendingCount} طلب معلق يحتاج للمراجعة`,
          timestamp: new Date(),
          isRead: false,
          actionRequired: true,
        });
      }

      // فحص المتاجر غير النشطة
      const inactiveStoresSnapshot = await getCountFromServer(
        query(collection(db, 'stores'), where('isActive', '==', false))
      );
      const inactiveCount = inactiveStoresSnapshot.data().count;

      if (inactiveCount > 5) {
        alerts.push({
          id: 'inactive_stores',
          type: 'info',
          title: 'متاجر غير نشطة',
          message: `يوجد ${inactiveCount} متجر غير نشط`,
          timestamp: new Date(),
          isRead: false,
          actionRequired: false,
        });
      }

      return alerts;
    } catch (error) {
      console.error('Error fetching system alerts:', error);
      return [];
    }
  }
}

export const adminDashboardService = new AdminDashboardService();
