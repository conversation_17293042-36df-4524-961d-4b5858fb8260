import { useState, useEffect } from 'react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { OrderDocument } from '@/types';

export interface RepresentativeStats {
  totalDeliveries: number;
  completedDeliveries: number;
  activeDeliveries: number;
  totalEarnings: number;
  thisMonthEarnings: number;
  thisWeekEarnings: number;
  todayEarnings: number;
  averageRating: number;
  totalRatings: number;
  averageDeliveryTime: number; // في الدقائق
  onTimeDeliveryRate: number; // نسبة التوصيل في الوقت المحدد
}

export interface UseRepresentativeStatsReturn {
  stats: RepresentativeStats;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useRepresentativeStats(representativeUid?: string): UseRepresentativeStatsReturn {
  const [stats, setStats] = useState<RepresentativeStats>({
    totalDeliveries: 0,
    completedDeliveries: 0,
    activeDeliveries: 0,
    totalEarnings: 0,
    thisMonthEarnings: 0,
    thisWeekEarnings: 0,
    todayEarnings: 0,
    averageRating: 0,
    totalRatings: 0,
    averageDeliveryTime: 0,
    onTimeDeliveryRate: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (!representativeUid) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // جلب جميع طلبات المندوب
      const ordersQuery = query(
        collection(db, 'orders'),
        where('representativeUid', '==', representativeUid),
        orderBy('createdAt', 'desc')
      );

      const ordersSnapshot = await getDocs(ordersQuery);
      const orders = ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as OrderDocument[];

      // حساب الإحصائيات الأساسية
      const totalDeliveries = orders.length;
      const completedDeliveries = orders.filter(order => order.status === 'delivered').length;
      const activeDeliveries = orders.filter(order => 
        ['picked_up', 'out_for_delivery'].includes(order.status)
      ).length;

      // حساب الأرباح
      const DELIVERY_FEE = 15; // رسوم التوصيل الافتراضية
      const COMMISSION_RATE = 0.9; // 90% للمندوب، 10% للتطبيق
      const earningPerDelivery = DELIVERY_FEE * COMMISSION_RATE;

      const totalEarnings = completedDeliveries * earningPerDelivery;

      // حساب أرباح هذا الشهر
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const thisMonthDeliveries = orders.filter(order => 
        order.status === 'delivered' && 
        order.deliveredAt && 
        order.deliveredAt.toDate() >= startOfMonth
      ).length;
      const thisMonthEarnings = thisMonthDeliveries * earningPerDelivery;

      // حساب أرباح هذا الأسبوع
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      const thisWeekDeliveries = orders.filter(order => 
        order.status === 'delivered' && 
        order.deliveredAt && 
        order.deliveredAt.toDate() >= startOfWeek
      ).length;
      const thisWeekEarnings = thisWeekDeliveries * earningPerDelivery;

      // حساب أرباح اليوم
      const startOfDay = new Date(now);
      startOfDay.setHours(0, 0, 0, 0);
      const todayDeliveries = orders.filter(order => 
        order.status === 'delivered' && 
        order.deliveredAt && 
        order.deliveredAt.toDate() >= startOfDay
      ).length;
      const todayEarnings = todayDeliveries * earningPerDelivery;

      // حساب متوسط وقت التوصيل
      const deliveredOrders = orders.filter(order => 
        order.status === 'delivered' && 
        order.pickedUpAt && 
        order.deliveredAt
      );

      let averageDeliveryTime = 0;
      if (deliveredOrders.length > 0) {
        const totalDeliveryTime = deliveredOrders.reduce((sum, order) => {
          const pickupTime = order.pickedUpAt!.toDate().getTime();
          const deliveryTime = order.deliveredAt!.toDate().getTime();
          return sum + (deliveryTime - pickupTime);
        }, 0);
        averageDeliveryTime = Math.round(totalDeliveryTime / deliveredOrders.length / (1000 * 60)); // تحويل إلى دقائق
      }

      // حساب نسبة التوصيل في الوقت المحدد (افتراض: 30 دقيقة هو الوقت المتوقع)
      const EXPECTED_DELIVERY_TIME = 30 * 60 * 1000; // 30 دقيقة بالميلي ثانية
      const onTimeDeliveries = deliveredOrders.filter(order => {
        const pickupTime = order.pickedUpAt!.toDate().getTime();
        const deliveryTime = order.deliveredAt!.toDate().getTime();
        return (deliveryTime - pickupTime) <= EXPECTED_DELIVERY_TIME;
      }).length;
      const onTimeDeliveryRate = deliveredOrders.length > 0 
        ? Math.round((onTimeDeliveries / deliveredOrders.length) * 100) 
        : 0;

      // جلب التقييمات (إذا كانت موجودة)
      let averageRating = 0;
      let totalRatings = 0;
      try {
        const ratingsQuery = query(
          collection(db, 'representative_ratings'),
          where('representativeUid', '==', representativeUid)
        );
        const ratingsSnapshot = await getDocs(ratingsQuery);
        
        if (!ratingsSnapshot.empty) {
          const ratings = ratingsSnapshot.docs.map(doc => doc.data().rating as number);
          totalRatings = ratings.length;
          averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / totalRatings;
          averageRating = Math.round(averageRating * 10) / 10; // تقريب إلى منزلة عشرية واحدة
        }
      } catch (ratingsError) {
        // مجموعة التقييمات قد لا تكون موجودة بعد
        console.log('Ratings collection not found, using default values');
      }

      setStats({
        totalDeliveries,
        completedDeliveries,
        activeDeliveries,
        totalEarnings,
        thisMonthEarnings,
        thisWeekEarnings,
        todayEarnings,
        averageRating,
        totalRatings,
        averageDeliveryTime,
        onTimeDeliveryRate,
      });

    } catch (err) {
      console.error('Error fetching representative stats:', err);
      setError('Failed to fetch representative statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [representativeUid]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  };
}

// Hook لجلب أفضل المندوبين (للاستخدام في لوحة الإدارة)
export interface TopRepresentative {
  uid: string;
  displayName: string;
  photoURL?: string;
  totalDeliveries: number;
  averageRating: number;
  totalEarnings: number;
}

export function useTopRepresentatives(limit: number = 10) {
  const [representatives, setRepresentatives] = useState<TopRepresentative[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTopRepresentatives = async () => {
      try {
        setLoading(true);
        setError(null);

        // جلب جميع المندوبين المعتمدين
        const representativesQuery = query(
          collection(db, 'representatives'),
          where('approvalStatus', '==', 'approved')
        );

        const representativesSnapshot = await getDocs(representativesQuery);
        const representativesData = representativesSnapshot.docs.map(doc => ({
          uid: doc.id,
          ...doc.data()
        }));

        // حساب إحصائيات كل مندوب
        const representativesWithStats = await Promise.all(
          representativesData.map(async (rep: any) => {
            const ordersQuery = query(
              collection(db, 'orders'),
              where('representativeUid', '==', rep.uid),
              where('status', '==', 'delivered')
            );

            const ordersSnapshot = await getDocs(ordersQuery);
            const totalDeliveries = ordersSnapshot.size;
            const totalEarnings = totalDeliveries * 15 * 0.9; // افتراضي

            // جلب التقييمات
            let averageRating = 0;
            try {
              const ratingsQuery = query(
                collection(db, 'representative_ratings'),
                where('representativeUid', '==', rep.uid)
              );
              const ratingsSnapshot = await getDocs(ratingsQuery);
              
              if (!ratingsSnapshot.empty) {
                const ratings = ratingsSnapshot.docs.map(doc => doc.data().rating as number);
                averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
              }
            } catch (error) {
              // تجاهل الأخطاء
            }

            return {
              uid: rep.uid,
              displayName: rep.displayName,
              photoURL: rep.photoURL,
              totalDeliveries,
              averageRating: Math.round(averageRating * 10) / 10,
              totalEarnings,
            };
          })
        );

        // ترتيب حسب إجمالي التوصيلات
        const sortedRepresentatives = representativesWithStats
          .sort((a, b) => b.totalDeliveries - a.totalDeliveries)
          .slice(0, limit);

        setRepresentatives(sortedRepresentatives);

      } catch (err) {
        console.error('Error fetching top representatives:', err);
        setError('Failed to fetch top representatives');
      } finally {
        setLoading(false);
      }
    };

    fetchTopRepresentatives();
  }, [limit]);

  return {
    representatives,
    loading,
    error
  };
}
