import React from 'react'
import { useMobile } from './use-mobile'

// مكون اختبار لاستخدام الـ hook
const TestComponent = () => {
  const isMobile = useMobile()
  
  return (
    <div data-testid="mobile-status">
      {isMobile ? 'موبايل' : 'سطح المكتب'}
    </div>
  )
}

describe('useMobile Hook', () => {
  it('يكتشف الشاشات الصغيرة بشكل صحيح', () => {
    // محاكاة شاشة موبايل
    cy.viewport(375, 667)
    cy.mount(<TestComponent />)
    cy.get('[data-testid="mobile-status"]').should('contain.text', 'موبايل')
  })

  it('يكتشف شاشات سطح المكتب بشكل صحيح', () => {
    // محاكاة شاشة سطح المكتب
    cy.viewport(1280, 720)
    cy.mount(<TestComponent />)
    cy.get('[data-testid="mobile-status"]').should('contain.text', 'سطح المكتب')
  })

  it('يتفاعل مع تغيير حجم الشاشة', () => {
    cy.mount(<TestComponent />)
    
    // البدء بشاشة كبيرة
    cy.viewport(1280, 720)
    cy.get('[data-testid="mobile-status"]').should('contain.text', 'سطح المكتب')
    
    // التغيير إلى شاشة صغيرة
    cy.viewport(375, 667)
    cy.get('[data-testid="mobile-status"]').should('contain.text', 'موبايل')
    
    // العودة إلى شاشة كبيرة
    cy.viewport(1024, 768)
    cy.get('[data-testid="mobile-status"]').should('contain.text', 'سطح المكتب')
  })

  it('يعمل مع نقاط التوقف المختلفة', () => {
    const breakpoints = [
      { width: 320, expected: 'موبايل' },
      { width: 480, expected: 'موبايل' },
      { width: 768, expected: 'سطح المكتب' },
      { width: 1024, expected: 'سطح المكتب' },
      { width: 1440, expected: 'سطح المكتب' }
    ]

    cy.mount(<TestComponent />)

    breakpoints.forEach(({ width, expected }) => {
      cy.viewport(width, 600)
      cy.get('[data-testid="mobile-status"]').should('contain.text', expected)
    })
  })
})
