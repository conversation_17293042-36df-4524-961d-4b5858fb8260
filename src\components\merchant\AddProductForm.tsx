// src/components/merchant/AddProductForm.tsx
"use client";

import { useState, type ChangeEvent, type FormEvent, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useLocale } from '@/hooks/use-locale';
import { useToast } from "@/hooks/use-toast";
import { Loader2, ImagePlus, XCircle } from 'lucide-react';
import { db } from '@/lib/firebase'; // Removed storage import
import { collection, doc, serverTimestamp, setDoc, getDoc, type Timestamp } from 'firebase/firestore';
import type { ProductDocument, StoreDocument } from '@/types';
import Image from 'next/image';

// Cloudinary Upload Function (ensure this is robustly handled in a real app)
async function uploadFileToCloudinary(file: File, uploadPreset: string, cloudName: string): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('upload_preset', uploadPreset);

  const response = await fetch(
    `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
    {
      method: 'POST',
      body: formData,
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error.message || 'Cloudinary upload failed');
  }
  const data = await response.json();
  return data.secure_url;
}


interface AddProductFormProps {
  merchantUid: string;
  maxProductImages: number;
}

const MAX_IMAGE_SIZE_MB = 5;

export default function AddProductForm({ merchantUid, maxProductImages }: AddProductFormProps) {
  const { t } = useLocale();
  const { toast } = useToast();

  const [storeName, setStoreName] = useState('');
  const [productName, setProductName] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [stockQuantity, setStockQuantity] = useState('');
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [isActive, setIsActive] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
  const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET;

  useEffect(() => {
    const fetchStoreName = async () => {
      if (!merchantUid) return;
      try {
        const storeDocRef = doc(db, "stores", merchantUid);
        const storeDocSnap = await getDoc(storeDocRef);
        if (storeDocSnap.exists()) {
          const storeData = storeDocSnap.data() as StoreDocument;
          setStoreName(storeData.storeName || t('defaultStoreName'));
        } else {
          setStoreName(t('defaultStoreName')); // Fallback
          console.warn("Store document not found for merchant to fetch store name.");
        }
      } catch (error) {
        console.error("Error fetching store name for product form:", error);
        setStoreName(t('defaultStoreName')); // Fallback
      }
    };
    fetchStoreName();
  }, [merchantUid, t]);


  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!productName.trim()) newErrors.productName = t('errorProductNameRequired');
    if (!price.trim() || isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      newErrors.price = t('errorPriceInvalid');
    }
    if (!category.trim()) newErrors.category = t('errorCategoryRequired');
    if (!stockQuantity.trim() || isNaN(parseInt(stockQuantity)) || parseInt(stockQuantity) < 0) {
      newErrors.stockQuantity = t('errorStockInvalid');
    }
    if (imageFiles.length === 0) {
      newErrors.images = t('errorMinOneImage');
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const filesArray = Array.from(event.target.files);
      const newImageFiles = [...imageFiles];
      const newImagePreviews = [...imagePreviews];

      for (const file of filesArray) {
        if (newImageFiles.length >= maxProductImages) {
          toast({ title: t('errorTitle'), description: t('errorMaxImagesReached', { count: maxProductImages }), variant: 'destructive' });
          break;
        }
        if (file.size > MAX_IMAGE_SIZE_MB * 1024 * 1024) {
          toast({ title: t('errorTitle'), description: t('errorImageSizeExceeded', { size: MAX_IMAGE_SIZE_MB }), variant: 'destructive' });
          continue;
        }
        if (!file.type.startsWith('image/')) {
          toast({ title: t('errorTitle'), description: t('errorFileTypeInvalid'), variant: 'destructive' });
          continue;
        }
        newImageFiles.push(file);
        newImagePreviews.push(URL.createObjectURL(file));
      }
      setImageFiles(newImageFiles);
      setImagePreviews(newImagePreviews);
    }
  };

  const removeImage = (index: number) => {
    setImageFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
    setImagePreviews(prevPreviews => {
      const newPreviews = prevPreviews.filter((_, i) => i !== index);
      URL.revokeObjectURL(prevPreviews[index]);
      return newPreviews;
    });
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!validateForm()) return;
    if (!cloudName || !uploadPreset) {
      toast({ title: t('errorTitle'), description: t('cloudinaryNotConfiguredError'), variant: "destructive" });
      return;
    }
    if (!storeName) {
        toast({ title: t('errorTitle'), description: t('storeNameNotLoadedError'), variant: "destructive" });
        return;
    }


    setIsLoading(true);
    toast({ title: t('savingProductTitle'), description: t('savingProductDesc') });

    try {
      const newProductRef = doc(collection(db, "products"));
      const productId = newProductRef.id;

      const imageUrls: string[] = [];
      toast({ title: t('uploadingProductImagesTitle'), description: t('uploadingProductImagesDesc') });
      for (let i = 0; i < imageFiles.length; i++) {
        try {
          const url = await uploadFileToCloudinary(imageFiles[i], uploadPreset, cloudName);
          imageUrls.push(url);
          toast({ title: t('imageUploadProgressTitle'), description: t('imageUploadProgressDesc', { current: i + 1, total: imageFiles.length, fileName: imageFiles[i].name }) });
        } catch (uploadError: any) {
          console.error(`Upload failed for image ${i + 1}:`, uploadError);
          toast({ title: t('errorTitle'), description: t('errorIndividualImageUploadFailed', { fileName: imageFiles[i].name, message: uploadError.message }), variant: 'destructive' });
          throw new Error(t('errorImageUploadFailedSummary'));
        }
      }

      if (imageUrls.length !== imageFiles.length && imageFiles.length > 0) {
        throw new Error(t('errorPartialImageUpload'));
      }

      const productData: ProductDocument = {
        id: productId,
        merchantUid: merchantUid,
        storeId: merchantUid, // Assuming storeId is the same as merchantUid for simplicity
        storeName: storeName,
        name: productName.trim(),
        description: description.trim(),
        price: parseFloat(price),
        currency: 'SAR',
        category: category.trim(),
        imageUrls: imageUrls,
        stockQuantity: parseInt(stockQuantity),
        isActive: isActive,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      };

      await setDoc(newProductRef, productData);

      toast({
        title: t('productAddedSuccessTitle'),
        description: t('productAddedSuccessMessage', { productName: productData.name }),
      });

      setProductName('');
      setDescription('');
      setPrice('');
      setCategory('');
      setStockQuantity('');
      setImageFiles([]);
      imagePreviews.forEach(preview => URL.revokeObjectURL(preview));
      setImagePreviews([]);
      setIsActive(true);
      setErrors({});

    } catch (error: any) {
      console.error("Error adding product:", error);
      toast({
        title: t('errorTitle'),
        description: error.message || t('errorAddingProductFailed'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <Label htmlFor="productName">{t('productName')}</Label>
        <Input
          id="productName"
          value={productName}
          onChange={(e) => setProductName(e.target.value)}
          disabled={isLoading}
          required
          className="mt-1"
        />
        {errors.productName && <p className="text-sm text-destructive mt-1">{errors.productName}</p>}
      </div>

      <div>
        <Label htmlFor="description">{t('productDescription')}</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          disabled={isLoading}
          className="mt-1"
          rows={4}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="price">{t('productPrice')}</Label>
          <Input
            id="price"
            type="number"
            value={price}
            onChange={(e) => setPrice(e.target.value)}
            disabled={isLoading}
            required
            min="0.01"
            step="0.01"
            className="mt-1"
            placeholder={t('currencySAR')}
          />
          {errors.price && <p className="text-sm text-destructive mt-1">{errors.price}</p>}
        </div>
        <div>
          <Label htmlFor="category">{t('productCategory')}</Label>
          <Input
            id="category"
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            disabled={isLoading}
            required
            className="mt-1"
            placeholder={t('productCategoryPlaceholder')}
          />
          {errors.category && <p className="text-sm text-destructive mt-1">{errors.category}</p>}
        </div>
      </div>

      <div>
        <Label htmlFor="stockQuantity">{t('productStockQuantity')}</Label>
        <Input
          id="stockQuantity"
          type="number"
          value={stockQuantity}
          onChange={(e) => setStockQuantity(e.target.value)}
          disabled={isLoading}
          required
          min="0"
          step="1"
          className="mt-1"
        />
        {errors.stockQuantity && <p className="text-sm text-destructive mt-1">{errors.stockQuantity}</p>}
      </div>

      <div>
        <Label htmlFor="productImages">{t('productImages')} ({t('imageTypesAndMaxSize', { size: MAX_IMAGE_SIZE_MB, count: maxProductImages })})</Label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md">
          <div className="space-y-1 text-center">
            <ImagePlus className="mx-auto h-12 w-12 text-muted-foreground" />
            <div className="flex text-sm text-muted-foreground">
              <Label
                htmlFor="productImagesInput"
                className="relative cursor-pointer rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary"
              >
                <span>{t('uploadFiles')}</span>
                <Input
                  id="productImagesInput"
                  name="productImagesInput"
                  type="file"
                  className="sr-only"
                  multiple
                  accept="image/*"
                  onChange={handleImageChange}
                  disabled={isLoading || imageFiles.length >= maxProductImages}
                />
              </Label>
              <p className="ps-1">{t('orDragAndDrop')}</p>
            </div>
            <p className="text-xs text-muted-foreground">{t('imageTypesAndMaxSize', { size: MAX_IMAGE_SIZE_MB, count: maxProductImages })}</p>
          </div>
        </div>
        {errors.images && <p className="text-sm text-destructive mt-1">{errors.images}</p>}
        {imagePreviews.length > 0 && (
          <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {imagePreviews.map((preview, index) => (
              <div key={index} className="relative group aspect-square">
                <Image
                  src={preview}
                  alt={`${t('productImagePreview')} ${index + 1}`}
                  fill
                  sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                  className="rounded-md object-cover"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute top-1 right-1 h-6 w-6 opacity-75 group-hover:opacity-100"
                  onClick={() => removeImage(index)}
                  disabled={isLoading}
                  aria-label={t('removeImage')}
                >
                  <XCircle className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2 rtl:space-x-reverse">
        <Checkbox
          id="isActive"
          checked={isActive}
          onCheckedChange={(checked) => setIsActive(checked as boolean)}
          disabled={isLoading}
        />
        <Label htmlFor="isActive" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {t('productIsActive')}
        </Label>
      </div>

      <Button type="submit" className="w-full bg-primary hover:bg-primary/90 text-primary-foreground" disabled={isLoading}>
        {isLoading && <Loader2 className="me-2 h-4 w-4 animate-spin" />}
        {isLoading ? t('savingProduct') : t('saveProduct')}
      </Button>
    </form>
  );
}
