// src/services/paymentService.ts
"use client";

import { doc, updateDoc, addDoc, collection, serverTimestamp, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// أنواع طرق الدفع
export type PaymentMethod = 'cash' | 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'stc_pay' | 'mada';

// حالات الدفع
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';

// واجهة بيانات الدفع
export interface PaymentData {
  id?: string;
  orderId: string;
  customerId: string;
  merchantUid: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  paypalOrderId?: string;
  gatewayResponse?: any;
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  createdAt?: any;
  updatedAt?: any;
  completedAt?: any;
}

// واجهة بيانات PayPal
export interface PayPalOrderData {
  intent: 'CAPTURE';
  purchase_units: Array<{
    reference_id: string;
    amount: {
      currency_code: string;
      value: string;
    };
    description?: string;
  }>;
  application_context?: {
    brand_name?: string;
    landing_page?: 'LOGIN' | 'BILLING' | 'NO_PREFERENCE';
    user_action?: 'PAY_NOW' | 'CONTINUE';
    return_url?: string;
    cancel_url?: string;
  };
}

// واجهة استجابة PayPal
export interface PayPalResponse {
  id: string;
  status: string;
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

class PaymentService {
  private paypalClientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
  private paypalBaseUrl = process.env.NODE_ENV === 'production' 
    ? 'https://api.paypal.com' 
    : 'https://api.sandbox.paypal.com';

  // إنشاء دفعة جديدة
  async createPayment(paymentData: Omit<PaymentData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> {
    try {
      const payment: Omit<PaymentData, 'id'> = {
        ...paymentData,
        status: 'pending',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const paymentRef = await addDoc(collection(db, 'payments'), payment);
      return paymentRef.id;
    } catch (error) {
      console.error('Error creating payment:', error);
      return null;
    }
  }

  // تحديث حالة الدفع
  async updatePaymentStatus(
    paymentId: string, 
    status: PaymentStatus, 
    transactionId?: string,
    gatewayResponse?: any
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: serverTimestamp()
      };

      if (transactionId) {
        updateData.transactionId = transactionId;
      }

      if (gatewayResponse) {
        updateData.gatewayResponse = gatewayResponse;
      }

      if (status === 'completed') {
        updateData.completedAt = serverTimestamp();
      }

      const paymentRef = doc(db, 'payments', paymentId);
      await updateDoc(paymentRef, updateData);
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }
  }

  // إنشاء طلب PayPal
  async createPayPalOrder(
    orderId: string,
    amount: number,
    currency: string = 'USD',
    description?: string
  ): Promise<PayPalResponse | null> {
    try {
      const accessToken = await this.getPayPalAccessToken();
      if (!accessToken) {
        throw new Error('Failed to get PayPal access token');
      }

      const orderData: PayPalOrderData = {
        intent: 'CAPTURE',
        purchase_units: [{
          reference_id: orderId,
          amount: {
            currency_code: currency,
            value: amount.toFixed(2)
          },
          description: description || `Order #${orderId}`
        }],
        application_context: {
          brand_name: 'مِخْلاة',
          landing_page: 'NO_PREFERENCE',
          user_action: 'PAY_NOW',
          return_url: `${window.location.origin}/payment/success`,
          cancel_url: `${window.location.origin}/payment/cancel`
        }
      };

      const response = await fetch(`${this.paypalBaseUrl}/v2/checkout/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
          'PayPal-Request-Id': `${orderId}-${Date.now()}`
        },
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`PayPal API error: ${errorData.message || response.statusText}`);
      }

      const paypalOrder = await response.json();
      return paypalOrder;
    } catch (error) {
      console.error('Error creating PayPal order:', error);
      return null;
    }
  }

  // الحصول على رمز الوصول من PayPal
  private async getPayPalAccessToken(): Promise<string | null> {
    try {
      if (!this.paypalClientId) {
        throw new Error('PayPal client ID not configured');
      }

      const clientSecret = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_SECRET;
      if (!clientSecret) {
        throw new Error('PayPal client secret not configured');
      }

      const auth = btoa(`${this.paypalClientId}:${clientSecret}`);

      const response = await fetch(`${this.paypalBaseUrl}/v1/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${auth}`
        },
        body: 'grant_type=client_credentials'
      });

      if (!response.ok) {
        throw new Error(`Failed to get access token: ${response.statusText}`);
      }

      const data = await response.json();
      return data.access_token;
    } catch (error) {
      console.error('Error getting PayPal access token:', error);
      return null;
    }
  }

  // التقاط دفعة PayPal
  async capturePayPalOrder(paypalOrderId: string): Promise<any> {
    try {
      const accessToken = await this.getPayPalAccessToken();
      if (!accessToken) {
        throw new Error('Failed to get PayPal access token');
      }

      const response = await fetch(`${this.paypalBaseUrl}/v2/checkout/orders/${paypalOrderId}/capture`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`PayPal capture error: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error capturing PayPal order:', error);
      throw error;
    }
  }

  // معالجة الدفع النقدي
  async processCashPayment(paymentId: string): Promise<boolean> {
    try {
      await this.updatePaymentStatus(paymentId, 'completed');
      return true;
    } catch (error) {
      console.error('Error processing cash payment:', error);
      return false;
    }
  }

  // معالجة دفع البطاقة (محاكاة)
  async processCardPayment(
    paymentId: string,
    cardData: {
      number: string;
      expiryMonth: string;
      expiryYear: string;
      cvv: string;
      holderName: string;
    }
  ): Promise<boolean> {
    try {
      // هذا مجرد محاكاة - في الواقع نحتاج لتكامل مع بوابة دفع حقيقية
      await this.updatePaymentStatus(paymentId, 'processing');
      
      // محاكاة معالجة البطاقة
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // محاكاة نجاح أو فشل الدفع
      const isSuccess = Math.random() > 0.1; // 90% نجاح
      
      if (isSuccess) {
        const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await this.updatePaymentStatus(paymentId, 'completed', transactionId);
        return true;
      } else {
        await this.updatePaymentStatus(paymentId, 'failed');
        return false;
      }
    } catch (error) {
      console.error('Error processing card payment:', error);
      await this.updatePaymentStatus(paymentId, 'failed');
      return false;
    }
  }

  // استرداد المبلغ
  async refundPayment(
    paymentId: string,
    refundAmount: number,
    reason: string
  ): Promise<boolean> {
    try {
      const paymentRef = doc(db, 'payments', paymentId);
      const paymentDoc = await getDoc(paymentRef);
      
      if (!paymentDoc.exists()) {
        throw new Error('Payment not found');
      }

      const paymentData = paymentDoc.data() as PaymentData;
      
      if (paymentData.status !== 'completed') {
        throw new Error('Cannot refund non-completed payment');
      }

      // تحديث بيانات الدفع
      await updateDoc(paymentRef, {
        status: 'refunded',
        refundAmount,
        refundReason: reason,
        updatedAt: serverTimestamp()
      });

      // إذا كان الدفع عبر PayPal، نحتاج لاستدعاء API الاسترداد
      if (paymentData.method === 'paypal' && paymentData.transactionId) {
        await this.processPayPalRefund(paymentData.transactionId, refundAmount);
      }

      return true;
    } catch (error) {
      console.error('Error refunding payment:', error);
      return false;
    }
  }

  // معالجة استرداد PayPal
  private async processPayPalRefund(transactionId: string, amount: number): Promise<void> {
    try {
      const accessToken = await this.getPayPalAccessToken();
      if (!accessToken) {
        throw new Error('Failed to get PayPal access token');
      }

      const refundData = {
        amount: {
          value: amount.toFixed(2),
          currency_code: 'USD'
        }
      };

      const response = await fetch(`${this.paypalBaseUrl}/v2/payments/captures/${transactionId}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify(refundData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`PayPal refund error: ${errorData.message || response.statusText}`);
      }
    } catch (error) {
      console.error('Error processing PayPal refund:', error);
      throw error;
    }
  }

  // جلب تفاصيل الدفع
  async getPaymentDetails(paymentId: string): Promise<PaymentData | null> {
    try {
      const paymentRef = doc(db, 'payments', paymentId);
      const paymentDoc = await getDoc(paymentRef);
      
      if (paymentDoc.exists()) {
        return {
          id: paymentDoc.id,
          ...paymentDoc.data()
        } as PaymentData;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting payment details:', error);
      return null;
    }
  }

  // التحقق من دعم طريقة الدفع
  isPaymentMethodSupported(method: PaymentMethod): boolean {
    const supportedMethods: PaymentMethod[] = ['cash', 'card', 'paypal'];
    
    // يمكن إضافة المزيد من طرق الدفع المحلية هنا
    if (typeof window !== 'undefined') {
      // التحقق من دعم Apple Pay
      if (method === 'apple_pay' && window.ApplePaySession) {
        supportedMethods.push('apple_pay');
      }
      
      // التحقق من دعم Google Pay
      if (method === 'google_pay' && window.google?.payments) {
        supportedMethods.push('google_pay');
      }
    }
    
    return supportedMethods.includes(method);
  }

  // حساب رسوم المعالجة
  calculateProcessingFee(amount: number, method: PaymentMethod): number {
    switch (method) {
      case 'cash':
        return 0;
      case 'card':
        return Math.max(amount * 0.025, 1); // 2.5% أو 1 ريال كحد أدنى
      case 'paypal':
        return amount * 0.034 + 1.2; // 3.4% + 1.2 ريال
      default:
        return 0;
    }
  }
}

export const paymentService = new PaymentService();
export default paymentService;
