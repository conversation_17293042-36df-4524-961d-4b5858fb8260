"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Store, 
  MapPin, 
  Phone, 
  Globe, 
  Clock, 
  Star, 
  Heart,
  Share2,
  AlertCircle,
  Instagram,
  Facebook,
  Twitter,
  Mail
} from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import ProductCard from "@/components/common/ProductCard";
import type { StoreDocument, ProductDocument } from "@/types";
import { doc, getDoc, collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface StorePageProps {
  params: {
    storeId: string;
  };
}

export default function StorePage() {
  const params = useParams();
  const storeId = params?.storeId as string;
  const { t } = useLocale();
  
  const [store, setStore] = useState<StoreDocument | null>(null);
  const [products, setProducts] = useState<ProductDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFollowing, setIsFollowing] = useState(false);
  const [activeTab, setActiveTab] = useState("products");

  // Fetch store data
  useEffect(() => {
    const fetchStore = async () => {
      if (!storeId) return;

      setIsLoading(true);
      setError(null);

      try {
        const storeDoc = await getDoc(doc(db, 'stores', storeId));
        
        if (storeDoc.exists()) {
          const storeData = { ...storeDoc.data(), id: storeDoc.id } as StoreDocument;
          setStore(storeData);
        } else {
          setError(t('storeNotFound'));
        }
      } catch (err) {
        console.error('Error fetching store:', err);
        setError(t('errorFetchingStore'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchStore();
  }, [storeId, t]);

  // Fetch store products
  useEffect(() => {
    const fetchProducts = async () => {
      if (!storeId) return;

      setIsLoadingProducts(true);

      try {
        const productsRef = collection(db, 'products');
        const productsQuery = query(
          productsRef,
          where('storeId', '==', storeId),
          where('isActive', '==', true)
        );

        const snapshot = await getDocs(productsQuery);
        const productsData: ProductDocument[] = [];

        snapshot.forEach((doc) => {
          productsData.push({ ...doc.data(), id: doc.id } as ProductDocument);
        });

        setProducts(productsData);
      } catch (err) {
        console.error('Error fetching products:', err);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, [storeId]);

  const getStoreInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatBusinessHours = (hours: any) => {
    if (!hours) return t('hoursNotAvailable');
    
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const today = new Date().getDay();
    const todayKey = days[today];
    
    if (hours[todayKey]) {
      const todayHours = hours[todayKey];
      if (todayHours.isOpen) {
        return `${todayHours.open} - ${todayHours.close}`;
      } else {
        return t('closedToday');
      }
    }
    
    return t('hoursNotAvailable');
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    // TODO: Implement follow/unfollow logic
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: store?.storeName,
        text: store?.storeDescription,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header Skeleton */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <Skeleton className="h-20 w-20 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Products Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <Skeleton className="h-40 w-full" />
                <CardContent className="p-4 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-8 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !store) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || t('storeNotFound')}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Store Header */}
      <Card className="mb-8">
        {/* Banner */}
        {store.bannerUrl && (
          <div className="relative h-48 md:h-64 overflow-hidden rounded-t-lg">
            <img 
              src={store.bannerUrl} 
              alt={store.storeName}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/20" />
          </div>
        )}

        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-start space-y-4 md:space-y-0 md:space-x-6 rtl:md:space-x-reverse">
            {/* Store Avatar */}
            <Avatar className="h-24 w-24 border-4 border-background shadow-lg">
              <AvatarImage src={store.logoUrl} alt={store.storeName} />
              <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                {getStoreInitials(store.storeName)}
              </AvatarFallback>
            </Avatar>

            {/* Store Info */}
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold mb-2">{store.storeName}</h1>
                  <p className="text-muted-foreground mb-3">
                    {store.storeDescription}
                  </p>
                  
                  {/* Rating and Stats */}
                  <div className="flex items-center space-x-4 rtl:space-x-reverse mb-3">
                    {store.stats?.averageRating && (
                      <div className="flex items-center">
                        <Star className="w-5 h-5 fill-yellow-400 text-yellow-400 mr-1" />
                        <span className="font-semibold">{store.stats.averageRating.toFixed(1)}</span>
                        <span className="text-muted-foreground ml-1">
                          ({store.stats.reviewCount || 0} {t('reviews')})
                        </span>
                      </div>
                    )}
                    
                    {store.stats?.totalOrders && (
                      <Badge variant="secondary">
                        {store.stats.totalOrders} {t('orders')}
                      </Badge>
                    )}
                  </div>

                  {/* Categories */}
                  {store.categories && store.categories.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {store.categories.map((category, index) => (
                        <Badge key={index} variant="outline">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <Button
                    variant={isFollowing ? "default" : "outline"}
                    onClick={handleFollow}
                  >
                    <Heart className={`w-4 h-4 mr-2 ${isFollowing ? 'fill-current' : ''}`} />
                    {isFollowing ? t('following') : t('follow')}
                  </Button>
                  <Button variant="outline" onClick={handleShare}>
                    <Share2 className="w-4 h-4 mr-2" />
                    {t('share')}
                  </Button>
                </div>
              </div>

              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                {store.address && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>
                      {store.address.city}, {store.address.country}
                    </span>
                  </div>
                )}
                
                {store.phoneNumber && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>{store.phoneNumber}</span>
                  </div>
                )}
                
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                  <span>{formatBusinessHours(store.businessHours)}</span>
                </div>

                {store.website && (
                  <div className="flex items-center">
                    <Globe className="w-4 h-4 mr-2 text-muted-foreground" />
                    <a 
                      href={store.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      {t('website')}
                    </a>
                  </div>
                )}
              </div>

              {/* Social Media */}
              {store.socialMedia && (
                <div className="flex items-center space-x-3 rtl:space-x-reverse mt-4">
                  {store.socialMedia.instagram && (
                    <a 
                      href={store.socialMedia.instagram} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary"
                    >
                      <Instagram className="w-5 h-5" />
                    </a>
                  )}
                  {store.socialMedia.facebook && (
                    <a 
                      href={store.socialMedia.facebook} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary"
                    >
                      <Facebook className="w-5 h-5" />
                    </a>
                  )}
                  {store.socialMedia.twitter && (
                    <a 
                      href={store.socialMedia.twitter} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary"
                    >
                      <Twitter className="w-5 h-5" />
                    </a>
                  )}
                  {store.email && (
                    <a 
                      href={`mailto:${store.email}`}
                      className="text-muted-foreground hover:text-primary"
                    >
                      <Mail className="w-5 h-5" />
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Store Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="products">{t('products')} ({products.length})</TabsTrigger>
          <TabsTrigger value="about">{t('about')}</TabsTrigger>
          <TabsTrigger value="reviews">{t('reviews')}</TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="mt-6">
          {isLoadingProducts ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i}>
                  <Skeleton className="h-40 w-full" />
                  <CardContent className="p-4 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-8 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : products.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Store className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">{t('noProductsInStore')}</h3>
                <p className="text-muted-foreground">
                  {t('storeHasNoProducts')}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  showStore={false}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="about" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('aboutStore')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">{t('description')}</h4>
                <p className="text-muted-foreground">
                  {store.storeDescription || t('noDescriptionAvailable')}
                </p>
              </div>
              
              {store.address && (
                <div>
                  <h4 className="font-semibold mb-2">{t('address')}</h4>
                  <p className="text-muted-foreground">
                    {store.address.street && `${store.address.street}, `}
                    {store.address.city}, {store.address.country}
                    {store.address.postalCode && ` ${store.address.postalCode}`}
                  </p>
                </div>
              )}

              {store.businessHours && (
                <div>
                  <h4 className="font-semibold mb-2">{t('businessHours')}</h4>
                  <div className="space-y-1 text-sm">
                    {Object.entries(store.businessHours).map(([day, hours]: [string, any]) => (
                      <div key={day} className="flex justify-between">
                        <span className="capitalize">{t(day)}</span>
                        <span className="text-muted-foreground">
                          {hours.isOpen ? `${hours.open} - ${hours.close}` : t('closed')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="mt-6">
          <Card>
            <CardContent className="text-center py-12">
              <Star className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('reviewsComingSoon')}</h3>
              <p className="text-muted-foreground">
                {t('reviewsFeatureWillBeAvailable')}
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
