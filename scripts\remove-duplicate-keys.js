#!/usr/bin/env node

/**
 * سكريبت لإزالة المفاتيح المكررة من ملفات الترجمة
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * قراءة ملف JSON مع الحفاظ على التنسيق
 */
function readJsonFileRaw(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return content;
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * إزالة المفاتيح المكررة من ملف JSON بطريقة آمنة
 */
function removeDuplicateKeys(filePath) {
  console.log(`🔧 معالجة الملف: ${filePath}`);

  try {
    // قراءة الملف كـ JSON object أولاً للتأكد من صحته
    const jsonContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // تحويل إلى نص مع تنسيق جميل
    const formattedContent = JSON.stringify(jsonContent, null, 2);

    // إنشاء نسخة احتياطية مع timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = filePath + `.backup.${timestamp}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath, 'utf8'));
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${backupPath}`);

    // كتابة الملف المنظف
    fs.writeFileSync(filePath, formattedContent);
    console.log(`   ✅ تم تنظيف الملف وإعادة تنسيقه`);

    // حساب عدد المفاتيح
    const keyCount = countKeys(jsonContent);
    console.log(`   📊 عدد المفاتيح: ${keyCount}`);

    return { success: true, duplicatesRemoved: 0, keyCount };

  } catch (error) {
    console.error(`   ❌ خطأ في معالجة الملف:`, error.message);
    return { success: false, duplicatesRemoved: 0 };
  }
}

/**
 * حساب عدد المفاتيح في كائن JSON بشكل تكراري
 */
function countKeys(obj) {
  let count = 0;

  function traverse(current) {
    if (typeof current === 'object' && current !== null) {
      if (Array.isArray(current)) {
        current.forEach(item => traverse(item));
      } else {
        count += Object.keys(current).length;
        Object.values(current).forEach(value => traverse(value));
      }
    }
  }

  traverse(obj);
  return count;
}

/**
 * التحقق من صحة JSON بعد التنظيف
 */
function validateJson(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    JSON.parse(content);
    console.log(`   ✅ الملف صحيح بعد التنظيف`);
    return true;
  } catch (error) {
    console.error(`   ❌ الملف غير صحيح بعد التنظيف:`, error.message);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧹 بدء إزالة المفاتيح المكررة من ملفات الترجمة...\n');

  let success = true;
  let totalDuplicatesRemoved = 0;

  // معالجة الملف العربي
  console.log('📝 معالجة الملف العربي:');
  const arResult = removeDuplicateKeys(AR_TRANSLATIONS_PATH);
  if (arResult.success) {
    totalDuplicatesRemoved += arResult.duplicatesRemoved;
    if (!validateJson(AR_TRANSLATIONS_PATH)) {
      success = false;
    }
  } else {
    success = false;
  }

  console.log('');

  // معالجة الملف الإنجليزي
  console.log('📝 معالجة الملف الإنجليزي:');
  const enResult = removeDuplicateKeys(EN_TRANSLATIONS_PATH);
  if (enResult.success) {
    totalDuplicatesRemoved += enResult.duplicatesRemoved;
    if (!validateJson(EN_TRANSLATIONS_PATH)) {
      success = false;
    }
  } else {
    success = false;
  }

  console.log('');

  if (success) {
    console.log('🎉 تم تنظيف جميع الملفات بنجاح!');
    console.log(`📊 إجمالي المفاتيح المكررة المحذوفة: ${totalDuplicatesRemoved}`);
    console.log('💡 تم إنشاء نسخ احتياطية بامتداد .backup');
    console.log('🔍 يمكنك الآن تشغيل سكريبت التحقق مرة أخرى للتأكد من النتائج');

    // تشغيل التحقق التلقائي
    console.log('\n🔍 تشغيل التحقق التلقائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في تنظيف بعض الملفات');
    console.log('🔄 يمكنك استعادة النسخ الاحتياطية إذا لزم الأمر');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  removeDuplicateKeys,
  validateJson
};
