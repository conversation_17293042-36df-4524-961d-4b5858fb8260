"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Search, X, Clock, TrendingUp, MapPin } from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import { useRouter } from "next/navigation";

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'product' | 'store' | 'category' | 'recent' | 'trending';
  icon?: React.ReactNode;
  metadata?: {
    storeId?: string;
    storeName?: string;
    category?: string;
    location?: string;
  };
}

interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  showSuggestions?: boolean;
  variant?: "default" | "compact" | "hero";
  className?: string;
  autoFocus?: boolean;
}

export default function SearchBar({
  placeholder,
  onSearch,
  onSuggestionSelect,
  showSuggestions = true,
  variant = "default",
  className = "",
  autoFocus = false
}: SearchBarProps) {
  const { t, locale } = useLocale();
  const router = useRouter();
  const [query, setQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('mikhla_recent_searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading recent searches:', error);
      }
    }
  }, []);

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Mock trending searches and categories
  const trendingSearches = [
    "قهوة عربية",
    "حرف يدوية",
    "ملابس تقليدية",
    "عسل طبيعي",
    "تمور فاخرة"
  ];

  const popularCategories = [
    "البقالة",
    "الحرف اليدوية", 
    "الأزياء",
    "الصحة والجمال",
    "الإلكترونيات"
  ];

  // Generate suggestions based on query
  useEffect(() => {
    const loadSuggestions = async () => {
      if (!query.trim()) {
        // Show recent searches and trending when no query
        const recentSuggestions: SearchSuggestion[] = recentSearches.slice(0, 3).map((search, index) => ({
          id: `recent-${index}`,
          text: search,
          type: 'recent',
          icon: <Clock className="w-4 h-4" />
        }));

        const trendingSuggestions: SearchSuggestion[] = trendingSearches.slice(0, 3).map((search, index) => ({
          id: `trending-${index}`,
          text: search,
          type: 'trending',
          icon: <TrendingUp className="w-4 h-4" />
        }));

        const categorySuggestions: SearchSuggestion[] = popularCategories.slice(0, 2).map((category, index) => ({
          id: `category-${index}`,
          text: category,
          type: 'category',
          icon: <Search className="w-4 h-4" />
        }));

        setSuggestions([...recentSuggestions, ...trendingSuggestions, ...categorySuggestions]);
        return;
      }

      // البحث الحقيقي في قاعدة البيانات
      setIsLoading(true);

      try {
        const searchResults = await fetchSearchSuggestions(query);
        setSuggestions(searchResults);
      } catch (error) {
        console.error('خطأ في جلب اقتراحات البحث:', error);
        // في حالة الخطأ، عرض الاقتراحات المحلية
        const fallbackSuggestions = generateFallbackSuggestions(query);
        setSuggestions(fallbackSuggestions);
      } finally {
        setIsLoading(false);
      }
    };

    loadSuggestions();
  }, [query, recentSearches]);

  // جلب اقتراحات البحث من قاعدة البيانات
  const fetchSearchSuggestions = async (searchQuery: string): Promise<SearchSuggestion[]> => {
    const suggestions: SearchSuggestion[] = [];

    try {
      // البحث في المنتجات
      const productsResponse = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}&type=products&limit=3`);
      if (productsResponse.ok) {
        const products = await productsResponse.json();
        products.forEach((product: any, index: number) => {
          suggestions.push({
            id: `product-${product.id}`,
            text: product.name,
            type: 'product',
            icon: <Search className="w-4 h-4" />,
            metadata: { productId: product.id, price: product.price }
          });
        });
      }

      // البحث في المتاجر
      const storesResponse = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}&type=stores&limit=2`);
      if (storesResponse.ok) {
        const stores = await storesResponse.json();
        stores.forEach((store: any) => {
          suggestions.push({
            id: `store-${store.id}`,
            text: store.storeName,
            type: 'store',
            icon: <MapPin className="w-4 h-4" />,
            metadata: { storeId: store.id, location: store.address?.city }
          });
        });
      }

      // إضافة الاقتراحات الشائعة المطابقة
      const matchingTrending = trendingSearches
        .filter(search => search.toLowerCase().includes(searchQuery.toLowerCase()))
        .slice(0, 2)
        .map((search, index) => ({
          id: `trending-match-${index}`,
          text: search,
          type: 'trending' as const,
          icon: <TrendingUp className="w-4 h-4" />
        }));

      suggestions.push(...matchingTrending);

    } catch (error) {
      console.error('خطأ في جلب اقتراحات البحث:', error);
      throw error;
    }

    return suggestions.slice(0, 8); // الحد الأقصى 8 اقتراحات
  };

  // إنشاء اقتراحات احتياطية في حالة الخطأ
  const generateFallbackSuggestions = (searchQuery: string): SearchSuggestion[] => {
    const fallbackSuggestions: SearchSuggestion[] = [];

    // إضافة اقتراح البحث العام
    fallbackSuggestions.push({
      id: 'fallback-general',
      text: `البحث عن "${searchQuery}"`,
      type: 'product',
      icon: <Search className="w-4 h-4" />
    });

    // إضافة الاقتراحات الشائعة المطابقة
    const matchingTrending = trendingSearches
      .filter(search => search.toLowerCase().includes(searchQuery.toLowerCase()))
      .slice(0, 3)
      .map((search, index) => ({
        id: `fallback-trending-${index}`,
        text: search,
        type: 'trending' as const,
        icon: <TrendingUp className="w-4 h-4" />
      }));

    fallbackSuggestions.push(...matchingTrending);

    return fallbackSuggestions;
  };

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    // Save to recent searches
    const updatedRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 10);
    setRecentSearches(updatedRecent);
    localStorage.setItem('mikhla_recent_searches', JSON.stringify(updatedRecent));

    // Close suggestions
    setIsOpen(false);

    // Call onSearch callback or navigate
    if (onSearch) {
      onSearch(searchQuery);
    } else {
      router.push(`/${locale}/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    } else {
      handleSearch(suggestion.text);
    }
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('mikhla_recent_searches');
    setSuggestions(suggestions.filter(s => s.type !== 'recent'));
  };

  const removeRecentSearch = (searchText: string) => {
    const updated = recentSearches.filter(s => s !== searchText);
    setRecentSearches(updated);
    localStorage.setItem('mikhla_recent_searches', JSON.stringify(updated));
    setSuggestions(suggestions.filter(s => s.text !== searchText));
  };

  const getInputSize = () => {
    switch (variant) {
      case "compact":
        return "sm";
      case "hero":
        return "lg";
      default:
        return "default";
    }
  };

  const getContainerClasses = () => {
    const base = "relative w-full";
    switch (variant) {
      case "compact":
        return `${base} max-w-sm`;
      case "hero":
        return `${base} max-w-2xl`;
      default:
        return `${base} max-w-md`;
    }
  };

  return (
    <div ref={containerRef} className={`${getContainerClasses()} ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder || t('searchPlaceholder')}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
          onFocus={() => showSuggestions && setIsOpen(true)}
          className={`pl-10 rtl:pr-10 rtl:pl-3 pr-10 ${variant === "hero" ? "h-12 text-lg" : ""}`}
          size={getInputSize()}
          autoFocus={autoFocus}
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 rtl:left-2 rtl:right-auto top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => {
              setQuery("");
              inputRef.current?.focus();
            }}
          >
            <X className="w-3 h-3" />
          </Button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && showSuggestions && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-96 overflow-y-auto">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="p-4 text-center text-muted-foreground">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2" />
                {t('searching')}
              </div>
            ) : suggestions.length > 0 ? (
              <div className="py-2">
                {/* Recent searches header */}
                {suggestions.some(s => s.type === 'recent') && (
                  <div className="flex items-center justify-between px-4 py-2 border-b">
                    <span className="text-sm font-medium text-muted-foreground">
                      {t('recentSearches')}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 text-xs"
                      onClick={clearRecentSearches}
                    >
                      {t('clearAll')}
                    </Button>
                  </div>
                )}

                {suggestions.map((suggestion) => (
                  <div
                    key={suggestion.id}
                    className="flex items-center justify-between px-4 py-2 hover:bg-muted cursor-pointer group"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <div className="flex items-center space-x-3 rtl:space-x-reverse flex-1">
                      <div className="text-muted-foreground">
                        {suggestion.icon}
                      </div>
                      <span className="text-sm">{suggestion.text}</span>
                      {suggestion.type === 'trending' && (
                        <Badge variant="secondary" className="text-xs">
                          {t('trending')}
                        </Badge>
                      )}
                    </div>
                    
                    {suggestion.type === 'recent' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeRecentSearch(suggestion.text);
                        }}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                ))}

                {/* Trending section */}
                {suggestions.some(s => s.type === 'trending') && !query && (
                  <div className="px-4 py-2 border-t">
                    <span className="text-sm font-medium text-muted-foreground">
                      {t('trendingSearches')}
                    </span>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-4 text-center text-muted-foreground text-sm">
                {t('noSuggestions')}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
