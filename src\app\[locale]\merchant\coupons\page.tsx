'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Plus, 
  TrendingUp, 
  Users, 
  DollarSign,
  Tag,
  Calendar,
  Activity
} from 'lucide-react';
import { CreateCouponForm } from '@/components/merchant/CreateCouponForm';
import { CouponsList } from '@/components/merchant/CouponsList';
import { couponService } from '@/services/couponService';
import { CouponDocument, CouponAnalytics } from '@/types/coupon';
import { useLocale } from '@/hooks/use-locale';
import { toast } from 'sonner';

export default function CouponsPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const [activeTab, setActiveTab] = useState('list');
  const [selectedCoupon, setSelectedCoupon] = useState<CouponDocument | null>(null);
  const [analytics, setAnalytics] = useState<CouponAnalytics | null>(null);
  const [loadingAnalytics, setLoadingAnalytics] = useState(true);

  useEffect(() => {
    if (user?.uid) {
      loadAnalytics();
    }
  }, [user?.uid]);

  const loadAnalytics = async () => {
    if (!user?.uid) return;

    try {
      setLoadingAnalytics(true);
      const analyticsData = await couponService.getCouponAnalytics(user.uid);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast.error('فشل في تحميل الإحصائيات');
    } finally {
      setLoadingAnalytics(false);
    }
  };

  const handleCreateSuccess = () => {
    setActiveTab('list');
    loadAnalytics();
    toast.success('تم إنشاء الكوبون بنجاح');
  };

  const handleEditCoupon = (coupon: CouponDocument) => {
    setSelectedCoupon(coupon);
    setActiveTab('edit');
  };

  const handleCreateNew = () => {
    setSelectedCoupon(null);
    setActiveTab('create');
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <p>يرجى تسجيل الدخول للوصول إلى هذه الصفحة</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* العنوان الرئيسي */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة الكوبونات</h1>
          <p className="text-gray-600 mt-1">
            أنشئ وأدر كوبونات الخصم لمتجرك
          </p>
        </div>
        <Button onClick={handleCreateNew} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          كوبون جديد
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الكوبونات</p>
                  <p className="text-2xl font-bold">{analytics.totalCoupons}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Tag className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">كوبونات نشطة</p>
                  <p className="text-2xl font-bold text-green-600">{analytics.activeCoupons}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Activity className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الاستخدام</p>
                  <p className="text-2xl font-bold">{analytics.totalUsage}</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الخصومات</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {analytics.totalDiscount.toFixed(2)} ريال
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <DollarSign className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* التبويبات الرئيسية */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list">قائمة الكوبونات</TabsTrigger>
          <TabsTrigger value="create">إنشاء كوبون</TabsTrigger>
          <TabsTrigger value="analytics">التحليلات</TabsTrigger>
        </TabsList>

        {/* قائمة الكوبونات */}
        <TabsContent value="list" className="space-y-4">
          <CouponsList
            merchantId={user.uid}
            onEditCoupon={handleEditCoupon}
            onCreateNew={handleCreateNew}
          />
        </TabsContent>

        {/* إنشاء كوبون جديد */}
        <TabsContent value="create" className="space-y-4">
          <CreateCouponForm
            merchantId={user.uid}
            onSuccess={handleCreateSuccess}
            onCancel={() => setActiveTab('list')}
          />
        </TabsContent>

        {/* التحليلات */}
        <TabsContent value="analytics" className="space-y-4">
          {loadingAnalytics ? (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              </CardContent>
            </Card>
          ) : analytics ? (
            <div className="space-y-6">
              {/* أفضل الكوبونات أداءً */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    أفضل الكوبونات أداءً
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analytics.topPerformingCoupons.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">
                      لا توجد بيانات كافية لعرض الإحصائيات
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {analytics.topPerformingCoupons.slice(0, 5).map((coupon, index) => (
                        <div key={coupon.couponId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <Badge variant="outline">#{index + 1}</Badge>
                            <div>
                              <div className="font-medium">{coupon.code}</div>
                              <div className="text-sm text-gray-500">
                                {coupon.usageCount} استخدام
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium text-green-600">
                              {coupon.totalDiscount.toFixed(2)} ريال
                            </div>
                            <div className="text-sm text-gray-500">
                              {coupon.conversionRate.toFixed(1)}% معدل التحويل
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* الاستخدام حسب النوع */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    الاستخدام حسب نوع الكوبون
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.usageByType).map(([type, usage]) => (
                      <div key={type} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {type === 'percentage' ? 'نسبة مئوية' :
                             type === 'fixed' ? 'مبلغ ثابت' :
                             type === 'free_shipping' ? 'شحن مجاني' : type}
                          </Badge>
                        </div>
                        <div className="font-medium">{usage} استخدام</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* الاستخدام الشهري */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    الاستخدام الشهري
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analytics.usageByMonth.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">
                      لا توجد بيانات شهرية متاحة
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {analytics.usageByMonth.slice(-6).map((monthData) => (
                        <div key={`${monthData.year}-${monthData.month}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium">
                              {monthData.month}/{monthData.year}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{monthData.usageCount} استخدام</div>
                            <div className="text-sm text-gray-500">
                              {monthData.totalDiscount.toFixed(2)} ريال خصم
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">فشل في تحميل البيانات</p>
                <Button onClick={loadAnalytics} className="mt-4">
                  إعادة المحاولة
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
