#!/usr/bin/env node

/**
 * سكريبت ذكي لتنظيف ملفات JSON من المفاتيح المكررة
 * يقوم بتحليل الملف سطر بسطر وإزالة التكرارات
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  fs.copyFileSync(filePath, backupPath);
  console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
  return backupPath;
}

/**
 * تنظيف المفاتيح المكررة من ملف JSON
 */
function cleanDuplicateKeys(filePath) {
  console.log(`\n🔧 تنظيف المفاتيح المكررة في: ${path.basename(filePath)}`);

  // إنشاء نسخة احتياطية
  createBackup(filePath);

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    const seenKeys = new Set();
    const cleanedLines = [];
    let removedCount = 0;
    
    console.log(`📊 تحليل ${lines.length} سطر...`);
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // البحث عن مفاتيح JSON
      const keyMatch = trimmedLine.match(/^\s*"([^"]+)"\s*:/);
      
      if (keyMatch) {
        const key = keyMatch[1];
        
        if (seenKeys.has(key)) {
          // مفتاح مكرر - تجاهله
          console.log(`❌ حذف مفتاح مكرر: "${key}" في السطر ${i + 1}`);
          removedCount++;
          continue;
        } else {
          // مفتاح جديد - إضافته
          seenKeys.add(key);
        }
      }
      
      cleanedLines.push(line);
    }
    
    // إعادة بناء المحتوى
    const cleanedContent = cleanedLines.join('\n');
    
    // التحقق من صحة JSON
    try {
      JSON.parse(cleanedContent);
      console.log('✅ تم التحقق من صحة تنسيق JSON');
    } catch (error) {
      console.error('❌ خطأ في تنسيق JSON:', error.message);
      throw error;
    }
    
    // كتابة الملف المنظف
    fs.writeFileSync(filePath, cleanedContent, 'utf8');
    
    console.log(`✅ تم حذف ${removedCount} مفتاح مكرر`);
    console.log(`✅ تم الاحتفاظ بـ ${seenKeys.size} مفتاح فريد`);
    
    return removedCount;
    
  } catch (error) {
    console.error(`❌ خطأ في تنظيف الملف ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * إضافة الترجمات المفقودة الأساسية
 */
function addEssentialMissingTranslations() {
  console.log('\n🔧 إضافة الترجمات المفقودة الأساسية...');
  
  // الترجمات الأساسية المفقودة
  const essentialTranslations = {
    ar: {
      "reviews.noReviews": "لا توجد تقييمات",
      "reviews.beFirstToReview": "كن أول من يقيم",
      "reviews.helpful": "مفيد",
      "reviews.report": "إبلاغ",
      "reviews.confirmDelete": "تأكيد الحذف",
      "reviews.viewImages": "عرض الصور",
      "reviews.loginToReview": "سجل دخولك للتقييم",
      "reviews.loginToReviewDescription": "يجب تسجيل الدخول لإضافة تقييم",
      "reviews.alreadyReviewed": "تم التقييم مسبقاً",
      "reviews.alreadyReviewedDescription": "لقد قمت بتقييم هذا المنتج مسبقاً",
      "closedToday": "مغلق اليوم",
      "hoursNotAvailable": "ساعات العمل غير متاحة",
      "following": "متابعة"
    },
    en: {
      "representative": "Representative",
      "representativeObj.dashboard.welcome": "Welcome, {{name}}!",
      "representativeObj.dashboard.subtitle": "Manage delivery orders and earnings from here",
      "representativeObj.dashboard.completed": "Completed",
      "representativeObj.dashboard.onTimeRate": "On-time delivery rate",
      "representativeObj.dashboard.avgDeliveryTime": "Average delivery time",
      "reviews": "Reviews",
      "closedToday": "Closed Today",
      "hoursNotAvailable": "Hours Not Available",
      "following": "Following",
      "reviews.noReviews": "No reviews",
      "reviews.beFirstToReview": "Be the first to review",
      "reviews.helpful": "Helpful",
      "reviews.report": "Report",
      "reviews.confirmDelete": "Confirm Delete",
      "reviews.viewImages": "View Images",
      "reviews.loginToReview": "Login to Review",
      "reviews.loginToReviewDescription": "You must be logged in to add a review",
      "reviews.alreadyReviewed": "Already Reviewed",
      "reviews.alreadyReviewedDescription": "You have already reviewed this product"
    }
  };
  
  // إضافة الترجمات للملف العربي
  try {
    const arContent = fs.readFileSync(AR_TRANSLATIONS_PATH, 'utf8');
    const arTranslations = JSON.parse(arContent);
    
    let addedCount = 0;
    Object.entries(essentialTranslations.ar).forEach(([key, value]) => {
      if (!arTranslations[key]) {
        arTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للعربية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(AR_TRANSLATIONS_PATH, JSON.stringify(arTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة عربية جديدة`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات العربية:', error.message);
  }
  
  // إضافة الترجمات للملف الإنجليزي
  try {
    const enContent = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const enTranslations = JSON.parse(enContent);
    
    let addedCount = 0;
    Object.entries(essentialTranslations.en).forEach(([key, value]) => {
      if (!enTranslations[key]) {
        enTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للإنجليزية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(EN_TRANSLATIONS_PATH, JSON.stringify(enTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة إنجليزية جديدة`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات الإنجليزية:', error.message);
  }
}

/**
 * إنشاء تقرير مفصل
 */
function generateCleanupReport(arRemoved, enRemoved) {
  const reportPath = path.join(__dirname, '../docs/cleanup-report.md');
  const timestamp = new Date().toLocaleString('ar-SA');

  let report = `# تقرير تنظيف المفاتيح المكررة\n\n`;
  report += `**تاريخ التنظيف**: ${timestamp}\n\n`;

  report += `## ملخص التنظيف\n\n`;
  report += `- **الملف العربي**: ${arRemoved} مفتاح مكرر تم حذفه\n`;
  report += `- **الملف الإنجليزي**: ${enRemoved} مفتاح مكرر تم حذفه\n`;
  report += `- **إجمالي المفاتيح المحذوفة**: ${arRemoved + enRemoved}\n\n`;

  report += `## التوصيات\n\n`;
  report += `1. تشغيل سكريبت التحقق للتأكد من التنظيف: \`node scripts/validate-translations.js\`\n`;
  report += `2. اختبار التطبيق للتأكد من عمل الترجمات بشكل صحيح\n`;
  report += `3. مراجعة النسخ الاحتياطية في حالة الحاجة للتراجع\n\n`;

  fs.writeFileSync(reportPath, report, 'utf8');
  console.log(`📄 تم إنشاء تقرير التنظيف: ${path.basename(reportPath)}`);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء التنظيف الذكي للمفاتيح المكررة...\n');

  try {
    // تنظيف المفاتيح المكررة في كلا الملفين
    const arRemoved = cleanDuplicateKeys(AR_TRANSLATIONS_PATH);
    const enRemoved = cleanDuplicateKeys(EN_TRANSLATIONS_PATH);

    // إضافة الترجمات المفقودة الأساسية
    addEssentialMissingTranslations();

    // إنشاء تقرير مفصل
    generateCleanupReport(arRemoved, enRemoved);

    console.log('\n📋 ملخص التنظيف:');
    console.log(`✅ الملف العربي: ${arRemoved} مفتاح مكرر تم حذفه`);
    console.log(`✅ الملف الإنجليزي: ${enRemoved} مفتاح مكرر تم حذفه`);
    console.log(`✅ إجمالي المفاتيح المحذوفة: ${arRemoved + enRemoved}`);
    console.log('✅ تم إضافة الترجمات الأساسية المفقودة');

    if (arRemoved > 0 || enRemoved > 0) {
      console.log('\n🎉 تم الانتهاء من التنظيف بنجاح!');
      console.log('💡 يُنصح بتشغيل سكريبت التحقق للتأكد من النتائج:');
      console.log('   node scripts/validate-translations.js');
    } else {
      console.log('\n✨ ممتاز! لا توجد مفاتيح مكررة تحتاج تنظيف');
    }

  } catch (error) {
    console.error('\n❌ فشل في تنفيذ التنظيف:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  cleanDuplicateKeys,
  addEssentialMissingTranslations,
  generateCleanupReport,
  createBackup
};
