"use client";

import { useState, useEffect } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { useAuth } from '@/context/AuthContext';
import { db } from '@/lib/firebase';
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';
import { Loader2, ShoppingBag, ExternalLink, Clock } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from 'next/link';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

interface Order {
  id: string;
  orderNumber: string;
  date: Date;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: number;
}

export default function OrderHistory() {
  const { t, locale } = useLocale();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<Order[]>([]);

  useEffect(() => {
    const fetchOrders = async () => {
      if (!user?.uid) return;
      
      try {
        setLoading(true);
        
        // في التطبيق الحقيقي، سنقوم بجلب البيانات من Firestore
        const ordersRef = collection(db, "orders");
        const q = query(
          ordersRef,
          where("userId", "==", user.uid),
          orderBy("createdAt", "desc"),
          limit(5)
        );
        
        try {
          const querySnapshot = await getDocs(q);
          const ordersData: Order[] = [];
          
          querySnapshot.forEach((doc) => {
            const data = doc.data();
            ordersData.push({
              id: doc.id,
              orderNumber: data.orderNumber,
              date: data.createdAt.toDate(),
              total: data.total,
              status: data.status,
              items: data.items?.length || 0,
            });
          });
          
          setOrders(ordersData);
        } catch (firestoreError) {
          console.error("Error fetching orders from Firestore:", firestoreError);
          // في حالة حدوث خطأ في جلب البيانات، نضع مصفوفة فارغة
          setOrders([]);
        }
        
        setLoading(false);
        
      } catch (error) {
        console.error("Error fetching orders:", error);
        setOrders([]);
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user]);

  const getStatusBadge = (status: Order['status']) => {
    const statusConfig = {
      pending: { label: t('pending'), variant: 'outline' },
      processing: { label: t('processing'), variant: 'secondary' },
      shipped: { label: t('shipped'), variant: 'default' },
      delivered: { label: t('delivered'), variant: 'success' },
      cancelled: { label: t('cancelled'), variant: 'destructive' },
    };
    
    const config = statusConfig[status];
    
    return (
      <Badge variant={config.variant as any}>{config.label}</Badge>
    );
  };

  const formatDate = (date: Date) => {
    return format(date, 'PPP', { 
      locale: locale === 'ar' ? ar : enUS 
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6 flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center">
          <ShoppingBag className="me-2 h-5 w-5" />
          {t('recentOrders')}
        </CardTitle>
        <CardDescription>{t('recentOrdersDescription')}</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">{t('loading')}</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">{t('noOrdersYet')}</h3>
            <p className="text-muted-foreground mt-1">{t('startShoppingMessage')}</p>
            <div className="flex justify-center mt-4">
              <Link href={`/${locale}/products`} passHref>
                <Button variant="outline">
                  {t('browseProducts')}
                </Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                {t('showing')} {Math.min(orders.length, 5)} {t('of')} {orders.length} {t('orders')}
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/${locale}/dashboard/orders`}>
                  {t('viewAllOrders')}
                  <ExternalLink className="ms-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('orderNumber')}</TableHead>
                    <TableHead>{t('date')}</TableHead>
                    <TableHead>{t('items')}</TableHead>
                    <TableHead>{t('total')}</TableHead>
                    <TableHead>{t('status')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.slice(0, 5).map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">{order.orderNumber}</TableCell>
                      <TableCell>{formatDate(order.date)}</TableCell>
                      <TableCell>{order.items}</TableCell>
                      <TableCell>{order.total.toFixed(2)} {t('SAR')}</TableCell>
                      <TableCell>{getStatusBadge(order.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
      {orders.length > 0 && (
        <CardFooter>
          <Link href={`/${locale}/dashboard/orders`} passHref className="w-full">
            <Button variant="outline" className="w-full">
              <ExternalLink className="me-2 h-4 w-4" />
              {t('viewAllOrders')}
            </Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  );
}
