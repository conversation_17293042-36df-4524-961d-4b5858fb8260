// src/app/[locale]/dashboard/notifications/page.tsx
"use client";

import React, { useState } from 'react';
import { useNotifications, useNotificationSettings } from '@/hooks/useNotifications';
import { useLocale } from '@/hooks/use-locale';
import { formatDistanceToNow } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import {
  Bell,
  Package,
  CheckCircle,
  Truck,
  CreditCard,
  UserCheck,
  Megaphone,
  Settings,
  Check,
  CheckCheck,
  Loader2,
  Filter,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import type { NotificationData } from '@/services/notificationService';

export default function NotificationsPage() {
  const { t, locale } = useLocale();
  const { 
    notifications, 
    unreadCount, 
    loading, 
    markAsRead, 
    markAllAsRead 
  } = useNotifications(50);
  
  const {
    settings,
    loading: settingsLoading,
    updateSettings
  } = useNotificationSettings();

  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // تصفية الإشعارات
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         notification.body.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = filterType === 'all' || notification.type === filterType;
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'read' && notification.read) ||
                         (filterStatus === 'unread' && !notification.read);

    return matchesSearch && matchesType && matchesStatus;
  });

  // أيقونات أنواع الإشعارات
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_order':
        return <Package className="w-5 h-5 text-blue-500" />;
      case 'order_status_update':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'order_assigned':
        return <Truck className="w-5 h-5 text-orange-500" />;
      case 'payment_received':
        return <CreditCard className="w-5 h-5 text-emerald-500" />;
      case 'merchant_approved':
      case 'representative_approved':
        return <UserCheck className="w-5 h-5 text-purple-500" />;
      case 'system_announcement':
        return <Megaphone className="w-5 h-5 text-red-500" />;
      default:
        return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  // تنسيق التاريخ
  const formatNotificationDate = (createdAt: any) => {
    if (!createdAt) return '';
    
    const date = createdAt.toDate ? createdAt.toDate() : new Date(createdAt);
    return formatDistanceToNow(date, {
      addSuffix: true,
      locale: locale === 'ar' ? ar : enUS
    });
  };

  // معالجة النقر على الإشعار
  const handleNotificationClick = async (notification: NotificationData) => {
    if (!notification.read && notification.id) {
      await markAsRead(notification.id);
    }

    // التنقل إلى الرابط المحدد
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
  };

  // معالجة تحديث الإعدادات
  const handleSettingChange = async (key: string, value: boolean) => {
    if (settings) {
      await updateSettings({ [key]: value });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">{t('notifications')}</h1>
            <p className="text-muted-foreground">
              {t('manageNotifications')}
            </p>
          </div>
          
          {unreadCount > 0 && (
            <Button onClick={markAllAsRead} variant="outline">
              <CheckCheck className="w-4 h-4 mr-2" />
              {t('markAllAsRead')} ({unreadCount})
            </Button>
          )}
        </div>

        <Tabs defaultValue="notifications" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="notifications">
              <Bell className="w-4 h-4 mr-2" />
              {t('notifications')}
              {unreadCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unreadCount}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="w-4 h-4 mr-2" />
              {t('settings')}
            </TabsTrigger>
          </TabsList>

          {/* قائمة الإشعارات */}
          <TabsContent value="notifications" className="space-y-6">
            {/* أدوات التصفية والبحث */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                      <Input
                        placeholder={t('searchNotifications')}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-full md:w-48">
                      <SelectValue placeholder={t('filterByType')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('allTypes')}</SelectItem>
                      <SelectItem value="new_order">{t('newOrders')}</SelectItem>
                      <SelectItem value="order_status_update">{t('orderUpdates')}</SelectItem>
                      <SelectItem value="order_assigned">{t('orderAssigned')}</SelectItem>
                      <SelectItem value="payment_received">{t('paymentReceived')}</SelectItem>
                      <SelectItem value="merchant_approved">{t('merchantApproved')}</SelectItem>
                      <SelectItem value="representative_approved">{t('representativeApproved')}</SelectItem>
                      <SelectItem value="system_announcement">{t('systemAnnouncements')}</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-full md:w-32">
                      <SelectValue placeholder={t('status')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('all')}</SelectItem>
                      <SelectItem value="unread">{t('unread')}</SelectItem>
                      <SelectItem value="read">{t('read')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* قائمة الإشعارات */}
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="w-6 h-6 animate-spin" />
                <span className="mr-2">{t('loading')}</span>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center p-12 text-center">
                  <Bell className="w-12 h-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">{t('noNotifications')}</h3>
                  <p className="text-muted-foreground">
                    {searchQuery || filterType !== 'all' || filterStatus !== 'all'
                      ? t('noNotificationsMatchFilter')
                      : t('noNotificationsYet')
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {filteredNotifications.map((notification) => (
                  <Card
                    key={notification.id}
                    className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                      !notification.read ? 'border-primary/50 bg-primary/5' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <h4 className={`font-medium ${!notification.read ? 'font-semibold' : ''}`}>
                              {notification.title}
                            </h4>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              {!notification.read && (
                                <div className="w-2 h-2 bg-primary rounded-full" />
                              )}
                              <span className="text-xs text-muted-foreground">
                                {formatNotificationDate(notification.createdAt)}
                              </span>
                            </div>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mt-1">
                            {notification.body}
                          </p>
                          
                          {notification.orderId && (
                            <Badge variant="outline" className="mt-2">
                              #{notification.orderId}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* إعدادات الإشعارات */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('notificationSettings')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {settingsLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 className="w-6 h-6 animate-spin" />
                    <span className="mr-2">{t('loading')}</span>
                  </div>
                ) : settings ? (
                  <>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="push-notifications">{t('pushNotifications')}</Label>
                        <p className="text-sm text-muted-foreground">
                          {t('pushNotificationsDescription')}
                        </p>
                      </div>
                      <Switch
                        id="push-notifications"
                        checked={settings.pushNotifications}
                        onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email-notifications">{t('emailNotifications')}</Label>
                        <p className="text-sm text-muted-foreground">
                          {t('emailNotificationsDescription')}
                        </p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={settings.emailNotifications}
                        onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="order-updates">{t('orderUpdates')}</Label>
                        <p className="text-sm text-muted-foreground">
                          {t('orderUpdatesDescription')}
                        </p>
                      </div>
                      <Switch
                        id="order-updates"
                        checked={settings.orderUpdates}
                        onCheckedChange={(checked) => handleSettingChange('orderUpdates', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="promotions">{t('promotions')}</Label>
                        <p className="text-sm text-muted-foreground">
                          {t('promotionsDescription')}
                        </p>
                      </div>
                      <Switch
                        id="promotions"
                        checked={settings.promotions}
                        onCheckedChange={(checked) => handleSettingChange('promotions', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="system-announcements">{t('systemAnnouncements')}</Label>
                        <p className="text-sm text-muted-foreground">
                          {t('systemAnnouncementsDescription')}
                        </p>
                      </div>
                      <Switch
                        id="system-announcements"
                        checked={settings.systemAnnouncements}
                        onCheckedChange={(checked) => handleSettingChange('systemAnnouncements', checked)}
                      />
                    </div>
                  </>
                ) : (
                  <p className="text-center text-muted-foreground">
                    {t('failedToLoadSettings')}
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
