// src/components/ui/colored-icons.tsx
"use client";

import React from 'react';

interface ColoredIconProps {
  size?: number;
  className?: string;
}

// أيقونة الرئيسية - بيت مع ألوان دافئة
export const HomeColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`nav-icon ${className}`}
  >
    <defs>
      <linearGradient id="homeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3B594" />
        <stop offset="100%" stopColor="#E2725B" />
      </linearGradient>
    </defs>
    <path 
      d="M3 12L12 3L21 12V20C21 20.5523 20.5523 21 20 21H15V16C15 15.4477 14.5523 15 14 15H10C9.44772 15 9 15.4477 9 16V21H4C3.44772 21 3 20.5523 3 20V12Z" 
      fill="url(#homeGradient)"
      stroke="#8B4513"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <circle cx="12" cy="10" r="1.5" fill="#8B4513" opacity="0.7" />
  </svg>
);

// أيقونة المتاجر - متجر مع ألوان ترابية
export const StoreColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`nav-icon ${className}`}
  >
    <defs>
      <linearGradient id="storeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E2725B" />
        <stop offset="100%" stopColor="#D3B594" />
      </linearGradient>
    </defs>
    <path 
      d="M3 7V5C3 4.44772 3.44772 4 4 4H20C20.5523 4 21 4.44772 21 5V7" 
      stroke="#8B4513" 
      strokeWidth="1.5"
    />
    <path 
      d="M3 7H21L20 9H4L3 7Z" 
      fill="url(#storeGradient)"
      stroke="#8B4513"
      strokeWidth="1.5"
    />
    <rect 
      x="5" 
      y="9" 
      width="14" 
      height="10" 
      fill="rgba(211, 181, 148, 0.3)"
      stroke="#8B4513"
      strokeWidth="1.5"
      rx="1"
    />
    <rect x="8" y="12" width="3" height="4" fill="#8B4513" rx="0.5" />
    <circle cx="16" cy="14" r="1" fill="#E2725B" />
  </svg>
);

// أيقونة الفئات - شبكة مع ألوان متدرجة
export const CategoriesColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`nav-icon ${className}`}
  >
    <defs>
      <linearGradient id="categoriesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3B594" />
        <stop offset="50%" stopColor="#E2725B" />
        <stop offset="100%" stopColor="#D3B594" />
      </linearGradient>
    </defs>
    <rect x="3" y="3" width="7" height="7" fill="url(#categoriesGradient)" rx="2" />
    <rect x="14" y="3" width="7" height="7" fill="rgba(226, 114, 91, 0.7)" rx="2" />
    <rect x="3" y="14" width="7" height="7" fill="rgba(226, 114, 91, 0.7)" rx="2" />
    <rect x="14" y="14" width="7" height="7" fill="url(#categoriesGradient)" rx="2" />
    <circle cx="6.5" cy="6.5" r="1" fill="#8B4513" opacity="0.8" />
    <circle cx="17.5" cy="6.5" r="1" fill="#8B4513" opacity="0.8" />
    <circle cx="6.5" cy="17.5" r="1" fill="#8B4513" opacity="0.8" />
    <circle cx="17.5" cy="17.5" r="1" fill="#8B4513" opacity="0.8" />
  </svg>
);

// أيقونة الخريطة - خريطة مع دبوس ملون
export const MapColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`nav-icon ${className}`}
  >
    <defs>
      <linearGradient id="mapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E2725B" />
        <stop offset="100%" stopColor="#D3B594" />
      </linearGradient>
    </defs>
    <path 
      d="M9 2L15 6L21 4V18L15 20L9 16L3 18V4L9 2Z" 
      fill="rgba(211, 181, 148, 0.2)"
      stroke="#8B4513"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <path d="M9 2V16" stroke="#8B4513" strokeWidth="1.5" />
    <path d="M15 6V20" stroke="#8B4513" strokeWidth="1.5" />
    <circle cx="12" cy="8" r="3" fill="url(#mapGradient)" stroke="#8B4513" strokeWidth="1" />
    <circle cx="12" cy="8" r="1.5" fill="#8B4513" />
  </svg>
);

// أيقونة خطة الاشتراك - تاج مع ألوان ذهبية
export const PricingColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`nav-icon ${className}`}
  >
    <defs>
      <linearGradient id="pricingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3B594" />
        <stop offset="50%" stopColor="#E2725B" />
        <stop offset="100%" stopColor="#D3B594" />
      </linearGradient>
    </defs>
    <path 
      d="M7 10L12 4L17 10L20 8V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V8L7 10Z" 
      fill="url(#pricingGradient)"
      stroke="#8B4513"
      strokeWidth="1.5"
    />
    <circle cx="12" cy="12" r="2" fill="#8B4513" />
    <path d="M10 14L12 16L14 14" stroke="#8B4513" strokeWidth="1.5" strokeLinecap="round" />
    <circle cx="8" cy="11" r="0.5" fill="#E2725B" />
    <circle cx="16" cy="11" r="0.5" fill="#E2725B" />
  </svg>
);

// أيقونات الفئات الملونة

// أيقونة الطعام والمشروبات
export const FoodColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="foodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF6B35" />
        <stop offset="50%" stopColor="#F7931E" />
        <stop offset="100%" stopColor="#FFD23F" />
      </linearGradient>
      <radialGradient id="plateGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FFF8E1" />
        <stop offset="100%" stopColor="#F5F5DC" />
      </radialGradient>
    </defs>
    {/* الطبق */}
    <circle cx="12" cy="14" r="8" fill="url(#plateGradient)" stroke="#D3B594" strokeWidth="1.5" />
    {/* الطعام */}
    <circle cx="10" cy="12" r="2" fill="url(#foodGradient)" />
    <circle cx="14" cy="13" r="1.5" fill="#E2725B" />
    <circle cx="12" cy="16" r="1" fill="#D3B594" />
    {/* الشوكة والسكين */}
    <path d="M6 4V8M6 8V12M6 8H4M6 8H8" stroke="#8B4513" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M18 4L18 12M16 6L20 6" stroke="#8B4513" strokeWidth="1.5" strokeLinecap="round" />
  </svg>
);

// أيقونة الأزياء والموضة
export const FashionColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="fashionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E91E63" />
        <stop offset="50%" stopColor="#AD1457" />
        <stop offset="100%" stopColor="#880E4F" />
      </linearGradient>
      <linearGradient id="shirtGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FCE4EC" />
        <stop offset="100%" stopColor="#F8BBD9" />
      </linearGradient>
    </defs>
    {/* القميص */}
    <path
      d="M8 6C8 4.89543 8.89543 4 10 4H14C15.1046 4 16 4.89543 16 6V8L18 10V20H6V10L8 8V6Z"
      fill="url(#shirtGradient)"
      stroke="url(#fashionGradient)"
      strokeWidth="1.5"
    />
    {/* الياقة */}
    <path d="M10 4V7L12 8L14 7V4" fill="url(#fashionGradient)" />
    {/* الأزرار */}
    <circle cx="12" cy="11" r="0.5" fill="url(#fashionGradient)" />
    <circle cx="12" cy="14" r="0.5" fill="url(#fashionGradient)" />
    <circle cx="12" cy="17" r="0.5" fill="url(#fashionGradient)" />
    {/* الجيب */}
    <rect x="9" y="15" width="2" height="1.5" fill="none" stroke="url(#fashionGradient)" strokeWidth="0.8" rx="0.2" />
  </svg>
);

// أيقونة الإلكترونيات
export const ElectronicsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="electronicsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#2196F3" />
        <stop offset="50%" stopColor="#1976D2" />
        <stop offset="100%" stopColor="#0D47A1" />
      </linearGradient>
      <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E3F2FD" />
        <stop offset="100%" stopColor="#BBDEFB" />
      </linearGradient>
    </defs>
    {/* الهاتف/الجهاز */}
    <rect
      x="6"
      y="3"
      width="12"
      height="18"
      rx="2"
      fill="url(#electronicsGradient)"
      stroke="#0D47A1"
      strokeWidth="1"
    />
    {/* الشاشة */}
    <rect x="7" y="5" width="10" height="14" rx="1" fill="url(#screenGradient)" />
    {/* الزر الرئيسي */}
    <circle cx="12" cy="20.5" r="0.8" fill="#E3F2FD" stroke="#0D47A1" strokeWidth="0.5" />
    {/* السماعة */}
    <rect x="9" y="3.5" width="6" height="0.8" rx="0.4" fill="#E3F2FD" />
    {/* أيقونات الشاشة */}
    <circle cx="9" cy="8" r="1" fill="#2196F3" opacity="0.6" />
    <rect x="11" y="7" width="4" height="2" rx="0.5" fill="#1976D2" opacity="0.6" />
    <rect x="8" y="11" width="8" height="1" rx="0.5" fill="#2196F3" opacity="0.4" />
    <rect x="8" y="13" width="6" height="1" rx="0.5" fill="#2196F3" opacity="0.4" />
  </svg>
);

// أيقونة المنزل والحديقة
export const HomeGardenColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="homeGardenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#4CAF50" />
        <stop offset="50%" stopColor="#388E3C" />
        <stop offset="100%" stopColor="#1B5E20" />
      </linearGradient>
      <linearGradient id="houseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3B594" />
        <stop offset="100%" stopColor="#E2725B" />
      </linearGradient>
    </defs>
    {/* البيت */}
    <path
      d="M3 12L12 3L21 12V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V12Z"
      fill="url(#houseGradient)"
      stroke="#8B4513"
      strokeWidth="1.5"
    />
    {/* السقف */}
    <path d="M3 12L12 3L21 12" stroke="#8B4513" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    {/* النافذة */}
    <rect x="14" y="10" width="4" height="4" fill="#87CEEB" stroke="#8B4513" strokeWidth="1" rx="0.5" />
    <path d="M16 10V14M14 12H18" stroke="#8B4513" strokeWidth="0.8" />
    {/* الباب */}
    <rect x="8" y="14" width="3" height="7" fill="#8B4513" rx="0.3" />
    <circle cx="10.2" cy="17.5" r="0.3" fill="#D3B594" />
    {/* النباتات */}
    <path d="M4 21C4 19 5 18 6 18C7 18 8 19 8 21" fill="url(#homeGardenGradient)" />
    <path d="M16 21C16 19 17 18 18 18C19 18 20 19 20 21" fill="url(#homeGardenGradient)" />
    <circle cx="6" cy="19" r="0.5" fill="#FFD54F" />
    <circle cx="18" cy="19" r="0.5" fill="#FF7043" />
  </svg>
);

// أيقونة الجمال والصحة
export const BeautyHealthColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="beautyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E91E63" />
        <stop offset="50%" stopColor="#AD1457" />
        <stop offset="100%" stopColor="#880E4F" />
      </linearGradient>
      <radialGradient id="heartGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FF69B4" />
        <stop offset="100%" stopColor="#C2185B" />
      </radialGradient>
    </defs>
    {/* القلب */}
    <path
      d="M12 21.35L10.55 20.03C5.4 15.36 2 12.28 2 8.5C2 5.42 4.42 3 7.5 3C9.24 3 10.91 3.81 12 5.09C13.09 3.81 14.76 3 16.5 3C19.58 3 22 5.42 22 8.5C22 12.28 18.6 15.36 13.45 20.04L12 21.35Z"
      fill="url(#heartGradient)"
      stroke="#880E4F"
      strokeWidth="1"
    />
    {/* أدوات التجميل */}
    <circle cx="8" cy="8" r="1" fill="#FFD54F" opacity="0.8" />
    <circle cx="16" cy="8" r="1" fill="#FF7043" opacity="0.8" />
    <path d="M10 12L14 12" stroke="#FFF" strokeWidth="1.5" strokeLinecap="round" />
    <circle cx="12" cy="15" r="0.8" fill="#FFF" opacity="0.9" />
  </svg>
);

// أيقونة الرياضة واللياقة
export const SportsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="sportsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF5722" />
        <stop offset="50%" stopColor="#D84315" />
        <stop offset="100%" stopColor="#BF360C" />
      </linearGradient>
      <linearGradient id="ballGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFF3E0" />
        <stop offset="100%" stopColor="#FFE0B2" />
      </linearGradient>
    </defs>
    {/* كرة القدم */}
    <circle cx="12" cy="12" r="8" fill="url(#ballGradient)" stroke="url(#sportsGradient)" strokeWidth="2" />
    {/* خطوط الكرة */}
    <path d="M12 4C16.4183 4 20 7.58172 20 12" stroke="url(#sportsGradient)" strokeWidth="1.5" fill="none" />
    <path d="M4 12C4 7.58172 7.58172 4 12 4" stroke="url(#sportsGradient)" strokeWidth="1.5" fill="none" />
    <path d="M12 20C7.58172 20 4 16.4183 4 12" stroke="url(#sportsGradient)" strokeWidth="1.5" fill="none" />
    <path d="M20 12C20 16.4183 16.4183 20 12 20" stroke="url(#sportsGradient)" strokeWidth="1.5" fill="none" />
    {/* النجمة في المنتصف */}
    <polygon
      points="12,8 13,11 16,11 13.5,13 14.5,16 12,14 9.5,16 10.5,13 8,11 11,11"
      fill="url(#sportsGradient)"
    />
  </svg>
);

// أيقونة السيارات
export const AutomotiveColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="carGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#607D8B" />
        <stop offset="50%" stopColor="#455A64" />
        <stop offset="100%" stopColor="#263238" />
      </linearGradient>
      <radialGradient id="wheelGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#37474F" />
        <stop offset="70%" stopColor="#263238" />
        <stop offset="100%" stopColor="#000" />
      </radialGradient>
    </defs>
    {/* جسم السيارة */}
    <path
      d="M5 11L6 7H18L19 11V17H17V19H15V17H9V19H7V17H5V11Z"
      fill="url(#carGradient)"
      stroke="#263238"
      strokeWidth="1.5"
    />
    {/* النوافذ */}
    <path d="M7 7L8 10H16L17 7" fill="#87CEEB" stroke="#263238" strokeWidth="1" />
    {/* العجلات */}
    <circle cx="8" cy="17" r="2" fill="url(#wheelGradient)" />
    <circle cx="16" cy="17" r="2" fill="url(#wheelGradient)" />
    <circle cx="8" cy="17" r="1" fill="#78909C" />
    <circle cx="16" cy="17" r="1" fill="#78909C" />
    {/* المصابيح */}
    <circle cx="5.5" cy="12" r="0.8" fill="#FFD54F" />
    <circle cx="18.5" cy="12" r="0.8" fill="#FFD54F" />
    {/* الشبكة الأمامية */}
    <rect x="6" y="11" width="12" height="2" fill="none" stroke="#263238" strokeWidth="0.8" />
    <path d="M8 11V13M10 11V13M12 11V13M14 11V13M16 11V13" stroke="#263238" strokeWidth="0.5" />
  </svg>
);

// أيقونة الكتب والإعلام
export const BooksMediaColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#3F51B5" />
        <stop offset="50%" stopColor="#303F9F" />
        <stop offset="100%" stopColor="#1A237E" />
      </linearGradient>
      <linearGradient id="pageGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFF" />
        <stop offset="100%" stopColor="#F5F5F5" />
      </linearGradient>
    </defs>
    {/* الكتاب */}
    <rect
      x="4"
      y="4"
      width="16"
      height="16"
      rx="1"
      fill="url(#bookGradient)"
      stroke="#1A237E"
      strokeWidth="1.5"
    />
    {/* الصفحات */}
    <rect x="6" y="6" width="12" height="12" rx="0.5" fill="url(#pageGradient)" />
    {/* النص */}
    <rect x="8" y="8" width="8" height="1" rx="0.5" fill="#3F51B5" opacity="0.7" />
    <rect x="8" y="10" width="6" height="1" rx="0.5" fill="#3F51B5" opacity="0.5" />
    <rect x="8" y="12" width="7" height="1" rx="0.5" fill="#3F51B5" opacity="0.5" />
    <rect x="8" y="14" width="5" height="1" rx="0.5" fill="#3F51B5" opacity="0.5" />
    {/* العلامة المرجعية */}
    <path d="M18 4V12L16 10L14 12V4" fill="#FF5722" stroke="#D84315" strokeWidth="1" />
    {/* رمز الوسائط */}
    <circle cx="12" cy="16" r="1.5" fill="#FF5722" />
    <polygon points="11.2,15.5 11.2,16.5 12.8,16" fill="#FFF" />
  </svg>
);

// أيقونة الفنون والحرف
export const ArtsCraftsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="paletteGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFC107" />
        <stop offset="50%" stopColor="#FF9800" />
        <stop offset="100%" stopColor="#FF5722" />
      </linearGradient>
      <linearGradient id="brushGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8BC34A" />
        <stop offset="100%" stopColor="#4CAF50" />
      </linearGradient>
    </defs>
    {/* لوحة الألوان */}
    <path
      d="M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2Z"
      fill="url(#paletteGradient)"
      stroke="#E65100"
      strokeWidth="1.5"
    />
    {/* ثقب الإبهام */}
    <circle cx="15" cy="12" r="2" fill="#FFF" stroke="#E65100" strokeWidth="1" />
    {/* ألوان الطلاء */}
    <circle cx="8" cy="8" r="1.2" fill="#E91E63" />
    <circle cx="12" cy="6" r="1.2" fill="#2196F3" />
    <circle cx="16" cy="8" r="1.2" fill="#4CAF50" />
    <circle cx="8" cy="16" r="1.2" fill="#9C27B0" />
    <circle cx="12" cy="18" r="1.2" fill="#FF9800" />
    {/* الفرشاة */}
    <path
      d="M18 4L20 6L14 12L12 10L18 4Z"
      fill="url(#brushGradient)"
      stroke="#2E7D32"
      strokeWidth="1"
    />
    <path d="M12 10L10 12L8 14" stroke="#8BC34A" strokeWidth="2" strokeLinecap="round" />
  </svg>
);

// أيقونة أشياء رائعة
export const AwesomeStuffColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="awesomeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF6B35" />
        <stop offset="25%" stopColor="#F7931E" />
        <stop offset="50%" stopColor="#FFD23F" />
        <stop offset="75%" stopColor="#4CAF50" />
        <stop offset="100%" stopColor="#2196F3" />
      </linearGradient>
      <radialGradient id="starGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FFD700" />
        <stop offset="100%" stopColor="#FFA000" />
      </radialGradient>
    </defs>
    {/* النجمة الرئيسية */}
    <path
      d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
      fill="url(#starGradient)"
      stroke="#FF8F00"
      strokeWidth="1.5"
    />
    {/* النجوم الصغيرة */}
    <circle cx="6" cy="6" r="1" fill="#FF6B35" />
    <circle cx="18" cy="6" r="1" fill="#2196F3" />
    <circle cx="4" cy="18" r="0.8" fill="#4CAF50" />
    <circle cx="20" cy="18" r="0.8" fill="#E91E63" />
    {/* البريق */}
    <path d="M8 4L9 6L8 8L7 6Z" fill="#FFF" opacity="0.8" />
    <path d="M16 16L17 18L16 20L15 18Z" fill="#FFF" opacity="0.8" />
    <path d="M20 10L21 11L20 12L19 11Z" fill="#FFF" opacity="0.8" />
  </svg>
);

// أيقونة أخرى
export const OtherColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="otherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#9E9E9E" />
        <stop offset="50%" stopColor="#757575" />
        <stop offset="100%" stopColor="#424242" />
      </linearGradient>
      <linearGradient id="bagGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3B594" />
        <stop offset="100%" stopColor="#E2725B" />
      </linearGradient>
    </defs>
    {/* الحقيبة */}
    <path
      d="M6 7V6C6 4.89543 6.89543 4 8 4H16C17.1046 4 18 4.89543 18 6V7H20C20.5523 7 21 7.44772 21 8V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V8C3 7.44772 3.44772 7 4 7H6Z"
      fill="url(#bagGradient)"
      stroke="#8B4513"
      strokeWidth="1.5"
    />
    {/* المقبض */}
    <path d="M8 7V6H16V7" stroke="#8B4513" strokeWidth="1.5" />
    {/* النقاط */}
    <circle cx="9" cy="12" r="1" fill="url(#otherGradient)" />
    <circle cx="12" cy="12" r="1" fill="url(#otherGradient)" />
    <circle cx="15" cy="12" r="1" fill="url(#otherGradient)" />
    {/* الزخرفة */}
    <path d="M7 15L17 15" stroke="#8B4513" strokeWidth="1" opacity="0.5" />
    <path d="M8 17L16 17" stroke="#8B4513" strokeWidth="1" opacity="0.3" />
  </svg>
);

// أيقونة البقالة والمواد الغذائية
export const GroceriesColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="groceriesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#4CAF50" />
        <stop offset="50%" stopColor="#8BC34A" />
        <stop offset="100%" stopColor="#CDDC39" />
      </linearGradient>
      <linearGradient id="basketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8D6E63" />
        <stop offset="100%" stopColor="#5D4037" />
      </linearGradient>
    </defs>
    {/* السلة */}
    <path
      d="M7 10L5 21H19L17 10H7Z"
      fill="url(#basketGradient)"
      stroke="#3E2723"
      strokeWidth="1.5"
    />
    <path d="M7 10L9 8H15L17 10" stroke="#3E2723" strokeWidth="1.5" fill="none" />
    {/* المواد الغذائية */}
    <circle cx="10" cy="14" r="1.5" fill="#FF5722" />
    <circle cx="14" cy="16" r="1.2" fill="url(#groceriesGradient)" />
    <rect x="11" y="12" width="2" height="3" fill="#FFC107" rx="0.5" />
    {/* الخضروات */}
    <path d="M8 6C8 4 9 3 10 3C11 3 12 4 12 6" stroke="url(#groceriesGradient)" strokeWidth="2" strokeLinecap="round" />
    <circle cx="10" cy="6" r="1" fill="url(#groceriesGradient)" />
    {/* التفاح */}
    <circle cx="16" cy="6" r="1.5" fill="#F44336" />
    <path d="M16 4.5C16 4.5 16.5 4 17 4.5" stroke="#4CAF50" strokeWidth="1" strokeLinecap="round" />
  </svg>
);

// أيقونة الحرف اليدوية والتراثية
export const HandicraftsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="handicraftsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8D6E63" />
        <stop offset="50%" stopColor="#A1887F" />
        <stop offset="100%" stopColor="#BCAAA4" />
      </linearGradient>
      <linearGradient id="potGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D7CCC8" />
        <stop offset="100%" stopColor="#A1887F" />
      </linearGradient>
    </defs>
    {/* الإناء التراثي */}
    <path
      d="M8 8C8 6 9 5 12 5C15 5 16 6 16 8V18C16 19 15 20 12 20C9 20 8 19 8 18V8Z"
      fill="url(#potGradient)"
      stroke="url(#handicraftsGradient)"
      strokeWidth="1.5"
    />
    {/* النقوش التراثية */}
    <path d="M9 10L15 10" stroke="url(#handicraftsGradient)" strokeWidth="1" />
    <path d="M9 12L15 12" stroke="url(#handicraftsGradient)" strokeWidth="1" />
    <path d="M10 14L14 14" stroke="url(#handicraftsGradient)" strokeWidth="1" />
    {/* المقابض */}
    <path d="M8 9C7 9 6 10 6 11C6 12 7 13 8 13" stroke="url(#handicraftsGradient)" strokeWidth="1.5" fill="none" />
    <path d="M16 9C17 9 18 10 18 11C18 12 17 13 16 13" stroke="url(#handicraftsGradient)" strokeWidth="1.5" fill="none" />
    {/* الزخارف */}
    <circle cx="12" cy="16" r="1" fill="url(#handicraftsGradient)" opacity="0.7" />
    <path d="M11 7L13 7" stroke="url(#handicraftsGradient)" strokeWidth="2" strokeLinecap="round" />
  </svg>
);

// أيقونة الألعاب والترفيه
export const ToysColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="toysGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF9800" />
        <stop offset="50%" stopColor="#FF5722" />
        <stop offset="100%" stopColor="#E91E63" />
      </linearGradient>
      <radialGradient id="ballGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FFF" />
        <stop offset="100%" stopColor="#FFE0B2" />
      </radialGradient>
    </defs>
    {/* الكرة */}
    <circle cx="12" cy="12" r="7" fill="url(#ballGradient)" stroke="url(#toysGradient)" strokeWidth="2" />
    {/* خطوط الكرة */}
    <path d="M5 12C5 12 8 8 12 8C16 8 19 12 19 12" stroke="url(#toysGradient)" strokeWidth="1.5" fill="none" />
    <path d="M5 12C5 12 8 16 12 16C16 16 19 12 19 12" stroke="url(#toysGradient)" strokeWidth="1.5" fill="none" />
    <path d="M12 5V19" stroke="url(#toysGradient)" strokeWidth="1.5" />
    {/* المكعبات الصغيرة */}
    <rect x="3" y="3" width="3" height="3" fill="#4CAF50" rx="0.5" />
    <rect x="18" y="3" width="3" height="3" fill="#2196F3" rx="0.5" />
    <rect x="3" y="18" width="3" height="3" fill="#9C27B0" rx="0.5" />
    <rect x="18" y="18" width="3" height="3" fill="#FF9800" rx="0.5" />
    {/* النجوم */}
    <path d="M6 8L6.5 9L8 9L7 10L7.5 11L6 10.5L4.5 11L5 10L4 9L5.5 9Z" fill="#FFD700" />
    <path d="M18 16L18.5 17L20 17L19 18L19.5 19L18 18.5L16.5 19L17 18L16 17L17.5 17Z" fill="#FFD700" />
  </svg>
);

// أيقونة الحيوانات الأليفة
export const PetsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="petsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF7043" />
        <stop offset="50%" stopColor="#FF5722" />
        <stop offset="100%" stopColor="#D84315" />
      </linearGradient>
      <radialGradient id="pawGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#8D6E63" />
        <stop offset="100%" stopColor="#5D4037" />
      </radialGradient>
    </defs>
    {/* أثر القدم الرئيسي */}
    <ellipse cx="12" cy="15" rx="3" ry="4" fill="url(#pawGradient)" />
    {/* أصابع القدم */}
    <ellipse cx="9" cy="9" rx="1.5" ry="2" fill="url(#pawGradient)" />
    <ellipse cx="12" cy="8" rx="1.5" ry="2" fill="url(#pawGradient)" />
    <ellipse cx="15" cy="9" rx="1.5" ry="2" fill="url(#pawGradient)" />
    <ellipse cx="12" cy="10" rx="1" ry="1.5" fill="url(#pawGradient)" />
    {/* القلب */}
    <path
      d="M12 5C10 3 7 3 7 6C7 8 12 12 12 12C12 12 17 8 17 6C17 3 14 3 12 5Z"
      fill="url(#petsGradient)"
      stroke="#BF360C"
      strokeWidth="1"
    />
    {/* النجوم الصغيرة */}
    <circle cx="6" cy="16" r="0.8" fill="#FFD54F" />
    <circle cx="18" cy="12" r="0.8" fill="#FFD54F" />
    <circle cx="20" cy="18" r="0.6" fill="#FFC107" />
  </svg>
);

// أيقونة الأطفال والرضع
export const BabyKidsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="babyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFB74D" />
        <stop offset="50%" stopColor="#FFA726" />
        <stop offset="100%" stopColor="#FF9800" />
      </linearGradient>
      <linearGradient id="toyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E91E63" />
        <stop offset="100%" stopColor="#AD1457" />
      </linearGradient>
    </defs>
    {/* رأس الطفل */}
    <circle cx="12" cy="8" r="4" fill="url(#babyGradient)" stroke="#F57C00" strokeWidth="1" />
    {/* العيون */}
    <circle cx="10.5" cy="7.5" r="0.5" fill="#333" />
    <circle cx="13.5" cy="7.5" r="0.5" fill="#333" />
    {/* الفم */}
    <path d="M11 9C11 9 12 10 13 9" stroke="#333" strokeWidth="1" strokeLinecap="round" fill="none" />
    {/* الجسم */}
    <rect x="9" y="12" width="6" height="8" fill="url(#babyGradient)" rx="3" />
    {/* الذراعان */}
    <circle cx="7" cy="14" r="1.5" fill="url(#babyGradient)" />
    <circle cx="17" cy="14" r="1.5" fill="url(#babyGradient)" />
    {/* الساقان */}
    <circle cx="10" cy="21" r="1.5" fill="url(#babyGradient)" />
    <circle cx="14" cy="21" r="1.5" fill="url(#babyGradient)" />
    {/* اللعبة */}
    <circle cx="5" cy="6" r="2" fill="url(#toyGradient)" />
    <path d="M4 4L6 8" stroke="url(#toyGradient)" strokeWidth="1.5" strokeLinecap="round" />
    {/* النجوم */}
    <circle cx="19" cy="8" r="0.8" fill="#FFD54F" />
    <circle cx="20" cy="15" r="0.6" fill="#FFC107" />
    <circle cx="3" cy="18" r="0.7" fill="#FF7043" />
  </svg>
);

// أيقونة المجوهرات والإكسسوارات
export const JewelryColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="jewelryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFD700" />
        <stop offset="50%" stopColor="#FFC107" />
        <stop offset="100%" stopColor="#FF8F00" />
      </linearGradient>
      <radialGradient id="gemGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#E1BEE7" />
        <stop offset="100%" stopColor="#9C27B0" />
      </radialGradient>
    </defs>
    {/* الخاتم */}
    <circle cx="12" cy="12" r="6" fill="none" stroke="url(#jewelryGradient)" strokeWidth="3" />
    <circle cx="12" cy="12" r="3" fill="none" stroke="url(#jewelryGradient)" strokeWidth="1" />
    {/* الجوهرة الرئيسية */}
    <polygon
      points="12,6 14,8 14,10 12,12 10,10 10,8"
      fill="url(#gemGradient)"
      stroke="#7B1FA2"
      strokeWidth="1"
    />
    {/* الجواهر الصغيرة */}
    <circle cx="8" cy="8" r="1" fill="#E91E63" />
    <circle cx="16" cy="8" r="1" fill="#2196F3" />
    <circle cx="8" cy="16" r="1" fill="#4CAF50" />
    <circle cx="16" cy="16" r="1" fill="#FF5722" />
    {/* البريق */}
    <path d="M10 4L11 6L10 8L9 6Z" fill="#FFF" opacity="0.8" />
    <path d="M14 16L15 18L14 20L13 18Z" fill="#FFF" opacity="0.8" />
    <path d="M20 10L21 11L20 12L19 11Z" fill="#FFF" opacity="0.8" />
    <path d="M4 14L5 15L4 16L3 15Z" fill="#FFF" opacity="0.8" />
  </svg>
);

// أيقونة الخدمات المحلية
export const ServicesColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="servicesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#2196F3" />
        <stop offset="50%" stopColor="#1976D2" />
        <stop offset="100%" stopColor="#0D47A1" />
      </linearGradient>
      <linearGradient id="toolsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#607D8B" />
        <stop offset="100%" stopColor="#455A64" />
      </linearGradient>
    </defs>
    {/* الشخص */}
    <circle cx="12" cy="6" r="3" fill="url(#servicesGradient)" />
    <path d="M12 10C8 10 6 12 6 15V18H18V15C18 12 16 10 12 10Z" fill="url(#servicesGradient)" />
    {/* الأدوات */}
    <path d="M4 8L6 6L8 8L6 10Z" fill="url(#toolsGradient)" />
    <rect x="16" y="7" width="4" height="1" fill="url(#toolsGradient)" rx="0.5" />
    <rect x="16" y="9" width="3" height="1" fill="url(#toolsGradient)" rx="0.5" />
    {/* الترس */}
    <circle cx="20" cy="16" r="2" fill="none" stroke="url(#toolsGradient)" strokeWidth="1.5" />
    <circle cx="20" cy="16" r="1" fill="url(#toolsGradient)" />
    <path d="M19 14L21 14M19 18L21 18M18 15L18 17M22 15L22 17" stroke="url(#toolsGradient)" strokeWidth="1" />
    {/* النجوم */}
    <circle cx="4" cy="16" r="0.8" fill="#FFD54F" />
    <circle cx="8" cy="20" r="0.6" fill="#FFC107" />
  </svg>
);

// أيقونة النباتات والزراعة
export const PlantsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="plantsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#4CAF50" />
        <stop offset="50%" stopColor="#8BC34A" />
        <stop offset="100%" stopColor="#CDDC39" />
      </linearGradient>
      <linearGradient id="potPlantsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8D6E63" />
        <stop offset="100%" stopColor="#5D4037" />
      </linearGradient>
    </defs>
    {/* الأصيص */}
    <path d="M8 16L7 21H17L16 16H8Z" fill="url(#potPlantsGradient)" stroke="#3E2723" strokeWidth="1" />
    <rect x="7" y="15" width="10" height="2" fill="url(#potPlantsGradient)" />
    {/* النبتة الرئيسية */}
    <path d="M12 15C12 15 10 12 8 10C6 8 6 6 8 6C10 6 12 8 12 10" fill="url(#plantsGradient)" />
    <path d="M12 15C12 15 14 12 16 10C18 8 18 6 16 6C14 6 12 8 12 10" fill="url(#plantsGradient)" />
    <path d="M12 10V15" stroke="url(#plantsGradient)" strokeWidth="2" />
    {/* الأوراق الصغيرة */}
    <ellipse cx="10" cy="8" rx="1" ry="2" fill="url(#plantsGradient)" transform="rotate(-30 10 8)" />
    <ellipse cx="14" cy="8" rx="1" ry="2" fill="url(#plantsGradient)" transform="rotate(30 14 8)" />
    {/* الزهرة */}
    <circle cx="12" cy="6" r="1.5" fill="#FF5722" />
    <circle cx="12" cy="6" r="0.8" fill="#FFD54F" />
    {/* النباتات الصغيرة */}
    <path d="M4 20C4 18 5 17 6 17C7 17 8 18 8 20" stroke="url(#plantsGradient)" strokeWidth="2" strokeLinecap="round" />
    <path d="M16 20C16 18 17 17 18 17C19 17 20 18 20 20" stroke="url(#plantsGradient)" strokeWidth="2" strokeLinecap="round" />
    <circle cx="6" cy="18" r="0.5" fill="#FF5722" />
    <circle cx="18" cy="18" r="0.5" fill="#E91E63" />
  </svg>
);

// أيقونة الأجهزة المنزلية
export const AppliancesColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="appliancesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#37474F" />
        <stop offset="50%" stopColor="#546E7A" />
        <stop offset="100%" stopColor="#78909C" />
      </linearGradient>
      <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E3F2FD" />
        <stop offset="100%" stopColor="#BBDEFB" />
      </linearGradient>
    </defs>
    {/* الثلاجة */}
    <rect
      x="6"
      y="3"
      width="8"
      height="16"
      rx="1"
      fill="url(#appliancesGradient)"
      stroke="#263238"
      strokeWidth="1.5"
    />
    {/* الباب العلوي */}
    <rect x="7" y="4" width="6" height="7" rx="0.5" fill="url(#screenGradient)" />
    {/* الباب السفلي */}
    <rect x="7" y="12" width="6" height="6" rx="0.5" fill="url(#screenGradient)" />
    {/* المقابض */}
    <rect x="13.5" y="6" width="0.5" height="2" fill="#263238" rx="0.25" />
    <rect x="13.5" y="14" width="0.5" height="2" fill="#263238" rx="0.25" />
    {/* الشاشة الرقمية */}
    <rect x="8" y="5" width="4" height="1" fill="#4CAF50" rx="0.2" />
    {/* الأزرار */}
    <circle cx="8.5" cy="7" r="0.3" fill="#2196F3" />
    <circle cx="9.5" cy="7" r="0.3" fill="#FF5722" />
    <circle cx="10.5" cy="7" r="0.3" fill="#FFC107" />
    {/* الغسالة الصغيرة */}
    <circle cx="18" cy="16" r="3" fill="url(#appliancesGradient)" stroke="#263238" strokeWidth="1" />
    <circle cx="18" cy="16" r="2" fill="url(#screenGradient)" />
    <circle cx="18" cy="16" r="1" fill="none" stroke="#263238" strokeWidth="0.8" />
    <circle cx="18" cy="13" r="0.5" fill="#4CAF50" />
    {/* الميكروويف الصغير */}
    <rect x="2" y="14" width="4" height="3" fill="url(#appliancesGradient)" rx="0.5" />
    <rect x="2.5" y="14.5" width="2" height="1.5" fill="url(#screenGradient)" rx="0.2" />
    <circle cx="5" cy="15.5" r="0.3" fill="#FF5722" />
  </svg>
);

// أيقونة الرياضات المائية والشاطئ
export const WaterSportsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="waterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#03A9F4" />
        <stop offset="50%" stopColor="#2196F3" />
        <stop offset="100%" stopColor="#1976D2" />
      </linearGradient>
      <linearGradient id="surfboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF9800" />
        <stop offset="50%" stopColor="#FF5722" />
        <stop offset="100%" stopColor="#E91E63" />
      </linearGradient>
      <radialGradient id="sunGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FFD54F" />
        <stop offset="100%" stopColor="#FFC107" />
      </radialGradient>
    </defs>
    {/* الأمواج */}
    <path
      d="M2 16C4 14 6 18 8 16C10 14 12 18 14 16C16 14 18 18 20 16C22 14 24 18 24 16V20H2V16Z"
      fill="url(#waterGradient)"
    />
    <path
      d="M2 18C4 16 6 20 8 18C10 16 12 20 14 18C16 16 18 20 20 18C22 16 24 20 24 18V22H2V18Z"
      fill="url(#waterGradient)"
      opacity="0.7"
    />
    {/* لوح التزلج */}
    <ellipse
      cx="12"
      cy="10"
      rx="6"
      ry="1.5"
      fill="url(#surfboardGradient)"
      transform="rotate(-15 12 10)"
    />
    {/* خطوط اللوح */}
    <path d="M8 9L16 11" stroke="#D84315" strokeWidth="1" opacity="0.8" />
    <path d="M8 10L16 12" stroke="#D84315" strokeWidth="1" opacity="0.6" />
    {/* الشمس */}
    <circle cx="19" cy="6" r="2.5" fill="url(#sunGradient)" />
    <path d="M19 2V4M19 8V10M15 6H17M21 6H23" stroke="#FFC107" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M16.5 3.5L17.5 4.5M20.5 7.5L21.5 8.5M21.5 3.5L20.5 4.5M17.5 7.5L16.5 8.5" stroke="#FFC107" strokeWidth="1" strokeLinecap="round" />
    {/* كرة الشاطئ */}
    <circle cx="6" cy="8" r="2" fill="#FF5722" />
    <path d="M4 8C4 8 5 6 6 6C7 6 8 8 8 8" fill="#FFF" />
    <path d="M4 8C4 8 5 10 6 10C7 10 8 8 8 8" fill="#2196F3" />
  </svg>
);

// أيقونة العطور والبخور
export const PerfumesColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="perfumeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#9C27B0" />
        <stop offset="50%" stopColor="#673AB7" />
        <stop offset="100%" stopColor="#3F51B5" />
      </linearGradient>
      <linearGradient id="bottleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E1BEE7" />
        <stop offset="100%" stopColor="#CE93D8" />
      </linearGradient>
      <radialGradient id="incenseGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#8D6E63" />
        <stop offset="100%" stopColor="#5D4037" />
      </radialGradient>
    </defs>
    {/* زجاجة العطر */}
    <rect
      x="8"
      y="8"
      width="6"
      height="10"
      rx="1"
      fill="url(#bottleGradient)"
      stroke="url(#perfumeGradient)"
      strokeWidth="1.5"
    />
    {/* العطر بالداخل */}
    <rect x="9" y="10" width="4" height="7" rx="0.5" fill="url(#perfumeGradient)" opacity="0.7" />
    {/* الغطاء */}
    <rect x="9" y="6" width="4" height="3" rx="0.5" fill="url(#perfumeGradient)" />
    <rect x="10" y="4" width="2" height="3" rx="0.3" fill="url(#perfumeGradient)" />
    {/* البخاخ */}
    <circle cx="11" cy="4" r="0.8" fill="#FFD700" />
    {/* رذاذ العطر */}
    <circle cx="9" cy="2" r="0.3" fill="url(#perfumeGradient)" opacity="0.6" />
    <circle cx="11" cy="1.5" r="0.2" fill="url(#perfumeGradient)" opacity="0.4" />
    <circle cx="13" cy="2.5" r="0.25" fill="url(#perfumeGradient)" opacity="0.5" />
    {/* عود البخور */}
    <rect x="16" y="8" width="1" height="8" fill="url(#incenseGradient)" rx="0.5" />
    <circle cx="16.5" cy="8" r="0.5" fill="#FF5722" />
    {/* دخان البخور */}
    <path d="M16.5 8C16.5 8 17 6 18 5C19 4 18.5 3 17.5 3" stroke="#9E9E9E" strokeWidth="1" fill="none" opacity="0.7" />
    <path d="M16.5 7C16.5 7 18 5.5 19 4.5C20 3.5 19.5 2.5 18.5 2.5" stroke="#9E9E9E" strokeWidth="0.8" fill="none" opacity="0.5" />
    {/* زجاجة صغيرة */}
    <rect x="3" y="12" width="3" height="5" rx="0.5" fill="url(#bottleGradient)" stroke="url(#perfumeGradient)" strokeWidth="1" />
    <rect x="3.5" y="13" width="2" height="3" rx="0.3" fill="url(#perfumeGradient)" opacity="0.6" />
    <rect x="4" y="11" width="1" height="1.5" rx="0.2" fill="url(#perfumeGradient)" />
    {/* النجوم العطرية */}
    <circle cx="20" cy="12" r="0.8" fill="#FFD700" opacity="0.8" />
    <circle cx="21" cy="15" r="0.6" fill="#FFC107" opacity="0.6" />
    <circle cx="2" cy="8" r="0.7" fill="#E91E63" opacity="0.7" />
  </svg>
);

// أيقونة الأدوات والمعدات
export const ToolsColoredIcon: React.FC<ColoredIconProps> = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={`category-icon ${className}`}
  >
    <defs>
      <linearGradient id="toolsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#607D8B" />
        <stop offset="50%" stopColor="#455A64" />
        <stop offset="100%" stopColor="#263238" />
      </linearGradient>
      <linearGradient id="handleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8D6E63" />
        <stop offset="100%" stopColor="#5D4037" />
      </linearGradient>
      <linearGradient id="screwdriverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF9800" />
        <stop offset="100%" stopColor="#F57C00" />
      </linearGradient>
    </defs>
    {/* المطرقة */}
    <rect
      x="3"
      y="8"
      width="6"
      height="3"
      rx="0.5"
      fill="url(#toolsGradient)"
      stroke="#37474F"
      strokeWidth="1"
    />
    <rect x="6" y="11" width="1.5" height="8" fill="url(#handleGradient)" rx="0.3" />
    {/* المفك */}
    <rect x="12" y="4" width="1" height="12" fill="url(#screwdriverGradient)" rx="0.2" />
    <rect x="11" y="16" width="3" height="2" fill="url(#handleGradient)" rx="0.5" />
    <circle cx="12.5" cy="4" r="0.8" fill="url(#toolsGradient)" />
    {/* المفتاح الإنجليزي */}
    <path
      d="M16 6L20 10L19 11L18 10L16 12L14 10L16 8L15 7L16 6Z"
      fill="url(#toolsGradient)"
      stroke="#37474F"
      strokeWidth="0.8"
    />
    {/* المنشار الصغير */}
    <path d="M2 16L8 16" stroke="url(#toolsGradient)" strokeWidth="2" />
    <path d="M2 16L3 15L4 16L5 15L6 16L7 15L8 16" stroke="url(#toolsGradient)" strokeWidth="1" fill="none" />
    <rect x="1.5" y="15.5" width="1" height="3" fill="url(#handleGradient)" rx="0.2" />
    {/* المسامير */}
    <circle cx="18" cy="16" r="0.8" fill="url(#toolsGradient)" />
    <circle cx="20" cy="17" r="0.6" fill="url(#toolsGradient)" />
    <circle cx="19" cy="19" r="0.7" fill="url(#toolsGradient)" />
    {/* صندوق الأدوات الصغير */}
    <rect x="10" y="18" width="6" height="3" fill="url(#handleGradient)" rx="0.5" />
    <rect x="11" y="19" width="4" height="1" fill="url(#toolsGradient)" rx="0.2" />
    <circle cx="13" cy="18.5" r="0.3" fill="url(#toolsGradient)" />
    {/* البراغي */}
    <circle cx="21" cy="4" r="0.5" fill="#FFD54F" />
    <circle cx="22" cy="6" r="0.4" fill="#FFC107" />
    <circle cx="20" cy="2" r="0.4" fill="#FF9800" />
  </svg>
);

interface NavIconProps extends ColoredIconProps {
  type: 'home' | 'store' | 'categories' | 'map' | 'pricing';
}

export const NavColoredIcon: React.FC<NavIconProps> = ({ type, ...props }) => {
  switch (type) {
    case 'home':
      return <HomeColoredIcon {...props} />;
    case 'store':
      return <StoreColoredIcon {...props} />;
    case 'categories':
      return <CategoriesColoredIcon {...props} />;
    case 'map':
      return <MapColoredIcon {...props} />;
    case 'pricing':
      return <PricingColoredIcon {...props} />;
    default:
      return <HomeColoredIcon {...props} />;
  }
};

// مكون موحد للأيقونات الملونة للفئات
interface CategoryIconProps extends ColoredIconProps {
  category: 'food' | 'fashion' | 'electronics' | 'home' | 'beauty' | 'sports' | 'automotive' | 'books' | 'arts' | 'awesome' | 'other' | 'all' | 'groceries' | 'handicrafts' | 'toys' | 'pets' | 'babyKids' | 'jewelry' | 'services' | 'plants' | 'appliances' | 'waterSports' | 'perfumes' | 'tools';
}

export const CategoryColoredIcon: React.FC<CategoryIconProps> = ({ category, ...props }) => {
  switch (category) {
    case 'food':
      return <FoodColoredIcon {...props} />;
    case 'fashion':
      return <FashionColoredIcon {...props} />;
    case 'electronics':
      return <ElectronicsColoredIcon {...props} />;
    case 'home':
      return <HomeGardenColoredIcon {...props} />;
    case 'beauty':
      return <BeautyHealthColoredIcon {...props} />;
    case 'sports':
      return <SportsColoredIcon {...props} />;
    case 'automotive':
      return <AutomotiveColoredIcon {...props} />;
    case 'books':
      return <BooksMediaColoredIcon {...props} />;
    case 'arts':
      return <ArtsCraftsColoredIcon {...props} />;
    case 'awesome':
      return <AwesomeStuffColoredIcon {...props} />;
    case 'other':
      return <OtherColoredIcon {...props} />;
    case 'all':
      return <CategoriesColoredIcon {...props} />;
    case 'groceries':
      return <GroceriesColoredIcon {...props} />;
    case 'handicrafts':
      return <HandicraftsColoredIcon {...props} />;
    case 'toys':
      return <ToysColoredIcon {...props} />;
    case 'pets':
      return <PetsColoredIcon {...props} />;
    case 'babyKids':
      return <BabyKidsColoredIcon {...props} />;
    case 'jewelry':
      return <JewelryColoredIcon {...props} />;
    case 'services':
      return <ServicesColoredIcon {...props} />;
    case 'plants':
      return <PlantsColoredIcon {...props} />;
    case 'appliances':
      return <AppliancesColoredIcon {...props} />;
    case 'waterSports':
      return <WaterSportsColoredIcon {...props} />;
    case 'perfumes':
      return <PerfumesColoredIcon {...props} />;
    case 'tools':
      return <ToolsColoredIcon {...props} />;
    default:
      return <OtherColoredIcon {...props} />;
  }
};
