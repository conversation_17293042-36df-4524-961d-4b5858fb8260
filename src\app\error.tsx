// src/app/error.tsx
'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  Home, 
  AlertTriangle, 
  Bug,
  ExternalLink 
} from 'lucide-react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // تسجيل الخطأ للمراقبة
    console.error('Application Error:', error);
  }, [error]);

  // تحديد نوع الخطأ
  const getErrorType = (error: Error) => {
    if (error.message.includes('ChunkLoadError') || error.message.includes('Loading chunk')) {
      return 'chunk';
    }
    if (error.message.includes('Stream') || error.message.includes('pipe') || error.digest?.includes('2710195599')) {
      return 'stream';
    }
    if (error.message.includes('Network') || error.message.includes('fetch')) {
      return 'network';
    }
    if (error.message.includes('Firebase') || error.message.includes('auth/')) {
      return 'firebase';
    }
    return 'unknown';
  };

  const errorType = getErrorType(error);

  // رسائل خطأ مخصصة
  const getErrorMessage = (type: string) => {
    switch (type) {
      case 'chunk':
        return {
          title: 'خطأ في تحميل التطبيق',
          description: 'فشل في تحميل جزء من التطبيق. قد يكون بسبب تحديث جديد.',
          solution: 'يرجى إعادة تحميل الصفحة أو مسح cache المتصفح.'
        };
      case 'stream':
        return {
          title: 'مشكلة مؤقتة في الخدمة',
          description: 'حدثت مشكلة مؤقتة في معالجة البيانات.',
          solution: 'هذه مشكلة مؤقتة، يرجى إعادة تحميل الصفحة.'
        };
      case 'network':
        return {
          title: 'خطأ في الشبكة',
          description: 'فشل في الاتصال بالخادم.',
          solution: 'تحقق من اتصال الإنترنت وحاول مرة أخرى.'
        };
      case 'firebase':
        return {
          title: 'خطأ في الخدمة',
          description: 'مشكلة في خدمات التطبيق.',
          solution: 'يرجى المحاولة مرة أخرى أو تسجيل الخروج وإعادة الدخول.'
        };
      default:
        return {
          title: 'حدث خطأ غير متوقع',
          description: 'نعتذر، حدث خطأ في التطبيق.',
          solution: 'يرجى إعادة المحاولة أو الاتصال بالدعم الفني.'
        };
    }
  };

  const errorInfo = getErrorMessage(errorType);

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-md border-destructive/20">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-destructive/10 rounded-full w-fit">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>
          <CardTitle className="text-destructive text-xl">
            {errorInfo.title}
          </CardTitle>
          <CardDescription className="text-center">
            {errorInfo.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* تفاصيل الخطأ */}
          <Alert>
            <Bug className="h-4 w-4" />
            <AlertDescription className="text-sm">
              <strong>تفاصيل الخطأ:</strong><br />
              {error.message}
              {error.digest && (
                <>
                  <br />
                  <strong>معرف الخطأ:</strong> {error.digest}
                </>
              )}
            </AlertDescription>
          </Alert>

          {/* الحل المقترح */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/10 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
              💡 الحل المقترح:
            </h4>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              {errorInfo.solution}
            </p>
          </div>

          {/* أزرار الإجراءات */}
          <div className="space-y-3">
            <Button
              onClick={reset}
              className="w-full"
              variant="default"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              إعادة المحاولة
            </Button>

            <Button
              onClick={() => window.location.href = '/ar'}
              variant="outline"
              className="w-full"
            >
              <Home className="mr-2 h-4 w-4" />
              العودة للصفحة الرئيسية
            </Button>

            {errorType === 'chunk' && (
              <Button
                onClick={() => {
                  // مسح cache ثم إعادة تحميل
                  if ('caches' in window) {
                    caches.keys().then(names => {
                      names.forEach(name => caches.delete(name));
                    });
                  }
                  window.location.reload();
                }}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                مسح Cache وإعادة التحميل
              </Button>
            )}

            {errorType === 'stream' && (
              <Button
                onClick={() => {
                  // إعادة تحميل فورية لأخطاء Stream
                  window.location.reload();
                }}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                إعادة تحميل الصفحة
              </Button>
            )}
          </div>

          {/* معلومات إضافية للمطورين */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                تفاصيل تقنية (للمطورين)
              </summary>
              <div className="mt-2 p-3 bg-muted rounded-lg">
                <pre className="text-xs overflow-auto">
                  {error.stack}
                </pre>
              </div>
            </details>
          )}

          {/* رابط الدعم */}
          <div className="text-center pt-4 border-t">
            <p className="text-xs text-muted-foreground mb-2">
              إذا استمرت المشكلة، يرجى الاتصال بالدعم الفني
            </p>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs"
              onClick={() => {
                const subject = encodeURIComponent(`خطأ في التطبيق: ${errorInfo.title}`);
                const body = encodeURIComponent(`
تفاصيل الخطأ:
- النوع: ${errorType}
- الرسالة: ${error.message}
- المعرف: ${error.digest || 'غير متوفر'}
- المتصفح: ${navigator.userAgent}
- الوقت: ${new Date().toISOString()}
                `);
                window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
              }}
            >
              <ExternalLink className="mr-1 h-3 w-3" />
              إرسال تقرير خطأ
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
