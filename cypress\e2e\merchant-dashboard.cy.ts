describe('لوحة تحكم التاجر', () => {
  beforeEach(() => {
    cy.mockFirebaseAuth()
    cy.mockLogin('merchant')
    cy.visitWithLocale('/merchant/dashboard')
  })

  it('يجب أن تعرض لوحة تحكم التاجر بنجاح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من العنوان الرئيسي
    cy.shouldContainArabicText('لوحة تحكم التاجر')
    cy.get('[data-testid="merchant-dashboard"]').should('be.visible')
    
    // التحقق من وجود الإحصائيات الرئيسية
    cy.get('[data-testid="dashboard-stats"]').should('be.visible')
    cy.get('[data-testid="total-sales-stat"]').should('be.visible')
    cy.get('[data-testid="total-orders-stat"]').should('be.visible')
    cy.get('[data-testid="total-products-stat"]').should('be.visible')
    cy.get('[data-testid="pending-orders-stat"]').should('be.visible')
  })

  it('يجب أن تعرض الإحصائيات بشكل صحيح', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من بطاقات الإحصائيات
    cy.get('[data-testid="dashboard-stats"]').within(() => {
      cy.shouldContainArabicText('إجمالي المبيعات')
      cy.shouldContainArabicText('إجمالي الطلبات')
      cy.shouldContainArabicText('إجمالي المنتجات')
      cy.shouldContainArabicText('الطلبات المعلقة')
    })
    
    // التحقق من وجود أرقام الإحصائيات
    cy.get('[data-testid="total-sales-amount"]').should('contain.text', /\d+/)
    cy.get('[data-testid="total-orders-count"]').should('contain.text', /\d+/)
    cy.get('[data-testid="total-products-count"]').should('contain.text', /\d+/)
  })

  it('يجب أن تعرض الإجراءات السريعة', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود الإجراءات السريعة
    cy.get('[data-testid="quick-actions"]').should('be.visible')
    cy.get('[data-testid="add-product-action"]').should('be.visible')
    cy.get('[data-testid="manage-products-action"]').should('be.visible')
    cy.get('[data-testid="manage-orders-action"]').should('be.visible')
    cy.get('[data-testid="store-settings-action"]').should('be.visible')
    cy.get('[data-testid="view-reports-action"]').should('be.visible')
    cy.get('[data-testid="manage-inventory-action"]').should('be.visible')
  })

  it('يجب أن تعمل الإجراءات السريعة', () => {
    cy.waitForLoadingToFinish()
    
    // اختبار إضافة منتج جديد
    cy.get('[data-testid="add-product-action"]').click()
    cy.url().should('include', '/merchant/products/add')
    
    // العودة للوحة التحكم
    cy.visitWithLocale('/merchant/dashboard')
    cy.waitForLoadingToFinish()
    
    // اختبار إدارة المنتجات
    cy.get('[data-testid="manage-products-action"]').click()
    cy.url().should('include', '/merchant/products')
    
    // العودة للوحة التحكم
    cy.visitWithLocale('/merchant/dashboard')
    cy.waitForLoadingToFinish()
    
    // اختبار إدارة الطلبات
    cy.get('[data-testid="manage-orders-action"]').click()
    cy.url().should('include', '/merchant/orders')
  })

  it('يجب أن تعرض الطلبات الأخيرة', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من قسم الطلبات الأخيرة
    cy.get('[data-testid="recent-orders"]').should('be.visible')
    cy.shouldContainArabicText('الطلبات الأخيرة')
    
    // التحقق من وجود قائمة الطلبات
    cy.get('[data-testid="orders-list"]').should('be.visible')
    cy.get('[data-testid="order-item"]').should('have.length.greaterThan', 0)
    
    // التحقق من تفاصيل الطلب
    cy.get('[data-testid="order-item"]').first().within(() => {
      cy.get('[data-testid="order-number"]').should('be.visible')
      cy.get('[data-testid="order-status"]').should('be.visible')
      cy.get('[data-testid="order-total"]').should('be.visible')
      cy.get('[data-testid="order-date"]').should('be.visible')
    })
  })

  it('يجب أن تعرض المنتجات الأكثر مبيعاً', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من قسم المنتجات الأكثر مبيعاً
    cy.get('[data-testid="top-selling-products"]').should('be.visible')
    cy.shouldContainArabicText('المنتجات الأكثر مبيعاً')
    
    // التحقق من قائمة المنتجات
    cy.get('[data-testid="top-products-list"]').should('be.visible')
    cy.get('[data-testid="product-item"]').should('have.length.greaterThan', 0)
    
    // التحقق من تفاصيل المنتج
    cy.get('[data-testid="product-item"]').first().within(() => {
      cy.get('[data-testid="product-name"]').should('be.visible')
      cy.get('[data-testid="product-sales"]').should('be.visible')
      cy.get('[data-testid="product-revenue"]').should('be.visible')
    })
  })

  it('يجب أن تعرض الرسم البياني للمبيعات', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من قسم الرسم البياني
    cy.get('[data-testid="sales-chart"]').should('be.visible')
    cy.shouldContainArabicText('مبيعات الشهر الحالي')
    
    // التحقق من وجود الرسم البياني
    cy.get('[data-testid="chart-container"]').should('be.visible')
    
    // اختبار تغيير فترة الرسم البياني
    cy.get('[data-testid="chart-period-selector"]').select('week')
    cy.get('[data-testid="chart-container"]').should('be.visible')
    
    cy.get('[data-testid="chart-period-selector"]').select('month')
    cy.get('[data-testid="chart-container"]').should('be.visible')
  })

  it('يجب أن تعرض التنبيهات والإشعارات', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من قسم التنبيهات
    cy.get('[data-testid="alerts-section"]').should('be.visible')
    cy.shouldContainArabicText('التنبيهات')
    
    // التحقق من أنواع التنبيهات المختلفة
    cy.get('[data-testid="low-stock-alert"]').should('be.visible')
    cy.get('[data-testid="pending-orders-alert"]').should('be.visible')
    cy.get('[data-testid="new-reviews-alert"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة تحديث البيانات', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على زر التحديث
    cy.get('[data-testid="refresh-dashboard"]').click()
    
    // التحقق من إعادة تحميل البيانات
    cy.get('[data-testid="loading-indicator"]').should('be.visible')
    cy.get('[data-testid="loading-indicator"]').should('not.exist')
    
    // التحقق من تحديث الإحصائيات
    cy.get('[data-testid="dashboard-stats"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة تصفية البيانات', () => {
    cy.waitForLoadingToFinish()
    
    // تصفية الطلبات حسب الحالة
    cy.get('[data-testid="orders-filter"]').select('pending')
    cy.get('[data-testid="filtered-orders"]').should('be.visible')
    
    // تصفية المنتجات حسب الفئة
    cy.get('[data-testid="products-filter"]').select('electronics')
    cy.get('[data-testid="filtered-products"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة البحث السريع', () => {
    cy.waitForLoadingToFinish()
    
    // البحث عن طلب
    cy.get('[data-testid="quick-search"]').type('ORD-001')
    cy.get('[data-testid="search-results"]').should('be.visible')
    cy.get('[data-testid="search-result-order"]').should('contain.text', 'ORD-001')
    
    // مسح البحث
    cy.get('[data-testid="clear-search"]').click()
    cy.get('[data-testid="search-results"]').should('not.exist')
  })

  it('يجب أن تعرض معلومات المتجر', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من قسم معلومات المتجر
    cy.get('[data-testid="store-info"]').should('be.visible')
    cy.get('[data-testid="store-name"]').should('be.visible')
    cy.get('[data-testid="store-rating"]').should('be.visible')
    cy.get('[data-testid="store-status"]').should('be.visible')
    
    // التحقق من رابط إعدادات المتجر
    cy.get('[data-testid="store-settings-link"]').should('be.visible')
    cy.get('[data-testid="store-settings-link"]').click()
    cy.url().should('include', '/merchant/store/settings')
  })

  it('يجب أن تعمل وظيفة تصدير التقارير', () => {
    cy.waitForLoadingToFinish()
    
    // النقر على زر تصدير التقارير
    cy.get('[data-testid="export-reports"]').click()
    cy.get('[data-testid="export-options"]').should('be.visible')
    
    // اختيار نوع التقرير
    cy.get('[data-testid="report-type"]').select('sales')
    cy.get('[data-testid="report-period"]').select('month')
    cy.get('[data-testid="report-format"]').select('pdf')
    
    // تصدير التقرير
    cy.get('[data-testid="export-report-button"]').click()
    
    // التحقق من بدء التحميل
    cy.get('[data-testid="export-progress"]').should('be.visible')
    cy.shouldContainArabicText('جاري تصدير التقرير')
  })

  it('يجب أن تعمل الروابط السريعة', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من وجود الروابط السريعة
    cy.get('[data-testid="quick-links"]').should('be.visible')
    
    // اختبار رابط إضافة منتج
    cy.get('[data-testid="quick-add-product"]').click()
    cy.url().should('include', '/merchant/products/add')
    
    // العودة واختبار رابط آخر
    cy.visitWithLocale('/merchant/dashboard')
    cy.waitForLoadingToFinish()
    
    cy.get('[data-testid="quick-view-analytics"]').click()
    cy.url().should('include', '/merchant/analytics')
  })

  it('يجب أن تعمل وظيفة الإشعارات', () => {
    cy.waitForLoadingToFinish()
    
    // التحقق من أيقونة الإشعارات
    cy.get('[data-testid="notifications-icon"]').should('be.visible')
    cy.get('[data-testid="notifications-count"]').should('be.visible')
    
    // فتح قائمة الإشعارات
    cy.get('[data-testid="notifications-icon"]').click()
    cy.get('[data-testid="notifications-dropdown"]').should('be.visible')
    
    // التحقق من وجود إشعارات
    cy.get('[data-testid="notification-item"]').should('have.length.greaterThan', 0)
    
    // قراءة إشعار
    cy.get('[data-testid="notification-item"]').first().click()
    cy.get('[data-testid="notification-details"]').should('be.visible')
  })

  it('يجب أن تتعامل مع الأخطاء بشكل صحيح', () => {
    // محاكاة خطأ في تحميل البيانات
    cy.intercept('GET', '**/api/merchant/dashboard**', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('dashboardError')
    
    cy.visitWithLocale('/merchant/dashboard')
    cy.wait('@dashboardError')
    
    // التحقق من عرض رسالة الخطأ
    cy.get('[data-testid="dashboard-error"]').should('be.visible')
    cy.shouldContainArabicText('حدث خطأ في تحميل البيانات')
    
    // التحقق من وجود زر إعادة المحاولة
    cy.get('[data-testid="retry-dashboard"]').should('be.visible')
  })

  it('يجب أن تعمل على الأجهزة المحمولة', () => {
    cy.viewport('iphone-x')
    cy.waitForLoadingToFinish()
    
    // التحقق من التجاوب
    cy.get('[data-testid="merchant-dashboard"]').should('be.visible')
    
    // اختبار القائمة المحمولة
    cy.get('[data-testid="mobile-menu-toggle"]').should('be.visible')
    cy.get('[data-testid="mobile-menu-toggle"]').click()
    cy.get('[data-testid="mobile-navigation"]').should('be.visible')
    
    // التحقق من تجاوب الإحصائيات
    cy.get('[data-testid="dashboard-stats"]').should('be.visible')
    cy.get('[data-testid="mobile-stats-grid"]').should('be.visible')
  })

  it('يجب أن تعمل وظيفة التخصيص', () => {
    cy.waitForLoadingToFinish()
    
    // فتح إعدادات التخصيص
    cy.get('[data-testid="customize-dashboard"]').click()
    cy.get('[data-testid="customization-panel"]').should('be.visible')
    
    // تخصيص ترتيب الأقسام
    cy.get('[data-testid="section-orders"]').drag('[data-testid="section-products"]')
    
    // حفظ التخصيص
    cy.get('[data-testid="save-customization"]').click()
    
    // التحقق من رسالة النجاح
    cy.get('[data-testid="customization-success"]').should('be.visible')
    cy.shouldContainArabicText('تم حفظ التخصيص بنجاح')
  })
})
