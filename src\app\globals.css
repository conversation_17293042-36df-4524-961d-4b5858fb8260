
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-inter), Arial, Helvetica, sans-serif;
}

/* Arabic font support */
.font-tajawal {
  font-family: var(--font-tajawal), '<PERSON><PERSON>wal', Arial, sans-serif;
}

.font-inter {
  font-family: var(--font-inter), 'Inter', Arial, sans-serif;
}

/* RTL support improvements */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .rtl\:text-left {
  text-align: left;
}

[dir="rtl"] .rtl\:text-right {
  text-align: right;
}

/* Margin and padding RTL support */
[dir="rtl"] .rtl\:mr-2 {
  margin-right: 0.5rem;
}

[dir="rtl"] .rtl\:ml-2 {
  margin-left: 0.5rem;
}

[dir="rtl"] .rtl\:me-2 {
  margin-inline-end: 0.5rem;
}

[dir="rtl"] .rtl\:ms-0 {
  margin-inline-start: 0;
}

@layer base {
  :root {
    --background: 60 56% 90%; /* Soft Beige - #F5F5DC */
    --foreground: 60 10% 25%; /* Darker text on beige */

    --card: 60 56% 93%; /* Lighter than background */
    --card-foreground: 60 10% 25%;

    --popover: 60 56% 93%;
    --popover-foreground: 60 10% 25%;

    --primary: 33 41% 70%; /* Sandstone - #D3B594 */
    --primary-foreground: 33 25% 20%; /* Dark text on Sandstone */

    --secondary: 33 41% 80%; /* Lighter Sandstone */
    --secondary-foreground: 33 25% 25%;

    --muted: 60 50% 85%; /* Muted beige */
    --muted-foreground: 60 20% 45%;

    --accent: 12 70% 62%; /* Terracotta - #E2725B */
    --accent-foreground: 0 0% 100%; /* White text on Terracotta */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 60 30% 80%; /* Beige border */
    --input: 60 30% 85%; /* Slightly lighter beige for input background */
    --ring: 33 41% 60%; /* Darker Sandstone for ring */

    --radius: 0.5rem;

    /* Chart colors can remain generic or be themed if specific requests arise */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar colors - Themed to match Mikhla palette */
    --sidebar-background: 33 41% 75%; /* Richer Sandstone */
    --sidebar-foreground: 33 25% 15%; /* Dark text */
    --sidebar-primary: 12 70% 58%; /* Darker Terracotta */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 33 41% 80%; /* Lighter Sandstone for hover */
    --sidebar-accent-foreground: 33 25% 15%;
    --sidebar-border: 33 41% 65%;
    --sidebar-ring: 12 70% 50%;
  }

  .dark {
    --background: 30 10% 15%; /* Dark, slightly warm gray */
    --foreground: 30 10% 85%; /* Light gray text */

    --card: 30 10% 18%;
    --card-foreground: 30 10% 85%;

    --popover: 30 10% 18%;
    --popover-foreground: 30 10% 85%;

    --primary: 33 35% 50%; /* Muted Sandstone */
    --primary-foreground: 33 25% 90%; /* Light text on muted Sandstone */

    --secondary: 33 30% 40%; /* Darker muted Sandstone */
    --secondary-foreground: 33 25% 85%;

    --muted: 30 10% 25%; /* Dark muted gray */
    --muted-foreground: 30 10% 65%;

    --accent: 12 50% 50%; /* Muted Terracotta */
    --accent-foreground: 12 80% 90%; /* Light text on muted Terracotta */
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 30 10% 28%;
    --input: 30 10% 28%;
    --ring: 33 35% 60%; /* Lighter muted Sandstone for ring */

    /* Chart colors for dark mode */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    /* Sidebar dark theme */
    --sidebar-background: 30 10% 12%;
    --sidebar-foreground: 30 10% 80%;
    --sidebar-primary: 12 50% 45%;
    --sidebar-primary-foreground: 12 80% 90%;
    --sidebar-accent: 30 10% 20%;
    --sidebar-accent-foreground: 30 10% 85%;
    --sidebar-border: 30 10% 22%;
    --sidebar-ring: 12 50% 55%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

/* تحسين: أنماط الشعار المتحرك محسنة للأداء */
@keyframes bagPath1 {
  0% { stroke-dashoffset: 1000; opacity: 0; }
  10% { stroke-dashoffset: 500; opacity: 1; }
  20%, 70% { stroke-dashoffset: 0; opacity: 1; }
  90% { stroke-dashoffset: 500; opacity: 0.5; }
  100% { stroke-dashoffset: 1000; opacity: 0; }
}

@keyframes bagPath2 {
  0% { stroke-dashoffset: 1000; opacity: 0; }
  15% { stroke-dashoffset: 500; opacity: 1; }
  25%, 65% { stroke-dashoffset: 0; opacity: 1; }
  85% { stroke-dashoffset: 500; opacity: 0.5; }
  100% { stroke-dashoffset: 1000; opacity: 0; }
}

@keyframes bagPath3 {
  0% { stroke-dashoffset: 1000; opacity: 0; }
  20% { stroke-dashoffset: 500; opacity: 1; }
  30%, 60% { stroke-dashoffset: 0; opacity: 1; }
  80% { stroke-dashoffset: 500; opacity: 0.5; }
  100% { stroke-dashoffset: 1000; opacity: 0; }
}

@keyframes bagPath4 {
  0% { stroke-dashoffset: 1000; opacity: 0; }
  25% { stroke-dashoffset: 500; opacity: 1; }
  35%, 55% { stroke-dashoffset: 0; opacity: 1; }
  75% { stroke-dashoffset: 500; opacity: 0.5; }
  100% { stroke-dashoffset: 1000; opacity: 0; }
}

@keyframes letterFade {
  0% { opacity: 0; transform: scale(0.8); }
  30% { opacity: 1; transform: scale(1); }
  70% { opacity: 1; transform: scale(1); }
  100% { opacity: 0; transform: scale(0.8); }
}

/* تحسين: رسوم متحركة أكثر كفاءة */
@keyframes pulseGentle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* تحسين: إضافة دعم لتقليل الرسوم المتحركة */
@media (prefers-reduced-motion: reduce) {
  .animate-path-1,
  .animate-path-2,
  .animate-path-3,
  .animate-path-4,
  .animate-letter,
  .animate-pulse-gentle {
    animation: none !important;
  }
}

@layer utilities {
  /* تحسين: رسوم متحركة محسنة للأداء */
  .animate-path-1 {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: bagPath1 6s ease-in-out infinite; /* تحسين: إبطاء الرسوم المتحركة */
    will-change: stroke-dashoffset, opacity; /* تحسين: تحسين الأداء */
  }

  .animate-path-2 {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: bagPath2 6s ease-in-out infinite;
    will-change: stroke-dashoffset, opacity;
  }

  .animate-path-3 {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: bagPath3 6s ease-in-out infinite;
    will-change: stroke-dashoffset, opacity;
  }

  .animate-path-4 {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: bagPath4 6s ease-in-out infinite;
    will-change: stroke-dashoffset, opacity;
  }

  .animate-letter {
    opacity: 0;
    animation: letterFade 6s ease-out infinite;
    will-change: opacity, transform;
  }

  .animate-pulse-gentle {
    animation: pulseGentle 4s infinite ease-in-out; /* تحسين: إبطاء النبضة */
    will-change: transform;
  }

  /* تحسين: فئات مساعدة للأداء */
  .performance-optimized {
    transform: translateZ(0); /* تفعيل hardware acceleration */
    backface-visibility: hidden;
  }
}

/* تحسينات خاصة بخريطة Pigeon Maps */
/* حل مشكلة الجزء الرصاصي والبقع في الخريطة */
[data-testid="location-map-container"] {
  background: transparent !important;
  position: relative;
  isolation: isolate;
  overflow: hidden;
}

/* إزالة جميع الخلفيات الرصاصية */
[data-testid="location-map-container"] *,
[data-testid="location-map-container"] > div,
[data-testid="location-map-container"] > div > div,
[data-testid="location-map-container"] > div > div > div {
  background: transparent !important;
  background-color: transparent !important;
}

/* تحسين عرض tiles الخريطة */
[data-testid="location-map-container"] img {
  background: transparent !important;
  transition: opacity 0.2s ease-in-out;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  max-width: none !important;
  height: auto !important;
}

/* تحسين عرض الخريطة على الشاشات المختلفة */
[data-testid="location-map-container"] canvas {
  background: transparent !important;
  max-width: none !important;
  height: auto !important;
}

/* تحسين أداء الخريطة */
[data-testid="location-map-container"] * {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* إزالة أي overlay أو طبقات إضافية */
[data-testid="location-map-container"] .pigeon-overlay,
[data-testid="location-map-container"] .pigeon-tiles,
[data-testid="location-map-container"] .pigeon-tiles-layer {
  background: transparent !important;
  background-color: transparent !important;
}



/* تحسينات النافذة المنبثقة للخريطة */
.map-modal-overlay {
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, 0.5);
}

.map-modal-content {
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* تحسينات markers الخريطة */
.map-marker-user {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  animation: pulse-marker 2s infinite;
}

.map-marker-store {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.2s ease-in-out;
}

.map-marker-store:hover {
  transform: scale(1.2);
}

@keyframes pulse-marker {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* تحسينات overlay المتاجر */
.store-overlay {
  animation: fadeInUp 0.3s ease-out;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات شريط المعلومات */
.map-info-bar {
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين عرض المؤشر */
[data-testid="location-map-container"] svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  z-index: 1000;
}

/* تحسين الاستجابة للمس */
[data-testid="location-map-container"] {
  touch-action: pan-x pan-y;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* إصلاح مشكلة البقع الفاتحة */
[data-testid="location-map-container"] .pigeon-map-overlay {
  display: none !important;
}

/* ضمان عدم وجود أي خلفيات مخفية */
[data-testid="location-map-container"] div[style*="background"] {
  background: transparent !important;
}

/* تحسينات قائمة التنقل المتحركة */
@keyframes navIconHover {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1) rotate(2deg);
    filter: brightness(1.2);
  }
  100% {
    transform: scale(1.05) rotate(0deg);
    filter: brightness(1.1);
  }
}

@keyframes navIconActive {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(210, 181, 148, 0);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(210, 181, 148, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 15px rgba(210, 181, 148, 0.3);
  }
}

@keyframes navTextSlide {
  0% {
    opacity: 0.7;
    transform: translateY(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes navGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(226, 114, 91, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(226, 114, 91, 0.6);
  }
}

/* فئات CSS للقائمة المتحركة */
.nav-item {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  padding: 8px 16px;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(210, 181, 148, 0.1), rgba(226, 114, 91, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.nav-item:hover::before {
  opacity: 1;
}

/* أنماط الأيقونات الملونة للفئات */
@keyframes categoryIconHover {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1);
  }
  50% {
    transform: scale(1.1) rotate(3deg);
    filter: brightness(1.2) saturate(1.3);
  }
  100% {
    transform: scale(1.05) rotate(0deg);
    filter: brightness(1.1) saturate(1.2);
  }
}

@keyframes categoryCardFloat {
  0%, 100% {
    transform: translateY(0px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

@keyframes categoryIconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes categoryGradientShift {
  0%, 100% {
    filter: hue-rotate(0deg);
  }
  50% {
    filter: hue-rotate(10deg);
  }
}

/* فئات CSS للأيقونات الملونة */
.category-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.category-icon:hover {
  animation: categoryIconHover 0.6s ease-in-out;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* بطاقات الفئات */
.category-card {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(245, 245, 220, 0.8));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(211, 181, 148, 0.2);
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(226, 114, 91, 0.05), rgba(211, 181, 148, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.category-card:hover::before {
  opacity: 1;
}

.category-card:hover {
  animation: categoryCardFloat 0.6s ease-in-out;
  border-color: rgba(226, 114, 91, 0.3);
}

.category-card.selected {
  background: linear-gradient(135deg, rgba(226, 114, 91, 0.1), rgba(211, 181, 148, 0.1));
  border-color: rgba(226, 114, 91, 0.5);
  box-shadow: 0 0 20px rgba(226, 114, 91, 0.2);
}

.category-card.selected .category-icon {
  animation: categoryIconPulse 2s ease-in-out infinite;
}

/* تحسينات للأيقونات المتدرجة */
.category-icon svg defs linearGradient,
.category-icon svg defs radialGradient {
  animation: categoryGradientShift 4s ease-in-out infinite;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .category-card {
    padding: 12px;
  }

  .category-icon {
    transform: scale(0.9);
  }
}

/* تحسينات إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
  .category-icon,
  .category-card,
  .category-icon:hover,
  .category-card:hover,
  .category-card.selected .category-icon {
    animation: none !important;
    transition: none !important;
  }
}

.nav-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-item:hover .nav-icon {
  animation: navIconHover 0.6s ease-in-out;
}

.nav-item:hover .nav-text {
  animation: navTextSlide 0.3s ease-out;
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(210, 181, 148, 0.2), rgba(226, 114, 91, 0.15));
  animation: navIconActive 2s ease-in-out infinite;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 3px;
  background: linear-gradient(90deg, #D3B594, #E2725B);
  border-radius: 2px;
  animation: navGlow 2s ease-in-out infinite;
}

.nav-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.nav-text {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .nav-item {
    padding: 6px 12px;
  }

  .nav-item:hover {
    transform: translateY(-1px);
  }

  /* تحسينات القائمة السفلية للهواتف */
  .mobile-nav-item {
    padding: 8px 4px;
    border-radius: 8px;
    min-height: 60px;
    transition: all 0.2s ease;
  }

  .mobile-nav-item:hover {
    transform: translateY(-2px);
    background: rgba(210, 181, 148, 0.1);
  }

  .mobile-nav-item.active {
    background: linear-gradient(135deg, rgba(210, 181, 148, 0.2), rgba(226, 114, 91, 0.15));
  }

  .mobile-nav-item.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: linear-gradient(90deg, #D3B594, #E2725B);
    border-radius: 0 0 2px 2px;
  }

  .mobile-nav-item .nav-icon {
    transform: scale(0.9);
  }

  .mobile-nav-item .nav-text {
    font-size: 0.7rem;
    line-height: 1;
  }

  /* إضافة مساحة آمنة للهواتف */
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* تحسينات إضافية للقائمة */
.nav-item {
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.nav-item:active {
  transform: scale(0.95);
}

/* تأثيرات خاصة للأيقونات */
.nav-icon svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover .nav-icon svg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.nav-item.active .nav-icon svg {
  filter: drop-shadow(0 4px 12px rgba(226, 114, 91, 0.3));
}

/* تحسين الخط والنص */
.nav-text {
  font-weight: 500;
  letter-spacing: 0.025em;
}

.nav-item.active .nav-text {
  font-weight: 600;
  color: hsl(var(--primary));
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  .nav-item,
  .nav-icon,
  .nav-text,
  .mobile-nav-item {
    transition: none !important;
    animation: none !important;
  }

  .nav-item:hover,
  .mobile-nav-item:hover {
    transform: none !important;
  }

  .nav-item:active {
    transform: none !important;
  }
}

