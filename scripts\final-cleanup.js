#!/usr/bin/env node

/**
 * سكريبت التنظيف النهائي للتكرارات المتبقية
 */

const fs = require('fs');
const path = require('path');

const EN_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'en.json');

/**
 * إصلاح التكرارات النهائية
 */
function finalCleanup() {
  console.log('🧹 بدء التنظيف النهائي للتكرارات...');
  
  try {
    // قراءة الملف
    const content = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const data = JSON.parse(content);
    
    // إنشاء نسخة احتياطية
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${EN_TRANSLATIONS_PATH}.backup.${timestamp}`;
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    
    let fixes = 0;
    
    // إزالة التكرارات المحددة من representative.dashboard
    const duplicatesToRemove = [
      'loading', 'active', 'inactive', 'available', 'unavailable',
      'totalDeliveries', 'monthlyEarnings', 'totalEarnings', 
      'averageRating', 'reviews', 'minutes'
    ];
    
    if (data.representative && data.representative.dashboard) {
      duplicatesToRemove.forEach(key => {
        if (data.representative.dashboard[key] && data[key]) {
          console.log(`   ❌ إزالة تكرار: representative.dashboard.${key}`);
          delete data.representative.dashboard[key];
          fixes++;
        }
      });
    }
    
    // إزالة تكرارات أخرى
    if (data.representative && data.representative.nav) {
      if (data.representative.nav.profile && data.profile) {
        console.log(`   ❌ إزالة تكرار: representative.nav.profile`);
        delete data.representative.nav.profile;
        fixes++;
      }
      
      if (data.representative.nav.orders && data.orders) {
        console.log(`   ❌ إزالة تكرار: representative.nav.orders`);
        delete data.representative.nav.orders;
        fixes++;
      }
      
      if (data.representative.nav.earnings && data.earnings) {
        console.log(`   ❌ إزالة تكرار: representative.nav.earnings`);
        delete data.representative.nav.earnings;
        fixes++;
      }
    }
    
    // كتابة الملف المصلح
    const cleanedContent = JSON.stringify(data, null, 2);
    fs.writeFileSync(EN_TRANSLATIONS_PATH, cleanedContent);
    
    console.log(`   ✅ تم إصلاح ${fixes} تكرار`);
    
    // التحقق من صحة JSON
    JSON.parse(fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8'));
    console.log(`   ✅ الملف صحيح بعد الإصلاح`);
    
    return { success: true, fixesApplied: fixes };
    
  } catch (error) {
    console.error(`   ❌ خطأ في الإصلاح:`, error.message);
    return { success: false, fixesApplied: 0 };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء التنظيف النهائي...\n');
  
  const result = finalCleanup();
  
  console.log('');
  
  if (result.success) {
    console.log('🎉 تم التنظيف النهائي بنجاح!');
    console.log(`📊 إجمالي الإصلاحات: ${result.fixesApplied}`);
    
    // تشغيل التحقق النهائي
    console.log('\n🔍 تشغيل التحقق النهائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في التنظيف النهائي');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  finalCleanup
};
