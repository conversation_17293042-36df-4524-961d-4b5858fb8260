import type { Locale } from '@/lib/i18n';
import { getTranslations } from '@/context/locale-context';
import LoginForm from '@/components/auth/LoginForm';
import Link from 'next/link';
import { BrandLogo } from '@/components/Logo';
import LoginPageClient from '@/components/auth/LoginPageClient';

export default async function LoginPage({ params }: { params: { locale: Locale } }) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale);
  const isRTL = locale === 'ar';

  return (
    <LoginPageClient locale={locale}>
      <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8 p-8 bg-card shadow-xl rounded-lg border border-border">
          <div className="space-y-6">
            <div className="flex flex-col items-center">
              <Link href={`/${locale}`} className="mb-4">
                <BrandLogo size="default" />
              </Link>
              <h2 className="text-3xl font-bold text-center text-card-foreground">
                {t('loginToAccount')}
              </h2>
            </div>

            <LoginForm locale={locale} />

            <p className="text-center text-sm text-muted-foreground">
              {t('needAccount')}{' '}
              <Link
                href={`/${locale}/signup`}
                className="font-medium text-primary hover:text-primary/80 transition-colors"
              >
                {t('signup')}
              </Link>
            </p>
          </div>
        </div>
      </div>
    </LoginPageClient>
  );
}
