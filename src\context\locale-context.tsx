// The hook `useLocale` will be the primary way for client components to access this.
import type { Locale } from '@/lib/i18n';
import { getTranslation } from '@/lib/i18n';
// Removed: import { createContext, useContext } from 'react';

export interface LocaleContextType {
  locale: Locale;
  translations: Record<string, string>;
  t: (key: string, params?: Record<string, string | number>) => string;
}

// Removed: export const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

// This async function is for server-side use to conceptually wrap or prepare data.
export async function LocaleProvider({ locale, children }: { locale: Locale; children: React.ReactNode }) {
  // Logic for LocaleProvider, if any beyond just returning children, would go here.
  // For now, it acts as a simple passthrough for children, with translations loaded for server-side logic.
  const translations = await getTranslation(locale);

  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[key] || key;
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(value));
      });
    }
    return translation;
  };
  
  return <>{children}</>;
}

// Helper function to be used in Server Components to get translations
export async function getTranslations(locale: Locale) {
  const translations = await getTranslation(locale);
  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[key] || key;
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(value));
      });
    }
    return translation;
  };
  return { t, translations, locale };
}
