// src/hooks/usePayment.ts
"use client";

import { useState, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { paymentService, type PaymentData, type PaymentMethod, type PaymentStatus } from '@/services/paymentService';

// Hook للدفع
interface UsePaymentReturn {
  processing: boolean;
  error: string | null;
  paymentData: PaymentData | null;
  createPayment: (data: Omit<PaymentData, 'id' | 'createdAt' | 'updatedAt' | 'customerId'>) => Promise<string | null>;
  processPayment: (paymentId: string, method: PaymentMethod, additionalData?: any) => Promise<boolean>;
  getPaymentDetails: (paymentId: string) => Promise<PaymentData | null>;
  refundPayment: (paymentId: string, amount: number, reason: string) => Promise<boolean>;
}

export function usePayment(): UsePaymentReturn {
  const { user } = useAuth();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);

  // إنشاء دفعة جديدة
  const createPayment = useCallback(async (
    data: Omit<PaymentData, 'id' | 'createdAt' | 'updatedAt' | 'customerId'>
  ): Promise<string | null> => {
    if (!user) {
      setError('يجب تسجيل الدخول أولاً');
      return null;
    }

    try {
      setProcessing(true);
      setError(null);

      const paymentId = await paymentService.createPayment({
        ...data,
        customerId: user.uid
      });

      if (paymentId) {
        const payment = await paymentService.getPaymentDetails(paymentId);
        setPaymentData(payment);
      }

      return paymentId;
    } catch (err) {
      console.error('Error creating payment:', err);
      setError('فشل في إنشاء الدفعة');
      return null;
    } finally {
      setProcessing(false);
    }
  }, [user]);

  // معالجة الدفع
  const processPayment = useCallback(async (
    paymentId: string,
    method: PaymentMethod,
    additionalData?: any
  ): Promise<boolean> => {
    try {
      setProcessing(true);
      setError(null);

      let success = false;

      switch (method) {
        case 'cash':
          success = await paymentService.processCashPayment(paymentId);
          break;

        case 'card':
          if (!additionalData?.cardData) {
            throw new Error('بيانات البطاقة مطلوبة');
          }
          success = await paymentService.processCardPayment(paymentId, additionalData.cardData);
          break;

        case 'paypal':
          if (!additionalData?.paypalOrderId) {
            throw new Error('معرف طلب PayPal مطلوب');
          }
          const captureResult = await paymentService.capturePayPalOrder(additionalData.paypalOrderId);
          success = captureResult.status === 'COMPLETED';
          
          if (success) {
            await paymentService.updatePaymentStatus(
              paymentId,
              'completed',
              captureResult.id,
              captureResult
            );
          }
          break;

        default:
          throw new Error(`طريقة الدفع ${method} غير مدعومة`);
      }

      if (success) {
        const updatedPayment = await paymentService.getPaymentDetails(paymentId);
        setPaymentData(updatedPayment);
      }

      return success;
    } catch (err) {
      console.error('Error processing payment:', err);
      setError(err instanceof Error ? err.message : 'فشل في معالجة الدفع');
      return false;
    } finally {
      setProcessing(false);
    }
  }, []);

  // جلب تفاصيل الدفع
  const getPaymentDetails = useCallback(async (paymentId: string): Promise<PaymentData | null> => {
    try {
      setError(null);
      const payment = await paymentService.getPaymentDetails(paymentId);
      setPaymentData(payment);
      return payment;
    } catch (err) {
      console.error('Error getting payment details:', err);
      setError('فشل في جلب تفاصيل الدفع');
      return null;
    }
  }, []);

  // استرداد المبلغ
  const refundPayment = useCallback(async (
    paymentId: string,
    amount: number,
    reason: string
  ): Promise<boolean> => {
    try {
      setProcessing(true);
      setError(null);

      const success = await paymentService.refundPayment(paymentId, amount, reason);
      
      if (success) {
        const updatedPayment = await paymentService.getPaymentDetails(paymentId);
        setPaymentData(updatedPayment);
      }

      return success;
    } catch (err) {
      console.error('Error refunding payment:', err);
      setError('فشل في استرداد المبلغ');
      return false;
    } finally {
      setProcessing(false);
    }
  }, []);

  return {
    processing,
    error,
    paymentData,
    createPayment,
    processPayment,
    getPaymentDetails,
    refundPayment
  };
}

// Hook لـ PayPal
interface UsePayPalReturn {
  loading: boolean;
  error: string | null;
  createOrder: (orderId: string, amount: number, currency?: string) => Promise<string | null>;
  isSupported: boolean;
}

export function usePayPal(): UsePayPalReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // إنشاء طلب PayPal
  const createOrder = useCallback(async (
    orderId: string,
    amount: number,
    currency: string = 'USD'
  ): Promise<string | null> => {
    try {
      setLoading(true);
      setError(null);

      const paypalOrder = await paymentService.createPayPalOrder(orderId, amount, currency);
      
      if (paypalOrder) {
        return paypalOrder.id;
      }

      return null;
    } catch (err) {
      console.error('Error creating PayPal order:', err);
      setError('فشل في إنشاء طلب PayPal');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // التحقق من دعم PayPal
  const isSupported = paymentService.isPaymentMethodSupported('paypal');

  return {
    loading,
    error,
    createOrder,
    isSupported
  };
}

// Hook لحساب الرسوم
interface UsePaymentFeesReturn {
  calculateFee: (amount: number, method: PaymentMethod) => number;
  calculateTotal: (amount: number, method: PaymentMethod) => number;
  getSupportedMethods: () => PaymentMethod[];
}

export function usePaymentFees(): UsePaymentFeesReturn {
  const calculateFee = useCallback((amount: number, method: PaymentMethod): number => {
    return paymentService.calculateProcessingFee(amount, method);
  }, []);

  const calculateTotal = useCallback((amount: number, method: PaymentMethod): number => {
    const fee = calculateFee(amount, method);
    return amount + fee;
  }, [calculateFee]);

  const getSupportedMethods = useCallback((): PaymentMethod[] => {
    const allMethods: PaymentMethod[] = ['cash', 'card', 'paypal', 'apple_pay', 'google_pay', 'stc_pay', 'mada'];
    return allMethods.filter(method => paymentService.isPaymentMethodSupported(method));
  }, []);

  return {
    calculateFee,
    calculateTotal,
    getSupportedMethods
  };
}

// Hook لحالة الدفع
interface UsePaymentStatusReturn {
  status: PaymentStatus | null;
  loading: boolean;
  error: string | null;
  checkStatus: (paymentId: string) => Promise<PaymentStatus | null>;
}

export function usePaymentStatus(paymentId?: string): UsePaymentStatusReturn {
  const [status, setStatus] = useState<PaymentStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkStatus = useCallback(async (id: string): Promise<PaymentStatus | null> => {
    try {
      setLoading(true);
      setError(null);

      const payment = await paymentService.getPaymentDetails(id);
      
      if (payment) {
        setStatus(payment.status);
        return payment.status;
      }

      return null;
    } catch (err) {
      console.error('Error checking payment status:', err);
      setError('فشل في التحقق من حالة الدفع');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // التحقق من الحالة عند تمرير paymentId
  useState(() => {
    if (paymentId) {
      checkStatus(paymentId);
    }
  });

  return {
    status,
    loading,
    error,
    checkStatus
  };
}
