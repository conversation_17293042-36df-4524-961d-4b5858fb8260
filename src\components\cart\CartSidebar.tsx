"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ShoppingCart, ShoppingBag, ArrowRight, Trash2 } from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import { useCart } from "./CartProvider";
import CartItem from "./CartItem";
import CartIcon from "./CartIcon";
import Link from "next/link";

interface CartSidebarProps {
  trigger?: React.ReactNode;
  side?: "left" | "right";
  className?: string;
}

export default function CartSidebar({
  trigger,
  side = "right",
  className = ""
}: CartSidebarProps) {
  const { t, locale } = useLocale();
  const { items, totalItems, totalAmount, clearCart, isLoading } = useCart();
  const [isOpen, setIsOpen] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const handleClearCart = async () => {
    setIsClearing(true);
    try {
      await clearCart();
    } catch (error) {
      console.error('Error clearing cart:', error);
    } finally {
      setIsClearing(false);
    }
  };

  const handleCheckout = () => {
    setIsOpen(false);
    // Navigation will be handled by the Link component
  };

  const deliveryFee = totalAmount > 100 ? 0 : 15; // Free delivery over 100 SAR
  const finalTotal = totalAmount + deliveryFee;

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {trigger || (
          <CartIcon 
            onClick={() => setIsOpen(true)}
            className={className}
          />
        )}
      </SheetTrigger>
      
      <SheetContent side={side} className="w-full sm:w-96 flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <ShoppingCart className="w-5 h-5 mr-2" />
              {t('cart')}
              {totalItems > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {totalItems}
                </Badge>
              )}
            </div>
            
            {items.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearCart}
                disabled={isClearing}
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </SheetTitle>
        </SheetHeader>

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">{t('loadingCart')}</p>
            </div>
          </div>
        ) : items.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <ShoppingBag className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('emptyCart')}</h3>
              <p className="text-muted-foreground mb-4">
                {t('emptyCartDescription')}
              </p>
              <Button onClick={() => setIsOpen(false)}>
                {t('startShopping')}
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Cart Items */}
            <ScrollArea className="flex-1 -mx-6 px-6">
              <div className="space-y-1">
                {items.map((item) => (
                  <CartItem
                    key={item.id}
                    item={item}
                    variant="compact"
                    showStore={false}
                  />
                ))}
              </div>
            </ScrollArea>

            {/* Cart Summary */}
            <div className="space-y-4 pt-4 border-t">
              {/* Subtotal */}
              <div className="flex items-center justify-between text-sm">
                <span>{t('subtotal')}:</span>
                <span className="font-semibold">{formatPrice(totalAmount)}</span>
              </div>

              {/* Delivery Fee */}
              <div className="flex items-center justify-between text-sm">
                <span>{t('deliveryFee')}:</span>
                <span className={deliveryFee === 0 ? "text-green-600 font-semibold" : "font-semibold"}>
                  {deliveryFee === 0 ? t('free') : formatPrice(deliveryFee)}
                </span>
              </div>

              {/* Free Delivery Threshold */}
              {deliveryFee > 0 && (
                <div className="text-xs text-muted-foreground">
                  {t('freeDeliveryThreshold', { amount: formatPrice(100) })}
                </div>
              )}

              <Separator />

              {/* Total */}
              <div className="flex items-center justify-between text-lg font-bold">
                <span>{t('total')}:</span>
                <span className="text-primary">{formatPrice(finalTotal)}</span>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <Link href={`/${locale}/checkout`} onClick={handleCheckout}>
                  <Button className="w-full" size="lg">
                    {t('proceedToCheckout')}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
                
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => setIsOpen(false)}
                >
                  {t('continueShopping')}
                </Button>
              </div>

              {/* Security Badge */}
              <div className="text-center text-xs text-muted-foreground">
                🔒 {t('secureCheckout')}
              </div>
            </div>
          </>
        )}
      </SheetContent>
    </Sheet>
  );
}
