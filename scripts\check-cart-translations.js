#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'en.json');

// المفاتيح المطلوبة لمكون السلة
const requiredCartKeys = [
  'cart',
  'emptyCart',
  'emptyCartDescription', 
  'startShopping',
  'loadingCart',
  'subtotal',
  'deliveryFee',
  'free',
  'total',
  'continueShopping',
  'proceedToCheckout',
  'secureCheckout',
  'freeDeliveryThreshold'
];

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * التحقق من وجود المفاتيح في ملف الترجمة
 */
function checkKeysInTranslations(translations, language, keys) {
  console.log(`\n🔍 التحقق من المفاتيح في الملف ${language}:`);
  
  const missingKeys = [];
  const existingKeys = [];
  
  keys.forEach(key => {
    if (translations[key]) {
      existingKeys.push(key);
      console.log(`✅ ${key}: "${translations[key]}"`);
    } else {
      missingKeys.push(key);
      console.log(`❌ ${key}: مفقود`);
    }
  });
  
  return { missingKeys, existingKeys };
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧪 فحص ترجمات مكون السلة\n');
  console.log('=' .repeat(50));
  
  // قراءة ملفات الترجمة
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    process.exit(1);
  }
  
  // التحقق من المفاتيح في الملف العربي
  const arResult = checkKeysInTranslations(arTranslations, 'العربي', requiredCartKeys);
  
  // التحقق من المفاتيح في الملف الإنجليزي
  const enResult = checkKeysInTranslations(enTranslations, 'الإنجليزي', requiredCartKeys);
  
  // ملخص النتائج
  console.log('\n' + '=' .repeat(50));
  console.log('📊 ملخص النتائج:');
  console.log(`   - المفاتيح المطلوبة: ${requiredCartKeys.length}`);
  console.log(`   - موجودة في العربي: ${arResult.existingKeys.length}`);
  console.log(`   - مفقودة في العربي: ${arResult.missingKeys.length}`);
  console.log(`   - موجودة في الإنجليزي: ${enResult.existingKeys.length}`);
  console.log(`   - مفقودة في الإنجليزي: ${enResult.missingKeys.length}`);
  
  // عرض المفاتيح المفقودة
  if (arResult.missingKeys.length > 0) {
    console.log('\n❌ مفاتيح مفقودة في الملف العربي:');
    arResult.missingKeys.forEach(key => console.log(`   - ${key}`));
  }
  
  if (enResult.missingKeys.length > 0) {
    console.log('\n❌ مفاتيح مفقودة في الملف الإنجليزي:');
    enResult.missingKeys.forEach(key => console.log(`   - ${key}`));
  }
  
  // تحديد حالة النجاح
  const allKeysPresent = arResult.missingKeys.length === 0 && enResult.missingKeys.length === 0;
  
  if (allKeysPresent) {
    console.log('\n✅ جميع المفاتيح المطلوبة لمكون السلة موجودة في كلا الملفين!');
    process.exit(0);
  } else {
    console.log('\n❌ هناك مفاتيح مفقودة تحتاج إلى إضافة');
    process.exit(1);
  }
}

// تشغيل السكريبت
main();
