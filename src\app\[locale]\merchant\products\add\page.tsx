// src/app/[locale]/merchant/products/add/page.tsx
"use client";

import AddProductForm from '@/components/merchant/AddProductForm';
import { useLocale } from '@/hooks/use-locale';
import { useAuth } from '@/context/AuthContext';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { Loader2, PackagePlus } from 'lucide-react';
import { merchantPlans } from '@/constants/plans'; 
import type { SubscriptionPlan, StoreDocument } from '@/types';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShieldAlert } from 'lucide-react';

export default function AddProductPage() {
  const { t, locale } = useLocale();
  const { user, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const [maxImagesAllowed, setMaxImagesAllowed] = useState<number>(3); // Default to basic plan's limit
  const [isLoadingPlan, setIsLoadingPlan] = useState<boolean>(true);
  const [planError, setPlanError] = useState<string | null>(null);

  useEffect(() => {
    if (initialLoadingCompleted && !user) {
      router.push(`/${locale}/login?redirect=/${locale}/merchant/products/add`);
      return;
    }

    if (initialLoadingCompleted && user) {
      const fetchMerchantPlan = async () => {
        setIsLoadingPlan(true);
        setPlanError(null);
        try {
          const storeDocRef = doc(db, "stores", user.uid);
          const storeDocSnap = await getDoc(storeDocRef);

          if (storeDocSnap.exists()) {
            const storeData = storeDocSnap.data() as StoreDocument;
            const currentPlanId = storeData.planId || 'merchant-basic'; // Fallback to basic if not set
            const currentPlan = merchantPlans.find(plan => plan.id === currentPlanId);
            
            if (currentPlan && typeof currentPlan.maxProductImages === 'number') {
              setMaxImagesAllowed(currentPlan.maxProductImages);
            } else {
              // Fallback if plan or maxProductImages not found in constants
              setMaxImagesAllowed(3); 
              console.warn(`Plan or maxProductImages not found for planId: ${currentPlanId}. Defaulting to 3.`);
            }
          } else {
            // This case should ideally not happen if store doc is created on signup
            setPlanError(t('errorStoreDataNotFound')); 
            setMaxImagesAllowed(3); // Fallback
          }
        } catch (error) {
          console.error("Error fetching merchant plan:", error);
          setPlanError(t('errorFetchingPlan'));
          setMaxImagesAllowed(3); // Fallback
        } finally {
          setIsLoadingPlan(false);
        }
      };

      fetchMerchantPlan();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, initialLoadingCompleted, router, locale, t]); // Added t to dependencies for error messages

  if (!initialLoadingCompleted || isLoadingPlan) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('loadingProfile')}</p> 
      </div>
    );
  }

  if (!user) {
    // Should be caught by MerchantLayout or useEffect redirect, but as a safeguard
    return null;
  }

  if (planError) {
     return (
      <div className="container mx-auto max-w-3xl py-8">
        <Alert variant="destructive">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>{t('errorTitle')}</AlertTitle>
          <AlertDescription>{planError}</AlertDescription>
        </Alert>
      </div>
    );
  }


  return (
    <div className="container mx-auto max-w-3xl">
      <Card className="shadow-xl">
        <CardHeader className="text-center">
          <PackagePlus className="mx-auto h-12 w-12 text-primary mb-2" />
          <CardTitle className="text-3xl font-bold">{t('addNewProduct')}</CardTitle>
          <CardDescription>{t('addNewProductSubtitle')}</CardDescription>
        </CardHeader>
        <CardContent>
          {user ? (
            <AddProductForm merchantUid={user.uid} maxProductImages={maxImagesAllowed} />
          ) : (
            <p>{t('mustBeLoggedInToAccessMerchant')}</p> // Fallback, should be handled by layout/redirect
          )}
        </CardContent>
      </Card>
    </div>
  );
}
