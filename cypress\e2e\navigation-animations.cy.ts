// cypress/e2e/navigation-animations.cy.ts
describe('اختبار حركات قائمة التنقل والأيقونات الملونة', () => {
  beforeEach(() => {
    cy.visit('/ar');
    cy.wait(1000); // انتظار تحميل الصفحة
  });

  describe('اختبار الأيقونات الملونة', () => {
    it('يجب أن تظهر الأيقونات الملونة في قائمة التنقل', () => {
      // التحقق من وجود الأيقونات الملونة
      cy.get('nav').within(() => {
        cy.get('.nav-icon svg').should('exist');
        cy.get('.nav-icon svg').should('have.length.at.least', 5);
      });
    });

    it('يجب أن تحتوي الأيقونات على gradients ملونة', () => {
      cy.get('.nav-icon svg defs linearGradient').should('exist');
      cy.get('.nav-icon svg').first().within(() => {
        cy.get('defs linearGradient stop').should('have.length.at.least', 2);
      });
    });

    it('يجب أن تكون الأيقونات بالحجم المناسب', () => {
      cy.get('.nav-icon svg').should('have.attr', 'width', '20');
      cy.get('.nav-icon svg').should('have.attr', 'height', '20');
    });
  });

  describe('اختبار حركات التنقل على سطح المكتب', () => {
    beforeEach(() => {
      cy.viewport(1200, 800); // شاشة سطح المكتب
    });

    it('يجب أن تظهر حركة hover عند تمرير الماوس', () => {
      cy.get('.nav-item').first().as('navItem');
      
      // التحقق من الحالة الأولية
      cy.get('@navItem').should('have.class', 'nav-item');
      
      // تمرير الماوس وفحص التأثير
      cy.get('@navItem').trigger('mouseover');
      cy.get('@navItem').should('have.css', 'transform');
      
      // إزالة الماوس
      cy.get('@navItem').trigger('mouseout');
    });

    it('يجب أن يكون للعنصر النشط فئة active', () => {
      cy.get('.nav-item.active').should('exist');
      cy.get('.nav-item.active').should('have.class', 'active');
    });

    it('يجب أن تعمل الروابط بشكل صحيح', () => {
      cy.get('.nav-item').contains('المتاجر').click();
      cy.url().should('include', '/stores');
      
      cy.get('.nav-item').contains('الفئات').click();
      cy.url().should('include', '/categories');
    });
  });

  describe('اختبار قائمة التنقل المحمولة', () => {
    beforeEach(() => {
      cy.viewport(375, 667); // شاشة هاتف محمول
    });

    it('يجب أن تظهر قائمة التنقل السفلية على الهواتف', () => {
      cy.get('.mobile-nav-item').should('exist');
      cy.get('.mobile-nav-item').should('have.length', 5);
    });

    it('يجب أن تكون قائمة التنقل مثبتة في الأسفل', () => {
      cy.get('.mobile-nav-item').parent().parent()
        .should('have.class', 'fixed')
        .should('have.class', 'bottom-0');
    });

    it('يجب أن تحتوي كل عنصر على أيقونة ونص', () => {
      cy.get('.mobile-nav-item').each(($item) => {
        cy.wrap($item).find('.nav-icon').should('exist');
        cy.wrap($item).find('.nav-text').should('exist');
        cy.wrap($item).find('.nav-text').should('not.be.empty');
      });
    });

    it('يجب أن تعمل الروابط في القائمة المحمولة', () => {
      cy.get('.mobile-nav-item').contains('المتاجر').click();
      cy.url().should('include', '/stores');
      
      cy.get('.mobile-nav-item.active').should('exist');
    });
  });

  describe('اختبار الاستجابة والأداء', () => {
    it('يجب أن تتكيف القائمة مع أحجام الشاشات المختلفة', () => {
      // شاشة كبيرة
      cy.viewport(1920, 1080);
      cy.get('nav .nav-item').should('be.visible');
      cy.get('.mobile-nav-item').should('not.exist');
      
      // شاشة متوسطة
      cy.viewport(768, 1024);
      cy.get('nav .nav-item').should('be.visible');
      
      // شاشة صغيرة
      cy.viewport(375, 667);
      cy.get('.mobile-nav-item').should('be.visible');
    });

    it('يجب أن تكون الحركات سلسة وبدون تأخير', () => {
      cy.get('.nav-item').first().as('navItem');
      
      // قياس وقت الاستجابة
      const startTime = Date.now();
      cy.get('@navItem').trigger('mouseover');
      cy.get('@navItem').should('have.css', 'transition');
      
      cy.then(() => {
        const endTime = Date.now();
        expect(endTime - startTime).to.be.lessThan(1000); // أقل من ثانية
      });
    });
  });

  describe('اختبار إمكانية الوصول', () => {
    it('يجب أن تكون عناصر التنقل قابلة للوصول بلوحة المفاتيح', () => {
      cy.get('.nav-item').first().focus();
      cy.focused().should('have.class', 'nav-item');
      
      // التنقل بـ Tab
      cy.focused().tab();
      cy.focused().should('have.class', 'nav-item');
    });

    it('يجب أن تحتوي الروابط على نصوص وصفية', () => {
      cy.get('.nav-item').each(($item) => {
        cy.wrap($item).find('.nav-text').should('not.be.empty');
      });
    });

    it('يجب أن تعمل الحركات مع تفضيلات تقليل الحركة', () => {
      // محاكاة تفضيل تقليل الحركة
      cy.window().then((win) => {
        Object.defineProperty(win, 'matchMedia', {
          writable: true,
          value: cy.stub().returns({
            matches: true,
            media: '(prefers-reduced-motion: reduce)',
            onchange: null,
            addListener: cy.stub(),
            removeListener: cy.stub(),
            addEventListener: cy.stub(),
            removeEventListener: cy.stub(),
            dispatchEvent: cy.stub(),
          }),
        });
      });
      
      cy.reload();
      cy.get('.nav-item').should('exist');
    });
  });

  describe('اختبار التفاعل والتجربة', () => {
    it('يجب أن تظهر تأثيرات بصرية عند النقر', () => {
      cy.get('.nav-item').first().as('navItem');
      
      cy.get('@navItem').click();
      cy.get('@navItem').should('have.css', 'transform');
    });

    it('يجب أن تكون الألوان متناسقة مع ثيم مخلاة', () => {
      cy.get('.nav-icon svg defs linearGradient stop').should('exist');
      
      // التحقق من وجود ألوان الثيم
      cy.get('.nav-icon svg').should('contain.html', '#D3B594'); // Sandstone
      cy.get('.nav-icon svg').should('contain.html', '#E2725B'); // Terracotta
    });

    it('يجب أن تعمل الحركات بسلاسة على الأجهزة المختلفة', () => {
      // اختبار على أجهزة مختلفة
      const devices = [
        { width: 375, height: 667 }, // iPhone
        { width: 768, height: 1024 }, // iPad
        { width: 1200, height: 800 }, // Desktop
      ];
      
      devices.forEach((device) => {
        cy.viewport(device.width, device.height);
        cy.get('.nav-item, .mobile-nav-item').should('be.visible');
        cy.get('.nav-icon').should('be.visible');
      });
    });
  });
});
