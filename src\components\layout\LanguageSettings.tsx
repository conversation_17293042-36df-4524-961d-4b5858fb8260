"use client";

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { detectUserLanguage, saveUserLanguagePreference } from '@/hooks/use-locale';
import type { Locale } from '@/lib/i18n';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Globe, 
  Settings, 
  Smartphone, 
  Monitor, 
  Clock, 
  MapPin,
  RefreshCw,
  Check,
  Info
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LanguageSettingsProps {
  currentLocale: Locale;
  onLanguageChange?: (locale: Locale) => void;
}

interface DetectionInfo {
  browserLanguage: string;
  userLanguages: string[];
  timezone: string;
  detectedLocale: Locale;
  confidence: number;
}

export default function LanguageSettings({ 
  currentLocale, 
  onLanguageChange 
}: LanguageSettingsProps) {
  const router = useRouter();
  const pathname = usePathname();
  
  const [autoDetectionEnabled, setAutoDetectionEnabled] = useState(true);
  const [detectionInfo, setDetectionInfo] = useState<DetectionInfo | null>(null);
  const [isChanging, setIsChanging] = useState(false);

  useEffect(() => {
    // تحميل إعدادات الكشف التلقائي
    const autoDetection = localStorage.getItem('auto-language-detection') !== 'false';
    setAutoDetectionEnabled(autoDetection);

    // جمع معلومات الكشف
    if (typeof window !== 'undefined') {
      const info: DetectionInfo = {
        browserLanguage: navigator.language,
        userLanguages: navigator.languages || [navigator.language],
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        detectedLocale: detectUserLanguage(),
        confidence: calculateDetectionConfidence()
      };
      setDetectionInfo(info);
    }
  }, []);

  const calculateDetectionConfidence = (): number => {
    if (typeof window === 'undefined') return 0;

    let confidence = 0;
    const userLanguages = navigator.languages || [navigator.language];
    
    // فحص اللغات المفضلة
    const hasArabic = userLanguages.some(lang => 
      lang.toLowerCase().startsWith('ar')
    );
    if (hasArabic) confidence += 40;

    // فحص المنطقة الزمنية
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const arabicTimezones = [
      'Asia/Riyadh', 'Asia/Kuwait', 'Asia/Qatar', 'Asia/Bahrain',
      'Asia/Dubai', 'Asia/Muscat', 'Asia/Baghdad', 'Asia/Damascus',
      'Asia/Beirut', 'Asia/Amman', 'Africa/Cairo', 'Africa/Tunis',
      'Africa/Algiers', 'Africa/Casablanca'
    ];
    if (arabicTimezones.includes(timezone)) confidence += 30;

    // فحص إعدادات الأرقام
    const numberFormat = new Intl.NumberFormat().resolvedOptions();
    if (numberFormat.locale && numberFormat.locale.startsWith('ar')) {
      confidence += 20;
    }

    // فحص اللغة الأساسية للمتصفح
    if (navigator.language.toLowerCase().startsWith('ar')) {
      confidence += 10;
    }

    return Math.min(confidence, 100);
  };

  const handleLanguageChange = async (locale: Locale) => {
    setIsChanging(true);
    
    try {
      // حفظ اللغة المفضلة
      saveUserLanguagePreference(locale);
      
      // تحديث cookie
      document.cookie = `preferred-language=${locale}; path=/; max-age=31536000`; // سنة واحدة
      
      // تغيير المسار
      const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
      
      // إشعار المكون الأب
      onLanguageChange?.(locale);
      
      // التنقل للمسار الجديد
      router.push(newPathname);
      
    } catch (error) {
      console.error('خطأ في تغيير اللغة:', error);
    } finally {
      setIsChanging(false);
    }
  };

  const handleAutoDetectionToggle = (enabled: boolean) => {
    setAutoDetectionEnabled(enabled);
    localStorage.setItem('auto-language-detection', enabled.toString());
    
    if (enabled) {
      // إزالة رفض الاقتراح السابق
      localStorage.removeItem('language-suggestion-dismissed');
    }
  };

  const getLanguageName = (locale: Locale): string => {
    return locale === 'ar' ? 'العربية' : 'English';
  };

  const getLanguageFlag = (locale: Locale): string => {
    return locale === 'ar' ? '🇸🇦' : '🇺🇸';
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          إعدادات اللغة المتقدمة
        </CardTitle>
        <CardDescription>
          إدارة تفضيلات اللغة والكشف التلقائي
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* اللغة الحالية */}
        <div>
          <Label className="text-base font-medium">اللغة الحالية</Label>
          <div className="flex items-center gap-3 mt-2 p-3 bg-muted/50 rounded-lg">
            <span className="text-2xl">{getLanguageFlag(currentLocale)}</span>
            <div>
              <p className="font-medium">{getLanguageName(currentLocale)}</p>
              <p className="text-sm text-muted-foreground">اللغة النشطة حالياً</p>
            </div>
          </div>
        </div>

        <Separator />

        {/* تغيير اللغة */}
        <div>
          <Label className="text-base font-medium">تغيير اللغة</Label>
          <div className="grid grid-cols-2 gap-3 mt-2">
            <Button
              variant={currentLocale === 'ar' ? 'default' : 'outline'}
              onClick={() => handleLanguageChange('ar')}
              disabled={isChanging || currentLocale === 'ar'}
              className="justify-start"
            >
              <span className="mr-2">🇸🇦</span>
              العربية
              {currentLocale === 'ar' && <Check className="w-4 h-4 ml-auto" />}
            </Button>
            
            <Button
              variant={currentLocale === 'en' ? 'default' : 'outline'}
              onClick={() => handleLanguageChange('en')}
              disabled={isChanging || currentLocale === 'en'}
              className="justify-start"
            >
              <span className="mr-2">🇺🇸</span>
              English
              {currentLocale === 'en' && <Check className="w-4 h-4 ml-auto" />}
            </Button>
          </div>
        </div>

        <Separator />

        {/* الكشف التلقائي */}
        <div>
          <div className="flex items-center justify-between">
            <Label htmlFor="auto-detection" className="text-base font-medium">
              الكشف التلقائي للغة
            </Label>
            <Switch
              id="auto-detection"
              checked={autoDetectionEnabled}
              onCheckedChange={handleAutoDetectionToggle}
            />
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            اكتشاف لغتك المفضلة تلقائياً بناءً على إعدادات المتصفح والجهاز
          </p>
        </div>

        {/* معلومات الكشف */}
        {detectionInfo && (
          <div>
            <Label className="text-base font-medium">معلومات الكشف</Label>
            <div className="space-y-3 mt-2">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>اللغة المكتشفة:</span>
                      <Badge variant="secondary">
                        {getLanguageFlag(detectionInfo.detectedLocale)} {getLanguageName(detectionInfo.detectedLocale)}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>مستوى الثقة:</span>
                      <Badge 
                        variant="outline" 
                        className={getConfidenceColor(detectionInfo.confidence)}
                      >
                        {detectionInfo.confidence}%
                      </Badge>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-muted-foreground" />
                  <span>لغة المتصفح: {detectionInfo.browserLanguage}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span>المنطقة الزمنية: {detectionInfo.timezone}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* إعادة تعيين */}
        <div className="pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              localStorage.removeItem('preferred-language');
              localStorage.removeItem('language-suggestion-dismissed');
              localStorage.removeItem('auto-language-detection');
              window.location.reload();
            }}
            className="w-full"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            إعادة تعيين إعدادات اللغة
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
