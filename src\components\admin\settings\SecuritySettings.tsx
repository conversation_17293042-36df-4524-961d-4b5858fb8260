'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLocale } from '@/hooks/use-locale';
import { Shield, Lock, Key, AlertTriangle, QrCode, Smartphone, CheckCircle } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { useToast } from "@/hooks/use-toast";

// ===== APEX SECURITY IMPORTS =====
import { Apex2FAEngine } from '@/lib/advanced-2fa';
import { ApexAuditSystem, logUserAction } from '@/lib/audit-system';
import { auth } from '@/lib/firebase';

interface SecuritySettingsProps {
  onSettingsChange: () => void;
}

export function SecuritySettings({ onSettingsChange }: SecuritySettingsProps) {
  const { t } = useLocale();
  const { toast } = useToast();

  // ===== APEX SECURITY: حالة المصادقة الثنائية =====
  const [twoFAStatus, setTwoFAStatus] = useState({
    isEnabled: false,
    isLoading: false,
    qrCode: '',
    backupCodes: [],
    showSetup: false,
    verificationCode: ''
  });

  const [settings, setSettings] = useState({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      maxAge: 90
    },
    twoFactorAuth: {
      enabled: false,
      required: false,
      methods: {
        sms: true,
        email: true,
        app: false
      }
    },
    sessionManagement: {
      timeout: 30,
      maxSessions: 3,
      rememberMe: true
    },
    ipWhitelist: {
      enabled: false,
      addresses: ['***********', '********']
    },
    rateLimiting: {
      enabled: true,
      loginAttempts: 5,
      timeWindow: 15,
      apiRequests: 100
    }
  });

  const handleSettingChange = (category: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [field]: value
      }
    }));
    onSettingsChange();
  };

  const handleNestedSettingChange = (category: string, subcategory: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [subcategory]: {
          ...(prev[category as keyof typeof prev] as any)[subcategory],
          [field]: value
        }
      }
    }));
    onSettingsChange();
  };

  // ===== APEX SECURITY: دوال المصادقة الثنائية =====

  const setup2FA = async () => {
    if (!auth.currentUser) {
      toast({
        title: "خطأ",
        description: "يجب تسجيل الدخول أولاً",
        variant: "destructive"
      });
      return;
    }

    setTwoFAStatus(prev => ({ ...prev, isLoading: true }));

    try {
      console.log('🔐 بدء إعداد المصادقة الثنائية للمدير...');

      const setup = await Apex2FAEngine.setup2FA(auth.currentUser.uid);

      setTwoFAStatus(prev => ({
        ...prev,
        qrCode: setup.qrCode,
        backupCodes: setup.backupCodes,
        showSetup: true,
        isLoading: false
      }));

      // تسجيل بداية إعداد 2FA
      await logUserAction('admin_2fa_setup_start', auth.currentUser.uid, {
        timestamp: new Date()
      });

      toast({
        title: "تم إنشاء إعدادات المصادقة الثنائية",
        description: "امسح رمز QR باستخدام تطبيق Google Authenticator"
      });

    } catch (error) {
      console.error('خطأ في إعداد 2FA:', error);
      setTwoFAStatus(prev => ({ ...prev, isLoading: false }));
      toast({
        title: "خطأ في الإعداد",
        description: "فشل في إعداد المصادقة الثنائية",
        variant: "destructive"
      });
    }
  };

  const enable2FA = async () => {
    if (!auth.currentUser || !twoFAStatus.verificationCode) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال رمز التحقق",
        variant: "destructive"
      });
      return;
    }

    setTwoFAStatus(prev => ({ ...prev, isLoading: true }));

    try {
      const success = await Apex2FAEngine.enable2FA(
        auth.currentUser.uid,
        twoFAStatus.verificationCode
      );

      if (success) {
        setTwoFAStatus(prev => ({
          ...prev,
          isEnabled: true,
          showSetup: false,
          isLoading: false
        }));

        // تحديث إعدادات المصادقة الثنائية
        handleSettingChange('twoFactorAuth', 'enabled', true);
        handleNestedSettingChange('twoFactorAuth', 'methods', 'app', true);

        toast({
          title: "تم تفعيل المصادقة الثنائية",
          description: "تم تفعيل المصادقة الثنائية بنجاح للحساب الإداري"
        });

        // تسجيل نجاح التفعيل
        await logUserAction('admin_2fa_enabled', auth.currentUser.uid, {
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('خطأ في تفعيل 2FA:', error);
      setTwoFAStatus(prev => ({ ...prev, isLoading: false }));
      toast({
        title: "خطأ في التفعيل",
        description: "رمز التحقق غير صحيح",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* سياسة كلمة المرور */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            سياسة كلمة المرور
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الحد الأدنى لطول كلمة المرور</Label>
              <Input
                type="number"
                value={settings.passwordPolicy.minLength}
                onChange={(e) => handleSettingChange('passwordPolicy', 'minLength', parseInt(e.target.value))}
                min="6"
                max="32"
              />
            </div>

            <div className="space-y-2">
              <Label>انتهاء صلاحية كلمة المرور (أيام)</Label>
              <Input
                type="number"
                value={settings.passwordPolicy.maxAge}
                onChange={(e) => handleSettingChange('passwordPolicy', 'maxAge', parseInt(e.target.value))}
                min="30"
                max="365"
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>يجب أن تحتوي على أحرف كبيرة</Label>
              <Switch
                checked={settings.passwordPolicy.requireUppercase}
                onCheckedChange={(checked) => handleSettingChange('passwordPolicy', 'requireUppercase', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>يجب أن تحتوي على أحرف صغيرة</Label>
              <Switch
                checked={settings.passwordPolicy.requireLowercase}
                onCheckedChange={(checked) => handleSettingChange('passwordPolicy', 'requireLowercase', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>يجب أن تحتوي على أرقام</Label>
              <Switch
                checked={settings.passwordPolicy.requireNumbers}
                onCheckedChange={(checked) => handleSettingChange('passwordPolicy', 'requireNumbers', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>يجب أن تحتوي على رموز خاصة</Label>
              <Switch
                checked={settings.passwordPolicy.requireSpecialChars}
                onCheckedChange={(checked) => handleSettingChange('passwordPolicy', 'requireSpecialChars', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* المصادقة الثنائية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            المصادقة الثنائية (2FA) - APEX SECURITY
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* حالة المصادقة الثنائية */}
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2">
              {twoFAStatus.isEnabled ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              )}
              <span className="font-medium">
                {twoFAStatus.isEnabled ? 'المصادقة الثنائية مفعلة' : 'المصادقة الثنائية غير مفعلة'}
              </span>
            </div>
            {!twoFAStatus.isEnabled && !twoFAStatus.showSetup && (
              <Button
                onClick={setup2FA}
                disabled={twoFAStatus.isLoading}
                className="flex items-center gap-2"
              >
                <Smartphone className="h-4 w-4" />
                إعداد المصادقة الثنائية
              </Button>
            )}
          </div>

          {/* واجهة إعداد المصادقة الثنائية */}
          {twoFAStatus.showSetup && (
            <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
              <h4 className="font-semibold flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                إعداد المصادقة الثنائية
              </h4>

              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  1. قم بتحميل تطبيق Google Authenticator على هاتفك
                </p>
                <p className="text-sm text-muted-foreground">
                  2. امسح رمز QR التالي باستخدام التطبيق
                </p>

                {twoFAStatus.qrCode && (
                  <div className="flex justify-center p-4 bg-white rounded border">
                    <img
                      src={twoFAStatus.qrCode}
                      alt="QR Code for 2FA"
                      className="w-48 h-48"
                    />
                  </div>
                )}

                <p className="text-sm text-muted-foreground">
                  3. أدخل الرمز المكون من 6 أرقام من التطبيق
                </p>

                <div className="flex gap-2">
                  <Input
                    placeholder="123456"
                    value={twoFAStatus.verificationCode}
                    onChange={(e) => setTwoFAStatus(prev => ({
                      ...prev,
                      verificationCode: e.target.value
                    }))}
                    maxLength={6}
                    className="text-center text-lg tracking-widest"
                  />
                  <Button
                    onClick={enable2FA}
                    disabled={twoFAStatus.isLoading || twoFAStatus.verificationCode.length !== 6}
                  >
                    تفعيل
                  </Button>
                </div>

                {/* رموز النسخ الاحتياطية */}
                {twoFAStatus.backupCodes.length > 0 && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <h5 className="font-medium text-yellow-800 mb-2">
                      رموز النسخ الاحتياطية (احفظها في مكان آمن):
                    </h5>
                    <div className="grid grid-cols-2 gap-1 text-sm font-mono">
                      {twoFAStatus.backupCodes.map((code, index) => (
                        <span key={index} className="text-yellow-700">
                          {code}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <Label>تفعيل المصادقة الثنائية</Label>
            <Switch
              checked={settings.twoFactorAuth.enabled || twoFAStatus.isEnabled}
              onCheckedChange={(checked) => {
                if (!checked && twoFAStatus.isEnabled) {
                  // منع إلغاء التفعيل مباشرة - يجب استخدام إعدادات متقدمة
                  toast({
                    title: "تحذير أمني",
                    description: "لا يمكن إلغاء المصادقة الثنائية مباشرة لأسباب أمنية",
                    variant: "destructive"
                  });
                  return;
                }
                handleSettingChange('twoFactorAuth', 'enabled', checked);
              }}
            />
          </div>

          {settings.twoFactorAuth.enabled && (
            <div className="space-y-4 pl-4 border-l-2 border-gray-200">
              <div className="flex items-center justify-between">
                <Label>إجبارية للمدراء</Label>
                <Switch
                  checked={settings.twoFactorAuth.required}
                  onCheckedChange={(checked) => handleSettingChange('twoFactorAuth', 'required', checked)}
                />
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium">طرق المصادقة المتاحة:</Label>
                
                <div className="flex items-center justify-between">
                  <Label>رسائل SMS</Label>
                  <Switch
                    checked={settings.twoFactorAuth.methods.sms}
                    onCheckedChange={(checked) => handleNestedSettingChange('twoFactorAuth', 'methods', 'sms', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>البريد الإلكتروني</Label>
                  <Switch
                    checked={settings.twoFactorAuth.methods.email}
                    onCheckedChange={(checked) => handleNestedSettingChange('twoFactorAuth', 'methods', 'email', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>تطبيق المصادقة</Label>
                  <Switch
                    checked={settings.twoFactorAuth.methods.app}
                    onCheckedChange={(checked) => handleNestedSettingChange('twoFactorAuth', 'methods', 'app', checked)}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* إدارة الجلسات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            إدارة الجلسات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>انتهاء الجلسة (دقيقة)</Label>
              <Input
                type="number"
                value={settings.sessionManagement.timeout}
                onChange={(e) => handleSettingChange('sessionManagement', 'timeout', parseInt(e.target.value))}
                min="5"
                max="480"
              />
            </div>

            <div className="space-y-2">
              <Label>الحد الأقصى للجلسات المتزامنة</Label>
              <Input
                type="number"
                value={settings.sessionManagement.maxSessions}
                onChange={(e) => handleSettingChange('sessionManagement', 'maxSessions', parseInt(e.target.value))}
                min="1"
                max="10"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Label>السماح بـ "تذكرني"</Label>
            <Switch
              checked={settings.sessionManagement.rememberMe}
              onCheckedChange={(checked) => handleSettingChange('sessionManagement', 'rememberMe', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* تحديد المعدل */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            تحديد المعدل والحماية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>تفعيل تحديد المعدل</Label>
            <Switch
              checked={settings.rateLimiting.enabled}
              onCheckedChange={(checked) => handleSettingChange('rateLimiting', 'enabled', checked)}
            />
          </div>

          {settings.rateLimiting.enabled && (
            <div className="space-y-4 pl-4 border-l-2 border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>محاولات تسجيل الدخول</Label>
                  <Input
                    type="number"
                    value={settings.rateLimiting.loginAttempts}
                    onChange={(e) => handleSettingChange('rateLimiting', 'loginAttempts', parseInt(e.target.value))}
                    min="3"
                    max="10"
                  />
                </div>

                <div className="space-y-2">
                  <Label>نافذة الوقت (دقيقة)</Label>
                  <Input
                    type="number"
                    value={settings.rateLimiting.timeWindow}
                    onChange={(e) => handleSettingChange('rateLimiting', 'timeWindow', parseInt(e.target.value))}
                    min="5"
                    max="60"
                  />
                </div>

                <div className="space-y-2">
                  <Label>طلبات API (في الدقيقة)</Label>
                  <Input
                    type="number"
                    value={settings.rateLimiting.apiRequests}
                    onChange={(e) => handleSettingChange('rateLimiting', 'apiRequests', parseInt(e.target.value))}
                    min="10"
                    max="1000"
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* قائمة IP المسموحة */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة IP المسموحة</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>تفعيل قائمة IP المسموحة</Label>
            <Switch
              checked={settings.ipWhitelist.enabled}
              onCheckedChange={(checked) => handleSettingChange('ipWhitelist', 'enabled', checked)}
            />
          </div>

          {settings.ipWhitelist.enabled && (
            <div className="space-y-3 pl-4 border-l-2 border-gray-200">
              <Label>عناوين IP المسموحة:</Label>
              {settings.ipWhitelist.addresses.map((ip, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    value={ip}
                    onChange={(e) => {
                      const newAddresses = [...settings.ipWhitelist.addresses];
                      newAddresses[index] = e.target.value;
                      handleSettingChange('ipWhitelist', 'addresses', newAddresses);
                    }}
                    placeholder="***********"
                  />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
