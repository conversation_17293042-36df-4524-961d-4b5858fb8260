'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // تسجيل الخطأ (يمكن إرساله لخدمة مراقبة الأخطاء)
    console.error('🚨 Global error:', error);
  }, [error]);

  const isStreamError = error.message?.includes('Stream') || 
                       error.message?.includes('pipe') ||
                       error.digest?.includes('2710195599');

  const isNetworkError = error.message?.includes('network') ||
                        error.message?.includes('timeout') ||
                        error.message?.includes('offline');

  return (
    <html>
      <body>
        <div className="flex flex-col items-center justify-center min-h-screen p-8 text-center bg-background">
          <div className="mb-6">
            <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
          </div>

          <h1 className="text-3xl font-bold mb-4">
            {isStreamError ? 'مشكلة مؤقتة في الخدمة' :
             isNetworkError ? 'مشكلة في الاتصال' :
             'حدث خطأ غير متوقع'}
          </h1>

          <p className="text-muted-foreground mb-6 max-w-md">
            {isStreamError ? 
              'حدثت مشكلة مؤقتة في الخدمة. يرجى إعادة تحميل الصفحة.' :
             isNetworkError ?
              'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.' :
              'نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة للصفحة الرئيسية.'
            }
          </p>

          {process.env.NODE_ENV === 'development' && (
            <details className="mb-6 p-4 bg-muted rounded-md text-left max-w-lg">
              <summary className="cursor-pointer font-medium mb-2">
                تفاصيل الخطأ (بيئة التطوير)
              </summary>
              <pre className="text-xs overflow-auto">
                {error.message}
                {error.digest && `\nDigest: ${error.digest}`}
              </pre>
            </details>
          )}

          <div className="flex gap-4">
            <Button 
              onClick={reset}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              إعادة المحاولة
            </Button>

            <Button 
              variant="outline"
              onClick={() => window.location.href = '/ar'}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              العودة للرئيسية
            </Button>

            <Button 
              variant="outline"
              onClick={() => window.location.reload()}
            >
              إعادة تحميل الصفحة
            </Button>
          </div>

          <p className="text-xs text-muted-foreground mt-8">
            إذا استمرت المشكلة، يرجى المحاولة لاحقاً أو الاتصال بالدعم الفني.
          </p>
        </div>
      </body>
    </html>
  );
}
