// src/components/admin/AdminDashboard.tsx
"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useAdminStats, useRealtimeData } from '@/hooks/useAdminStats';
import { useLocale } from '@/hooks/use-locale';
import { 
  StatsCard, 
  FinancialStatsCard, 
  PercentageStatsCard, 
  StatsGrid 
} from './StatsCard';
import { RealtimeChart } from './RealtimeChart';
import { ActivityFeed } from './ActivityFeed';
import { AlertsPanel, SystemStatus } from './AlertsPanel';
import { TopPerformers } from './TopPerformers';
import { GeographicMap } from './GeographicMap';
import {
  Users,
  Store,
  Package,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  Truck,
  Eye,
  UserCheck,
  RefreshCw,
  Download,
  Calendar,
  Clock,
  Shield,
  Activity
} from 'lucide-react';

// ===== APEX SECURITY IMPORTS =====
import { ApexSessionManager, SecurityLevel } from '@/lib/session-manager';
import { ApexAuditSystem, logUserAction } from '@/lib/audit-system';
import { auth } from '@/lib/firebase';
import { useToast } from "@/hooks/use-toast";

interface AdminDashboardProps {
  className?: string;
}

export function AdminDashboard({ className }: AdminDashboardProps) {
  const { t } = useLocale();
  const { toast } = useToast();
  const { stats, recentActivity, systemAlerts, loading, error, refetch } = useAdminStats();
  const { isConnected, lastUpdate } = useRealtimeData();
  const [refreshing, setRefreshing] = useState(false);

  // ===== APEX SECURITY: حالة إدارة الجلسات =====
  const [sessionInfo, setSessionInfo] = useState({
    currentSession: null,
    activeSessions: [],
    securityLevel: SecurityLevel.STANDARD,
    isLoading: false
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setTimeout(() => setRefreshing(false), 1000);
  };

  // ===== APEX SECURITY: دوال إدارة الجلسات =====

  const initializeAdminSession = async () => {
    if (!auth.currentUser) return;

    setSessionInfo(prev => ({ ...prev, isLoading: true }));

    try {
      console.log('🔐 تهيئة جلسة إدارية آمنة...');

      // إنشاء جلسة إدارية بمستوى أمان عالي
      const session = await ApexSessionManager.createSession(
        auth.currentUser.uid,
        SecurityLevel.ADMIN,
        {
          userAgent: navigator.userAgent,
          ip: 'admin-dashboard',
          location: {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language
          }
        }
      );

      // جلب الجلسات النشطة
      const activeSessions = await ApexSessionManager.getUserSessions(auth.currentUser.uid);

      setSessionInfo({
        currentSession: session,
        activeSessions,
        securityLevel: SecurityLevel.ADMIN,
        isLoading: false
      });

      // تسجيل دخول الإدارة
      await logUserAction('admin_dashboard_access', auth.currentUser.uid, {
        sessionId: session.sessionId,
        securityLevel: SecurityLevel.ADMIN,
        timestamp: new Date()
      });

      console.log('✅ تم تهيئة الجلسة الإدارية بنجاح');

    } catch (error) {
      console.error('خطأ في تهيئة الجلسة الإدارية:', error);
      setSessionInfo(prev => ({ ...prev, isLoading: false }));

      toast({
        title: "تحذير أمني",
        description: "فشل في تهيئة الجلسة الآمنة",
        variant: "destructive"
      });
    }
  };

  const terminateSession = async (sessionId: string) => {
    try {
      await ApexSessionManager.terminateSession(sessionId);

      // تحديث قائمة الجلسات
      if (auth.currentUser) {
        const activeSessions = await ApexSessionManager.getUserSessions(auth.currentUser.uid);
        setSessionInfo(prev => ({ ...prev, activeSessions }));
      }

      toast({
        title: "تم إنهاء الجلسة",
        description: "تم إنهاء الجلسة بنجاح"
      });

    } catch (error) {
      console.error('خطأ في إنهاء الجلسة:', error);
      toast({
        title: "خطأ",
        description: "فشل في إنهاء الجلسة",
        variant: "destructive"
      });
    }
  };

  // ===== APEX SECURITY: تهيئة الجلسة الإدارية =====
  React.useEffect(() => {
    if (auth.currentUser) {
      initializeAdminSession();
    }
  }, [auth.currentUser]);

  const formatLastUpdate = (date: Date | null) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(date);
  };

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="text-center p-6">
            <div className="text-red-500 mb-4">
              <TrendingUp className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {t('errorLoadingData')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {error}
            </p>
            <Button onClick={refetch} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('retryLoading')}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('adminDashboard')}</h1>
          <p className="text-muted-foreground mt-1">
            {t('adminDashboardSubtitle')}
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* حالة الاتصال المباشر */}
          <div className="flex items-center gap-2">
            <div className={`h-2 w-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-muted-foreground">
              {isConnected ? t('realTimeData') : 'غير متصل'}
            </span>
            {lastUpdate && (
              <span className="text-xs text-muted-foreground">
                {formatLastUpdate(lastUpdate)}
              </span>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t('refreshData')}
          </Button>

          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            {t('exportReport')}
          </Button>
        </div>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div>
        <h2 className="text-xl font-semibold mb-4">{t('platformOverview')}</h2>
        <StatsGrid columns={4}>
          <StatsCard
            title={t('totalUsers')}
            value={stats?.totalUsers || 0}
            icon={<Users className="h-4 w-4" />}
            description={`${stats?.activeUsers || 0} ${t('activeUsers').toLowerCase()}`}
            trend={{
              value: 12,
              isPositive: true,
              period: 'هذا الشهر'
            }}
            loading={loading}
          />

          <StatsCard
            title={t('totalStores')}
            value={stats?.totalStores || 0}
            icon={<Store className="h-4 w-4" />}
            description={`${stats?.activeStores || 0} ${t('activeStores').toLowerCase()}`}
            trend={{
              value: 8,
              isPositive: true,
              period: 'هذا الشهر'
            }}
            loading={loading}
          />

          <StatsCard
            title={t('totalProducts')}
            value={stats?.totalProducts || 0}
            icon={<Package className="h-4 w-4" />}
            description={`${stats?.activeProducts || 0} ${t('activeProducts').toLowerCase()}`}
            trend={{
              value: 15,
              isPositive: true,
              period: 'هذا الشهر'
            }}
            loading={loading}
          />

          <StatsCard
            title={t('totalOrders')}
            value={stats?.totalOrders || 0}
            icon={<ShoppingCart className="h-4 w-4" />}
            description={`${stats?.todayOrders || 0} ${t('todayOrders').toLowerCase()}`}
            trend={{
              value: 5,
              isPositive: true,
              period: 'اليوم'
            }}
            loading={loading}
          />
        </StatsGrid>
      </div>

      {/* ===== APEX SECURITY: بطاقة الأمان والجلسات ===== */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600" />
          الأمان والجلسات النشطة
        </h2>
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* الجلسة الحالية */}
              <div className="p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 mb-3">
                  <Activity className="h-4 w-4 text-green-500" />
                  <span className="font-medium text-sm">الجلسة الحالية</span>
                </div>
                {sessionInfo.currentSession ? (
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <p><span className="font-medium">المعرف:</span> {sessionInfo.currentSession.sessionId.substring(0, 8)}...</p>
                    <p><span className="font-medium">المستوى:</span> {sessionInfo.securityLevel}</p>
                    <p><span className="font-medium">بدأت:</span> {new Date(sessionInfo.currentSession.createdAt).toLocaleTimeString('ar-SA')}</p>
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">لا توجد جلسة نشطة</p>
                )}
              </div>

              {/* الجلسات النشطة */}
              <div className="p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 mb-3">
                  <Users className="h-4 w-4 text-blue-500" />
                  <span className="font-medium text-sm">الجلسات النشطة</span>
                </div>
                <p className="text-2xl font-bold text-blue-600">
                  {sessionInfo.activeSessions.length}
                </p>
                <p className="text-xs text-muted-foreground">جلسة نشطة</p>
              </div>

              {/* مستوى الأمان */}
              <div className="p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 mb-3">
                  <Shield className="h-4 w-4 text-red-500" />
                  <span className="font-medium text-sm">مستوى الأمان</span>
                </div>
                <Badge variant={sessionInfo.securityLevel === SecurityLevel.ADMIN ? "destructive" : "secondary"} className="text-sm">
                  {sessionInfo.securityLevel}
                </Badge>
                <p className="text-xs text-muted-foreground mt-1">مستوى إداري عالي</p>
              </div>
            </div>

            {/* قائمة الجلسات النشطة */}
            {sessionInfo.activeSessions.length > 0 && (
              <div className="mt-6">
                <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  إدارة الجلسات النشطة:
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {sessionInfo.activeSessions.slice(0, 5).map((session: any) => (
                    <div key={session.sessionId} className="flex items-center justify-between p-3 bg-white rounded border text-sm">
                      <div className="flex-1">
                        <span className="font-mono text-xs">{session.sessionId.substring(0, 16)}...</span>
                        <div className="text-xs text-muted-foreground mt-1">
                          <span>بدأت: {new Date(session.createdAt).toLocaleTimeString('ar-SA')}</span>
                          <span className="mx-2">•</span>
                          <span>المستوى: {session.securityLevel}</span>
                        </div>
                      </div>
                      {session.sessionId !== sessionInfo.currentSession?.sessionId && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => terminateSession(session.sessionId)}
                          className="h-8 px-3 text-xs"
                        >
                          إنهاء
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* الإحصائيات المالية */}
      <div>
        <h2 className="text-xl font-semibold mb-4">الإحصائيات المالية</h2>
        <StatsGrid columns={3}>
          <FinancialStatsCard
            title={t('totalRevenue')}
            amount={stats?.totalRevenue || 0}
            icon={<DollarSign className="h-4 w-4" />}
            trend={{
              value: 18,
              isPositive: true,
              period: 'هذا الشهر'
            }}
            loading={loading}
          />

          <FinancialStatsCard
            title={t('monthlyRevenue')}
            amount={stats?.monthlyRevenue || 0}
            icon={<TrendingUp className="h-4 w-4" />}
            trend={{
              value: 22,
              isPositive: true,
              period: 'مقارنة بالشهر الماضي'
            }}
            loading={loading}
          />

          <FinancialStatsCard
            title={t('totalCommissions')}
            amount={stats?.totalCommissions || 0}
            icon={<DollarSign className="h-4 w-4" />}
            trend={{
              value: 10,
              isPositive: true,
              period: 'هذا الشهر'
            }}
            loading={loading}
          />
        </StatsGrid>
      </div>

      {/* مقاييس الأداء */}
      <div>
        <h2 className="text-xl font-semibold mb-4">{t('performanceMetrics')}</h2>
        <StatsGrid columns={4}>
          <PercentageStatsCard
            title={t('conversionRate')}
            percentage={stats?.conversionRate || 0}
            icon={<TrendingUp className="h-4 w-4" />}
            target={5}
            loading={loading}
          />

          <PercentageStatsCard
            title={t('customerRetentionRate')}
            percentage={stats?.customerRetentionRate || 0}
            icon={<UserCheck className="h-4 w-4" />}
            target={80}
            loading={loading}
          />

          <PercentageStatsCard
            title={t('growthRate')}
            percentage={stats?.growthRate || 0}
            icon={<TrendingUp className="h-4 w-4" />}
            target={10}
            loading={loading}
          />

          <StatsCard
            title={t('averageOrderValue')}
            value={new Intl.NumberFormat('ar-SA', {
              style: 'currency',
              currency: 'SAR',
            }).format(stats?.averageOrderValue || 0)}
            icon={<ShoppingCart className="h-4 w-4" />}
            description="متوسط قيمة الطلب"
            loading={loading}
          />
        </StatsGrid>
      </div>

      {/* الرسوم البيانية والتحليلات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RealtimeChart
          title={t('salesChart')}
          dataKey="sales"
          type="area"
          height={300}
        />

        <RealtimeChart
          title={t('ordersChart')}
          dataKey="orders"
          type="bar"
          height={300}
        />
      </div>

      {/* النشاط والتنبيهات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ActivityFeed
          activities={recentActivity}
          loading={loading}
          maxHeight={400}
          onRefresh={refetch}
        />

        <AlertsPanel
          maxHeight={400}
        />
      </div>

      {/* أفضل المؤدين والتوزيع الجغرافي */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TopPerformers />
        
        <GeographicMap
          height={400}
        />
      </div>

      {/* حالة النظام */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <SystemStatus />
        
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Clock className="h-5 w-5" />
              آخر التحديثات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="text-sm">آخر تحديث للبيانات</span>
                <Badge variant="outline">
                  {lastUpdate ? formatLastUpdate(lastUpdate) : 'غير متاح'}
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="text-sm">حالة النظام</span>
                <Badge variant="outline" className="text-green-600">
                  <div className="h-2 w-2 bg-green-500 rounded-full mr-2" />
                  يعمل بشكل طبيعي
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <span className="text-sm">آخر نسخة احتياطية</span>
                <Badge variant="outline">
                  منذ ساعتين
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
