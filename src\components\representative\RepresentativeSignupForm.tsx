'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useLocale } from '@/hooks/use-locale';
import { auth, db } from '@/lib/firebase';
import { createUserWithEmailAndPassword, updateProfile, fetchSignInMethodsForEmail } from 'firebase/auth';
import { doc, setDoc, serverTimestamp, type Timestamp, collection, query, where, getDocs } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { Loader2, Upload, User, Car, FileText, Shield } from 'lucide-react';
import type { RepresentativeSignupData, VehicleType, NationalIdType } from '@/types/representative';
import { representativePlans } from '@/constants/plans';
import TermsAndConditionsModal from '@/components/auth/TermsAndConditionsModal';

// ===== APEX SECURITY IMPORTS =====
import { DocumentEncryptionService, ApexEncryptionEngine } from '@/lib/encryption';
import { ApexAuditSystem, logUserAction, logDataAccess } from '@/lib/audit-system';

// ===== APEX SECURITY: دالة رفع آمنة مع التشفير =====
async function uploadFileSecurely(file: File, documentType: string, userId?: string): Promise<string> {
  try {
    console.log(`🔐 بدء رفع آمن للمستند: ${documentType}`);

    // 1. تشفير المستند قبل الرفع
    const encryptedPayload = await DocumentEncryptionService.encryptDocument(file);

    // 2. تسجيل عملية التشفير للمراجعة
    if (userId) {
      await logDataAccess('document_encryption', userId, {
        documentType,
        fileName: file.name,
        fileSize: file.size,
        encryptionAlgorithm: 'AES-256-GCM',
        timestamp: new Date()
      });
    }

    // 3. تحويل البيانات المشفرة إلى ملف للرفع
    const encryptedBlob = new Blob([JSON.stringify(encryptedPayload)], {
      type: 'application/json'
    });
    const encryptedFile = new File([encryptedBlob], `encrypted_${file.name}.json`, {
      type: 'application/json'
    });

    // 4. رفع الملف المشفر إلى Cloudinary
    const formData = new FormData();
    formData.append('file', encryptedFile);
    const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET;
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;

    if (!uploadPreset || !cloudName) {
      console.error('Cloudinary environment variables are not set.');
      throw new Error('Cloudinary configuration error.');
    }

    formData.append('upload_preset', uploadPreset);

    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/raw/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload encrypted file to Cloudinary');
    }

    const data = await response.json();

    // 5. تسجيل نجاح الرفع
    if (userId) {
      await logUserAction('document_upload_success', userId, {
        documentType,
        fileName: file.name,
        encryptedUrl: data.secure_url,
        uploadTimestamp: new Date()
      });
    }

    console.log(`✅ تم رفع المستند المشفر بنجاح: ${documentType}`);
    return data.secure_url;

  } catch (error) {
    console.error(`🔴 خطأ في رفع المستند المشفر:`, error);

    // تسجيل الخطأ للمراجعة
    if (userId) {
      await logUserAction('document_upload_failed', userId, {
        documentType,
        fileName: file.name,
        error: error.message,
        timestamp: new Date()
      });
    }

    throw new Error(`فشل في رفع المستند المشفر: ${documentType}`);
  }
}

// دالة احتياطية للرفع العادي (للملفات غير الحساسة)
async function uploadFileToCloudinary(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET;
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;

  if (!uploadPreset || !cloudName) {
    console.error('Cloudinary environment variables are not set.');
    throw new Error('Cloudinary configuration error.');
  }

  formData.append('upload_preset', uploadPreset);

  const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error('Failed to upload file to Cloudinary');
  }

  const data = await response.json();
  return data.secure_url;
}

export default function RepresentativeSignupForm() {
  const { t, locale } = useLocale();
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);

  // Form data state
  const [formData, setFormData] = useState<RepresentativeSignupData>({
    // Personal Information
    displayName: '',
    phoneNumber: '',
    email: '',
    
    // Identity Information
    nationalId: '',
    nationalIdType: 'national',
    
    // Driving License
    drivingLicenseNumber: '',
    drivingLicenseIssueDate: new Date(),
    drivingLicenseExpiryDate: new Date(),
    drivingLicenseImage: null as any,
    
    // Vehicle Information
    vehicleType: 'car',
    vehicleModel: '',
    vehicleYear: new Date().getFullYear(),
    vehiclePlateNumber: '',
    vehicleColor: '',
    vehicleImage: undefined,
    
    // Vehicle Inspection
    inspectionCertificateNumber: '',
    inspectionIssueDate: new Date(),
    inspectionExpiryDate: new Date(),
    inspectionCertificateImage: null as any,
    
    // Profile Image
    profileImage: undefined,
    
    // Terms Agreement
    agreeToTerms: false,
  });

  const [filePreview, setFilePreview] = useState<{
    drivingLicense?: string;
    vehicleImage?: string;
    inspectionCertificate?: string;
    profileImage?: string;
  }>({});

  const handleInputChange = (field: keyof RepresentativeSignupData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileChange = (field: keyof RepresentativeSignupData, file: File | undefined) => {
    if (file) {
      setFormData(prev => ({
        ...prev,
        [field]: file
      }));

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setFilePreview(prev => ({
        ...prev,
        [field.replace('Image', '').replace('drivingLicense', 'drivingLicense')]: previewUrl
      }));
    }
  };

  // دالة للتحقق من وجود الإيميل مسبقاً
  const checkEmailExists = async (emailToCheck: string): Promise<boolean> => {
    try {
      setIsCheckingEmail(true);
      const signInMethods = await fetchSignInMethodsForEmail(auth, emailToCheck);
      return signInMethods.length > 0;
    } catch (error: any) {
      console.error('Error checking email:', error);
      // في حالة وجود خطأ، نعتبر أن الإيميل غير موجود لتجنب منع المستخدم من المتابعة
      return false;
    } finally {
      setIsCheckingEmail(false);
    }
  };

  // دالة للتحقق من وجود اسم المستخدم مسبقاً
  const checkUsernameExists = async (usernameToCheck: string): Promise<boolean> => {
    try {
      setIsCheckingUsername(true);

      // التحقق من صحة اسم المستخدم أولاً
      if (!usernameToCheck.trim() || usernameToCheck.length < 3) {
        return false;
      }

      // البحث في مجموعة المستخدمين عن اسم المستخدم
      const usersRef = collection(db, 'users');
      const usernameQuery = query(
        usersRef,
        where('displayName', '==', usernameToCheck.trim())
      );

      const querySnapshot = await getDocs(usernameQuery);
      return !querySnapshot.empty;
    } catch (error: any) {
      console.error('Error checking username:', error);

      // في حالة وجود خطأ، نسمح بالمتابعة لتجنب منع المستخدم
      console.warn('Username check failed, allowing signup to proceed:', error.message);
      return false;
    } finally {
      setIsCheckingUsername(false);
    }
  };

  const validateStep = async (step: number): Promise<boolean> => {
    switch (step) {
      case 1: // Personal Information
        if (!formData.displayName || !formData.phoneNumber || !formData.email) {
          return false;
        }

        // التحقق من وجود اسم المستخدم مسبقاً
        if (!isCheckingUsername) {
          const usernameExists = await checkUsernameExists(formData.displayName);
          if (usernameExists) {
            toast({
              title: t('error'),
              description: t('usernameAlreadyInUse'),
              variant: 'destructive',
            });
            return false;
          }
        }

        // التحقق من وجود الإيميل مسبقاً
        const emailExists = await checkEmailExists(formData.email);
        if (emailExists) {
          toast({
            title: t('error'),
            description: t('emailAlreadyInUse'),
            variant: 'destructive',
          });
          return false;
        }

        return true;
      case 2: // Identity Information
        return !!(formData.nationalId && formData.nationalIdType);
      case 3: // Driving License
        return !!(
          formData.drivingLicenseNumber &&
          formData.drivingLicenseImage &&
          formData.drivingLicenseIssueDate &&
          formData.drivingLicenseExpiryDate
        );
      case 4: // Vehicle Information
        return !!(
          formData.vehicleType &&
          formData.vehicleModel &&
          formData.vehicleYear &&
          formData.vehiclePlateNumber &&
          formData.vehicleColor &&
          formData.inspectionCertificateNumber &&
          formData.inspectionCertificateImage
        );
      case 5: // Terms Agreement
        return formData.agreeToTerms;
      default:
        return false;
    }
  };

  const nextStep = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      toast({
        title: t('error'),
        description: t('pleaseCompleteAllRequiredFields'),
        variant: 'destructive',
      });
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = await validateStep(5);
    if (!isValid) {
      toast({
        title: t('error'),
        description: t('pleaseCompleteAllRequiredFields'),
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Create Firebase Auth user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        formData.email,
        'temp_password_' + Date.now() // Temporary password, user will reset it
      );
      
      const firebaseUser = userCredential.user;

      // ===== APEX SECURITY: رفع آمن للمستندات الحساسة =====
      console.log('🔐 بدء رفع المستندات بشكل آمن...');

      // تسجيل بداية عملية التسجيل
      await logUserAction('representative_signup_start', firebaseUser.uid, {
        email: formData.email.substring(0, 3) + '***',
        displayName: formData.displayName,
        phoneNumber: formData.phoneNumber.substring(0, 3) + '***',
        timestamp: new Date()
      });

      const [
        drivingLicenseUrl,
        inspectionCertificateUrl,
        vehicleImageUrl,
        profileImageUrl
      ] = await Promise.all([
        // المستندات الحساسة - رفع مشفر
        uploadFileSecurely(formData.drivingLicenseImage, 'driving_license', firebaseUser.uid),
        uploadFileSecurely(formData.inspectionCertificateImage, 'inspection_certificate', firebaseUser.uid),
        // الصور العادية - رفع عادي
        formData.vehicleImage ? uploadFileToCloudinary(formData.vehicleImage) : Promise.resolve(undefined),
        formData.profileImage ? uploadFileToCloudinary(formData.profileImage) : Promise.resolve(undefined),
      ]);

      console.log('✅ تم رفع جميع المستندات بنجاح');

      // Update Firebase Auth profile
      await updateProfile(firebaseUser, {
        displayName: formData.displayName,
        photoURL: profileImageUrl || null,
      });

      // Get default plan
      const defaultPlan = representativePlans.find(plan => plan.priceDisplayKey === 'free');
      const defaultPlanId = defaultPlan ? defaultPlan.id : 'representative-basic';

      // Create user document
      const userDocRef = doc(db, "users", firebaseUser.uid);
      await setDoc(userDocRef, {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: formData.displayName,
        photoURL: profileImageUrl || null,
        userType: 'representative',
        planId: defaultPlanId,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      });

      // Create representative document
      const representativeDocRef = doc(db, "representatives", firebaseUser.uid);
      await setDoc(representativeDocRef, {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: formData.displayName,
        phoneNumber: formData.phoneNumber,
        photoURL: profileImageUrl || null,
        
        // Identity Information
        nationalId: formData.nationalId,
        nationalIdType: formData.nationalIdType,
        
        // Driving License
        drivingLicense: {
          number: formData.drivingLicenseNumber,
          issueDate: serverTimestamp() as Timestamp, // Convert from Date
          expiryDate: serverTimestamp() as Timestamp, // Convert from Date
          imageURL: drivingLicenseUrl,
        },
        
        // Vehicle Information
        vehicle: {
          type: formData.vehicleType,
          model: formData.vehicleModel,
          year: formData.vehicleYear,
          plateNumber: formData.vehiclePlateNumber,
          color: formData.vehicleColor,
          imageURL: vehicleImageUrl,
        },
        
        // Vehicle Inspection
        vehicleInspection: {
          certificateNumber: formData.inspectionCertificateNumber,
          issueDate: serverTimestamp() as Timestamp, // Convert from Date
          expiryDate: serverTimestamp() as Timestamp, // Convert from Date
          imageURL: inspectionCertificateUrl,
        },
        
        // Status and Plan
        approvalStatus: 'pending',
        planId: defaultPlanId,
        commissionRate: defaultPlan?.commission || 10,
        isActive: false,
        isAvailable: false,
        
        // Timestamps
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        submittedAt: serverTimestamp() as Timestamp,
      });

      // ===== APEX SECURITY: تسجيل نجاح التسجيل =====
      await logUserAction('representative_signup_success', firebaseUser.uid, {
        email: formData.email.substring(0, 3) + '***',
        displayName: formData.displayName,
        phoneNumber: formData.phoneNumber.substring(0, 3) + '***',
        vehicleType: formData.vehicleType,
        documentsUploaded: {
          drivingLicense: !!drivingLicenseUrl,
          inspectionCertificate: !!inspectionCertificateUrl,
          vehicleImage: !!vehicleImageUrl,
          profileImage: !!profileImageUrl
        },
        timestamp: new Date()
      });

      console.log('✅ تم تسجيل المندوب بنجاح مع الحماية الأمنية الكاملة');

      toast({
        title: t('success'),
        description: t('representativeSignupSuccess'),
      });

      // Redirect to pending approval page
      router.push(`/${locale}/representative/pending-approval`);

    } catch (error: any) {
      console.error('Representative signup error:', error);

      // ===== APEX SECURITY: تسجيل فشل التسجيل =====
      await logUserAction('representative_signup_failed', formData.email, {
        email: formData.email.substring(0, 3) + '***',
        error: error.message,
        step: currentStep,
        timestamp: new Date()
      });

      toast({
        title: t('error'),
        description: error.message || t('representativeSignupError'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {t('personalInformation')}
              </CardTitle>
              <CardDescription>
                {t('enterYourPersonalInformation')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="displayName">{t('fullName')} *</Label>
                <Input
                  id="displayName"
                  value={formData.displayName}
                  onChange={(e) => handleInputChange('displayName', e.target.value)}
                  placeholder={t('enterFullName')}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phoneNumber">{t('phoneNumber')} *</Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  placeholder={t('enterPhoneNumber')}
                  required
                />
              </div>
              <div>
                <Label htmlFor="email">{t('email')} *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t('enterEmail')}
                  required
                />
              </div>
              <div>
                <Label htmlFor="profileImage">{t('profileImage')}</Label>
                <Input
                  id="profileImage"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileChange('profileImage', e.target.files?.[0])}
                />
                {filePreview.profileImage && (
                  <img
                    src={filePreview.profileImage}
                    alt="Profile preview"
                    className="mt-2 h-20 w-20 rounded-full object-cover"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {t('identityInformation')}
              </CardTitle>
              <CardDescription>
                {t('enterYourIdentityInformation')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>{t('nationalIdType')} *</Label>
                <RadioGroup
                  value={formData.nationalIdType}
                  onValueChange={(value) => handleInputChange('nationalIdType', value as NationalIdType)}
                  className="flex gap-6 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="national" id="national" />
                    <Label htmlFor="national">{t('nationalIdCard')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="resident" id="resident" />
                    <Label htmlFor="resident">{t('residentId')}</Label>
                  </div>
                </RadioGroup>
              </div>
              <div>
                <Label htmlFor="nationalId">{t('nationalId')} *</Label>
                <Input
                  id="nationalId"
                  value={formData.nationalId}
                  onChange={(e) => handleInputChange('nationalId', e.target.value)}
                  placeholder={t('enterNationalId')}
                  required
                />
              </div>
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t('drivingLicense')}
              </CardTitle>
              <CardDescription>
                {t('uploadYourDrivingLicense')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="licenseNumber">{t('licenseNumber')} *</Label>
                <Input
                  id="licenseNumber"
                  value={formData.drivingLicenseNumber}
                  onChange={(e) => handleInputChange('drivingLicenseNumber', e.target.value)}
                  placeholder={t('enterLicenseNumber')}
                  required
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="licenseIssueDate">{t('issueDate')} *</Label>
                  <Input
                    id="licenseIssueDate"
                    type="date"
                    value={formData.drivingLicenseIssueDate.toISOString().split('T')[0]}
                    onChange={(e) => handleInputChange('drivingLicenseIssueDate', new Date(e.target.value))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="licenseExpiryDate">{t('expiryDate')} *</Label>
                  <Input
                    id="licenseExpiryDate"
                    type="date"
                    value={formData.drivingLicenseExpiryDate.toISOString().split('T')[0]}
                    onChange={(e) => handleInputChange('drivingLicenseExpiryDate', new Date(e.target.value))}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="licenseImage">{t('licenseImage')} *</Label>
                <Input
                  id="licenseImage"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileChange('drivingLicenseImage', e.target.files?.[0])}
                  required
                />
                {filePreview.drivingLicense && (
                  <img
                    src={filePreview.drivingLicense}
                    alt="License preview"
                    className="mt-2 h-32 w-auto rounded border"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Car className="h-5 w-5" />
                {t('vehicleInformation')}
              </CardTitle>
              <CardDescription>
                {t('enterVehicleDetails')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>{t('vehicleType')} *</Label>
                <RadioGroup
                  value={formData.vehicleType}
                  onValueChange={(value) => handleInputChange('vehicleType', value as VehicleType)}
                  className="flex gap-6 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="car" id="car" />
                    <Label htmlFor="car">{t('car')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="motorcycle" id="motorcycle" />
                    <Label htmlFor="motorcycle">{t('motorcycle')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="bicycle" id="bicycle" />
                    <Label htmlFor="bicycle">{t('bicycle')}</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vehicleModel">{t('vehicleModel')} *</Label>
                  <Input
                    id="vehicleModel"
                    value={formData.vehicleModel}
                    onChange={(e) => handleInputChange('vehicleModel', e.target.value)}
                    placeholder={t('enterVehicleModel')}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="vehicleYear">{t('vehicleYear')} *</Label>
                  <Input
                    id="vehicleYear"
                    type="number"
                    min="1990"
                    max={new Date().getFullYear()}
                    value={formData.vehicleYear}
                    onChange={(e) => handleInputChange('vehicleYear', parseInt(e.target.value))}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="plateNumber">{t('plateNumber')} *</Label>
                  <Input
                    id="plateNumber"
                    value={formData.vehiclePlateNumber}
                    onChange={(e) => handleInputChange('vehiclePlateNumber', e.target.value)}
                    placeholder={t('enterPlateNumber')}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="vehicleColor">{t('vehicleColor')} *</Label>
                  <Input
                    id="vehicleColor"
                    value={formData.vehicleColor}
                    onChange={(e) => handleInputChange('vehicleColor', e.target.value)}
                    placeholder={t('enterVehicleColor')}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="vehicleImage">{t('vehicleImage')}</Label>
                <Input
                  id="vehicleImage"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileChange('vehicleImage', e.target.files?.[0])}
                />
                {filePreview.vehicleImage && (
                  <img
                    src={filePreview.vehicleImage}
                    alt="Vehicle preview"
                    className="mt-2 h-32 w-auto rounded border"
                  />
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{t('vehicleInspection')}</h3>
                <div>
                  <Label htmlFor="inspectionNumber">{t('certificateNumber')} *</Label>
                  <Input
                    id="inspectionNumber"
                    value={formData.inspectionCertificateNumber}
                    onChange={(e) => handleInputChange('inspectionCertificateNumber', e.target.value)}
                    placeholder={t('enterCertificateNumber')}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="inspectionIssueDate">{t('issueDate')} *</Label>
                    <Input
                      id="inspectionIssueDate"
                      type="date"
                      value={formData.inspectionIssueDate.toISOString().split('T')[0]}
                      onChange={(e) => handleInputChange('inspectionIssueDate', new Date(e.target.value))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="inspectionExpiryDate">{t('expiryDate')} *</Label>
                    <Input
                      id="inspectionExpiryDate"
                      type="date"
                      value={formData.inspectionExpiryDate.toISOString().split('T')[0]}
                      onChange={(e) => handleInputChange('inspectionExpiryDate', new Date(e.target.value))}
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="inspectionImage">{t('inspectionImage')} *</Label>
                  <Input
                    id="inspectionImage"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange('inspectionCertificateImage', e.target.files?.[0])}
                    required
                  />
                  {filePreview.inspectionCertificate && (
                    <img
                      src={filePreview.inspectionCertificate}
                      alt="Inspection preview"
                      className="mt-2 h-32 w-auto rounded border"
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 5:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t('termsAndConditions')}
              </CardTitle>
              <CardDescription>
                {t('reviewAndAcceptTerms')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4 max-h-60 overflow-y-auto bg-muted/50">
                <h4 className="font-semibold mb-2">{t('representativeTermsTitle')}</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('representativeTermsIntro')}
                </p>
                <div className="space-y-2 text-sm">
                  <p><strong>1. {t('eligibilityRequirements')}</strong></p>
                  <p><strong>2. {t('vehicleRequirements')}</strong></p>
                  <p><strong>3. {t('deliveryResponsibilities')}</strong></p>
                  <p><strong>4. {t('commissionStructure')}</strong></p>
                  <p><strong>5. {t('conductStandards')}</strong></p>
                  <p><strong>6. {t('terminationConditions')}</strong></p>
                </div>
              </div>

              <div className="flex items-center space-x-2 rtl:space-x-reverse justify-center">
                <Checkbox
                  id="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => handleInputChange('agreeToTerms', checked)}
                  disabled={isLoading}
                  className="mt-1"
                />
                <div className="text-sm leading-normal">
                  {t('iAgreeToThe')}{' '}
                  <Button
                    type="button"
                    variant="link"
                    className="p-0 h-auto text-primary hover:text-primary/80 underline"
                    onClick={() => setIsTermsModalOpen(true)}
                    disabled={isLoading}
                  >
                    {t('termsAndConditions')}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return <div>Step {currentStep} content</div>;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">{t('representativeSignup')}</h1>
        <p className="text-muted-foreground">{t('joinOurDeliveryTeam')}</p>
      </div>

      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-muted-foreground">
            {t('step')} {currentStep} {t('of')} {totalSteps}
          </span>
          <span className="text-sm text-muted-foreground">
            {Math.round((currentStep / totalSteps) * 100)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {renderStepContent()}

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            {t('previous')}
          </Button>

          {currentStep < totalSteps ? (
            <Button type="button" onClick={nextStep} disabled={isLoading || isCheckingEmail}>
              {isCheckingEmail && currentStep === 1 && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isCheckingEmail && currentStep === 1 ? t('checkingEmail') : t('next')}
            </Button>
          ) : (
            <Button type="submit" disabled={isLoading || isCheckingEmail}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('submitApplication')}
            </Button>
          )}
        </div>
      </form>

      <TermsAndConditionsModal
        isOpen={isTermsModalOpen}
        onClose={() => setIsTermsModalOpen(false)}
        userType="representative"
        locale={locale}
      />
    </div>
  );
}
