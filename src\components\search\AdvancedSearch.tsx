"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  MapPin,
  Star,
  DollarSign,
  Clock,
  Package,
  Store,
  X,
  Save,
  History,
  Bookmark
} from 'lucide-react';

interface SearchFilters {
  query: string;
  category: string;
  location: string;
  priceRange: {
    min: number;
    max: number;
  };
  rating: number;
  deliveryTime: string;
  storeType: string[];
  sortBy: string;
  availability: boolean;
}

interface SavedSearch {
  id: string;
  name: string;
  filters: SearchFilters;
  createdAt: Date;
}

interface AdvancedSearchProps {
  onSearch: (filters: SearchFilters) => void;
  onSaveSearch?: (search: SavedSearch) => void;
  savedSearches?: SavedSearch[];
  className?: string;
}

export default function AdvancedSearch({
  onSearch,
  onSaveSearch,
  savedSearches = [],
  className
}: AdvancedSearchProps) {
  const router = useRouter();
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    category: '',
    location: '',
    priceRange: { min: 0, max: 1000 },
    rating: 0,
    deliveryTime: '',
    storeType: [],
    sortBy: 'relevance',
    availability: true
  });

  const [showFilters, setShowFilters] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [saveSearchName, setSaveSearchName] = useState('');

  // تحميل تاريخ البحث من localStorage
  useEffect(() => {
    const history = localStorage.getItem('searchHistory');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, []);

  // حفظ البحث في التاريخ
  const saveToHistory = (query: string) => {
    if (!query.trim()) return;
    
    const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('searchHistory', JSON.stringify(newHistory));
  };

  // تحديث فلتر
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // تحديث نوع المتجر
  const updateStoreType = (type: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      storeType: checked 
        ? [...prev.storeType, type]
        : prev.storeType.filter(t => t !== type)
    }));
  };

  // تنفيذ البحث
  const handleSearch = () => {
    saveToHistory(filters.query);
    onSearch(filters);
    
    // التنقل إلى صفحة النتائج مع المعاملات
    const searchParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '' && value !== 0) {
        if (typeof value === 'object') {
          searchParams.set(key, JSON.stringify(value));
        } else {
          searchParams.set(key, value.toString());
        }
      }
    });
    
    router.push(`/search?${searchParams.toString()}`);
  };

  // مسح الفلاتر
  const clearFilters = () => {
    setFilters({
      query: '',
      category: '',
      location: '',
      priceRange: { min: 0, max: 1000 },
      rating: 0,
      deliveryTime: '',
      storeType: [],
      sortBy: 'relevance',
      availability: true
    });
  };

  // حفظ البحث
  const handleSaveSearch = () => {
    if (!saveSearchName.trim() || !onSaveSearch) return;
    
    const savedSearch: SavedSearch = {
      id: Date.now().toString(),
      name: saveSearchName,
      filters: { ...filters },
      createdAt: new Date()
    };
    
    onSaveSearch(savedSearch);
    setSaveSearchName('');
  };

  // تحميل بحث محفوظ
  const loadSavedSearch = (search: SavedSearch) => {
    setFilters(search.filters);
    handleSearch();
  };

  // استخدام بحث من التاريخ
  const useHistorySearch = (query: string) => {
    setFilters(prev => ({ ...prev, query }));
  };

  const categories = [
    'الكل',
    'مطاعم',
    'بقالة',
    'صيدلية',
    'إلكترونيات',
    'ملابس',
    'كتب',
    'رياضة',
    'منزل وحديقة'
  ];

  const storeTypes = [
    'مطعم',
    'متجر',
    'صيدلية',
    'سوبر ماركت',
    'مخبز',
    'حلويات'
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* شريط البحث الرئيسي */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="ابحث عن المنتجات والمتاجر..."
                value={filters.query}
                onChange={(e) => updateFilter('query', e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
              
              {/* اقتراحات من التاريخ */}
              {filters.query && searchHistory.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border rounded-md shadow-lg z-10 mt-1">
                  {searchHistory
                    .filter(h => h.toLowerCase().includes(filters.query.toLowerCase()))
                    .slice(0, 5)
                    .map((query, index) => (
                      <div
                        key={index}
                        className="p-2 hover:bg-gray-50 cursor-pointer flex items-center gap-2"
                        onClick={() => useHistorySearch(query)}
                      >
                        <History className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">{query}</span>
                      </div>
                    ))}
                </div>
              )}
            </div>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="w-4 h-4" />
              فلاتر
            </Button>
            
            <Button onClick={handleSearch} className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              بحث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* الفلاتر المتقدمة */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>فلاتر البحث المتقدم</span>
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                <X className="w-4 h-4" />
                مسح الكل
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* الفئة والموقع */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">الفئة</Label>
                <Select value={filters.category} onValueChange={(value) => updateFilter('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="location">الموقع</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="location"
                    placeholder="المدينة أو الحي"
                    value={filters.location}
                    onChange={(e) => updateFilter('location', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* نطاق السعر */}
            <div>
              <Label>نطاق السعر (ريال)</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label htmlFor="min-price" className="text-sm">من</Label>
                  <Input
                    id="min-price"
                    type="number"
                    placeholder="0"
                    value={filters.priceRange.min}
                    onChange={(e) => updateFilter('priceRange', {
                      ...filters.priceRange,
                      min: Number(e.target.value)
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="max-price" className="text-sm">إلى</Label>
                  <Input
                    id="max-price"
                    type="number"
                    placeholder="1000"
                    value={filters.priceRange.max}
                    onChange={(e) => updateFilter('priceRange', {
                      ...filters.priceRange,
                      max: Number(e.target.value)
                    })}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* التقييم ووقت التسليم */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>التقييم الأدنى</Label>
                <Select value={filters.rating.toString()} onValueChange={(value) => updateFilter('rating', Number(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر التقييم" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">الكل</SelectItem>
                    <SelectItem value="1">⭐ 1+</SelectItem>
                    <SelectItem value="2">⭐ 2+</SelectItem>
                    <SelectItem value="3">⭐ 3+</SelectItem>
                    <SelectItem value="4">⭐ 4+</SelectItem>
                    <SelectItem value="5">⭐ 5</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>وقت التسليم</Label>
                <Select value={filters.deliveryTime} onValueChange={(value) => updateFilter('deliveryTime', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الوقت" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">الكل</SelectItem>
                    <SelectItem value="15">أقل من 15 دقيقة</SelectItem>
                    <SelectItem value="30">أقل من 30 دقيقة</SelectItem>
                    <SelectItem value="60">أقل من ساعة</SelectItem>
                    <SelectItem value="120">أقل من ساعتين</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* نوع المتجر */}
            <div>
              <Label>نوع المتجر</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                {storeTypes.map(type => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={type}
                      checked={filters.storeType.includes(type)}
                      onCheckedChange={(checked) => updateStoreType(type, checked as boolean)}
                    />
                    <Label htmlFor={type} className="text-sm">{type}</Label>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* ترتيب النتائج */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>ترتيب النتائج</Label>
                <Select value={filters.sortBy} onValueChange={(value) => updateFilter('sortBy', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">الأكثر صلة</SelectItem>
                    <SelectItem value="price_low">السعر: من الأقل للأعلى</SelectItem>
                    <SelectItem value="price_high">السعر: من الأعلى للأقل</SelectItem>
                    <SelectItem value="rating">التقييم الأعلى</SelectItem>
                    <SelectItem value="delivery_time">وقت التسليم الأسرع</SelectItem>
                    <SelectItem value="newest">الأحدث</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="availability"
                  checked={filters.availability}
                  onCheckedChange={(checked) => updateFilter('availability', checked)}
                />
                <Label htmlFor="availability">المتاح فقط</Label>
              </div>
            </div>

            {/* حفظ البحث */}
            <Separator />
            <div className="flex items-center gap-2">
              <Input
                placeholder="اسم البحث المحفوظ"
                value={saveSearchName}
                onChange={(e) => setSaveSearchName(e.target.value)}
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={handleSaveSearch}
                disabled={!saveSearchName.trim()}
                className="flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                حفظ البحث
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* البحثات المحفوظة */}
      {savedSearches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bookmark className="w-5 h-5" />
              البحثات المحفوظة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {savedSearches.map(search => (
                <Badge
                  key={search.id}
                  variant="outline"
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                  onClick={() => loadSavedSearch(search)}
                >
                  {search.name}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
