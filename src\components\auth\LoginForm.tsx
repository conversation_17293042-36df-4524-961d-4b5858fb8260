// src/components/auth/LoginForm.tsx
"use client";

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useLocale } from '@/hooks/use-locale';
import Link from 'next/link';
import type { Locale } from '@/lib/i18n';
import { auth, db } from '@/lib/firebase';
import { signInWithEmailAndPassword, setPersistence, browserLocalPersistence, browserSessionPersistence } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from 'lucide-react';
import type { UserDocument, StoreDocument } from '@/types';
import GoogleSignInButton from './GoogleSignInButton';

// ===== APEX SECURITY IMPORTS =====
import { ApexAuditSystem, AuditEventType, AuditCategory, logUserAction, logSecurityViolation } from '@/lib/audit-system';
import { ApexIntrusionDetection } from '@/lib/intrusion-detection';

export default function LoginForm({ locale }: { locale: Locale}) {
  const { t } = useLocale();
  const router = useRouter();
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ===== APEX SECURITY: حالة المصادقة الثنائية =====
  const [twoFAState, setTwoFAState] = useState({
    isRequired: false,
    isVerifying: false,
    verificationCode: '',
    tempUserId: null,
    showTwoFA: false
  });

  // ===== APEX SECURITY FUNCTIONS =====

  /**
   * جمع بيانات الطلب للتحليل الأمني
   */
  const getRequestData = () => {
    return {
      ip: 'client-side', // سيتم تحديثه في الـ middleware
      userAgent: navigator.userAgent,
      timestamp: new Date(),
      location: {
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        platform: navigator.platform
      },
      deviceFingerprint: {
        screen: `${screen.width}x${screen.height}`,
        colorDepth: screen.colorDepth,
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack
      }
    };
  };

  // ===== APEX SECURITY: دالة التحقق من المصادقة الثنائية =====
  const checkTwoFARequirement = async (userId: string) => {
    try {
      const is2FAEnabled = await Apex2FAEngine.is2FAEnabled(userId);

      if (is2FAEnabled) {
        console.log('🔐 المصادقة الثنائية مطلوبة للمستخدم');
        setTwoFAState({
          isRequired: true,
          isVerifying: false,
          verificationCode: '',
          tempUserId: userId,
          showTwoFA: true
        });
        return true;
      }

      return false;
    } catch (error) {
      console.error('خطأ في فحص المصادقة الثنائية:', error);
      return false;
    }
  };

  const verifyTwoFA = async () => {
    if (!twoFAState.tempUserId || !twoFAState.verificationCode) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال رمز التحقق",
        variant: "destructive"
      });
      return;
    }

    setTwoFAState(prev => ({ ...prev, isVerifying: true }));

    try {
      const isValid = await Apex2FAEngine.verifyTOTP(
        twoFAState.tempUserId,
        twoFAState.verificationCode
      );

      if (isValid) {
        await completeLogin(twoFAState.tempUserId);
      } else {
        toast({
          title: "رمز غير صحيح",
          description: "رمز التحقق غير صحيح، يرجى المحاولة مرة أخرى",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('خطأ في التحقق من 2FA:', error);
      toast({
        title: "خطأ في التحقق",
        description: "حدث خطأ أثناء التحقق من الرمز",
        variant: "destructive"
      });
    } finally {
      setTwoFAState(prev => ({ ...prev, isVerifying: false }));
    }
  };

  const completeLogin = async (userId: string) => {
    const userDocRef = doc(db, "users", userId);
    const userDocSnap = await getDoc(userDocRef);

    if (userDocSnap.exists()) {
      const userData = userDocSnap.data() as UserDocument;

      // التوجيه حسب نوع المستخدم
      if (userData.role === 'admin') {
        router.push(`/${locale}/admin/dashboard`);
      } else if (userData.role === 'merchant') {
        if (userData.approvalStatus === 'approved') {
          router.push(`/${locale}/merchant/dashboard`);
        } else {
          router.push(`/${locale}/merchant/pending-approval`);
        }
      } else if (userData.role === 'representative') {
        if (userData.approvalStatus === 'approved') {
          router.push(`/${locale}/representative/dashboard`);
        } else {
          router.push(`/${locale}/representative/pending-approval`);
        }
      } else {
        router.push(`/${locale}/dashboard`);
      }

      toast({
        title: t('loginSuccess'),
        description: t('welcomeBack'),
      });
    }

    setTwoFAState({
      isRequired: false,
      isVerifying: false,
      verificationCode: '',
      tempUserId: null,
      showTwoFA: false
    });
    setIsLoading(false);
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setIsLoading(true);

    // ===== APEX SECURITY: جمع بيانات الطلب =====
    const requestData = getRequestData();
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // ===== APEX SECURITY: تسجيل محاولة تسجيل الدخول =====
      await logUserAction('login_attempt', email, {
        email: email.substring(0, 3) + '***', // إخفاء جزئي للخصوصية
        rememberMe,
        requestData,
        sessionId
      });

      // Set persistence based on "Remember me"
      // browserLocalPersistence for "Remember me" (persists across browser sessions)
      // browserSessionPersistence for not "Remember me" (cleared when browser/tab is closed)
      await setPersistence(auth, rememberMe ? browserLocalPersistence : browserSessionPersistence);

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      console.log("Login successful, user:", firebaseUser.uid);

      // ===== APEX SECURITY: تحليل التهديدات في الوقت الفعلي =====
      const threatAssessment = await ApexIntrusionDetection.analyzeRealTimeThreats(
        firebaseUser.uid,
        sessionId,
        requestData
      );

      // ===== APEX SECURITY: تسجيل نجاح تسجيل الدخول =====
      await logUserAction('login_success', firebaseUser.uid, {
        email: email.substring(0, 3) + '***',
        rememberMe,
        threatLevel: threatAssessment.threatLevel,
        riskScore: threatAssessment.riskScore,
        sessionId,
        requestData
      });

      // Fetch user document to determine user type and approval status
      const userDocRef = doc(db, "users", firebaseUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      let redirectPath = `/${locale}/profile`; // Default fallback

      if (userDocSnap.exists()) {
        const userData = userDocSnap.data() as UserDocument;
        if (userData.userType === 'merchant') {
          // للتجار، نحتاج للتحقق من حالة الموافقة
          const storeDocRef = doc(db, "stores", firebaseUser.uid);
          const storeDocSnap = await getDoc(storeDocRef);

          if (storeDocSnap.exists()) {
            const storeData = storeDocSnap.data() as StoreDocument;

            // إذا كان التاجر معتمد ومفعل
            if (storeData.approvalStatus === 'approved' && storeData.isActive) {
              redirectPath = `/${locale}/merchant/dashboard`;
            } else {
              // إذا كان في انتظار الموافقة أو تم رفضه
              redirectPath = `/${locale}/merchant/pending-approval`;
            }
          } else {
            // لا يوجد مستند متجر
            redirectPath = `/${locale}/merchant/pending-approval`;
          }
        } else if (userData.userType === 'customer') {
          redirectPath = `/${locale}/dashboard`;
        }
      }

      // ===== APEX SECURITY: فحص المصادقة الثنائية =====
      const requires2FA = await checkTwoFARequirement(firebaseUser.uid);

      if (requires2FA) {
        // إيقاف التحميل مؤقتاً لعرض نموذج 2FA
        setIsLoading(false);
        return;
      }

      // إكمال تسجيل الدخول العادي (بدون 2FA)
      toast({
        title: t('loginSuccessTitle'),
        description: t('loginSuccessMessage'),
      });

      router.push(redirectPath);
    } catch (err: any) {
      console.error("Login error:", err);
      let errorMessage = t('loginFailed'); // Default message
      let securityViolationType = 'login_failed';

      // معالجة أخطاء Firebase Auth المختلفة
      switch (err.code) {
        case 'auth/user-not-found':
        case 'auth/wrong-password':
        case 'auth/invalid-credential':
        case 'auth/invalid-email':
          errorMessage = t('invalidCredentials');
          securityViolationType = 'invalid_credentials';
          break;
        case 'auth/user-disabled':
          errorMessage = t('accountDisabled');
          securityViolationType = 'account_disabled';
          break;
        case 'auth/too-many-requests':
          errorMessage = t('tooManyRequests');
          securityViolationType = 'too_many_requests';
          break;
        case 'auth/network-request-failed':
          errorMessage = t('networkError');
          securityViolationType = 'network_error';
          break;
        default:
          if (err.message) {
            errorMessage = err.message;
          }
          securityViolationType = 'unknown_error';
          break;
      }

      // ===== APEX SECURITY: تسجيل الانتهاك الأمني =====
      await logSecurityViolation(securityViolationType, email, {
        email: email.substring(0, 3) + '***',
        errorCode: err.code,
        errorMessage: err.message,
        requestData,
        sessionId,
        attemptCount: 1 // سيتم تطويرها لاحقاً لتتبع المحاولات
      });

      setError(errorMessage);
      toast({
        title: t('errorTitle'),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      {/* Google Sign-In Section - الأولوية للطريقة الأسرع */}
      <div className="space-y-4">
        <GoogleSignInButton
          rememberMe={rememberMe}
          variant="default"
          className="w-full"
        />

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              {t('orSigninManually')}
            </span>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="block text-sm font-medium text-foreground">
          {t('emailAddress')}
        </Label>
        <Input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          className="mt-1 block w-full"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={isLoading}
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="password" className="block text-sm font-medium text-foreground">
            {t('password')}
          </Label>
          <Link
            href={`/${locale}/forgot-password`}
            className="text-sm text-primary hover:text-primary/80 transition-colors"
          >
            {t('forgotPassword')}
          </Link>
        </div>
        <Input
          id="password"
          name="password"
          type="password"
          autoComplete="current-password"
          required
          className="mt-1 block w-full"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          disabled={isLoading}
        />
      </div>

      <div className="flex items-center">
        <Checkbox
          id="remember-me"
          name="remember-me"
          className="h-4 w-4"
          checked={rememberMe}
          onCheckedChange={(checked) => setRememberMe(checked as boolean)}
          disabled={isLoading}
        />
        <Label htmlFor="remember-me" className="ms-2 block text-sm text-foreground">
          {t('rememberMe')}
        </Label>
      </div>
      {error && <p className="text-sm text-destructive py-2">{error}</p>}
      <div>
        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('login')}
            </>
          ) : (
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              {t('login')}
            </span>
          )}
        </Button>
      </div>

      {/* ===== APEX SECURITY: نموذج المصادقة الثنائية ===== */}
      {twoFAState.showTwoFA && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="text-center mb-6">
              <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                المصادقة الثنائية مطلوبة
              </h3>
              <p className="text-sm text-gray-600 mt-2">
                يرجى إدخال الرمز المكون من 6 أرقام من تطبيق Google Authenticator
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="twofa-code" className="block text-sm font-medium text-gray-700 mb-2">
                  رمز التحقق
                </Label>
                <Input
                  id="twofa-code"
                  type="text"
                  placeholder="123456"
                  value={twoFAState.verificationCode}
                  onChange={(e) => setTwoFAState(prev => ({
                    ...prev,
                    verificationCode: e.target.value.replace(/\D/g, '').slice(0, 6)
                  }))}
                  className="text-center text-lg tracking-widest"
                  maxLength={6}
                  disabled={twoFAState.isVerifying}
                />
              </div>

              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    setTwoFAState({
                      isRequired: false,
                      isVerifying: false,
                      verificationCode: '',
                      tempUserId: null,
                      showTwoFA: false
                    });
                    setIsLoading(false);
                  }}
                  disabled={twoFAState.isVerifying}
                >
                  إلغاء
                </Button>
                <Button
                  type="button"
                  className="flex-1"
                  onClick={verifyTwoFA}
                  disabled={twoFAState.isVerifying || twoFAState.verificationCode.length !== 6}
                >
                  {twoFAState.isVerifying ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جاري التحقق...
                    </>
                  ) : (
                    'تحقق'
                  )}
                </Button>
              </div>

              <p className="text-xs text-gray-500 text-center">
                لا يمكنك الوصول لتطبيق المصادقة؟
                <button className="text-blue-600 hover:underline ml-1">
                  استخدم رمز النسخ الاحتياطي
                </button>
              </p>
            </div>
          </div>
        </div>
      )}

    </form>
  );
}
