'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Flag, Eye, Check, X, AlertTriangle, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useAuth } from '@/context/AuthContext';
import { collection, query, where, orderBy, getDocs, doc, updateDoc, getDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { ReviewReport, CustomerReview } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { useLocale } from 'next-intl';
import { toast } from 'sonner';

export default function ReviewReportsPage() {
  const t = useTranslations();
  const locale = useLocale();
  const { user } = useAuth();
  
  const [reports, setReports] = useState<(ReviewReport & { review?: CustomerReview })[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<ReviewReport | null>(null);
  const [selectedReview, setSelectedReview] = useState<CustomerReview | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [moderationNotes, setModerationNotes] = useState('');

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      setLoading(true);
      
      // جلب التقارير المعلقة
      const reportsQuery = query(
        collection(db, 'review_reports'),
        where('status', '==', 'pending'),
        orderBy('createdAt', 'desc')
      );
      
      const reportsSnapshot = await getDocs(reportsQuery);
      const reportsData = reportsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ReviewReport[];

      // جلب المراجعات المرتبطة
      const reportsWithReviews = await Promise.all(
        reportsData.map(async (report) => {
          try {
            const reviewDoc = await getDoc(doc(db, 'reviews', report.reviewId));
            if (reviewDoc.exists()) {
              return {
                ...report,
                review: { id: reviewDoc.id, ...reviewDoc.data() } as CustomerReview
              };
            }
            return report;
          } catch (error) {
            console.error('Error fetching review:', error);
            return report;
          }
        })
      );

      setReports(reportsWithReviews);
    } catch (error) {
      console.error('Error fetching reports:', error);
      toast.error(t('admin.errorFetchingReports'));
    } finally {
      setLoading(false);
    }
  };

  const handleViewReport = (report: ReviewReport, review?: CustomerReview) => {
    setSelectedReport(report);
    setSelectedReview(review || null);
    setModerationNotes('');
    setDialogOpen(true);
  };

  const handleResolveReport = async (action: 'approve' | 'reject') => {
    if (!selectedReport || !user) return;

    setProcessing(true);
    
    try {
      // تحديث حالة التقرير
      const reportRef = doc(db, 'review_reports', selectedReport.id);
      await updateDoc(reportRef, {
        status: 'resolved',
        reviewedAt: Timestamp.now(),
        reviewedBy: user.uid,
        moderationAction: action
      });

      // إذا تم رفض المراجعة، قم بإخفائها
      if (action === 'reject' && selectedReview) {
        const reviewRef = doc(db, 'reviews', selectedReview.id);
        await updateDoc(reviewRef, {
          isHidden: true,
          moderationStatus: 'rejected',
          moderationNotes: moderationNotes.trim() || undefined,
          updatedAt: Timestamp.now()
        });
      } else if (action === 'approve' && selectedReview) {
        // إذا تم قبول المراجعة، تأكد من أنها مرئية
        const reviewRef = doc(db, 'reviews', selectedReview.id);
        await updateDoc(reviewRef, {
          isHidden: false,
          moderationStatus: 'approved',
          moderationNotes: moderationNotes.trim() || undefined,
          updatedAt: Timestamp.now()
        });
      }

      toast.success(
        action === 'approve' 
          ? t('admin.reportApproved') 
          : t('admin.reportRejected')
      );
      
      setDialogOpen(false);
      fetchReports(); // إعادة تحميل التقارير
    } catch (error) {
      console.error('Error resolving report:', error);
      toast.error(t('admin.errorResolvingReport'));
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (timestamp: any) => {
    const date = timestamp?.toDate?.() || new Date(timestamp);
    return formatDistanceToNow(date, {
      addSuffix: true,
      locale: locale === 'ar' ? ar : enUS
    });
  };

  const getReasonLabel = (reason: ReviewReport['reason']) => {
    return t(`reviews.reportReasons.${reason}`);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating 
            ? 'fill-yellow-400 text-yellow-400' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3" />
          <div className="h-64 bg-gray-200 rounded" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('admin.reviewReports')}
        </h1>
        <p className="text-gray-600">
          {t('admin.reviewReportsDescription')}
        </p>
      </div>

      {reports.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Flag className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('admin.noReportsFound')}
            </h3>
            <p className="text-gray-500">
              {t('admin.noReportsFoundDescription')}
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
              <Flag className="h-5 w-5 text-red-500" />
              <span>{t('admin.pendingReports')} ({reports.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('admin.reporter')}</TableHead>
                  <TableHead>{t('admin.reason')}</TableHead>
                  <TableHead>{t('admin.reviewContent')}</TableHead>
                  <TableHead>{t('admin.reportDate')}</TableHead>
                  <TableHead>{t('admin.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{report.reporterName}</div>
                        <div className="text-sm text-gray-500">
                          {report.reporterId}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getReasonLabel(report.reason)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {report.review ? (
                        <div className="max-w-xs">
                          <div className="flex items-center space-x-1 rtl:space-x-reverse mb-1">
                            {renderStars(report.review.rating)}
                          </div>
                          <p className="text-sm text-gray-600 truncate">
                            {report.review.comment}
                          </p>
                          <p className="text-xs text-gray-500">
                            {t('admin.by')} {report.review.customerName}
                          </p>
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">
                          {t('admin.reviewNotFound')}
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-500">
                        {formatDate(report.createdAt)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewReport(report, report.review)}
                      >
                        <Eye className="h-4 w-4 me-1" />
                        {t('admin.review')}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* حوار مراجعة التقرير */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 rtl:space-x-reverse">
              <Flag className="h-5 w-5 text-red-500" />
              <span>{t('admin.reviewReport')}</span>
            </DialogTitle>
            <DialogDescription>
              {t('admin.reviewReportDescription')}
            </DialogDescription>
          </DialogHeader>

          {selectedReport && (
            <div className="space-y-6">
              {/* معلومات التقرير */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-900 mb-2">
                  {t('admin.reportDetails')}
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-red-700">{t('admin.reporter')}:</span>
                    <span className="font-medium">{selectedReport.reporterName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-red-700">{t('admin.reason')}:</span>
                    <Badge variant="outline">
                      {getReasonLabel(selectedReport.reason)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-red-700">{t('admin.reportDate')}:</span>
                    <span>{formatDate(selectedReport.createdAt)}</span>
                  </div>
                  {selectedReport.description && (
                    <div>
                      <span className="text-red-700">{t('admin.description')}:</span>
                      <p className="mt-1 text-gray-700">{selectedReport.description}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* المراجعة المُبلغ عنها */}
              {selectedReview && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">
                    {t('admin.reportedReview')}
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <span className="font-medium">{selectedReview.customerName}</span>
                      <div className="flex items-center">
                        {renderStars(selectedReview.rating)}
                      </div>
                    </div>
                    <p className="text-gray-700">{selectedReview.comment}</p>
                    <div className="text-sm text-gray-500">
                      {formatDate(selectedReview.createdAt)}
                    </div>
                  </div>
                </div>
              )}

              {/* ملاحظات الإدارة */}
              <div className="space-y-2">
                <Label htmlFor="moderationNotes">
                  {t('admin.moderationNotes')} ({t('reviews.optional')})
                </Label>
                <Textarea
                  id="moderationNotes"
                  value={moderationNotes}
                  onChange={(e) => setModerationNotes(e.target.value)}
                  placeholder={t('admin.moderationNotesPlaceholder')}
                  className="min-h-[100px]"
                />
              </div>

              {/* تحذير */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium mb-1">
                      {t('admin.moderationWarning')}
                    </p>
                    <p>
                      {t('admin.moderationWarningDescription')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex space-x-3 rtl:space-x-reverse">
            <Button
              variant="outline"
              onClick={() => setDialogOpen(false)}
              disabled={processing}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="outline"
              onClick={() => handleResolveReport('approve')}
              disabled={processing}
              className="text-green-600 hover:text-green-700"
            >
              <Check className="h-4 w-4 me-1" />
              {processing ? t('admin.processing') : t('admin.approveReview')}
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleResolveReport('reject')}
              disabled={processing}
            >
              <X className="h-4 w-4 me-1" />
              {processing ? t('admin.processing') : t('admin.rejectReview')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
