// src/components/debug/ConsoleErrorHandler.tsx
"use client";

import { useEffect } from 'react';

interface ConsoleErrorHandlerProps {
  enableInProduction?: boolean;
}

export default function ConsoleErrorHandler({ enableInProduction = false }: ConsoleErrorHandlerProps) {
  useEffect(() => {
    // تشغيل معالج الأخطاء فقط في التطوير أو إذا تم تفعيله صراحة
    if (process.env.NODE_ENV !== 'development' && !enableInProduction) {
      return;
    }

    // معالج أخطاء JavaScript العامة
    const handleError = (event: ErrorEvent) => {
      const { message, filename, lineno, colno, error } = event;
      
      // تجاهل أخطاء معينة غير مهمة
      const ignoredErrors = [
        'ResizeObserver loop limit exceeded',
        'Non-Error promise rejection captured',
        'Script error',
        'Network request failed',
        'Loading chunk',
        'ChunkLoadError',
        'Failed to get document because the client is offline',
        'Request timeout',
        'Transport error',
        'Stream is already ended',
        'failed to pipe response',
        'deadline-exceeded',
        'unavailable'
      ];

      if (ignoredErrors.some(ignored => message.includes(ignored))) {
        return;
      }

      console.group('🚨 JavaScript Error Caught');
      console.error('Message:', message);
      console.error('File:', filename);
      console.error('Line:', lineno, 'Column:', colno);
      if (error) {
        console.error('Stack:', error.stack);
      }
      console.groupEnd();
    };

    // معالج أخطاء Promise المرفوضة
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const reason = event.reason;
      
      // تجاهل أخطاء معينة
      if (reason && typeof reason === 'object') {
        if (reason.code === 'auth/popup-closed-by-user' || 
            reason.code === 'auth/cancelled-popup-request' ||
            reason.message?.includes('popup_closed_by_user')) {
          return; // هذه أخطاء طبيعية عندما يغلق المستخدم النافذة
        }
      }

      console.group('🚨 Unhandled Promise Rejection');
      console.error('Reason:', reason);
      if (reason && reason.stack) {
        console.error('Stack:', reason.stack);
      }
      console.groupEnd();
    };

    // معالج أخطاء الموارد (الصور، CSS، إلخ)
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target && target.tagName) {
        console.group('🚨 Resource Loading Error');
        console.error('Element:', target.tagName);
        console.error('Source:', (target as any).src || (target as any).href);
        console.groupEnd();
      }
    };

    // إضافة مستمعي الأحداث
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleResourceError, true);

    // معالج خاص لأخطاء CSP
    const handleCSPViolation = (event: SecurityPolicyViolationEvent) => {
      console.group('🛡️ CSP Violation');
      console.warn('Blocked URI:', event.blockedURI);
      console.warn('Violated Directive:', event.violatedDirective);
      console.warn('Original Policy:', event.originalPolicy);
      console.warn('Source File:', event.sourceFile);
      console.warn('Line Number:', event.lineNumber);
      console.groupEnd();
    };

    // إضافة مستمع أحداث CSP
    document.addEventListener('securitypolicyviolation', handleCSPViolation);

    // تنظيف المستمعين عند إلغاء التحميل
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleResourceError, true);
      document.removeEventListener('securitypolicyviolation', handleCSPViolation);
    };
  }, [enableInProduction]);

  // معالج خاص لأخطاء Firebase
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development' && !enableInProduction) {
      return;
    }

    // مراقبة أخطاء Firebase المحددة
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      
      // تجاهل أخطاء Firebase غير المهمة
      const ignoredFirebaseErrors = [
        'Firebase: Error (auth/popup-closed-by-user)',
        'Firebase: Error (auth/cancelled-popup-request)',
        'Firebase: Error (auth/network-request-failed)',
        'Firebase: Error (auth/too-many-requests)',
        'Failed to get document because the client is offline',
        'Request timeout',
        'Transport error',
        'deadline-exceeded',
        'unavailable',
        'Stream is already ended',
        'failed to pipe response'
      ];

      if (ignoredFirebaseErrors.some(ignored => message.includes(ignored))) {
        return; // لا تعرض هذه الأخطاء
      }

      // عرض أخطاء Firebase المهمة مع تنسيق أفضل
      if (message.includes('Firebase:')) {
        console.group('🔥 Firebase Error');
        originalConsoleError.apply(console, args);
        console.groupEnd();
      } else {
        originalConsoleError.apply(console, args);
      }
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, [enableInProduction]);

  // معلومات مفيدة للمطورين
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Console Error Handler loaded');
      console.log('📊 To test network fixes, use: window.networkTests.quick()');
    }
  }, []);

  return null; // هذا المكون لا يعرض أي شيء
}

// دالة مساعدة لتنظيف وحدة التحكم
export const clearConsoleErrors = () => {
  if (typeof console.clear === 'function') {
    console.clear();
    console.log('🧹 Console cleared');
  }
};

// دالة لتصدير سجل الأخطاء
export const exportErrorLog = () => {
  const errors: any[] = [];
  
  // حفظ console.error الأصلي
  const originalError = console.error;
  
  // استبدال console.error لجمع الأخطاء
  console.error = (...args) => {
    errors.push({
      timestamp: new Date().toISOString(),
      message: args.join(' '),
      stack: new Error().stack
    });
    originalError.apply(console, args);
  };

  // إرجاع دالة لاستخراج السجل
  return {
    getErrors: () => errors,
    downloadLog: () => {
      const blob = new Blob([JSON.stringify(errors, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `error-log-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
    },
    clearLog: () => {
      errors.length = 0;
      console.log('📝 Error log cleared');
    },
    restore: () => {
      console.error = originalError;
    }
  };
};
