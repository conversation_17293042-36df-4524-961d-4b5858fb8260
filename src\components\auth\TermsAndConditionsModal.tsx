// src/components/auth/TermsAndConditionsModal.tsx
"use client";

import { useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useLocale } from "@/hooks/use-locale";
import type { Locale } from '@/lib/i18n';
import { cn } from "@/lib/utils";
import { termsService } from '@/services/termsService';
import { TermsAndConditions } from '@/types/terms';

type UserType = 'customer' | 'merchant' | 'representative';

interface TermsAndConditionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userType: UserType | undefined;
  locale: Locale;
}

export default function TermsAndConditionsModal({
  isOpen,
  onClose,
  userType,
}: TermsAndConditionsModalProps) {
  const { t, locale: currentLocale, isLoading: localeIsLoading } = useLocale();
  const [terms, setTerms] = useState<TermsAndConditions | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTerms = async () => {
      if (!isOpen || localeIsLoading) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // استخدام نوع المستخدم المحدد أو 'general' إذا لم يتم تحديد نوع
        const userTypeParam = userType || 'general';
        
        const response = await termsService.getTerms({
          userType: userTypeParam,
          locale: currentLocale
        });
        
        if (response.success && response.data) {
          setTerms(response.data);
        } else {
          setError(response.error || t('errorLoadingTerms'));
        }
      } catch (err) {
        console.error('Error fetching terms:', err);
        setError(t('errorLoadingTerms'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTerms();
  }, [isOpen, userType, currentLocale, localeIsLoading, t]);

  const getContent = () => {
    if (isLoading || localeIsLoading) {
      return t('loadingTerms');
    }

    if (error) {
      return error;
    }

    if (!terms) {
      return t('generalTermsText');
    }

    return terms.content;
  };

  const getTitle = () => {
    if (terms && terms.title) {
      return terms.title;
    }

    // إذا لم تكن هناك شروط محملة، استخدم العنوان المناسب حسب نوع المستخدم
    switch (userType) {
      case 'customer':
        return t('termsAndConditionsForCustomers');
      case 'merchant':
        return t('termsAndConditionsForMerchants');
      case 'representative':
        return t('termsAndConditionsForRepresentatives');
      default:
        return t('termsAndConditionsTitle');
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className="sm:max-w-[425px] md:max-w-[600px] max-h-[80vh] flex flex-col p-0" 
        dir={currentLocale === 'ar' ? 'rtl' : 'ltr'} 
      >
        <DialogHeader
          dir={currentLocale === 'ar' ? 'rtl' : 'ltr'}
          className={cn(
            "p-6 border-b flex-shrink-0",
            currentLocale === 'ar' ? "text-right sm:text-right" : "text-left sm:text-left"
          )}
        >
          <DialogTitle>
            {getTitle()}
          </DialogTitle>
          <DialogDescription>
            {t('pleaseReadTermsCarefully')}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-grow min-h-0 overflow-y-auto"> 
          <div 
            className="whitespace-pre-wrap text-sm text-foreground p-6" 
            dir={currentLocale === 'ar' ? 'rtl' : 'ltr'}
          >
            {getContent()}
          </div>
        </div>
        
        <DialogFooter 
          className="p-6 border-t mt-auto flex-shrink-0"
          dir={currentLocale === 'ar' ? 'rtl' : 'ltr'}
        >
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('close')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
