"use client";

import { useLocale } from '@/hooks/use-locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  CheckCircle, 
  Package, 
  Truck, 
  MapPin,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { OrderStatus } from '@/types';

interface OrderStep {
  status: OrderStatus;
  titleKey: string;
  descriptionKey: string;
  icon: React.ReactNode;
  completed: boolean;
  current: boolean;
  cancelled?: boolean;
}

interface OrderTrackerProps {
  currentStatus: OrderStatus;
  orderDate?: any;
  className?: string;
}

export default function OrderTracker({ currentStatus, orderDate, className }: OrderTrackerProps) {
  const { t } = useLocale();

  const getOrderSteps = (status: OrderStatus): OrderStep[] => {
    const allSteps: Omit<OrderStep, 'completed' | 'current' | 'cancelled'>[] = [
      {
        status: 'pending',
        titleKey: 'orderStatusPending',
        descriptionKey: 'orderStatusPendingDesc',
        icon: <Clock className="h-4 w-4" />
      },
      {
        status: 'confirmed',
        titleKey: 'orderStatusConfirmed',
        descriptionKey: 'orderStatusConfirmedDesc',
        icon: <CheckCircle className="h-4 w-4" />
      },
      {
        status: 'preparing',
        titleKey: 'orderStatusPreparing',
        descriptionKey: 'orderStatusPreparingDesc',
        icon: <Package className="h-4 w-4" />
      },
      {
        status: 'ready',
        titleKey: 'orderStatusReady',
        descriptionKey: 'orderStatusReadyDesc',
        icon: <CheckCircle className="h-4 w-4" />
      },
      {
        status: 'shipped',
        titleKey: 'orderStatusShipped',
        descriptionKey: 'orderStatusShippedDesc',
        icon: <Truck className="h-4 w-4" />
      },
      {
        status: 'delivered',
        titleKey: 'orderStatusDelivered',
        descriptionKey: 'orderStatusDeliveredDesc',
        icon: <MapPin className="h-4 w-4" />
      }
    ];

    const statusOrder = ['pending', 'confirmed', 'preparing', 'ready', 'shipped', 'delivered'];
    const currentIndex = statusOrder.indexOf(status);
    const isCancelled = status === 'cancelled';

    return allSteps.map((step, index) => ({
      ...step,
      completed: !isCancelled && index < currentIndex,
      current: !isCancelled && index === currentIndex,
      cancelled: isCancelled && index > 0 // Show first step as completed even if cancelled
    }));
  };

  const steps = getOrderSteps(currentStatus);
  const isCancelled = currentStatus === 'cancelled';

  const formatDate = (timestamp: any) => {
    if (!timestamp || !timestamp.toDate) return '';
    return timestamp.toDate().toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {t('orderTracking')}
        </CardTitle>
        {orderDate && (
          <p className="text-sm text-muted-foreground">
            {t('orderPlacedOn')} {formatDate(orderDate)}
          </p>
        )}
      </CardHeader>
      <CardContent>
        {isCancelled && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-center gap-2 text-destructive">
              <XCircle className="h-4 w-4" />
              <span className="font-medium">{t('orderStatusCancelled')}</span>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {t('orderCancelledDesc')}
            </p>
          </div>
        )}

        <div className="space-y-4">
          {steps.map((step, index) => {
            const isLast = index === steps.length - 1;
            
            return (
              <div key={step.status} className="relative">
                {/* Connector Line */}
                {!isLast && (
                  <div 
                    className={cn(
                      "absolute left-4 top-8 w-0.5 h-8 -ml-px",
                      step.completed || step.current 
                        ? "bg-primary" 
                        : step.cancelled 
                        ? "bg-destructive/30" 
                        : "bg-muted"
                    )}
                  />
                )}

                {/* Step Content */}
                <div className="flex items-start gap-4">
                  {/* Step Icon */}
                  <div 
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors",
                      step.completed 
                        ? "bg-primary border-primary text-primary-foreground" 
                        : step.current 
                        ? "bg-primary/10 border-primary text-primary" 
                        : step.cancelled
                        ? "bg-destructive/10 border-destructive/30 text-destructive/60"
                        : "bg-muted border-muted-foreground/30 text-muted-foreground"
                    )}
                  >
                    {step.completed ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : step.cancelled ? (
                      <XCircle className="h-4 w-4" />
                    ) : (
                      step.icon
                    )}
                  </div>

                  {/* Step Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 
                        className={cn(
                          "font-medium",
                          step.completed || step.current 
                            ? "text-foreground" 
                            : step.cancelled
                            ? "text-destructive/60"
                            : "text-muted-foreground"
                        )}
                      >
                        {t(step.titleKey)}
                      </h4>
                      
                      {step.current && !isCancelled && (
                        <Badge variant="outline" className="text-xs">
                          {t('current')}
                        </Badge>
                      )}
                      
                      {step.completed && (
                        <Badge variant="default" className="text-xs">
                          {t('completed')}
                        </Badge>
                      )}
                    </div>
                    
                    <p 
                      className={cn(
                        "text-sm",
                        step.completed || step.current 
                          ? "text-muted-foreground" 
                          : step.cancelled
                          ? "text-destructive/40"
                          : "text-muted-foreground/60"
                      )}
                    >
                      {t(step.descriptionKey)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Info */}
        {!isCancelled && (
          <div className="mt-6 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                {currentStatus === 'delivered' 
                  ? t('orderCompletedInfo')
                  : t('orderTrackingInfo')
                }
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Helper component for estimated delivery time
interface DeliveryEstimateProps {
  status: OrderStatus;
  className?: string;
}

export function DeliveryEstimate({ status, className }: DeliveryEstimateProps) {
  const { t } = useLocale();

  const getEstimatedTime = (status: OrderStatus): string => {
    switch (status) {
      case 'pending':
        return t('estimatedTime24Hours');
      case 'confirmed':
        return t('estimatedTime12Hours');
      case 'preparing':
        return t('estimatedTime6Hours');
      case 'ready':
        return t('estimatedTime2Hours');
      case 'shipped':
        return t('estimatedTime1Hour');
      case 'delivered':
        return t('delivered');
      case 'cancelled':
        return t('cancelled');
      default:
        return t('unknown');
    }
  };

  if (status === 'delivered' || status === 'cancelled') {
    return null;
  }

  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Clock className="h-4 w-4 text-primary" />
          </div>
          <div>
            <h4 className="font-medium">{t('estimatedDelivery')}</h4>
            <p className="text-sm text-muted-foreground">
              {getEstimatedTime(status)}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
