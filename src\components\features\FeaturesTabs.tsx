"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import FeatureCard from "./FeatureCard";
import type { SubscriptionPlan } from '@/types';
import type { Locale } from '@/lib/i18n';
import { useLocale } from '@/hooks/use-locale';

interface FeaturesTabsProps {
  merchantPlans: SubscriptionPlan[];
  customerPlans: SubscriptionPlan[];
  locale: Locale; // Passed for initial hydration, useLocale hook handles client-side updates
}

export default function FeaturesTabs({ merchantPlans, customerPlans, locale }: FeaturesTabsProps) {
  const { t } = useLocale(); // Use client-side hook for translations

  return (
    <Tabs defaultValue="merchants" className="w-full">
      <TabsList className="grid w-full grid-cols-2 md:w-1/2 mx-auto mb-8">
        <TabsTrigger value="merchants">{t('merchants')}</TabsTrigger>
        <TabsTrigger value="customers">{t('customers')}</TabsTrigger>
      </TabsList>
      <TabsContent value="merchants">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-semibold">{t('merchantFeaturesTitle')}</h2>
          <p className="text-muted-foreground mt-2">{t('featuresPageSubtitle')}</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {merchantPlans.map((plan) => (
            <FeatureCard key={plan.id} plan={plan} showPricing={false} />
          ))}
        </div>
      </TabsContent>
      <TabsContent value="customers">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-semibold">{t('customerFeaturesTitle')}</h2>
          <p className="text-muted-foreground mt-2">{t('featuresPageSubtitle')}</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {customerPlans.map((plan) => (
            <FeatureCard key={plan.id} plan={plan} showPricing={false} />
          ))}
        </div>
      </TabsContent>
    </Tabs>
  );
}
