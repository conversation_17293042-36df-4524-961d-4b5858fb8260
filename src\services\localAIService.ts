// خدمة الذكاء الاصطناعي المحلي
import { modelLoader } from '../../ai-models/utils/model-loader.js';
import { localOCREngine } from '../../ai-models/workers/ocr-worker.js';

export interface LocalAIAnalysisResult {
  extractedData: any;
  confidence: number;
  processingLocation: 'local_browser';
  privacyGuaranteed: true;
  dataEncrypted: boolean;
  externalRequestsMade: false;
  processingTime: number;
  modelUsed: string;
  fraudDetection?: {
    isFraudulent: boolean;
    riskLevel: number;
    indicators: string[];
  };
}

export class LocalAIService {
  private static initialized = false;
  private static config: any = null;

  /**
   * تهيئة الخدمة
   */
  static async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      console.log('🤖 تهيئة خدمة الذكاء الاصطناعي المحلي...');
      
      // تهيئة محمل النماذج
      await modelLoader.initialize();
      
      // تهيئة محرك OCR
      await localOCREngine.initialize();
      
      this.initialized = true;
      console.log('✅ تم تهيئة خدمة الذكاء الاصطناعي المحلي بنجاح');
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة الذكاء الاصطناعي المحلي:', error);
      throw error;
    }
  }

  /**
   * تحليل المستند محلياً
   */
  static async analyzeDocument(
    documentUrl: string,
    documentType: string,
    isRepresentative: boolean = false
  ): Promise<LocalAIAnalysisResult> {
    try {
      console.log(`🔍 بدء التحليل المحلي للمستند: ${documentType}`);

      // تهيئة الخدمة إذا لم تكن مهيأة
      await this.initialize();

      const startTime = Date.now();

      // 1. استخراج النص من المستند
      const ocrResult = await this.extractTextFromDocument(documentUrl, documentType);
      
      // 2. تصنيف نوع المستند
      const classification = await this.classifyDocument(ocrResult.text, documentType);
      
      // 3. استخراج البيانات المنظمة
      const extractedData = await this.extractStructuredData(ocrResult.text, documentType);
      
      // 4. التحقق من صحة البيانات
      const validation = await this.validateExtractedData(extractedData, documentType);
      
      // 5. كشف الاحتيال
      const fraudDetection = await this.detectFraud(extractedData, documentType);
      
      // 6. حساب مستوى الثقة
      const confidence = this.calculateConfidence(ocrResult, classification, validation, fraudDetection);
      
      const processingTime = Date.now() - startTime;

      const result: LocalAIAnalysisResult = {
        extractedData,
        confidence,
        processingLocation: 'local_browser',
        privacyGuaranteed: true,
        dataEncrypted: true,
        externalRequestsMade: false,
        processingTime,
        modelUsed: ocrResult.modelUsed,
        fraudDetection
      };

      console.log(`✅ تم التحليل المحلي بنجاح في ${processingTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في التحليل المحلي:', error);
      throw new Error(`فشل في التحليل المحلي: ${error.message}`);
    }
  }

  /**
   * استخراج النص من المستند
   */
  private static async extractTextFromDocument(documentUrl: string, documentType: string) {
    try {
      // تحميل الصورة
      const imageData = await this.loadImageFromUrl(documentUrl);
      
      // تحديد خيارات OCR حسب نوع المستند
      const ocrOptions = this.getOCROptions(documentType);
      
      // استخراج النص محلياً
      const result = await localOCREngine.extractText(imageData, ocrOptions);
      
      return result;
      
    } catch (error) {
      console.error('❌ خطأ في استخراج النص:', error);
      throw error;
    }
  }

  /**
   * تحميل الصورة من الرابط
   */
  private static async loadImageFromUrl(url: string): Promise<Blob> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`فشل في تحميل الصورة: ${response.status}`);
      }
      
      return await response.blob();
      
    } catch (error) {
      console.error('❌ خطأ في تحميل الصورة:', error);
      throw error;
    }
  }

  /**
   * الحصول على خيارات OCR حسب نوع المستند
   */
  private static getOCROptions(documentType: string) {
    const options: any = {
      documentType,
      enhanceContrast: true,
      removeNoise: true
    };

    switch (documentType) {
      case 'commercial_registration':
        options.quality = 'high';
        options.speed = 'normal';
        break;
      case 'freelance_document':
        options.quality = 'high';
        options.speed = 'normal';
        break;
      case 'driving_license':
        options.quality = 'high';
        options.speed = 'normal';
        options.sharpen = true;
        break;
      default:
        options.quality = 'normal';
        options.speed = 'fast';
    }

    return options;
  }

  /**
   * تصنيف المستند
   */
  private static async classifyDocument(text: string, expectedType: string) {
    try {
      // تحميل نموذج التصنيف
      const classifierModel = await modelLoader.getModel('document_classifier');
      
      // تحضير النص للتصنيف
      const processedText = this.preprocessTextForClassification(text);
      
      // تشغيل النموذج (محاكاة)
      const classification = this.classifyTextLocally(processedText, expectedType);
      
      return classification;
      
    } catch (error) {
      console.warn('⚠️ فشل في تصنيف المستند، استخدام النوع المتوقع:', error);
      return {
        type: expectedType,
        confidence: 0.7,
        alternatives: []
      };
    }
  }

  /**
   * تصنيف النص محلياً
   */
  private static classifyTextLocally(text: string, expectedType: string) {
    const keywords = {
      commercial_registration: [
        'سجل تجاري', 'وزارة التجارة', 'رقم السجل', 'اسم المنشأة',
        'commercial registration', 'ministry of commerce'
      ],
      freelance_document: [
        'وثيقة العمل الحر', 'وزارة الموارد البشرية', 'رخصة العمل الحر',
        'freelance document', 'ministry of human resources'
      ],
      driving_license: [
        'رخصة القيادة', 'إدارة المرور', 'رقم الرخصة', 'فئة الرخصة',
        'driving license', 'traffic department'
      ]
    };

    let maxScore = 0;
    let detectedType = expectedType;
    
    for (const [type, typeKeywords] of Object.entries(keywords)) {
      let score = 0;
      for (const keyword of typeKeywords) {
        if (text.toLowerCase().includes(keyword.toLowerCase())) {
          score++;
        }
      }
      
      if (score > maxScore) {
        maxScore = score;
        detectedType = type;
      }
    }

    const confidence = Math.min(0.95, 0.5 + (maxScore * 0.1));

    return {
      type: detectedType,
      confidence,
      alternatives: Object.keys(keywords).filter(t => t !== detectedType)
    };
  }

  /**
   * معالجة النص للتصنيف
   */
  private static preprocessTextForClassification(text: string): string {
    return text
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * استخراج البيانات المنظمة
   */
  private static async extractStructuredData(text: string, documentType: string) {
    try {
      // استخدام محرك OCR لاستخراج البيانات المنظمة
      const structuredData = localOCREngine.extractStructuredData(text, documentType);
      
      // تحسين البيانات المستخرجة
      const enhancedData = this.enhanceExtractedData(structuredData, documentType);
      
      return enhancedData;
      
    } catch (error) {
      console.error('❌ خطأ في استخراج البيانات المنظمة:', error);
      return {};
    }
  }

  /**
   * تحسين البيانات المستخرجة
   */
  private static enhanceExtractedData(data: any, documentType: string) {
    const enhanced = { ...data };

    // تنظيف وتحسين البيانات
    for (const [key, value] of Object.entries(enhanced)) {
      if (typeof value === 'string') {
        enhanced[key] = value.trim();
      }
    }

    // إضافة معلومات إضافية حسب نوع المستند
    switch (documentType) {
      case 'commercial_registration':
        enhanced.documentType = 'commercial_registration';
        enhanced.isValid = this.validateCommercialRegistration(enhanced);
        break;
      case 'freelance_document':
        enhanced.documentType = 'freelance_document';
        enhanced.isValid = this.validateFreelanceDocument(enhanced);
        break;
      case 'driving_license':
        enhanced.documentType = 'driving_license';
        enhanced.isValid = this.validateDrivingLicense(enhanced);
        break;
    }

    return enhanced;
  }

  /**
   * التحقق من صحة البيانات
   */
  private static async validateExtractedData(data: any, documentType: string) {
    try {
      // تحميل نموذج التحقق
      const validatorModel = await modelLoader.getModel('format_validator');
      
      // تشغيل التحقق المحلي
      const validation = this.validateDataLocally(data, documentType);
      
      return validation;
      
    } catch (error) {
      console.warn('⚠️ فشل في التحقق من البيانات:', error);
      return {
        isValid: false,
        errors: ['فشل في التحقق من البيانات'],
        confidence: 0.5
      };
    }
  }

  /**
   * التحقق المحلي من البيانات
   */
  private static validateDataLocally(data: any, documentType: string) {
    const errors: string[] = [];
    let isValid = true;

    switch (documentType) {
      case 'commercial_registration':
        if (!data.registrationNumber || !/^\d{10}$/.test(data.registrationNumber)) {
          errors.push('رقم السجل التجاري غير صحيح');
          isValid = false;
        }
        if (!data.businessName || data.businessName.length < 3) {
          errors.push('اسم المنشأة غير صحيح');
          isValid = false;
        }
        break;
        
      case 'freelance_document':
        if (!data.documentNumber || data.documentNumber.length < 8) {
          errors.push('رقم وثيقة العمل الحر غير صحيح');
          isValid = false;
        }
        break;
        
      case 'driving_license':
        if (!data.licenseNumber || !/^\d{10}$/.test(data.licenseNumber)) {
          errors.push('رقم رخصة القيادة غير صحيح');
          isValid = false;
        }
        break;
    }

    return {
      isValid,
      errors,
      confidence: isValid ? 0.9 : 0.3
    };
  }

  /**
   * كشف الاحتيال
   */
  private static async detectFraud(data: any, documentType: string) {
    try {
      // تحميل نموذج كشف الاحتيال
      const fraudModel = await modelLoader.getModel('fraud_detector');
      
      // تشغيل كشف الاحتيال المحلي
      const fraudDetection = this.detectFraudLocally(data, documentType);
      
      return fraudDetection;
      
    } catch (error) {
      console.warn('⚠️ فشل في كشف الاحتيال:', error);
      return {
        isFraudulent: false,
        riskLevel: 0,
        indicators: []
      };
    }
  }

  /**
   * كشف الاحتيال المحلي
   */
  private static detectFraudLocally(data: any, documentType: string) {
    const indicators: string[] = [];
    let riskLevel = 0;

    // فحص التواريخ
    if (data.issueDate && data.expiryDate) {
      const issueDate = new Date(data.issueDate);
      const expiryDate = new Date(data.expiryDate);
      
      if (expiryDate <= issueDate) {
        indicators.push('تاريخ الانتهاء قبل تاريخ الإصدار');
        riskLevel += 30;
      }
      
      if (expiryDate < new Date()) {
        indicators.push('المستند منتهي الصلاحية');
        riskLevel += 20;
      }
    }

    // فحص الأرقام
    if (documentType === 'commercial_registration' && data.registrationNumber) {
      if (!/^\d{10}$/.test(data.registrationNumber)) {
        indicators.push('تنسيق رقم السجل التجاري غير صحيح');
        riskLevel += 25;
      }
    }

    return {
      isFraudulent: riskLevel > 50,
      riskLevel,
      indicators
    };
  }

  /**
   * حساب مستوى الثقة
   */
  private static calculateConfidence(
    ocrResult: any,
    classification: any,
    validation: any,
    fraudDetection: any
  ): number {
    const weights = {
      ocr: 0.3,
      classification: 0.2,
      validation: 0.3,
      fraud: 0.2
    };

    const scores = {
      ocr: ocrResult.confidence || 0.7,
      classification: classification.confidence || 0.7,
      validation: validation.confidence || 0.7,
      fraud: fraudDetection.isFraudulent ? 0.3 : 0.9
    };

    const weightedScore = Object.entries(weights).reduce((total, [key, weight]) => {
      return total + (scores[key as keyof typeof scores] * weight);
    }, 0);

    return Math.round(weightedScore * 100) / 100;
  }

  /**
   * التحقق من السجل التجاري
   */
  private static validateCommercialRegistration(data: any): boolean {
    return !!(data.businessName && data.ownerName && data.registrationNumber);
  }

  /**
   * التحقق من وثيقة العمل الحر
   */
  private static validateFreelanceDocument(data: any): boolean {
    return !!(data.ownerName && data.documentNumber);
  }

  /**
   * التحقق من رخصة القيادة
   */
  private static validateDrivingLicense(data: any): boolean {
    return !!(data.holderName && data.licenseNumber);
  }

  /**
   * تنظيف الخدمة
   */
  static async cleanup() {
    console.log('🧹 تنظيف خدمة الذكاء الاصطناعي المحلي...');
    
    await modelLoader.cleanup();
    await localOCREngine.cleanup();
    
    this.initialized = false;
    console.log('✅ تم تنظيف خدمة الذكاء الاصطناعي المحلي');
  }
}
