{"general": {"platformName": "مخلة - منصة التجارة المحلية", "platformDescription": "منصة تربط التجار المحليين بالعملاء", "contactEmail": "<EMAIL>", "contactPhone": "+966501234567", "supportEmail": "<EMAIL>", "defaultLanguage": "ar", "timezone": "Asia/Riyadh", "currency": "SAR", "logo": "/images/logo.png", "favicon": "/images/favicon.ico"}, "commission": {"merchantCommission": 5, "merchantCommissionType": "percentage", "representativeCommission": 10, "representativeCommissionType": "fixed", "platformCommission": 2.5, "enableTieredCommission": true, "tiers": [{"threshold": 1000, "rate": 3}, {"threshold": 5000, "rate": 2.5}, {"threshold": 10000, "rate": 2}]}, "notifications": {"emailEnabled": true, "pushEnabled": true, "smsEnabled": true, "email": {"smtpServer": "smtp.gmail.com", "smtpPort": 587, "smtpUsername": "<EMAIL>", "smtpPassword": "encrypted_password", "fromName": "مخلة", "fromEmail": "<EMAIL>"}, "push": {"firebaseServerKey": "encrypted_firebase_key", "vapidKey": "encrypted_vapid_key"}, "sms": {"provider": "twi<PERSON>", "apiKey": "encrypted_sms_key", "apiSecret": "encrypted_sms_secret", "fromNumber": "+966501234567"}}, "payment": {"enabledMethods": ["card", "cod", "apple_pay", "stc_pay", "mada"], "primaryGateway": "hyperpay", "gateways": {"hyperpay": {"apiKey": "encrypted_hyperpay_key", "apiSecret": "encrypted_hyperpay_secret", "testMode": false}, "stripe": {"publishableKey": "encrypted_stripe_public", "secretKey": "encrypted_stripe_secret", "testMode": false}}, "currency": {"default": "SAR", "symbol": "ر.س", "position": "after"}}, "security": {"passwordPolicy": {"minLength": 8, "requireUppercase": true, "requireLowercase": true, "requireNumbers": true, "requireSpecialChars": true, "maxAge": 90}, "twoFactorAuth": {"enabled": true, "method": "sms", "required": false}, "session": {"timeout": 30, "maxLoginAttempts": 5, "lockoutDuration": 15}, "encryption": {"algorithm": "AES-256-GCM", "keyRotationDays": 30}}, "backup": {"autoBackupEnabled": true, "frequency": "daily", "time": "02:00", "storage": "cloud", "retention": 30, "encryption": true, "destinations": [{"type": "aws_s3", "bucket": "mikhla-backups", "region": "me-south-1"}, {"type": "google_drive", "folderId": "backup_folder_id"}]}, "features": {"enableReviews": true, "enableWishlist": true, "enableLoyaltyProgram": true, "enableCoupons": true, "enableInventoryTracking": true, "enableMultiLanguage": true, "enableGeolocation": true, "enablePushNotifications": true}, "limits": {"maxProductsPerMerchant": 1000, "maxImagesPerProduct": 10, "maxFileSize": "10MB", "maxOrdersPerDay": 100, "maxUsersPerMerchant": 50}, "integrations": {"googleMaps": {"apiKey": "encrypted_google_maps_key", "enabled": true}, "firebase": {"projectId": "mikhla-production", "apiKey": "encrypted_firebase_key", "enabled": true}, "cloudinary": {"cloudName": "mikhla", "apiKey": "encrypted_cloudinary_key", "enabled": true}}}