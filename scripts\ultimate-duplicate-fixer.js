#!/usr/bin/env node

/**
 * سكريبت نهائي لإصلاح جميع المفاتيح المكررة في ملفات الترجمة
 * يستخدم معالجة JSON مباشرة لضمان الدقة
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  fs.copyFileSync(filePath, backupPath);
  console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
  return backupPath;
}

/**
 * إصلاح المفاتيح المكررة نهائياً
 */
function ultimateFixDuplicates(filePath) {
  console.log(`\n🔧 إصلاح نهائي للمفاتيح المكررة في: ${path.basename(filePath)}`);

  // إنشاء نسخة احتياطية
  createBackup(filePath);

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // تحليل JSON
    const jsonData = JSON.parse(content);
    
    // إعادة كتابة الملف بتنسيق نظيف
    const cleanedContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync(filePath, cleanedContent, 'utf8');
    
    console.log(`✅ تم تنظيف وإعادة تنسيق الملف: ${path.basename(filePath)}`);
    
    return true;
    
  } catch (error) {
    console.error(`❌ خطأ في إصلاح الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * إضافة الترجمات المفقودة الأساسية
 */
function addCriticalMissingTranslations() {
  console.log('\n🔧 إضافة الترجمات المفقودة الحرجة...');
  
  // الترجمات الحرجة المفقودة
  const criticalTranslations = {
    ar: {
      "representative": "مندوب",
      "reviews": "التقييمات",
      "viewStore": "عرض المتجر",
      "contactAvailable": "التواصل متاح",
      "websiteAvailable": "الموقع متاح",
      "locationPermissionPrompt": "يرجى السماح بالوصول للموقع",
      "loadingMap": "جاري تحميل الخريطة...",
      "yourLocation": "موقعك",
      "checkout": "الدفع",
      "customerInformation": "معلومات العميل",
      "shippingAddress": "عنوان الشحن",
      "paymentMethod": "طريقة الدفع",
      "orderSummary": "ملخص الطلب",
      "placeOrder": "تأكيد الطلب",
      "advancedSearch": "البحث المتقدم",
      "searchFilters": "فلاتر البحث",
      "sortBy": "ترتيب حسب",
      "filterByCategory": "تصفية حسب الفئة",
      "priceRange": "نطاق السعر",
      "storesFound": "تم العثور على {{count}} متجر",
      "profileUpdateFailed": "فشل في تحديث الملف الشخصي",
      "logoutFailed": "فشل في تسجيل الخروج",
      "yourCurrentLocation": "موقعك الحالي",
      "latitude": "خط العرض",
      "longitude": "خط الطول"
    },
    en: {
      "representative": "Representative",
      "reviews": "Reviews",
      "viewStore": "View Store",
      "contactAvailable": "Contact Available",
      "websiteAvailable": "Website Available",
      "locationPermissionPrompt": "Please allow location access",
      "loadingMap": "Loading map...",
      "yourLocation": "Your Location",
      "checkout": "Checkout",
      "customerInformation": "Customer Information",
      "shippingAddress": "Shipping Address",
      "paymentMethod": "Payment Method",
      "orderSummary": "Order Summary",
      "placeOrder": "Place Order",
      "advancedSearch": "Advanced Search",
      "searchFilters": "Search Filters",
      "sortBy": "Sort By",
      "filterByCategory": "Filter by Category",
      "priceRange": "Price Range",
      "storesFound": "Found {{count}} stores",
      "profileUpdateFailed": "Profile update failed",
      "logoutFailed": "Logout failed",
      "yourCurrentLocation": "Your Current Location",
      "latitude": "Latitude",
      "longitude": "Longitude",
      "sar": "SAR",
      "status": "Status",
      "processing": "Processing",
      "cancel": "Cancel",
      "loading": "Loading...",
      "close": "Close",
      "actions": "Actions",
      "category": "Category",
      "price": "Price",
      "stock": "Stock",
      "active": "Active",
      "inactive": "Inactive",
      "delete": "Delete",
      "back": "Back",
      "next": "Next",
      "previous": "Previous",
      "search": "Search",
      "filter": "Filter",
      "store": "Store",
      "available": "Available",
      "outOfStock": "Out of Stock",
      "address": "Address",
      "businessHours": "Business Hours",
      "totalOrders": "Total Orders",
      "productNotFound": "Product Not Found",
      "storeNotFound": "Store Not Found",
      "description": "Description",
      "moderationNotes": "Moderation Notes"
    }
  };
  
  // إضافة الترجمات للملف العربي
  try {
    const arContent = fs.readFileSync(AR_TRANSLATIONS_PATH, 'utf8');
    const arTranslations = JSON.parse(arContent);
    
    let addedCount = 0;
    Object.entries(criticalTranslations.ar).forEach(([key, value]) => {
      if (!arTranslations[key]) {
        arTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للعربية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(AR_TRANSLATIONS_PATH, JSON.stringify(arTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة عربية جديدة`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات العربية:', error.message);
  }
  
  // إضافة الترجمات للملف الإنجليزي
  try {
    const enContent = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const enTranslations = JSON.parse(enContent);
    
    let addedCount = 0;
    Object.entries(criticalTranslations.en).forEach(([key, value]) => {
      if (!enTranslations[key]) {
        enTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للإنجليزية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(EN_TRANSLATIONS_PATH, JSON.stringify(enTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة إنجليزية جديدة`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الترجمات الإنجليزية:', error.message);
  }
}

/**
 * إضافة ترجمات المندوبين المفقودة
 */
function addRepresentativeTranslations() {
  console.log('\n🔧 إضافة ترجمات المندوبين المفقودة...');
  
  const representativeTranslations = {
    ar: {
      "representative.dashboard.welcome": "مرحباً بك، {{name}}!",
      "representative.dashboard.subtitle": "إدارة طلبات التوصيل والأرباح من هنا",
      "representative.dashboard.completed": "مكتملة",
      "representative.dashboard.onTimeRate": "معدل التوصيل في الوقت المحدد",
      "representative.dashboard.avgDeliveryTime": "متوسط وقت التوصيل"
    },
    en: {
      "representative.dashboard.welcome": "Welcome, {{name}}!",
      "representative.dashboard.subtitle": "Manage delivery orders and earnings from here",
      "representative.dashboard.completed": "Completed",
      "representative.dashboard.onTimeRate": "On-time delivery rate",
      "representative.dashboard.avgDeliveryTime": "Average delivery time"
    }
  };
  
  // إضافة ترجمات المندوبين للعربية
  try {
    const arContent = fs.readFileSync(AR_TRANSLATIONS_PATH, 'utf8');
    const arTranslations = JSON.parse(arContent);
    
    let addedCount = 0;
    Object.entries(representativeTranslations.ar).forEach(([key, value]) => {
      if (!arTranslations[key]) {
        arTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للعربية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(AR_TRANSLATIONS_PATH, JSON.stringify(arTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة مندوب عربية`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة ترجمات المندوبين العربية:', error.message);
  }
  
  // إضافة ترجمات المندوبين للإنجليزية
  try {
    const enContent = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const enTranslations = JSON.parse(enContent);
    
    let addedCount = 0;
    Object.entries(representativeTranslations.en).forEach(([key, value]) => {
      if (!enTranslations[key]) {
        enTranslations[key] = value;
        addedCount++;
        console.log(`✅ أضيف للإنجليزية: "${key}"`);
      }
    });
    
    if (addedCount > 0) {
      fs.writeFileSync(EN_TRANSLATIONS_PATH, JSON.stringify(enTranslations, null, 2), 'utf8');
      console.log(`✅ تم إضافة ${addedCount} ترجمة مندوب إنجليزية`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في إضافة ترجمات المندوبين الإنجليزية:', error.message);
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء الإصلاح النهائي لجميع مشاكل الترجمات...\n');

  try {
    // إصلاح المفاتيح المكررة نهائياً
    const arFixed = ultimateFixDuplicates(AR_TRANSLATIONS_PATH);
    const enFixed = ultimateFixDuplicates(EN_TRANSLATIONS_PATH);

    // إضافة الترجمات المفقودة الحرجة
    addCriticalMissingTranslations();

    // إضافة ترجمات المندوبين
    addRepresentativeTranslations();

    console.log('\n📋 ملخص الإصلاحات:');
    console.log(`✅ تم إصلاح الملف العربي: ${arFixed ? 'نجح' : 'فشل'}`);
    console.log(`✅ تم إصلاح الملف الإنجليزي: ${enFixed ? 'نجح' : 'فشل'}`);
    console.log('✅ تم إضافة الترجمات المفقودة الحرجة');
    console.log('✅ تم إضافة ترجمات المندوبين');

    console.log('\n🎉 تم الانتهاء من الإصلاح النهائي بنجاح!');
    console.log('💡 يُنصح بتشغيل سكريبت التحقق للتأكد من الإصلاحات:');
    console.log('   node scripts/validate-translations.js');

  } catch (error) {
    console.error('\n❌ فشل في تنفيذ الإصلاحات:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  ultimateFixDuplicates,
  addCriticalMissingTranslations,
  addRepresentativeTranslations,
  createBackup
};
