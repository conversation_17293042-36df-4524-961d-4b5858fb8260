// نموذج بيانات الشروط والأحكام
export interface TermsAndConditions {
  id: string;
  title: string;
  content: string;
  userType: 'general' | 'customer' | 'merchant' | 'representative';
  locale: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

// نموذج طلب الشروط والأحكام
export interface TermsRequestParams {
  userType?: 'general' | 'customer' | 'merchant' | 'representative';
  locale: string;
  version?: string; // إذا لم يتم تحديد الإصدار، سيتم استرجاع أحدث إصدار نشط
}

// نموذج استجابة الشروط والأحكام
export interface TermsResponse {
  success: boolean;
  data?: TermsAndConditions;
  error?: string;
}

// نموذج بيانات الشروط للقاعدة
export interface TermsData {
  id: string;
  userType: string;
  locale: string;
  title: string;
  content: string;
  version: string;
  effectiveDate: Date;
  lastUpdated: Date;
  isActive: boolean;
}
