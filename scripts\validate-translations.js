#!/usr/bin/env node

/**
 * سكريبت شامل للتحقق من صحة الترجمات ومطابقتها
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * استخراج جميع المفاتيح من كائن مترجم (بما في ذلك المفاتيح المتداخلة)
 */
function extractAllKeys(obj, prefix = '') {
  const keys = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        // إذا كان الكائن متداخل، استخرج المفاتيح الفرعية
        keys.push(...extractAllKeys(obj[key], fullKey));
      } else {
        // إذا كان قيمة نهائية، أضف المفتاح
        keys.push(fullKey);
      }
    }
  }
  
  return keys;
}

/**
 * البحث عن المفاتيح المكررة في ملف JSON
 */
function findDuplicateKeys(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const keyOccurrences = {};
    const duplicates = [];
    
    lines.forEach((line, index) => {
      const match = line.match(/^\s*"([^"]+)"\s*:/);
      if (match) {
        const key = match[1];
        if (keyOccurrences[key]) {
          keyOccurrences[key].push(index + 1);
        } else {
          keyOccurrences[key] = [index + 1];
        }
      }
    });
    
    Object.keys(keyOccurrences).forEach(key => {
      if (keyOccurrences[key].length > 1) {
        duplicates.push({
          key,
          lines: keyOccurrences[key]
        });
      }
    });
    
    return duplicates;
  } catch (error) {
    console.error(`❌ خطأ في فحص المفاتيح المكررة:`, error.message);
    return [];
  }
}

/**
 * التحقق من صحة استخدام المتغيرات في الترجمات
 */
function validateVariableUsage(arTranslations, enTranslations) {
  const issues = [];
  const arKeys = extractAllKeys(arTranslations);

  arKeys.forEach(key => {
    const arValue = getNestedValue(arTranslations, key);
    const enValue = getNestedValue(enTranslations, key);

    if (arValue && enValue && typeof arValue === 'string' && typeof enValue === 'string') {
      // البحث عن المتغيرات في النص العربي
      const arVariables = arValue.match(/\{\{[^}]+\}\}/g) || [];
      const enVariables = enValue.match(/\{\{[^}]+\}\}/g) || [];

      // مقارنة المتغيرات
      if (arVariables.length !== enVariables.length) {
        issues.push({
          key,
          issue: 'عدد المتغيرات غير متطابق',
          arabic: arValue,
          english: enValue,
          arVariables,
          enVariables
        });
      } else {
        // التحقق من أسماء المتغيرات
        const arVarNames = arVariables.map(v => v.replace(/[{}]/g, '')).sort();
        const enVarNames = enVariables.map(v => v.replace(/[{}]/g, '')).sort();

        if (JSON.stringify(arVarNames) !== JSON.stringify(enVarNames)) {
          issues.push({
            key,
            issue: 'أسماء المتغيرات غير متطابقة',
            arabic: arValue,
            english: enValue,
            arVariables: arVarNames,
            enVariables: enVarNames
          });
        }
      }
    }
  });

  return issues;
}

/**
 * الحصول على قيمة متداخلة من كائن
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔍 بدء التحقق من صحة الترجمات...\n');
  
  // قراءة ملفات الترجمة
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    process.exit(1);
  }
  
  console.log('✅ تم تحميل ملفات الترجمة بنجاح\n');
  
  // 1. فحص المفاتيح المكررة
  console.log('🔍 فحص المفاتيح المكررة...');
  const arDuplicates = findDuplicateKeys(AR_TRANSLATIONS_PATH);
  const enDuplicates = findDuplicateKeys(EN_TRANSLATIONS_PATH);
  
  if (arDuplicates.length > 0) {
    console.log('❌ مفاتيح مكررة في الملف العربي:');
    arDuplicates.forEach(dup => {
      console.log(`   - "${dup.key}" في الأسطر: ${dup.lines.join(', ')}`);
    });
  } else {
    console.log('✅ لا توجد مفاتيح مكررة في الملف العربي');
  }
  
  if (enDuplicates.length > 0) {
    console.log('❌ مفاتيح مكررة في الملف الإنجليزي:');
    enDuplicates.forEach(dup => {
      console.log(`   - "${dup.key}" في الأسطر: ${dup.lines.join(', ')}`);
    });
  } else {
    console.log('✅ لا توجد مفاتيح مكررة في الملف الإنجليزي');
  }
  
  console.log('');
  
  // 2. مقارنة عدد المفاتيح
  const arKeys = extractAllKeys(arTranslations);
  const enKeys = extractAllKeys(enTranslations);
  
  console.log('📊 إحصائيات الترجمة:');
  console.log(`   - المفاتيح العربية: ${arKeys.length}`);
  console.log(`   - المفاتيح الإنجليزية: ${enKeys.length}`);
  console.log(`   - الفرق: ${Math.abs(arKeys.length - enKeys.length)}`);
  console.log('');
  
  // 3. العثور على المفاتيح المفقودة
  const missingInEnglish = arKeys.filter(key => !enKeys.includes(key));
  const missingInArabic = enKeys.filter(key => !arKeys.includes(key));
  
  if (missingInEnglish.length > 0) {
    console.log(`❌ مفاتيح مفقودة في الإنجليزية (${missingInEnglish.length}):`);
    missingInEnglish.slice(0, 10).forEach(key => {
      console.log(`   - ${key}`);
    });
    if (missingInEnglish.length > 10) {
      console.log(`   ... و ${missingInEnglish.length - 10} مفتاح آخر`);
    }
    console.log('');
  }
  
  if (missingInArabic.length > 0) {
    console.log(`❌ مفاتيح مفقودة في العربية (${missingInArabic.length}):`);
    missingInArabic.slice(0, 10).forEach(key => {
      console.log(`   - ${key}`);
    });
    if (missingInArabic.length > 10) {
      console.log(`   ... و ${missingInArabic.length - 10} مفتاح آخر`);
    }
    console.log('');
  }
  
  // 4. التحقق من صحة المتغيرات
  console.log('🔍 فحص صحة المتغيرات...');
  const variableIssues = validateVariableUsage(arTranslations, enTranslations);
  
  if (variableIssues.length > 0) {
    console.log(`❌ مشاكل في المتغيرات (${variableIssues.length}):`);
    variableIssues.slice(0, 5).forEach(issue => {
      console.log(`   - ${issue.key}: ${issue.issue}`);
      console.log(`     العربية: "${issue.arabic}"`);
      console.log(`     الإنجليزية: "${issue.english}"`);
      console.log('');
    });
    if (variableIssues.length > 5) {
      console.log(`   ... و ${variableIssues.length - 5} مشكلة أخرى`);
    }
  } else {
    console.log('✅ جميع المتغيرات صحيحة');
  }
  
  // 5. ملخص النتائج
  console.log('\n📋 ملخص النتائج:');
  const totalIssues = arDuplicates.length + enDuplicates.length + missingInEnglish.length + missingInArabic.length + variableIssues.length;
  
  if (totalIssues === 0) {
    console.log('🎉 ممتاز! جميع الترجمات صحيحة ومتطابقة');
  } else {
    console.log(`⚠️  تم العثور على ${totalIssues} مشكلة تحتاج إلى إصلاح:`);
    if (arDuplicates.length > 0) console.log(`   - ${arDuplicates.length} مفتاح مكرر في العربية`);
    if (enDuplicates.length > 0) console.log(`   - ${enDuplicates.length} مفتاح مكرر في الإنجليزية`);
    if (missingInEnglish.length > 0) console.log(`   - ${missingInEnglish.length} مفتاح مفقود في الإنجليزية`);
    if (missingInArabic.length > 0) console.log(`   - ${missingInArabic.length} مفتاح مفقود في العربية`);
    if (variableIssues.length > 0) console.log(`   - ${variableIssues.length} مشكلة في المتغيرات`);
  }
  
  console.log('\n✅ انتهى فحص الترجمات');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  extractAllKeys,
  findDuplicateKeys,
  validateVariableUsage,
  getNestedValue
};
