describe('اختبار ترجمات السلة - Cart Translations Test', () => {
  beforeEach(() => {
    // زيارة الصفحة الرئيسية
    cy.visit('/ar');
    cy.wait(2000);
  });

  it('يجب أن تظهر ترجمات السلة الفارغة بشكل صحيح باللغة العربية', () => {
    // البحث عن زر السلة وفتحها
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // التحقق من وجود النصوص المترجمة وليس مفاتيح الترجمة
    cy.get('[data-testid="cart-sidebar"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        // التحقق من عنوان السلة
        cy.contains('السلة').should('be.visible');
        
        // التحقق من رسالة السلة الفارغة
        cy.contains('السلة فارغة').should('be.visible');
        cy.contains('لم تقم بإضافة أي منتجات للسلة بعد').should('be.visible');
        
        // التحقق من زر "ابدأ التسوق"
        cy.contains('ابدأ التسوق').should('be.visible');
        
        // التأكد من عدم وجود مفاتيح الترجمة غير المترجمة
        cy.should('not.contain', 'emptyCart');
        cy.should('not.contain', 'emptyCartDescription');
        cy.should('not.contain', 'startShopping');
      });
  });

  it('يجب أن تظهر ترجمات السلة الفارغة بشكل صحيح باللغة الإنجليزية', () => {
    // تغيير اللغة إلى الإنجليزية
    cy.visit('/en');
    cy.wait(2000);

    // البحث عن زر السلة وفتحها
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // التحقق من وجود النصوص المترجمة وليس مفاتيح الترجمة
    cy.get('[data-testid="cart-sidebar"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        // التحقق من عنوان السلة
        cy.contains('Cart').should('be.visible');
        
        // التحقق من رسالة السلة الفارغة
        cy.contains('Cart is empty').should('be.visible');
        cy.contains('You haven\'t added any products to your cart yet').should('be.visible');
        
        // التحقق من زر "Start Shopping"
        cy.contains('Start Shopping').should('be.visible');
        
        // التأكد من عدم وجود مفاتيح الترجمة غير المترجمة
        cy.should('not.contain', 'emptyCart');
        cy.should('not.contain', 'emptyCartDescription');
        cy.should('not.contain', 'startShopping');
      });
  });

  it('يجب أن يعمل زر "ابدأ التسوق" بشكل صحيح', () => {
    // فتح السلة
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // النقر على زر "ابدأ التسوق"
    cy.get('[data-testid="cart-sidebar"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        cy.contains('ابدأ التسوق')
          .should('be.visible')
          .click();
      });

    // التحقق من إغلاق السلة
    cy.get('[data-testid="cart-sidebar"]').should('not.exist');
  });

  it('يجب أن تظهر حالة التحميل بشكل صحيح', () => {
    // محاكاة حالة التحميل (إذا كانت متاحة)
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // التحقق من وجود النص المترجم لحالة التحميل (إذا ظهرت)
    cy.get('body').then(($body) => {
      if ($body.text().includes('جاري تحميل السلة')) {
        cy.contains('جاري تحميل السلة').should('be.visible');
        cy.should('not.contain', 'loadingCart');
      }
    });
  });

  it('يجب أن تعمل ترجمات السلة مع تبديل اللغة', () => {
    // فتح السلة باللغة العربية
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // التحقق من النصوص العربية
    cy.get('[data-testid="cart-sidebar"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        cy.contains('السلة فارغة').should('be.visible');
      });

    // إغلاق السلة
    cy.get('[data-testid="cart-sidebar"]').within(() => {
      cy.get('button[aria-label="Close"]').click();
    });

    // تغيير اللغة إلى الإنجليزية
    cy.get('[data-testid="language-switcher"]', { timeout: 5000 })
      .should('be.visible')
      .click();

    cy.contains('English').click();
    cy.wait(2000);

    // فتح السلة مرة أخرى
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // التحقق من النصوص الإنجليزية
    cy.get('[data-testid="cart-sidebar"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        cy.contains('Cart is empty').should('be.visible');
      });
  });

  it('يجب أن تكون جميع عناصر السلة قابلة للوصول', () => {
    // فتح السلة
    cy.get('[data-testid="cart-trigger"]', { timeout: 10000 })
      .should('be.visible')
      .click();

    // التحقق من إمكانية الوصول للعناصر
    cy.get('[data-testid="cart-sidebar"]', { timeout: 5000 })
      .should('be.visible')
      .within(() => {
        // التحقق من وجود العناوين المناسبة
        cy.get('h3').should('contain', 'السلة فارغة');
        
        // التحقق من وجود الأزرار القابلة للنقر
        cy.get('button').contains('ابدأ التسوق').should('be.visible').and('not.be.disabled');
        
        // التحقق من وجود النصوص الوصفية
        cy.get('p').should('contain', 'لم تقم بإضافة أي منتجات للسلة بعد');
      });
  });
});
