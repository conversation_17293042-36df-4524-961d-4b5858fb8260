"use client";

import { useParams } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import type { Locale } from '@/lib/i18n';
import { getTranslation } from '@/lib/i18n'; // browser-compatible version

/**
 * نظام التعرف التلقائي على لغة المستخدم
 * يحلل إعدادات المتصفح والجهاز لتحديد اللغة المفضلة
 */
export function detectUserLanguage(): Locale {
  if (typeof window === 'undefined') return 'en';

  try {
    // 1. فحص اللغة المحفوظة في localStorage
    const savedLanguage = localStorage.getItem('preferred-language') as Locale;
    if (savedLanguage && ['en', 'ar'].includes(savedLanguage)) {
      console.log('🌍 [Language Detection] استخدام اللغة المحفوظة:', savedLanguage);
      return savedLanguage;
    }

    // 2. فحص لغة المتصفح الأساسية
    const browserLanguage = navigator.language.toLowerCase();
    console.log('🌍 [Language Detection] لغة المتصفح:', browserLanguage);

    // 3. فحص جميع اللغات المفضلة للمستخدم
    const userLanguages = navigator.languages || [navigator.language];
    console.log('🌍 [Language Detection] اللغات المفضلة:', userLanguages);

    // 4. البحث عن العربية في اللغات المفضلة
    const hasArabic = userLanguages.some(lang =>
      lang.toLowerCase().startsWith('ar') ||
      lang.toLowerCase().includes('arab')
    );

    if (hasArabic) {
      console.log('🌍 [Language Detection] تم اكتشاف العربية في اللغات المفضلة');
      return 'ar';
    }

    // 5. فحص المنطقة الزمنية (البلدان العربية)
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const arabicTimezones = [
      'Asia/Riyadh', 'Asia/Kuwait', 'Asia/Qatar', 'Asia/Bahrain',
      'Asia/Dubai', 'Asia/Muscat', 'Asia/Baghdad', 'Asia/Damascus',
      'Asia/Beirut', 'Asia/Amman', 'Africa/Cairo', 'Africa/Tunis',
      'Africa/Algiers', 'Africa/Casablanca', 'Asia/Gaza', 'Asia/Hebron'
    ];

    if (arabicTimezones.includes(timezone)) {
      console.log('🌍 [Language Detection] تم اكتشاف منطقة زمنية عربية:', timezone);
      return 'ar';
    }

    // 6. فحص إعدادات التاريخ والأرقام
    const numberFormat = new Intl.NumberFormat().resolvedOptions();
    if (numberFormat.locale && numberFormat.locale.startsWith('ar')) {
      console.log('🌍 [Language Detection] تم اكتشاف إعدادات أرقام عربية');
      return 'ar';
    }

    // 7. الافتراضي: الإنجليزية
    console.log('🌍 [Language Detection] استخدام اللغة الافتراضية: en');
    return 'en';

  } catch (error) {
    console.warn('⚠️ [Language Detection] خطأ في اكتشاف اللغة:', error);
    return 'en';
  }
}

/**
 * حفظ اللغة المفضلة للمستخدم
 */
export function saveUserLanguagePreference(locale: Locale): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('preferred-language', locale);
    localStorage.setItem('language-detection-timestamp', Date.now().toString());
    console.log('💾 [Language Detection] تم حفظ اللغة المفضلة:', locale);
  } catch (error) {
    console.warn('⚠️ [Language Detection] خطأ في حفظ اللغة:', error);
  }
}

export function useLocale() {
  const params = useParams();
  const currentLocale = (params.locale as Locale) || 'en';

  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastLoadedLocale, setLastLoadedLocale] = useState<Locale | null>(null);
  const [detectedLanguage, setDetectedLanguage] = useState<Locale | null>(null);

  // تشغيل اكتشاف اللغة عند التحميل الأول
  useEffect(() => {
    const detected = detectUserLanguage();
    setDetectedLanguage(detected);
    console.log('🌍 [useLocale] اللغة المكتشفة:', detected);
  }, []);

  useEffect(() => {
    let isMounted = true;

    console.log('🔄 [useLocale] useEffect triggered');
    console.log('🔄 [useLocale] currentLocale:', currentLocale);
    console.log('🔄 [useLocale] lastLoadedLocale:', lastLoadedLocale);
    console.log('🔄 [useLocale] translations count:', Object.keys(translations).length);

    async function loadTranslations() {
      // تجنب التحميل إذا كانت اللغة نفسها محملة بالفعل
      if (lastLoadedLocale === currentLocale && Object.keys(translations).length > 0) {
        console.log('✅ [useLocale] تخطي التحميل - الترجمات محملة بالفعل');
        setIsLoading(false); // تأكد من إيقاف التحميل
        return;
      }

      console.log('🔄 [useLocale] بدء تحميل الترجمات للغة:', currentLocale);
      setIsLoading(true);

      try {
        const loadedTranslations = await getTranslation(currentLocale);
        console.log('✅ [useLocale] تم تحميل الترجمات بنجاح، عدد المفاتيح:', Object.keys(loadedTranslations).length);

        // التحقق من أن المكون لا يزال mounted
        if (isMounted) {
          setTranslations(loadedTranslations);
          setLastLoadedLocale(currentLocale);
          console.log('✅ [useLocale] تم تحديث state بنجاح');
        } else {
          console.log('⚠️ [useLocale] المكون غير mounted، تم تجاهل التحديث');
        }
      } catch (error) {
        console.error('❌ [useLocale] خطأ في تحميل الترجمات:', error);
        if (isMounted) {
          setTranslations({}); // Fallback to empty or default keys
          setLastLoadedLocale(currentLocale);
          console.log('⚠️ [useLocale] تم تعيين ترجمات فارغة كحل بديل');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
          console.log('✅ [useLocale] انتهى تحميل الترجمات');
        }
      }
    }

    // إعادة تعيين الترجمات فقط عند تغيير اللغة فعلياً
    if (lastLoadedLocale !== null && lastLoadedLocale !== currentLocale) {
      console.log('🔄 [useLocale] تغيير اللغة من', lastLoadedLocale, 'إلى', currentLocale);
      setTranslations({});
      setIsLoading(true);
    }

    loadTranslations();

    // Cleanup function
    return () => {
      isMounted = false;
      console.log('🧹 [useLocale] cleanup - المكون غير mounted');
    };
  }, [currentLocale]); // الاحتفاظ بـ currentLocale فقط

  const t = useCallback((key: string, params?: Record<string, string | number>): string => {
    if (isLoading) {
      // Return empty string during loading to avoid showing English keys
      return '';
    }

    let translation = translations[key];

    // If translation is not found, log it for debugging and return the key
    if (!translation) {
      // تقليل رسائل الترجمات المفقودة
      if (process.env.NODE_ENV === 'development' && !key.startsWith('_')) {
        console.warn(`Missing translation: "${key}" (${currentLocale})`);
      }
      return key;
    }

    // Critical fix: Ensure translation is always a string, never an object
    if (typeof translation === 'object') {
      console.error(`❌ Translation for "${key}" is an object, not a string:`, translation);
      return key; // Return the key as fallback
    }

    // Handle parameter substitution
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(value));
      });
    }

    return String(translation); // Ensure we always return a string
  }, [translations, isLoading, currentLocale]);

  // وظيفة لحفظ اللغة المفضلة
  const saveLanguagePreference = useCallback((locale: Locale) => {
    saveUserLanguagePreference(locale);
  }, []);

  // وظيفة للحصول على اللغة المكتشفة
  const getDetectedLanguage = useCallback(() => {
    return detectedLanguage || detectUserLanguage();
  }, [detectedLanguage]);

  return {
    locale: currentLocale,
    t,
    translations,
    isLoading,
    detectedLanguage,
    saveLanguagePreference,
    getDetectedLanguage
  };
}
