"use client";

import { useParams } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import type { Locale } from '@/lib/i18n';
import { getTranslation } from '@/lib/i18n'; // browser-compatible version

export function useLocale() {
  const params = useParams();
  const currentLocale = (params.locale as Locale) || 'en';

  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastLoadedLocale, setLastLoadedLocale] = useState<Locale | null>(null);

  useEffect(() => {
    let isMounted = true;

    console.log('🔄 [useLocale] useEffect triggered');
    console.log('🔄 [useLocale] currentLocale:', currentLocale);
    console.log('🔄 [useLocale] lastLoadedLocale:', lastLoadedLocale);
    console.log('🔄 [useLocale] translations count:', Object.keys(translations).length);

    async function loadTranslations() {
      // تجنب التحميل إذا كانت اللغة نفسها محملة بالفعل
      if (lastLoadedLocale === currentLocale && Object.keys(translations).length > 0) {
        console.log('✅ [useLocale] تخطي التحميل - الترجمات محملة بالفعل');
        setIsLoading(false); // تأكد من إيقاف التحميل
        return;
      }

      console.log('🔄 [useLocale] بدء تحميل الترجمات للغة:', currentLocale);
      setIsLoading(true);

      try {
        const loadedTranslations = await getTranslation(currentLocale);
        console.log('✅ [useLocale] تم تحميل الترجمات بنجاح، عدد المفاتيح:', Object.keys(loadedTranslations).length);

        // التحقق من أن المكون لا يزال mounted
        if (isMounted) {
          setTranslations(loadedTranslations);
          setLastLoadedLocale(currentLocale);
          console.log('✅ [useLocale] تم تحديث state بنجاح');
        } else {
          console.log('⚠️ [useLocale] المكون غير mounted، تم تجاهل التحديث');
        }
      } catch (error) {
        console.error('❌ [useLocale] خطأ في تحميل الترجمات:', error);
        if (isMounted) {
          setTranslations({}); // Fallback to empty or default keys
          setLastLoadedLocale(currentLocale);
          console.log('⚠️ [useLocale] تم تعيين ترجمات فارغة كحل بديل');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
          console.log('✅ [useLocale] انتهى تحميل الترجمات');
        }
      }
    }

    // إعادة تعيين الترجمات فقط عند تغيير اللغة فعلياً
    if (lastLoadedLocale !== null && lastLoadedLocale !== currentLocale) {
      console.log('🔄 [useLocale] تغيير اللغة من', lastLoadedLocale, 'إلى', currentLocale);
      setTranslations({});
      setIsLoading(true);
    }

    loadTranslations();

    // Cleanup function
    return () => {
      isMounted = false;
      console.log('🧹 [useLocale] cleanup - المكون غير mounted');
    };
  }, [currentLocale]); // الاحتفاظ بـ currentLocale فقط

  const t = useCallback((key: string, params?: Record<string, string | number>): string => {
    if (isLoading) {
      // Return empty string during loading to avoid showing English keys
      return '';
    }

    let translation = translations[key];

    // If translation is not found, log it for debugging and return the key
    if (!translation) {
      // تقليل رسائل الترجمات المفقودة
      if (process.env.NODE_ENV === 'development' && !key.startsWith('_')) {
        console.warn(`Missing translation: "${key}" (${currentLocale})`);
      }
      return key;
    }

    // Critical fix: Ensure translation is always a string, never an object
    if (typeof translation === 'object') {
      console.error(`❌ Translation for "${key}" is an object, not a string:`, translation);
      return key; // Return the key as fallback
    }

    // Handle parameter substitution
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(value));
      });
    }

    return String(translation); // Ensure we always return a string
  }, [translations, isLoading, currentLocale]);

  return { locale: currentLocale, t, translations, isLoading };
}
