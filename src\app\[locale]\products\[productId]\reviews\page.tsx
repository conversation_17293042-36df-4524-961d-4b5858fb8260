'use client';

import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { ArrowLeft, Star } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ReviewsList, AddReviewForm, ReviewStats } from '@/components/reviews';
import { useAuth } from '@/context/AuthContext';
import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { ProductDocument } from '@/types';

export default function ProductReviewsPage() {
  const params = useParams();
  const t = useTranslations();
  const { user } = useAuth();
  const productId = params.productId as string;
  
  const [product, setProduct] = useState<ProductDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAddReview, setShowAddReview] = useState(false);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const productRef = doc(db, 'products', productId);
        const productSnap = await getDoc(productRef);
        
        if (productSnap.exists()) {
          setProduct({ id: productSnap.id, ...productSnap.data() } as ProductDocument);
        }
      } catch (error) {
        console.error('Error fetching product:', error);
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3" />
          <div className="h-64 bg-gray-200 rounded" />
          <div className="h-96 bg-gray-200 rounded" />
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t('common.productNotFound')}
            </h1>
            <p className="text-gray-600 mb-6">
              {t('common.productNotFoundDescription')}
            </p>
            <Link href="/products">
              <Button>
                <ArrowLeft className="h-4 w-4 me-2" />
                {t('common.backToProducts')}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* رأس الصفحة */}
      <div className="mb-8">
        <Link 
          href={`/products/${productId}`}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="h-4 w-4 me-2" />
          {t('common.backToProduct')}
        </Link>
        
        <div className="flex items-start space-x-4 rtl:space-x-reverse">
          <div className="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
            {product.imageUrls && product.imageUrls.length > 0 ? (
              <img
                src={product.imageUrls[0]}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Star className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {product.name}
            </h1>
            <p className="text-gray-600 mb-2">
              {product.description}
            </p>
            <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
              <span>{t('common.price')}: {product.price} {t('common.sar')}</span>
              <span>{t('common.category')}: {product.category}</span>
              {product.storeName && (
                <span>{t('common.store')}: {product.storeName}</span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* العمود الرئيسي - قائمة المراجعات */}
        <div className="lg:col-span-2 space-y-6">
          {/* إضافة مراجعة جديدة */}
          {user && (
            <div className="space-y-4">
              {!showAddReview ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Star className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t('reviews.shareYourExperience')}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {t('reviews.helpOthersWithYourReview')}
                    </p>
                    <Button onClick={() => setShowAddReview(true)}>
                      <Star className="h-4 w-4 me-2" />
                      {t('reviews.addProductReview')}
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <AddReviewForm
                  targetId={productId}
                  type="product"
                  targetName={product.name}
                  onSuccess={() => setShowAddReview(false)}
                />
              )}
              
              {showAddReview && (
                <div className="text-center">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddReview(false)}
                  >
                    {t('common.cancel')}
                  </Button>
                </div>
              )}
            </div>
          )}

          <Separator />

          {/* قائمة المراجعات */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {t('reviews.customerReviews')}
            </h2>
            <ReviewsList
              targetId={productId}
              type="product"
            />
          </div>
        </div>

        {/* الشريط الجانبي - الإحصائيات */}
        <div className="space-y-6">
          <ReviewStats
            targetId={productId}
            type="product"
          />

          {/* معلومات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t('reviews.reviewGuidelines')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-sm text-gray-600">
                <h4 className="font-medium text-gray-900 mb-2">
                  {t('reviews.helpfulReviewTips')}
                </h4>
                <ul className="space-y-1 list-disc list-inside">
                  <li>{t('reviews.tip1')}</li>
                  <li>{t('reviews.tip2')}</li>
                  <li>{t('reviews.tip3')}</li>
                  <li>{t('reviews.tip4')}</li>
                </ul>
              </div>
              
              <Separator />
              
              <div className="text-sm text-gray-600">
                <h4 className="font-medium text-gray-900 mb-2">
                  {t('reviews.communityGuidelines')}
                </h4>
                <p>
                  {t('reviews.guidelinesDescription')}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* معلومات المنتج */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t('common.productInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">{t('common.price')}:</span>
                <span className="font-medium">{product.price} {t('common.sar')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{t('common.category')}:</span>
                <span className="font-medium">{product.category}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{t('common.stock')}:</span>
                <span className="font-medium">
                  {product.stockQuantity > 0 
                    ? `${product.stockQuantity} ${t('common.available')}`
                    : t('common.outOfStock')
                  }
                </span>
              </div>
              {product.storeName && (
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('common.store')}:</span>
                  <span className="font-medium">{product.storeName}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
