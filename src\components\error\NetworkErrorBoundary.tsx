'use client';

import React, { Component, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Wifi, WifiOff } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  isOnline: boolean;
}

export default class NetworkErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // تسجيل الأخطاء المتعلقة بالشبكة فقط (مع تقليل الرسائل)
    if (this.isNetworkError(error) && process.env.NODE_ENV === 'development') {
      console.warn('🌐 Network Error Boundary:', error.message);
    }
  }

  componentDidMount() {
    // مراقبة حالة الشبكة
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline);
      window.addEventListener('offline', this.handleOffline);
    }
  }

  componentWillUnmount() {
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline);
      window.removeEventListener('offline', this.handleOffline);
    }
  }

  handleOnline = () => {
    this.setState({ isOnline: true });
    // إعادة المحاولة تلقائياً عند عودة الاتصال
    if (this.state.hasError && this.isNetworkError(this.state.error)) {
      this.handleRetry();
    }
  };

  handleOffline = () => {
    this.setState({ isOnline: false });
  };

  isNetworkError = (error?: Error): boolean => {
    if (!error) return false;
    
    const networkErrorMessages = [
      'network',
      'timeout',
      'offline',
      'connection',
      'fetch',
      'Failed to get document because the client is offline',
      'Request timeout',
      'Transport error'
    ];

    return networkErrorMessages.some(msg => 
      error.message.toLowerCase().includes(msg.toLowerCase())
    );
  };

  handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({ hasError: false, error: undefined });
      
      // إعادة تحميل الصفحة كحل أخير
      if (this.retryCount === this.maxRetries) {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    }
  };

  render() {
    if (this.state.hasError && this.isNetworkError(this.state.error)) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] p-8 text-center">
          <div className="mb-6">
            {this.state.isOnline ? (
              <Wifi className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            ) : (
              <WifiOff className="h-16 w-16 text-destructive mx-auto mb-4" />
            )}
            <AlertTriangle className="h-8 w-8 text-warning mx-auto" />
          </div>

          <h2 className="text-2xl font-bold mb-4">
            {this.state.isOnline ? 'مشكلة في الاتصال' : 'لا يوجد اتصال بالإنترنت'}
          </h2>

          <p className="text-muted-foreground mb-6 max-w-md">
            {this.state.isOnline 
              ? 'حدثت مشكلة في الاتصال بالخدمة. يرجى المحاولة مرة أخرى.'
              : 'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.'
            }
          </p>

          <div className="flex gap-4">
            <Button 
              onClick={this.handleRetry}
              disabled={this.retryCount >= this.maxRetries}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              {this.retryCount >= this.maxRetries ? 'جاري إعادة التحميل...' : 'إعادة المحاولة'}
            </Button>

            <Button 
              variant="outline"
              onClick={() => window.location.reload()}
            >
              إعادة تحميل الصفحة
            </Button>
          </div>

          {this.retryCount > 0 && (
            <p className="text-sm text-muted-foreground mt-4">
              المحاولة {this.retryCount} من {this.maxRetries}
            </p>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}
