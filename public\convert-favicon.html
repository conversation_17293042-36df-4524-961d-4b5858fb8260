<!DOCTYPE html>
<html>
<head>
    <title>تحويل SVG إلى PNG</title>
</head>
<body>
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadPNG()">تحميل PNG</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // رسم الخلفية بتدرج ألوان ثيم مخلاة
        const gradient = ctx.createLinearGradient(0, 0, 32, 32);
        gradient.addColorStop(0, '#D3B594'); // Sandstone
        gradient.addColorStop(1, '#E2725B'); // Terracotta
        
        // رسم دائرة الخلفية
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(16, 16, 15, 0, 2 * Math.PI);
        ctx.fill();
        
        // رسم حدود
        ctx.strokeStyle = '#B8956F';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // رسم أيقونة البحث
        ctx.strokeStyle = '#F5F5DC';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        
        // العدسة
        ctx.beginPath();
        ctx.arc(13, 13, 6, 0, 2 * Math.PI);
        ctx.stroke();
        
        // المقبض
        ctx.beginPath();
        ctx.moveTo(17.5, 17.5);
        ctx.lineTo(23, 23);
        ctx.stroke();
        
        // نقطة في المركز
        ctx.fillStyle = '#F5F5DC';
        ctx.globalAlpha = 0.6;
        ctx.beginPath();
        ctx.arc(13, 13, 2, 0, 2 * Math.PI);
        ctx.fill();
        
        function downloadPNG() {
            const link = document.createElement('a');
            link.download = 'favicon-32.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
