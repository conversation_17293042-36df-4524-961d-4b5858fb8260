// src/components/auth/SignupPageClient.tsx
"use client";

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Loader2, <PERSON><PERSON>he<PERSON>, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { Locale } from '@/lib/i18n';
import AuthErrorHandler from './AuthErrorHandler';
import CacheCleaner from './CacheCleaner';
import { determineUserRedirectPath, executeRedirectWithFallback, clearAuthCache } from '@/utils/authRedirect';

interface SignupPageClientProps {
  locale: Locale;
  children: React.ReactNode;
}

export default function SignupPageClient({ locale, children }: SignupPageClientProps) {
  const { user, loading, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const { t } = useLocale();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [redirectError, setRedirectError] = useState<string | null>(null);
  const [allowRedirect, setAllowRedirect] = useState(false);
  const [isNewSignup, setIsNewSignup] = useState(false);

  // وظيفة لمسح التخزين المؤقت وإعادة تحميل الصفحة
  const clearCacheAndReload = () => {
    clearAuthCache();

    if (typeof window !== 'undefined') {
      // مسح جميع البيانات المحلية
      localStorage.clear();
      sessionStorage.clear();

      // مسح cookies إن أمكن
      document.cookie.split(";").forEach((c) => {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });

      // إعادة تحميل الصفحة
      window.location.reload();
    }
  };

  // مسح التخزين المؤقت عند تحميل صفحة التسجيل
  useEffect(() => {
    // مسح أي بيانات مؤقتة قد تسبب مشاكل
    if (typeof window !== 'undefined') {
      // مسح بيانات Firebase المؤقتة
      const firebaseKeys = Object.keys(localStorage).filter(key =>
        key.startsWith('firebase:') ||
        key.includes('authUser') ||
        key.includes('firebase')
      );

      firebaseKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      // مسح session storage
      sessionStorage.clear();

      if (process.env.NODE_ENV === 'development') {
        console.log('🧹 Cleared Firebase cache for fresh signup');
      }
    }
  }, []);

  // التحقق من المصدر لتحديد ما إذا كان المستخدم جاء من عملية تسجيل
  useEffect(() => {
    if (initialLoadingCompleted && user) {
      // التحقق من المصدر (referrer) لمعرفة ما إذا كان المستخدم جاء من صفحة التسجيل
      const justSignedUpFlag = sessionStorage.getItem('justSignedUp');
      const isFromSignupForm = justSignedUpFlag === 'true';

      console.log('SignupPageClient: User detected, checking source', {
        user: !!user,
        justSignedUpFlag,
        isFromSignupForm
      });

      if (isFromSignupForm) {
        // هذا تسجيل جديد
        console.log('SignupPageClient: New signup detected');
        setIsNewSignup(true);
        // مسح العلامة
        sessionStorage.removeItem('justSignedUp');
      }

      // السماح بالتوجيه في جميع الحالات
      setAllowRedirect(true);
    }
  }, [initialLoadingCompleted, user]);

  useEffect(() => {
    if (initialLoadingCompleted && user && allowRedirect && !isRedirecting) {
      setIsRedirecting(true);

      // تقليل التأخير وتبسيط المنطق
      const delay = isNewSignup ? 1500 : 800;

      // User is already logged in, redirect to appropriate dashboard
      const redirectUser = async () => {
        try {
          console.log('🔄 SignupPageClient: Determining redirect path for user:', user.uid);

          const result = await determineUserRedirectPath(user, locale);

          if (result.success && result.redirectPath) {
            console.log('✅ SignupPageClient: Redirecting to:', result.redirectPath);

            // تحسين آلية إعادة التوجيه
            const executeRedirect = () => {
              try {
                // محاولة استخدام router أولاً
                router.push(result.redirectPath!);

                // آلية fallback مع timeout أقصر
                setTimeout(() => {
                  if (window.location.pathname !== result.redirectPath!.replace(`/${locale}`, '')) {
                    console.warn("⚠️ Router redirect timeout, using window.location...");
                    window.location.href = result.redirectPath!;
                  }
                }, 2000);
              } catch (redirectError) {
                console.error("❌ Router redirect failed:", redirectError);
                window.location.href = result.redirectPath!;
              }
            };

            // تنفيذ إعادة التوجيه مع تأخير قصير
            setTimeout(executeRedirect, 300);

          } else {
            throw new Error(result.error || 'Failed to determine redirect path');
          }
        } catch (error) {
          console.error("❌ Error during redirect:", error);
          setRedirectError(t('authenticationErrorMessage'));
          setIsRedirecting(false);

          // آلية fallback محسنة: إعادة تحميل الصفحة بعد 3 ثوان
          setTimeout(() => {
            console.log("🔄 Fallback redirect to dashboard");
            window.location.href = `/${locale}/dashboard`;
          }, 3000);
        }
      };

      // Add a delay to show the redirect message properly
      const timeoutId = setTimeout(redirectUser, delay);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [user, initialLoadingCompleted, allowRedirect, isRedirecting, router, locale, t, isNewSignup]);

  // Show loading while checking auth state
  if (loading || !initialLoadingCompleted) {
    return (
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    );
  }

  // Show redirecting message if user is logged in and redirect is allowed
  if (user && isRedirecting && allowRedirect) {
    const messageTitle = isNewSignup ? t('signupSuccessful') : t('alreadyLoggedIn');
    const messageDescription = isNewSignup ? t('redirectingToYourAccount') : t('redirectingToYourDashboard');

    return (
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <ShieldCheck className="h-16 w-16 text-primary" />
          <h2 className="text-2xl font-semibold">{messageTitle}</h2>
          <p className="text-muted-foreground">{messageDescription}</p>
          <Loader2 className="h-8 w-8 animate-spin text-primary" />

          {/* زر لمسح التخزين المؤقت في حالة التعليق */}
          <div className="mt-8">
            <p className="text-sm text-muted-foreground mb-4">
              إذا استمر التحميل لفترة طويلة، جرب مسح التخزين المؤقت:
            </p>
            <Button
              onClick={clearCacheAndReload}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              مسح التخزين المؤقت وإعادة التحميل
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show error handler if there's a redirect error
  if (redirectError) {
    return (
      <AuthErrorHandler
        locale={locale}
        error={redirectError}
        onRetry={() => {
          setRedirectError(null);
          setIsRedirecting(true);
          // Retry redirect logic
          window.location.reload();
        }}
      />
    );
  }

  // User is not logged in, show signup form
  if (!user) {
    return (
      <>
        <CacheCleaner clearOnMount={true} clearFirebaseOnly={true} />
        {children}
      </>
    );
  }

  // Fallback (shouldn't reach here)
  return null;
}
