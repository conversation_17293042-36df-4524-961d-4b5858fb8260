// src/components/auth/GoogleSignInButton.tsx
"use client";

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useLocale } from '@/hooks/use-locale';
import { auth, db, googleProvider } from '@/lib/firebase';
import { signInWithPopup, setPersistence, browserLocalPersistence, browserSessionPersistence } from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp, type Timestamp } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from 'lucide-react';
import type { UserDocument, StoreDocument, UserType } from '@/types';

interface GoogleSignInButtonProps {
  userType?: UserType;
  rememberMe?: boolean;
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  onClick?: () => void;
}

export default function GoogleSignInButton({
  userType,
  rememberMe = false,
  className = "",
  variant = "outline",
  onClick
}: GoogleSignInButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { t, locale } = useLocale();
  const router = useRouter();
  const { toast } = useToast();

  const handleGoogleSignIn = async () => {
    if (isLoading) return;

    // If onClick is provided, use it instead of the default logic
    if (onClick) {
      onClick();
      return;
    }

    // التحقق من أن التسجيل عبر Google متاح للعملاء فقط
    if (userType && userType !== 'customer') {
      toast({
        title: t('errorTitle'),
        description: t('googleSignInCustomersOnly'),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      console.log("🔄 Starting Google sign-in process...");

      // Set persistence based on "Remember me"
      await setPersistence(auth, rememberMe ? browserLocalPersistence : browserSessionPersistence);
      console.log("✅ Firebase persistence set");

      // Sign in with Google
      const result = await signInWithPopup(auth, googleProvider);
      const firebaseUser = result.user;

      console.log("✅ Google sign-in successful, user:", firebaseUser.uid);

      // Check if user document exists using retry mechanism
      const { getDocumentWithRetry } = await import('@/lib/firestore-utils');
      const userDocResult = await getDocumentWithRetry(
        `users/${firebaseUser.uid}`,
        {
          retries: 3,
          timeout: 10000,
          enableOffline: true
        }
      );

      let redirectPath = `/${locale}/dashboard`;

      // معالجة نتيجة التحقق من وثيقة المستخدم
      if (!userDocResult.success) {
        // فشل في التحقق من وثيقة المستخدم
        console.error("❌ Failed to check user document:", userDocResult.error);
        throw new Error(userDocResult.error || 'فشل في التحقق من بيانات المستخدم');
      }

      if (userDocResult.exists && userDocResult.data) {
        // User exists, handle login
        const userData = userDocResult.data as UserDocument;
        console.log("📄 Existing user found, type:", userData.userType);

        if (userData.userType === 'merchant') {
          // للتجار، نحتاج للتحقق من حالة الموافقة
          const storeDocRef = doc(db, "stores", firebaseUser.uid);
          const storeDocSnap = await getDoc(storeDocRef);

          if (storeDocSnap.exists()) {
            const storeData = storeDocSnap.data() as StoreDocument;

            // إذا كان التاجر معتمد ومفعل
            if (storeData.approvalStatus === 'approved' && storeData.isActive) {
              redirectPath = `/${locale}/merchant/dashboard`;
            } else {
              // إذا كان في انتظار الموافقة أو تم رفضه
              redirectPath = `/${locale}/merchant/pending-approval`;
            }
          } else {
            // لا يوجد مستند متجر
            redirectPath = `/${locale}/merchant/pending-approval`;
          }
        } else if (userData.userType === 'representative') {
          redirectPath = `/${locale}/representative/dashboard`;
        } else {
          redirectPath = `/${locale}/dashboard`;
        }

        toast({
          title: t('loginSuccessTitle'),
          description: t('loginSuccessMessage'),
        });
      } else {
        // User doesn't exist, create new user document
        if (!userType) {
          // If no userType specified, redirect to signup to choose type
          redirectPath = `/${locale}/signup`;
          console.log("📝 No userType specified, redirecting to signup");
        } else {
          // Create user document with specified type
          console.log("📝 Creating new user document...");

          const defaultPlanId = userType === 'customer' ? 'customer-basic' :
                               userType === 'merchant' ? 'merchant-basic' :
                               'representative-basic';

          const userDocData: UserDocument = {
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'مستخدم',
            photoURL: firebaseUser.photoURL,
            userType: userType,
            planId: defaultPlanId,
            createdAt: serverTimestamp() as Timestamp,
            updatedAt: serverTimestamp() as Timestamp,
          };

          // استخدام دالة setDocumentWithRetry المحسنة
          const { setDocumentWithRetry } = await import('@/lib/firestore-utils');
          const createResult = await setDocumentWithRetry(
            `users/${firebaseUser.uid}`,
            userDocData,
            {
              retries: 3,
              timeout: 10000
            }
          );

          if (!createResult.success) {
            throw new Error(createResult.error || 'فشل في إنشاء حساب المستخدم');
          }

          console.log("✅ User document created successfully");

          // Handle different user types
          if (userType === 'merchant') {
            redirectPath = `/${locale}/merchant/pending-approval`;
          } else if (userType === 'representative') {
            redirectPath = `/${locale}/representative/signup`;
          } else {
            redirectPath = `/${locale}/dashboard`;
          }

          toast({
            title: t('signupSuccessTitle'),
            description: t('signupSuccessMessage'),
          });
        }
      }

      console.log("🔄 Redirecting to:", redirectPath);

      // تحسين آلية إعادة التوجيه مع fallback
      const executeRedirect = () => {
        try {
          router.push(redirectPath);

          // آلية fallback: استخدام window.location إذا فشل router
          setTimeout(() => {
            if (window.location.pathname !== redirectPath.replace(`/${locale}`, '')) {
              console.warn("⚠️ Router redirect timeout, using window.location...");
              window.location.href = redirectPath;
            }
          }, 3000);
        } catch (redirectError) {
          console.error("❌ Router redirect failed:", redirectError);
          window.location.href = redirectPath;
        }
      };

      // تأخير قصير للسماح للـ toast بالظهور
      setTimeout(executeRedirect, 500);

    } catch (error: any) {
      console.error("Google sign-in error:", error);
      
      let errorMessage = t('googleSignInFailed');
      
      // معالجة أخطاء Google Sign-In المختلفة
      switch (error.code) {
        case 'auth/popup-closed-by-user':
          errorMessage = t('googleSignInCancelled');
          break;
        case 'auth/popup-blocked':
          errorMessage = t('googleSignInPopupBlocked');
          break;
        case 'auth/network-request-failed':
          errorMessage = t('networkError');
          break;
        case 'auth/too-many-requests':
          errorMessage = t('tooManyRequests');
          break;
        case 'auth/timeout':
          errorMessage = 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
          break;
        default:
          if (error.message) {
            // معالجة رسائل الأخطاء المختلفة
            if (error.message.includes('timeout') || error.message.includes('Request timeout')) {
              errorMessage = 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
            } else if (error.message.includes('offline') || error.message.includes('network')) {
              errorMessage = 'مشكلة في الاتصال بالإنترنت، يرجى التحقق من الاتصال';
            } else if (error.message.includes('فشل في التحقق من بيانات المستخدم')) {
              errorMessage = 'فشل في التحقق من بيانات المستخدم، يرجى المحاولة مرة أخرى';
            } else if (error.message.includes('فشل في إنشاء حساب المستخدم')) {
              errorMessage = 'فشل في إنشاء حساب المستخدم، يرجى المحاولة مرة أخرى';
            } else {
              errorMessage = error.message;
            }
          }
          break;
      }

      toast({
        title: t('errorTitle'),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      type="button"
      variant={variant}
      className={`w-full ${className}`}
      onClick={handleGoogleSignIn}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <Loader2 className="me-2 h-4 w-4 animate-spin" />
          {t('signingIn')}
        </>
      ) : (
        <>
          <svg className="me-2 h-4 w-4" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {t('continueWithGoogle')}
        </>
      )}
    </Button>
  );
}
