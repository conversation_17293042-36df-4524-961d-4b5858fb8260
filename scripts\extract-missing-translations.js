#!/usr/bin/env node

/**
 * سكريبت لاستخراج الترجمات المفقودة من ملف الترجمة العربية
 * وإضافتها إلى ملف الترجمة الإنجليزية
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');
const OUTPUT_PATH = path.join(__dirname, '../docs/missing-translations-keys.json');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * كتابة ملف JSON
 */
function writeJsonFile(filePath, data) {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error(`خطأ في كتابة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * استخراج جميع المفاتيح من كائن متداخل
 */
function extractAllKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // إذا كان الكائن متداخل، استخرج المفاتيح الفرعية
      keys.push(...extractAllKeys(value, fullKey));
    } else {
      // إذا كان قيمة نهائية، أضف المفتاح
      keys.push(fullKey);
    }
  }
  
  return keys;
}

/**
 * تحويل مفتاح متداخل إلى قيمة من كائن
 */
function getNestedValue(obj, keyPath) {
  return keyPath.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * تعيين قيمة متداخلة في كائن
 */
function setNestedValue(obj, keyPath, value) {
  const keys = keyPath.split('.');
  const lastKey = keys.pop();
  
  let current = obj;
  for (const key of keys) {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[lastKey] = value;
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔍 بدء استخراج الترجمات المفقودة...\n');
  
  // قراءة ملفات الترجمة
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations || !enTranslations) {
    console.error('❌ فشل في قراءة ملفات الترجمة');
    process.exit(1);
  }
  
  // استخراج جميع المفاتيح
  const arKeys = extractAllKeys(arTranslations);
  const enKeys = extractAllKeys(enTranslations);
  
  console.log(`📊 إحصائيات الترجمة:`);
  console.log(`   - المفاتيح العربية: ${arKeys.length}`);
  console.log(`   - المفاتيح الإنجليزية: ${enKeys.length}`);
  
  // العثور على المفاتيح المفقودة
  const missingKeys = arKeys.filter(key => !enKeys.includes(key));
  
  console.log(`   - المفاتيح المفقودة: ${missingKeys.length}\n`);
  
  if (missingKeys.length === 0) {
    console.log('✅ لا توجد ترجمات مفقودة!');
    return;
  }
  
  // إنشاء كائن الترجمات المفقودة
  const missingTranslations = {};
  const missingKeysWithValues = [];
  
  missingKeys.forEach(key => {
    const arabicValue = getNestedValue(arTranslations, key);
    if (arabicValue !== undefined) {
      // إضافة القيمة العربية كتعليق للمترجم
      const englishPlaceholder = `[TRANSLATE: ${arabicValue}]`;
      setNestedValue(missingTranslations, key, englishPlaceholder);
      
      missingKeysWithValues.push({
        key,
        arabicValue,
        englishPlaceholder
      });
    }
  });
  
  // حفظ المفاتيح المفقودة في ملف منفصل
  const reportData = {
    timestamp: new Date().toISOString(),
    totalMissingKeys: missingKeys.length,
    missingKeys: missingKeysWithValues,
    missingTranslationsObject: missingTranslations
  };
  
  if (writeJsonFile(OUTPUT_PATH, reportData)) {
    console.log(`📄 تم حفظ تقرير الترجمات المفقودة في: ${OUTPUT_PATH}`);
  }
  
  // عرض أول 10 مفاتيح مفقودة كمثال
  console.log('\n📋 أمثلة على المفاتيح المفقودة:');
  missingKeysWithValues.slice(0, 10).forEach((item, index) => {
    console.log(`   ${index + 1}. ${item.key}`);
    console.log(`      العربية: "${item.arabicValue}"`);
    console.log(`      مطلوب: ترجمة إنجليزية\n`);
  });
  
  if (missingKeysWithValues.length > 10) {
    console.log(`   ... و ${missingKeysWithValues.length - 10} مفتاح آخر\n`);
  }
  
  // اقتراح الخطوات التالية
  console.log('🛠️  الخطوات التالية:');
  console.log('   1. راجع الملف المُنشأ: docs/missing-translations-keys.json');
  console.log('   2. ترجم المفاتيح المفقودة إلى الإنجليزية');
  console.log('   3. أضف الترجمات إلى src/locales/en.json');
  console.log('   4. اختبر التطبيق للتأكد من صحة الترجمات');
  
  console.log('\n✨ تم الانتهاء من استخراج الترجمات المفقودة!');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  extractAllKeys,
  getNestedValue,
  setNestedValue,
  readJsonFile,
  writeJsonFile
};
