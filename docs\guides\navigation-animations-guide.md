# دليل قائمة التنقل المتحركة والأيقونات الملونة - مِخْلاة

## 🎨 نظرة عامة

تم تطوير قائمة تنقل متحركة مع أيقونات ملونة مخصصة تتناسب مع ثيم مخلاة، مما يوفر تجربة مستخدم بصرية متميزة وتفاعلية.

## ✨ الميزات الرئيسية

### 1. الأيقونات الملونة المخصصة

#### الأيقونات المتاحة:
- **🏠 الرئيسية**: أيقونة بيت بتدرج دافئ (Sandstone → Terracotta)
- **🏪 المتاجر**: أيقونة متجر بألوان ترابية مع تفاصيل
- **📋 الفئات**: شبكة ملونة بتدرجات متعددة
- **🗺️ الخريطة**: خريطة مع دبوس موقع ملون
- **👑 خطة الاشتراك**: تاج بألوان ذهبية متدرجة

#### الألوان المستخدمة:
- **Sandstone**: `#D3B594` - اللون الأساسي لثيم مخلاة
- **Terracotta**: `#E2725B` - لون التمييز والتفاعل
- **Brown**: `#8B4513` - للتفاصيل والحدود

### 2. الحركات والتأثيرات

#### حركات التمرير (Hover):
```css
/* تكبير وإضاءة الأيقونة */
@keyframes navIconHover {
  0% { transform: scale(1) rotate(0deg); filter: brightness(1); }
  50% { transform: scale(1.1) rotate(2deg); filter: brightness(1.2); }
  100% { transform: scale(1.05) rotate(0deg); filter: brightness(1.1); }
}
```

#### حركات العنصر النشط:
```css
/* نبضة مع تأثير glow */
@keyframes navIconActive {
  0% { transform: scale(1); box-shadow: 0 0 0 rgba(210, 181, 148, 0); }
  50% { transform: scale(1.02); box-shadow: 0 0 20px rgba(210, 181, 148, 0.4); }
  100% { transform: scale(1); box-shadow: 0 0 15px rgba(210, 181, 148, 0.3); }
}
```

### 3. التصميم المتجاوب

#### سطح المكتب:
- قائمة أفقية في الشريط العلوي
- أيقونات بحجم 20px مع نصوص
- تأثيرات hover متقدمة
- خط سفلي متدرج للعنصر النشط

#### الهواتف المحمولة:
- قائمة تنقل ثابتة في الأسفل
- أيقونات مصغرة مع نصوص قصيرة
- تأثيرات لمس محسنة
- دعم safe-area للهواتف الحديثة

## 🔧 التطبيق التقني

### 1. مكون الأيقونات الملونة

```typescript
// src/components/ui/colored-icons.tsx
export const NavColoredIcon: React.FC<NavIconProps> = ({ type, ...props }) => {
  switch (type) {
    case 'home': return <HomeColoredIcon {...props} />;
    case 'store': return <StoreColoredIcon {...props} />;
    case 'categories': return <CategoriesColoredIcon {...props} />;
    case 'map': return <MapColoredIcon {...props} />;
    case 'pricing': return <PricingColoredIcon {...props} />;
  }
};
```

### 2. تحديث Header

```typescript
// استخدام الأيقونات الجديدة
const navLinks = [
  { href: `/${locale}`, labelKey: 'home', icon: <NavColoredIcon type="home" size={20} /> },
  { href: `/${locale}/stores`, labelKey: 'stores', icon: <NavColoredIcon type="store" size={20} /> },
  // ...
];
```

### 3. فئات CSS للحركات

```css
.nav-item {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  padding: 8px 16px;
}

.nav-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
```

## 📱 قائمة التنقل المحمولة

### الميزات:
- **موضع ثابت**: `position: fixed; bottom: 0`
- **تصميم مرن**: `flex` layout مع توزيع متساوي
- **أيقونات مصغرة**: حجم مناسب للمس
- **نصوص مختصرة**: `text-xs` مع `truncate`
- **تأثيرات لمس**: حركات مخصصة للهواتف

### الكود:
```tsx
{isMobile && (
  <div className="fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md">
    <nav className="flex items-center justify-around py-2 px-4">
      {navLinks.map((link) => (
        <Link key={link.href} href={link.href} 
              className="mobile-nav-item flex flex-col items-center">
          <span className="nav-icon mb-1">{link.icon}</span>
          <span className="nav-text text-xs">{t(link.labelKey)}</span>
        </Link>
      ))}
    </nav>
  </div>
)}
```

## 🎯 تحسينات الأداء

### 1. Hardware Acceleration:
```css
.nav-icon {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}
```

### 2. تقليل الحركة:
```css
@media (prefers-reduced-motion: reduce) {
  .nav-item, .nav-icon, .nav-text {
    transition: none !important;
    animation: none !important;
  }
}
```

### 3. تحسين الرسوم المتحركة:
- استخدام `cubic-bezier` للانتقالات السلسة
- تحديد `will-change` للعناصر المتحركة
- تجنب `repaint` مع `backface-visibility`

## 🧪 الاختبارات

### اختبارات Cypress المتاحة:
```typescript
// cypress/e2e/navigation-animations.cy.ts
describe('اختبار حركات قائمة التنقل والأيقونات الملونة', () => {
  it('يجب أن تظهر الأيقونات الملونة', () => {
    cy.get('.nav-icon svg').should('exist');
    cy.get('.nav-icon svg defs linearGradient').should('exist');
  });
  
  it('يجب أن تعمل حركات التمرير', () => {
    cy.get('.nav-item').first().trigger('mouseover');
    cy.get('.nav-item').should('have.css', 'transform');
  });
});
```

## 🎨 التخصيص

### تغيير الألوان:
```css
:root {
  --nav-primary: #D3B594;    /* Sandstone */
  --nav-accent: #E2725B;     /* Terracotta */
  --nav-detail: #8B4513;     /* Brown */
}
```

### إضافة أيقونة جديدة:
1. إنشاء مكون SVG في `colored-icons.tsx`
2. إضافة gradient مخصص
3. تحديث `NavColoredIcon` switch statement
4. إضافة إلى `navLinks` في Header

## 📊 الإحصائيات

- **5 أيقونات ملونة** مخصصة
- **4 حركات CSS** رئيسية
- **2 تخطيط** (سطح المكتب + محمول)
- **100% متجاوب** مع جميع الأحجام
- **دعم RTL** كامل للعربية
- **تحسين الأداء** مع hardware acceleration

## 🏆 النتيجة النهائية

تم إنشاء قائمة تنقل احترافية ومتحركة تعزز من تجربة المستخدم وتجعل التفاعل مع تطبيق مخلاة أكثر متعة وسلاسة، مع الحفاظ على الأداء والإتاحة.
