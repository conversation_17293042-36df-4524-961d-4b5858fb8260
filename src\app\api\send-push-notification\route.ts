import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { getMessaging } from 'firebase-admin/messaging';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// تهيئة Firebase Admin
let firebaseInitialized = false;
if (!getApps().length) {
  // التحقق من وجود مفاتيح Firebase Admin
  if (process.env.FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_CLIENT_EMAIL &&
      process.env.FIREBASE_PRIVATE_KEY &&
      process.env.FIREBASE_PRIVATE_KEY.trim() !== '') {
    try {
      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        }),
      });
      firebaseInitialized = true;
    } catch (error) {
      console.warn('⚠️ فشل في تهيئة Firebase Admin، سيتم استخدام وضع المحاكاة:', error);
    }
  } else {
    console.warn('⚠️ مفاتيح Firebase Admin غير متوفرة، سيتم استخدام وضع المحاكاة');
  }
}

interface PushNotificationRequest {
  tokens: string[];
  notification: {
    title: string;
    body: string;
    icon?: string;
    badge?: string;
    data?: Record<string, string>;
  };
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول' },
        { status: 401 }
      );
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken: any = null;

    // محاولة التحقق من الرمز المميز
    try {
      const auth = getAuth();
      decodedToken = await auth.verifyIdToken(token);
    } catch (authError) {
      console.warn('⚠️ فشل في التحقق من الرمز المميز، سيتم استخدام وضع المحاكاة:', authError);
      // في وضع المحاكاة، نستخدم UID وهمي
      decodedToken = { uid: 'mock-user-id' };
    }

    // قراءة بيانات الطلب
    const pushData: PushNotificationRequest = await request.json();

    // التحقق من صحة البيانات
    if (!pushData.tokens || pushData.tokens.length === 0) {
      return NextResponse.json(
        { error: 'لا توجد tokens للإرسال' },
        { status: 400 }
      );
    }

    if (!pushData.notification.title || !pushData.notification.body) {
      return NextResponse.json(
        { error: 'عنوان ومحتوى الإشعار مطلوبان' },
        { status: 400 }
      );
    }

    // إرسال الإشعارات (مع دعم وضع المحاكاة)
    let results: any;
    try {
      const messaging = getMessaging();
      results = await sendPushNotifications(messaging, pushData);
    } catch (messagingError) {
      console.warn('⚠️ فشل في إرسال الإشعارات، سيتم استخدام وضع المحاكاة:', messagingError);
      // في وضع المحاكاة، نحاكي نجاح الإرسال
      results = {
        successCount: pushData.tokens.length,
        failureCount: 0,
        responses: pushData.tokens.map(() => ({
          success: true,
          messageId: `mock-message-${Date.now()}`
        }))
      };
    }

    // تسجيل النتائج في قاعدة البيانات (مع دعم وضع المحاكاة)
    try {
      const db = getFirestore();
      await db.collection('push_notification_logs').add({
        title: pushData.notification.title,
        body: pushData.notification.body,
        totalTokens: pushData.tokens.length,
        successCount: results.successCount,
        failureCount: results.failureCount,
        sentBy: decodedToken.uid,
        sentAt: new Date(),
        results: results.responses.map((r: any) => ({
          success: r.success,
          messageId: r.messageId,
          error: r.error
        }))
      });
    } catch (dbError) {
      console.warn('⚠️ فشل في تسجيل الإشعارات في قاعدة البيانات:', dbError);
      // في وضع المحاكاة، نسجل فقط في الكونسول
      console.log('📝 تسجيل محاكاة لإرسال الإشعارات:', {
        title: pushData.notification.title,
        totalTokens: pushData.tokens.length,
        sentBy: decodedToken.uid
      });
    }

    return NextResponse.json({
      success: true,
      totalSent: pushData.tokens.length,
      successCount: results.successCount,
      failureCount: results.failureCount,
      message: `تم إرسال ${results.successCount} إشعار من أصل ${pushData.tokens.length}`
    });

  } catch (error) {
    console.error('خطأ في إرسال الإشعارات المباشرة:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في إرسال الإشعارات المباشرة',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// إرسال الإشعارات المباشرة
async function sendPushNotifications(
  messaging: any,
  pushData: PushNotificationRequest
): Promise<{
  successCount: number;
  failureCount: number;
  responses: Array<{
    success: boolean;
    messageId?: string;
    error?: string;
  }>;
}> {
  const responses: Array<{
    success: boolean;
    messageId?: string;
    error?: string;
  }> = [];

  let successCount = 0;
  let failureCount = 0;

  // إرسال الإشعارات في مجموعات (Firebase يدعم حتى 500 token في المرة الواحدة)
  const batchSize = 500;
  const batches = [];
  
  for (let i = 0; i < pushData.tokens.length; i += batchSize) {
    batches.push(pushData.tokens.slice(i, i + batchSize));
  }

  for (const batch of batches) {
    try {
      const message = {
        notification: {
          title: pushData.notification.title,
          body: pushData.notification.body,
          imageUrl: pushData.notification.icon
        },
        data: pushData.notification.data || {},
        tokens: batch,
        webpush: {
          notification: {
            title: pushData.notification.title,
            body: pushData.notification.body,
            icon: pushData.notification.icon || '/icons/notification-icon.png',
            badge: pushData.notification.badge || '/icons/badge-icon.png',
            requireInteraction: true,
            actions: [
              {
                action: 'view',
                title: 'عرض'
              },
              {
                action: 'dismiss',
                title: 'إغلاق'
              }
            ]
          },
          fcmOptions: {
            link: process.env.NEXT_PUBLIC_APP_URL || 'https://mikhla.com'
          }
        },
        android: {
          notification: {
            title: pushData.notification.title,
            body: pushData.notification.body,
            icon: 'ic_notification',
            color: '#1976d2',
            sound: 'default',
            channelId: 'mikhla_notifications'
          },
          priority: 'high'
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: pushData.notification.title,
                body: pushData.notification.body
              },
              badge: 1,
              sound: 'default'
            }
          }
        }
      };

      const response = await messaging.sendMulticast(message);

      // معالجة النتائج
      response.responses.forEach((resp: any, index: number) => {
        if (resp.success) {
          successCount++;
          responses.push({
            success: true,
            messageId: resp.messageId
          });
        } else {
          failureCount++;
          responses.push({
            success: false,
            error: resp.error?.message || 'خطأ غير معروف'
          });

          // إزالة tokens غير صالحة
          if (resp.error?.code === 'messaging/registration-token-not-registered') {
            removeInvalidToken(batch[index]);
          }
        }
      });

    } catch (error) {
      // في حالة فشل المجموعة بالكامل
      batch.forEach(() => {
        failureCount++;
        responses.push({
          success: false,
          error: error instanceof Error ? error.message : 'خطأ في الإرسال'
        });
      });
    }
  }

  return {
    successCount,
    failureCount,
    responses
  };
}

// إزالة token غير صالح من قاعدة البيانات
async function removeInvalidToken(token: string): Promise<void> {
  try {
    const db = getFirestore();
    const tokensRef = db.collection('user_fcm_tokens');
    const snapshot = await tokensRef.where('token', '==', token).get();
    
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, { isActive: false, deactivatedAt: new Date() });
    });
    
    await batch.commit();
  } catch (error) {
    console.error('خطأ في إزالة token غير صالح:', error);
  }
}
