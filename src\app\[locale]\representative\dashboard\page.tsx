'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Truck,
  DollarSign,
  Star,
  Clock,
  Package,
  TrendingUp,
  MapPin,
  Settings,
  User,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { useRepresentativeStats } from '@/hooks/useRepresentativeStats';
import { formatCurrency } from '@/lib/utils';
import type { RepresentativeDocument } from '@/types/representative';

export default function RepresentativeDashboardPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const [representativeData, setRepresentativeData] = useState<RepresentativeDocument | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // استخدام Hook الإحصائيات الجديد
  const { stats, loading: statsLoading } = useRepresentativeStats(user?.uid);

  useEffect(() => {
    const fetchRepresentativeData = async () => {
      if (!user) return;

      try {
        const representativeDoc = await getDoc(doc(db, 'representatives', user.uid));
        if (representativeDoc.exists()) {
          setRepresentativeData(representativeDoc.data() as RepresentativeDocument);
        }
      } catch (error) {
        console.error('Error fetching representative data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRepresentativeData();
  }, [user]);

  if (isLoading || statsLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{t('representative.dashboard.loading')}</p>
      </div>
    );
  }

  const representativeName = representativeData?.displayName || user?.displayName || t('representative.dashboard.representative');

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Welcome Header */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">
          {t('representative.dashboard.welcome', { name: representativeName })}
        </h1>
        <p className="text-muted-foreground mb-4">
          {t('representative.dashboard.subtitle')}
        </p>

        {/* Status Badge */}
        <div className="flex justify-center gap-4 items-center">
          <Badge
            variant={representativeData?.isActive ? 'default' : 'secondary'}
            className="text-sm px-4 py-2"
          >
            {representativeData?.isActive ? t('representative.dashboard.active') : t('representative.dashboard.inactive')}
          </Badge>
          <Badge
            variant={representativeData?.isAvailable ? 'default' : 'outline'}
            className="text-sm px-4 py-2"
          >
            {representativeData?.isAvailable ? t('representative.dashboard.available') : t('representative.dashboard.unavailable')}
          </Badge>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Deliveries */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('representative.dashboard.totalDeliveries')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDeliveries}</div>
            <p className="text-xs text-muted-foreground">
              {t('representative.dashboard.completed')}: {stats.completedDeliveries}
            </p>
          </CardContent>
        </Card>

        {/* Monthly Earnings */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('representative.dashboard.monthlyEarnings')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.thisMonthEarnings)}</div>
            <p className="text-xs text-muted-foreground">
              {t('representative.dashboard.totalEarnings')}: {formatCurrency(stats.totalEarnings)}
            </p>
          </CardContent>
        </Card>

        {/* Average Rating */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('representative.dashboard.averageRating')}</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalRatings} {t('representative.dashboard.reviews')}
            </p>
          </CardContent>
        </Card>

        {/* On-Time Delivery Rate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('representative.dashboard.onTimeRate')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.onTimeDeliveryRate}%</div>
            <p className="text-xs text-muted-foreground">
              {t('representative.dashboard.avgDeliveryTime')}: {stats.averageDeliveryTime} {t('representative.dashboard.minutes')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>{t('quickActions')}</CardTitle>
          <CardDescription>{t('quickActionsSubtitle')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button asChild className="h-auto p-4 flex flex-col items-center gap-2">
              <Link href={`/${locale}/representative/orders`}>
                <Package className="h-6 w-6" />
                <span>{t('representativeOrders')}</span>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Link href={`/${locale}/representative/earnings`}>
                <DollarSign className="h-6 w-6" />
                <span>{t('representativeEarnings')}</span>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Link href={`/${locale}/representative/profile`}>
                <User className="h-6 w-6" />
                <span>{t('representativeProfile')}</span>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Link href={`/${locale}/representative/settings`}>
                <Settings className="h-6 w-6" />
                <span>{t('representativeSettings')}</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Status */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {t('currentStatus')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t('workingStatus')}</span>
              <Badge variant={representativeData?.isAvailable ? 'default' : 'secondary'}>
                {representativeData?.isAvailable ? t('available') : t('unavailable')}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t('currentPlan')}</span>
              <Badge variant="outline">
                {representativeData?.planId === 'representative-premium' ? t('plan_representative_premium_name') : t('plan_representative_basic_name')}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t('commissionRate')}</span>
              <span className="font-medium">{representativeData?.commissionRate || 10}%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle>{t('recentActivity')}</CardTitle>
          <CardDescription>{t('recentActivitySubtitle')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">{t('noRecentActivity')}</p>
            <p className="text-sm text-muted-foreground mt-2">{t('startAcceptingOrders')}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
