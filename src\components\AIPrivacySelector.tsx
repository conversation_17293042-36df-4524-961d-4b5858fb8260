// مكون اختيار نظام الذكاء الاصطناعي مع التركيز على الخصوصية
'use client';

import React, { useState, useEffect } from 'react';
import { Shield, Lock, Cloud, Zap, Eye, AlertTriangle } from 'lucide-react';

interface AISystem {
  id: string;
  name: string;
  privacy: string;
  speed: string;
  accuracy: string;
  cost: string;
  description: string;
  pros: string[];
  cons: string[];
  icon: React.ReactNode;
  privacyScore: number;
  recommended?: boolean;
}

export default function AIPrivacySelector() {
  const [selectedSystem, setSelectedSystem] = useState<string>('local_privacy');
  const [showDetails, setShowDetails] = useState<string | null>(null);

  const systems: AISystem[] = [
    {
      id: 'local_privacy',
      name: 'النظام المحلي 100%',
      privacy: '100%',
      speed: 'متوسط',
      accuracy: '85-90%',
      cost: 'مجاني',
      description: 'معالجة محلية بالكامل في المتصفح - لا إرسال بيانات للخارج أبداً',
      pros: [
        'خصوصية كاملة مضمونة 100%',
        'لا تكلفة تشغيل',
        'يعمل بدون إنترنت',
        'امتثال كامل لقوانين الخصوصية',
        'شفافية كاملة في المعالجة',
        'تحكم كامل في البيانات'
      ],
      cons: [
        'دقة أقل نسبياً (85-90%)',
        'سرعة متوسطة (3-5 ثوانٍ)',
        'يحتاج تحميل مكتبات JavaScript'
      ],
      icon: <Shield className="w-6 h-6 text-green-600" />,
      privacyScore: 100,
      recommended: true
    },
    {
      id: 'cloud_encrypted',
      name: 'النظام السحابي المشفر',
      privacy: '70%',
      speed: 'سريع',
      accuracy: '98%',
      cost: 'مدفوع',
      description: 'تشفير البيانات قبل الإرسال للسحابة مع قناع للمعلومات الحساسة',
      pros: [
        'دقة عالية جداً (98%+)',
        'سرعة فائقة (< 2 ثانية)',
        'تشفير البيانات الحساسة',
        'قناع للمعلومات الشخصية'
      ],
      cons: [
        'إرسال بيانات مشفرة للخارج',
        'يحتاج اتصال إنترنت',
        'تكلفة حسب الاستخدام',
        'اعتماد على خوادم خارجية',
        'مخاطر خصوصية محتملة'
      ],
      icon: <Cloud className="w-6 h-6 text-blue-600" />,
      privacyScore: 70
    },
    {
      id: 'cloud_direct',
      name: 'النظام السحابي المباشر',
      privacy: '30%',
      speed: 'سريع جداً',
      accuracy: '99%',
      cost: 'مدفوع',
      description: 'إرسال مباشر للبيانات للسحابة بدون تشفير أو حماية',
      pros: [
        'أعلى دقة ممكنة (99%+)',
        'أسرع معالجة (< 1 ثانية)',
        'ميزات متقدمة'
      ],
      cons: [
        'لا حماية للخصوصية',
        'إرسال جميع البيانات للخارج',
        'مخاطر تسرب عالية',
        'عدم امتثال لقوانين الخصوصية',
        'فقدان السيطرة على البيانات'
      ],
      icon: <AlertTriangle className="w-6 h-6 text-red-600" />,
      privacyScore: 30
    }
  ];

  useEffect(() => {
    // تحميل الاختيار المحفوظ
    const saved = localStorage.getItem('ai_system_preference');
    if (saved && systems.find(s => s.id === saved)) {
      setSelectedSystem(saved);
    }
  }, []);

  const handleSystemSelect = (systemId: string) => {
    setSelectedSystem(systemId);
    localStorage.setItem('ai_system_preference', systemId);
    
    // إشعار المستخدم
    const system = systems.find(s => s.id === systemId);
    if (system) {
      alert(`✅ تم اختيار: ${system.name}\n🔒 مستوى الخصوصية: ${system.privacy}`);
    }
  };

  const getPrivacyColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getPrivacyIcon = (score: number) => {
    if (score >= 90) return <Shield className="w-4 h-4" />;
    if (score >= 70) return <Lock className="w-4 h-4" />;
    return <Eye className="w-4 h-4" />;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">
          🔒 اختر نظام الذكاء الاصطناعي
        </h2>
        <p className="text-gray-600 text-lg">
          اختر النظام الذي يناسب احتياجاتك للخصوصية والأداء
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
        {systems.map((system) => (
          <div
            key={system.id}
            className={`relative border-2 rounded-xl p-6 cursor-pointer transition-all duration-300 hover:shadow-lg ${
              selectedSystem === system.id
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleSystemSelect(system.id)}
          >
            {/* شارة الترشيح */}
            {system.recommended && (
              <div className="absolute -top-3 -right-3 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                مُوصى به
              </div>
            )}

            {/* رأس البطاقة */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                {system.icon}
                <h3 className="font-bold text-lg">{system.name}</h3>
              </div>
              <div className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-bold ${getPrivacyColor(system.privacyScore)}`}>
                {getPrivacyIcon(system.privacyScore)}
                {system.privacy}
              </div>
            </div>

            {/* الوصف */}
            <p className="text-gray-600 mb-4 text-sm leading-relaxed">
              {system.description}
            </p>

            {/* المقاييس */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className="text-xs text-gray-500">الخصوصية</div>
                <div className="font-bold text-sm">{system.privacy}</div>
              </div>
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className="text-xs text-gray-500">السرعة</div>
                <div className="font-bold text-sm">{system.speed}</div>
              </div>
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className="text-xs text-gray-500">الدقة</div>
                <div className="font-bold text-sm">{system.accuracy}</div>
              </div>
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className="text-xs text-gray-500">التكلفة</div>
                <div className="font-bold text-sm">{system.cost}</div>
              </div>
            </div>

            {/* زر التفاصيل */}
            <button
              className="w-full text-blue-600 text-sm hover:text-blue-800 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setShowDetails(showDetails === system.id ? null : system.id);
              }}
            >
              {showDetails === system.id ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </button>

            {/* التفاصيل المنسدلة */}
            {showDetails === system.id && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-green-600 mb-2">✅ المميزات:</h4>
                    <ul className="text-sm text-green-600 space-y-1">
                      {system.pros.map((pro, i) => (
                        <li key={i} className="flex items-start gap-1">
                          <span className="text-green-500 mt-1">•</span>
                          <span>{pro}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-red-600 mb-2">❌ العيوب:</h4>
                    <ul className="text-sm text-red-600 space-y-1">
                      {system.cons.map((con, i) => (
                        <li key={i} className="flex items-start gap-1">
                          <span className="text-red-500 mt-1">•</span>
                          <span>{con}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* معلومات النظام المختار */}
      <div className="mt-8 p-6 bg-gray-50 rounded-lg">
        <h3 className="font-bold text-lg mb-4">📊 ملخص النظام المختار</h3>
        {(() => {
          const selected = systems.find(s => s.id === selectedSystem);
          if (!selected) return null;
          
          return (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">🔒 ضمانات الخصوصية:</h4>
                <div className="space-y-2">
                  {selected.id === 'local_privacy' && (
                    <>
                      <div className="flex items-center gap-2 text-green-600">
                        <Shield className="w-4 h-4" />
                        <span className="text-sm">لا إرسال بيانات للخارج أبداً</span>
                      </div>
                      <div className="flex items-center gap-2 text-green-600">
                        <Lock className="w-4 h-4" />
                        <span className="text-sm">معالجة محلية 100%</span>
                      </div>
                      <div className="flex items-center gap-2 text-green-600">
                        <Zap className="w-4 h-4" />
                        <span className="text-sm">تنظيف تلقائي للذاكرة</span>
                      </div>
                    </>
                  )}
                  {selected.id === 'cloud_encrypted' && (
                    <>
                      <div className="flex items-center gap-2 text-yellow-600">
                        <Lock className="w-4 h-4" />
                        <span className="text-sm">تشفير البيانات الحساسة</span>
                      </div>
                      <div className="flex items-center gap-2 text-yellow-600">
                        <Eye className="w-4 h-4" />
                        <span className="text-sm">قناع للمعلومات الشخصية</span>
                      </div>
                    </>
                  )}
                  {selected.id === 'cloud_direct' && (
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="w-4 h-4" />
                      <span className="text-sm">لا حماية للخصوصية</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">⚡ الأداء المتوقع:</h4>
                <div className="space-y-2 text-sm">
                  <div>الدقة: <span className="font-bold">{selected.accuracy}</span></div>
                  <div>السرعة: <span className="font-bold">{selected.speed}</span></div>
                  <div>التكلفة: <span className="font-bold">{selected.cost}</span></div>
                  <div>مستوى الخصوصية: <span className="font-bold">{selected.privacy}</span></div>
                </div>
              </div>
            </div>
          );
        })()}
      </div>

      {/* تحذير مهم */}
      {selectedSystem !== 'local_privacy' && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800">⚠️ تحذير مهم</h4>
              <p className="text-yellow-700 text-sm mt-1">
                النظام المختار قد يرسل بيانات حساسة للخوادم الخارجية. 
                للحصول على أقصى حماية للخصوصية، يُنصح باستخدام النظام المحلي 100%.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* زر الحفظ */}
      <div className="mt-8 text-center">
        <button
          className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          onClick={() => {
            const selected = systems.find(s => s.id === selectedSystem);
            alert(`✅ تم حفظ الاختيار: ${selected?.name}\n🔒 مستوى الخصوصية: ${selected?.privacy}`);
          }}
        >
          💾 حفظ الاختيار وتطبيق النظام
        </button>
      </div>
    </div>
  );
}
