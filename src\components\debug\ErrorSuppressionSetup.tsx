'use client';

import { useEffect } from 'react';
import { setupErrorSuppression } from '@/lib/error-suppression';

/**
 * مكون لإعداد قمع الأخطاء الشائعة
 */
export default function ErrorSuppressionSetup() {
  useEffect(() => {
    // إعداد قمع الأخطاء فقط في المتصفح
    if (typeof window !== 'undefined') {
      setupErrorSuppression();
    }
  }, []);

  // هذا المكون لا يعرض أي شيء
  return null;
}
