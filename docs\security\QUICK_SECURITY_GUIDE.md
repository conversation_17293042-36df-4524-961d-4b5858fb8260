# ⚡ دليل الأمان السريع للمطورين

> **إرشادات سريعة لاستخدام أنظمة الأمان الجديدة**  
> **للمطورين والمدراء التقنيين**

---

## 🚨 **إجراءات فورية مطلوبة**

### **1. تحديث متغيرات البيئة (CRITICAL)**

```env
# في ملف .env.local
DOCUMENT_ENCRYPTION_KEY=your-super-secret-256-bit-key-here
AUDIT_ENCRYPTION_KEY=your-audit-encryption-256-bit-key-here
ENABLE_2FA=true
SESSION_TIMEOUT_MINUTES=30
MAX_CONCURRENT_SESSIONS=5
AUDIT_LOG_RETENTION_DAYS=365
SECURITY_ALERT_EMAIL=<EMAIL>
```

### **2. نشر قواعد Firebase**

```bash
firebase deploy --only firestore:rules
```

### **3. تفعيل المصادقة الثنائية للمدراء**

```typescript
import { Apex2FAEngine } from '@/lib/advanced-2fa';

// إعداد 2FA للمدير
const setup = await Apex2FAEngine.setup2FA(adminUserId);
console.log('QR Code:', setup.qrCode);
console.log('Backup Codes:', setup.backupCodes);

// تفعيل بعد التحقق
await Apex2FAEngine.enable2FA(adminUserId, totpCode);
```

---

## 🔐 **استخدام نظام التشفير**

### **تشفير البيانات الحساسة:**

```typescript
import { ApexEncryptionEngine } from '@/lib/encryption';

// تشفير متقدم مع PFS
const context = {
  userId: 'user123',
  deviceFingerprint: 'device456',
  sessionId: 'session789'
};

const encrypted = await ApexEncryptionEngine.encryptWithPFS(
  sensitiveData, 
  context
);

// فك التشفير
const decrypted = await ApexEncryptionEngine.decryptWithVerification(
  encrypted, 
  context
);
```

### **تشفير سريع:**

```typescript
// للبيانات البسيطة
const quickEncrypted = ApexEncryptionEngine.encryptSensitiveData(password);
const quickDecrypted = ApexEncryptionEngine.decryptSensitiveData(quickEncrypted);
```

---

## 🔒 **إعداد المصادقة الثنائية**

### **للمستخدمين الجدد:**

```typescript
import { Apex2FAEngine } from '@/lib/advanced-2fa';

// 1. إعداد 2FA
const setup = await Apex2FAEngine.setup2FA(userId);

// 2. عرض QR Code للمستخدم
showQRCode(setup.qrCode);

// 3. حفظ رموز النسخ الاحتياطية
saveBackupCodes(setup.backupCodes);

// 4. تفعيل بعد التحقق
const verified = await Apex2FAEngine.enable2FA(userId, userEnteredCode);
```

### **التحقق من الرموز:**

```typescript
// التحقق من TOTP
const isValid = await Apex2FAEngine.verifyTOTP(userId, totpCode);

// التحقق من رمز النسخ الاحتياطية
const backupValid = await Apex2FAEngine.verifyBackupCode(userId, backupCode);
```

---

## 🕵️ **مراقبة التهديدات**

### **تحليل التهديدات في الوقت الفعلي:**

```typescript
import { ApexIntrusionDetection } from '@/lib/intrusion-detection';

// في middleware أو API routes
const requestData = {
  ip: req.ip,
  userAgent: req.headers['user-agent'],
  location: getLocationFromIP(req.ip),
  deviceFingerprint: getDeviceFingerprint(req)
};

const threat = await ApexIntrusionDetection.analyzeRealTimeThreats(
  userId,
  sessionId,
  requestData
);

// التعامل مع التهديدات
if (threat.threatLevel >= ThreatLevel.HIGH) {
  // إجراءات أمنية فورية
  await handleHighThreat(userId, threat);
}
```

### **التعامل مع التهديدات:**

```typescript
async function handleHighThreat(userId: string, threat: ThreatAssessment) {
  if (threat.threatLevel === ThreatLevel.CRITICAL) {
    // قفل الحساب فوراً
    await lockUserAccount(userId);
    
    // إنهاء جميع الجلسات
    await ApexSessionManager.terminateAllUserSessions(userId);
    
    // تنبيه فريق الأمان
    await alertSecurityTeam(threat);
  } else if (threat.threatLevel === ThreatLevel.HIGH) {
    // طلب تحقق إضافي
    await requireAdditionalVerification(userId);
    
    // تقييد الصلاحيات
    await limitUserPermissions(userId);
  }
}
```

---

## 📊 **تسجيل أحداث المراجعة**

### **تسجيل الأحداث الأساسية:**

```typescript
import { 
  ApexAuditSystem, 
  logUserAction, 
  logDataAccess, 
  logSecurityViolation 
} from '@/lib/audit-system';

// تسجيل إجراء المستخدم
await logUserAction('login', userId, { 
  method: '2fa', 
  deviceType: 'mobile' 
});

// تسجيل الوصول للبيانات
await logDataAccess('user_profile', userId, { 
  fields: ['name', 'email'] 
});

// تسجيل انتهاك أمني
await logSecurityViolation('suspicious_login', userId, { 
  reason: 'unusual_location',
  location: 'Unknown Country'
});
```

### **تسجيل مخصص:**

```typescript
await ApexAuditSystem.logAuditEvent(
  AuditEventType.ADMIN_ACTION,
  AuditCategory.SECURITY,
  { action: 'user_account_locked', reason: 'security_violation' },
  {
    userId: adminUserId,
    resource: `user:${targetUserId}`,
    action: 'lock_account',
    result: 'success'
  }
);
```

---

## 🔐 **إدارة الجلسات**

### **إنشاء جلسة آمنة:**

```typescript
import { ApexSessionManager } from '@/lib/session-manager';

const deviceInfo = {
  name: 'iPhone 13',
  type: 'mobile',
  os: 'iOS 15',
  browser: 'Safari'
};

const securityContext = {
  ipAddress: req.ip,
  userAgent: req.headers['user-agent'],
  location: await getLocationFromIP(req.ip),
  loginMethod: '2fa',
  mfaVerified: true,
  riskScore: 0.2 // منخفض المخاطر
};

const session = await ApexSessionManager.createSession(
  userId,
  deviceInfo,
  securityContext
);
```

### **التحقق من الجلسة:**

```typescript
// في middleware
const validation = await ApexSessionManager.validateSession(sessionId);

if (!validation.valid) {
  if (validation.requiresReauth) {
    // إعادة توجيه لتسجيل الدخول
    return redirectToLogin();
  } else {
    // خطأ أمني
    await logSecurityViolation('invalid_session', userId);
    return unauthorizedResponse();
  }
}

// تحديث نشاط الجلسة
await ApexSessionManager.updateSessionActivity(sessionId, {
  ipAddress: req.ip,
  action: req.path
});
```

---

## 🚨 **التعامل مع الحوادث الأمنية**

### **سيناريوهات شائعة:**

#### **1. محاولة اختراق كلمة المرور:**
```typescript
// كشف محاولات متعددة فاشلة
if (failedAttempts >= 5) {
  await logSecurityViolation('brute_force_attempt', userId);
  await lockUserAccount(userId, '15 minutes');
  await alertSecurityTeam({
    type: 'brute_force',
    userId,
    attempts: failedAttempts
  });
}
```

#### **2. تسجيل دخول من موقع مشبوه:**
```typescript
const userLocation = await getUserNormalLocations(userId);
const currentLocation = await getLocationFromIP(req.ip);

if (!isLocationNormal(currentLocation, userLocation)) {
  await logSecurityViolation('suspicious_location', userId, {
    currentLocation,
    normalLocations: userLocation
  });
  
  // طلب تحقق إضافي
  await requireLocationVerification(userId);
}
```

#### **3. جهاز جديد:**
```typescript
const knownDevices = await getKnownDevices(userId);
const currentDevice = getDeviceFingerprint(req);

if (!knownDevices.includes(currentDevice)) {
  await logUserAction('new_device_detected', userId, {
    deviceFingerprint: currentDevice
  });
  
  // طلب تحقق عبر البريد الإلكتروني
  await sendDeviceVerificationEmail(userId, currentDevice);
}
```

---

## 📋 **قائمة مراجعة الأمان**

### **يومياً:**
- [ ] مراجعة سجلات الأمان
- [ ] فحص التنبيهات الأمنية
- [ ] مراقبة الجلسات النشطة
- [ ] فحص محاولات تسجيل الدخول الفاشلة

### **أسبوعياً:**
- [ ] مراجعة تقارير كشف التسلل
- [ ] فحص الأجهزة الجديدة
- [ ] تحديث قوائم التهديدات
- [ ] اختبار أنظمة الإنذار

### **شهرياً:**
- [ ] إنشاء تقرير امتثال شامل
- [ ] مراجعة وتحديث السياسات
- [ ] اختبار خطط الاستجابة للحوادث
- [ ] تدريب الفريق على الإجراءات الأمنية

---

## 🔧 **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **خطأ في التشفير:**
```typescript
// تحقق من وجود مفتاح التشفير
if (!process.env.DOCUMENT_ENCRYPTION_KEY) {
  console.error('❌ مفتاح التشفير مفقود في متغيرات البيئة');
}

// تحقق من صحة البيانات المشفرة
try {
  const decrypted = await ApexEncryptionEngine.decryptWithVerification(data);
} catch (error) {
  console.error('❌ فشل في فك التشفير:', error.message);
}
```

#### **مشاكل 2FA:**
```typescript
// تحقق من إعداد 2FA
const config = await get2FAConfig(userId);
if (!config || !config.enabled) {
  console.warn('⚠️ 2FA غير مفعل للمستخدم:', userId);
}

// تحقق من صحة الرمز
if (!isValidTOTPCode(code)) {
  console.error('❌ رمز TOTP غير صالح');
}
```

#### **مشاكل الجلسات:**
```typescript
// تحقق من انتهاء صلاحية الجلسة
const session = await getSession(sessionId);
if (session.expiresAt < new Date()) {
  console.warn('⚠️ انتهت صلاحية الجلسة:', sessionId);
  await invalidateSession(sessionId);
}
```

---

## 📞 **جهات الاتصال للطوارئ**

### **في حالة حدوث خرق أمني:**

1. **فريق الأمان**: <EMAIL>
2. **المدير التقني**: <EMAIL>
3. **الإدارة العليا**: <EMAIL>

### **خطوات الطوارئ:**

1. **عزل التهديد فوراً**
2. **إشعار فريق الأمان**
3. **توثيق الحادث**
4. **تنفيذ خطة الاستجابة**
5. **متابعة التحقيق**

---

## 🎯 **الخلاصة**

أنظمة الأمان الجديدة توفر حماية شاملة ومتقدمة. تأكد من:

- ✅ **تحديث متغيرات البيئة** بمفاتيح آمنة
- ✅ **تفعيل 2FA** لجميع الحسابات الحساسة
- ✅ **مراقبة السجلات** بانتظام
- ✅ **التدريب المستمر** على الإجراءات الأمنية
- ✅ **اختبار الأنظمة** دورياً

**تذكر**: الأمان مسؤولية الجميع! 🛡️
