// src/components/common/NetworkErrorHandler.tsx
"use client";

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Wifi, 
  WifiOff, 
  Refresh<PERSON>w, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { useLocale } from '@/hooks/use-locale';

interface NetworkErrorHandlerProps {
  error?: string | null;
  onRetry?: () => void;
  showNetworkStatus?: boolean;
  retryCount?: number;
  maxRetries?: number;
  children?: React.ReactNode;
}

export default function NetworkErrorHandler({
  error,
  onRetry,
  showNetworkStatus = true,
  retryCount = 0,
  maxRetries = 3,
  children
}: NetworkErrorHandlerProps) {
  const { t } = useLocale();
  const [isOnline, setIsOnline] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);
  const [lastRetryTime, setLastRetryTime] = useState<Date | null>(null);

  // مراقبة حالة الشبكة
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    // التحقق الأولي
    updateOnlineStatus();

    // إضافة مستمعي الأحداث
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // دالة إعادة المحاولة المحسنة
  const handleRetry = async () => {
    if (isRetrying || !onRetry) return;

    setIsRetrying(true);
    setLastRetryTime(new Date());

    try {
      await onRetry();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  // تحديد نوع الخطأ
  const getErrorType = (errorMessage: string) => {
    if (errorMessage.includes('timeout') || errorMessage.includes('انتهت المهلة')) {
      return 'timeout';
    }
    if (errorMessage.includes('network') || errorMessage.includes('الشبكة')) {
      return 'network';
    }
    if (errorMessage.includes('permission') || errorMessage.includes('صلاحية')) {
      return 'permission';
    }
    if (errorMessage.includes('not-found') || errorMessage.includes('غير موجود')) {
      return 'not-found';
    }
    return 'unknown';
  };

  // رسائل الخطأ المخصصة
  const getErrorMessage = (errorType: string) => {
    switch (errorType) {
      case 'timeout':
        return {
          title: t('connectionTimeout'),
          description: t('connectionTimeoutDescription'),
          icon: Clock,
          color: 'text-orange-600'
        };
      case 'network':
        return {
          title: t('networkError'),
          description: t('networkErrorDescription'),
          icon: WifiOff,
          color: 'text-red-600'
        };
      case 'permission':
        return {
          title: t('permissionError'),
          description: t('permissionErrorDescription'),
          icon: AlertTriangle,
          color: 'text-yellow-600'
        };
      case 'not-found':
        return {
          title: t('dataNotFound'),
          description: t('dataNotFoundDescription'),
          icon: AlertTriangle,
          color: 'text-blue-600'
        };
      default:
        return {
          title: t('unknownError'),
          description: t('unknownErrorDescription'),
          icon: AlertTriangle,
          color: 'text-gray-600'
        };
    }
  };

  // إذا لم يكن هناك خطأ، عرض المحتوى العادي
  if (!error) {
    return (
      <>
        {showNetworkStatus && (
          <div className="mb-4">
            <Badge 
              variant={isOnline ? "default" : "destructive"}
              className="flex items-center gap-2 w-fit"
            >
              {isOnline ? (
                <>
                  <Wifi className="h-3 w-3" />
                  {t('online')}
                </>
              ) : (
                <>
                  <WifiOff className="h-3 w-3" />
                  {t('offline')}
                </>
              )}
            </Badge>
          </div>
        )}
        {children}
      </>
    );
  }

  const errorType = getErrorType(error);
  const errorInfo = getErrorMessage(errorType);
  const IconComponent = errorInfo.icon;

  return (
    <Card className="border-destructive/20">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-full bg-destructive/10`}>
            <IconComponent className={`h-6 w-6 ${errorInfo.color}`} />
          </div>
          <div>
            <CardTitle className={`${errorInfo.color} text-lg`}>
              {errorInfo.title}
            </CardTitle>
            <CardDescription>
              {errorInfo.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* تفاصيل الخطأ */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {error}
          </AlertDescription>
        </Alert>

        {/* معلومات حالة الشبكة */}
        {showNetworkStatus && (
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2">
              {isOnline ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">{t('internetConnected')}</span>
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium">{t('internetDisconnected')}</span>
                </>
              )}
            </div>
            <Badge variant={isOnline ? "default" : "destructive"}>
              {isOnline ? t('online') : t('offline')}
            </Badge>
          </div>
        )}

        {/* معلومات إعادة المحاولة */}
        {retryCount > 0 && (
          <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg dark:bg-orange-900/10 dark:border-orange-800">
            <p className="text-sm text-orange-800 dark:text-orange-300">
              {t('retryAttempt', { current: retryCount, max: maxRetries })}
            </p>
            {lastRetryTime && (
              <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                {t('lastRetryAt')}: {lastRetryTime.toLocaleTimeString()}
              </p>
            )}
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex flex-col gap-3">
          {onRetry && retryCount < maxRetries && (
            <Button
              onClick={handleRetry}
              disabled={isRetrying || !isOnline}
              className="w-full"
              variant={isOnline ? "default" : "secondary"}
            >
              {isRetrying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('retrying')}
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {t('retry')}
                </>
              )}
            </Button>
          )}

          {retryCount >= maxRetries && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {t('maxRetriesReached')}
              </AlertDescription>
            </Alert>
          )}

          {/* زر إعادة تحميل الصفحة */}
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-full"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            {t('reloadPage')}
          </Button>
        </div>

        {/* نصائح للمستخدم */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/10 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
            {t('troubleshootingTips')}
          </h4>
          <ul className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
            <li>• {t('checkInternetConnection')}</li>
            <li>• {t('refreshPage')}</li>
            <li>• {t('clearBrowserCache')}</li>
            {errorType === 'timeout' && <li>• {t('tryAgainLater')}</li>}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
