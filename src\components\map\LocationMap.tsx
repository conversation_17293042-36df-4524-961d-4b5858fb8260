// src/components/map/LocationMap.tsx
"use client";

import type { FC } from 'react';
import { Map, Marker, Overlay } from 'pigeon-maps';
import { useEffect, useState, memo, useMemo, useCallback } from "react";
import type { Point } from "pigeon-maps";
import { MapPin, Store, Navigation, Zap } from 'lucide-react';

interface LocationMapProps {
  latitude: number;
  longitude: number;
  zoom?: number;
  height?: string;
  showNearbyStores?: boolean;
  interactive?: boolean;
  theme?: 'light' | 'dark' | 'satellite';
}

interface NearbyStore {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  category: string;
  distance: number;
}

// تحسين: استخدام memo لمنع إعادة الرسم غير الضرورية
const LocationMap: FC<LocationMapProps> = memo(({
  latitude,
  longitude,
  zoom = 15,
  height = "h-[600px] md:h-[700px] lg:h-[800px] xl:h-[900px]",
  showNearbyStores = false,
  interactive = true,
  theme = 'light'
}) => {
  const [isClient, setIsClient] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [nearbyStores, setNearbyStores] = useState<NearbyStore[]>([]);
  const [selectedStore, setSelectedStore] = useState<NearbyStore | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحسين: مركز الخريطة مع معالجة أفضل للإحداثيات
  const center: Point = useMemo(() => {
    // التأكد من أن الإحداثيات صحيحة وفي النطاق المسموح
    const lat = Math.max(-90, Math.min(90, latitude));
    const lng = Math.max(-180, Math.min(180, longitude));
    return [lat, lng];
  }, [latitude, longitude]);

  const markerPosition: Point = useMemo(() => center, [center]);

  // تحسين: مزودي خرائط متعددين مع دعم themes مختلفة
  const mapProviders = useMemo(() => {
    const providers = {
      light: [
        (x: number, y: number, z: number) => `https://tile.openstreetmap.org/${z}/${x}/${y}.png`,
        (x: number, y: number, z: number) => `https://a.tile.openstreetmap.org/${z}/${x}/${y}.png`,
        (x: number, y: number, z: number) => `https://b.tile.openstreetmap.org/${z}/${x}/${y}.png`,
      ],
      dark: [
        (x: number, y: number, z: number) => `https://cartodb-basemaps-a.global.ssl.fastly.net/dark_all/${z}/${x}/${y}.png`,
        (x: number, y: number, z: number) => `https://cartodb-basemaps-b.global.ssl.fastly.net/dark_all/${z}/${x}/${y}.png`,
      ],
      satellite: [
        (x: number, y: number, z: number) => `https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/${z}/${y}/${x}`,
      ]
    };
    return providers[theme] || providers.light;
  }, [theme]);

  // تحسين: مزود خرائط محسن مع معالجة أخطاء وإعادة محاولة
  const mapProvider = useMemo(() =>
    (x: number, y: number, z: number) => {
      try {
        const providerIndex = retryCount % mapProviders.length;
        return mapProviders[providerIndex](x, y, z);
      } catch (error) {
        console.error('خطأ في تحميل الخريطة:', error);
        if (retryCount < mapProviders.length - 1) {
          setTimeout(() => setRetryCount(prev => prev + 1), 1000);
        } else {
          setMapError('خطأ في تحميل الخريطة');
        }
        return '';
      }
    }, [mapProviders, retryCount]
  );

  // محاكاة بيانات المتاجر القريبة
  const generateNearbyStores = useCallback(() => {
    if (!showNearbyStores) return;

    const mockStores: NearbyStore[] = [
      {
        id: '1',
        name: 'سوبر ماركت الأهلي',
        latitude: latitude + 0.002,
        longitude: longitude + 0.001,
        category: 'بقالة',
        distance: 0.3
      },
      {
        id: '2',
        name: 'مخبز الشام',
        latitude: latitude - 0.001,
        longitude: longitude + 0.002,
        category: 'مخبز',
        distance: 0.5
      },
      {
        id: '3',
        name: 'صيدلية النور',
        latitude: latitude + 0.001,
        longitude: longitude - 0.001,
        category: 'صيدلية',
        distance: 0.2
      }
    ];

    setNearbyStores(mockStores);
  }, [latitude, longitude, showNearbyStores]);

  useEffect(() => {
    generateNearbyStores();
  }, [generateNearbyStores]);

  // معالجة إعادة المحاولة
  const handleRetry = () => {
    setMapError(null);
    setRetryCount(0);
  };

  // معالجة النقر على المتجر
  const handleStoreClick = useCallback((store: NearbyStore) => {
    setSelectedStore(store);
  }, []);

  // رسم أيقونة مخصصة للمتجر
  const renderStoreIcon = (category: string) => {
    switch (category) {
      case 'بقالة':
        return <Store className="w-4 h-4 text-white" />;
      case 'مخبز':
        return <Zap className="w-4 h-4 text-white" />;
      case 'صيدلية':
        return <MapPin className="w-4 h-4 text-white" />;
      default:
        return <Store className="w-4 h-4 text-white" />;
    }
  };

  if (!isClient) {
    return (
      <div className={`${height} w-full bg-muted rounded-lg shadow-md flex items-center justify-center`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
          <p className="text-muted-foreground text-lg font-medium">جاري تحميل الخريطة...</p>
          <p className="text-muted-foreground text-sm">يرجى الانتظار قليلاً</p>
        </div>
      </div>
    );
  }

  if (mapError) {
    return (
      <div className={`${height} w-full bg-muted rounded-lg shadow-md flex items-center justify-center text-muted-foreground`}>
        <div className="text-center space-y-4">
          <p className="font-medium text-lg">{mapError}</p>
          <p className="text-sm">حدث خطأ في تحميل الخريطة</p>
          <button
            onClick={handleRetry}
            className="px-6 py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors font-medium"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${height} w-full rounded-lg shadow-lg overflow-hidden border border-border/50 relative`}
      data-testid="location-map-container"
      style={{
        // إضافة CSS مخصص لحل مشكلة الجزء الرصاصي والبقع
        position: 'relative',
        isolation: 'isolate',
        background: 'transparent',
        backgroundColor: 'transparent',
      }}
    >
      <Map
        center={center}
        zoom={zoom}
        provider={mapProvider}
        animate={true}
        animateMaxScreens={5}
        zoomSnap={true}
        mouseEvents={interactive}
        touchEvents={interactive}
        minZoom={10}
        maxZoom={19}
        attribution={false}
        attributionPrefix={false}
        dprs={[1, 2]}
        metaWheelZoom={interactive}
        metaWheelZoomWarning="استخدم Ctrl + scroll للتكبير"
        twoFingerDrag={interactive}
        twoFingerDragWarning="استخدم إصبعين للتحريك"
        warningZIndex={1000}
        limitBounds="edge"
        style={{
          background: 'transparent',
          backgroundColor: 'transparent',
        }}
      >
        {/* موقع المستخدم الحالي */}
        <Marker
          width={50}
          anchor={markerPosition}
          color="hsl(var(--primary))"
          onClick={() => console.log('تم النقر على موقعك')}
        />

        {/* عرض المتاجر القريبة */}
        {showNearbyStores && nearbyStores.map((store) => (
          <Marker
            key={store.id}
            width={40}
            anchor={[store.latitude, store.longitude]}
            color="hsl(var(--secondary))"
            onClick={() => handleStoreClick(store)}
          />
        ))}

        {/* عرض تفاصيل المتجر المحدد */}
        {selectedStore && (
          <Overlay
            anchor={[selectedStore.latitude, selectedStore.longitude]}
            offset={[0, -40]}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 border max-w-xs">
              <div className="flex items-center gap-2 mb-2">
                {renderStoreIcon(selectedStore.category)}
                <h3 className="font-semibold text-sm">{selectedStore.name}</h3>
              </div>
              <p className="text-xs text-muted-foreground mb-1">{selectedStore.category}</p>
              <p className="text-xs text-muted-foreground">
                المسافة: {selectedStore.distance} كم
              </p>
              <button
                onClick={() => setSelectedStore(null)}
                className="absolute top-1 right-1 w-5 h-5 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-xs hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                ×
              </button>
            </div>
          </Overlay>
        )}
      </Map>

      {/* شريط المعلومات */}
      {showNearbyStores && nearbyStores.length > 0 && (
        <div className="absolute bottom-4 left-4 right-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Navigation className="w-4 h-4 text-primary" />
              <span className="font-medium">تم العثور على {nearbyStores.length} متاجر قريبة</span>
            </div>
            <div className="text-xs text-muted-foreground">
              اضغط على العلامات لعرض التفاصيل
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

// تحسين: إضافة displayName للمكون المحسن
LocationMap.displayName = 'LocationMap';

export default LocationMap;
