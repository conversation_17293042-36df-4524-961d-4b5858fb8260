{"users": [{"id": "1", "name": "<PERSON><PERSON><PERSON><PERSON> محمد", "email": "<EMAIL>", "type": "customer", "status": "active", "createdAt": "2024-01-01T00:00:00Z", "lastLogin": "2024-01-28T10:00:00Z"}, {"id": "2", "name": "فاطمة علي", "email": "<EMAIL>", "type": "merchant", "status": "active", "createdAt": "2024-01-02T00:00:00Z", "lastLogin": "2024-01-28T09:30:00Z"}, {"id": "3", "name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "representative", "status": "active", "createdAt": "2024-01-03T00:00:00Z", "lastLogin": "2024-01-28T11:15:00Z"}, {"id": "4", "name": "سارة أحمد", "email": "<EMAIL>", "type": "customer", "status": "inactive", "createdAt": "2024-01-04T00:00:00Z", "lastLogin": "2024-01-25T14:20:00Z"}, {"id": "5", "name": "عب<PERSON><PERSON><PERSON><PERSON><PERSON> محمد", "email": "ab<PERSON><PERSON>@example.com", "type": "merchant", "status": "pending", "createdAt": "2024-01-05T00:00:00Z", "lastLogin": null}], "pagination": {"total": 1250, "page": 1, "limit": 50, "totalPages": 25}, "stats": {"totalUsers": 1250, "activeUsers": 980, "newUsersToday": 15, "customers": 800, "merchants": 350, "representatives": 100}}