'use client';

import { useEffect } from 'react';
import type { Locale } from '@/lib/i18n';

interface FontProviderProps {
  locale: Locale;
  children: React.ReactNode;
}

/**
 * مكون لإدارة الخطوط بناءً على اللغة المحددة
 * يضمن تطبيق الخط الصحيح على عنصر HTML الجذر
 */
export default function FontProvider({ locale, children }: FontProviderProps) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const htmlElement = document.documentElement;
    const isRTL = locale === 'ar';
    const fontClass = isRTL ? 'font-tajawal' : 'font-inter';

    // إزالة جميع فئات الخطوط السابقة
    htmlElement.classList.remove('font-tajawal', 'font-inter');
    
    // إضافة فئة الخط الجديدة
    htmlElement.classList.add(fontClass);

    // تحديث اتجاه النص
    htmlElement.dir = isRTL ? 'rtl' : 'ltr';
    htmlElement.lang = locale;

    // تنظيف عند إلغاء التحميل
    return () => {
      if (typeof window !== 'undefined') {
        htmlElement.classList.remove('font-tajawal', 'font-inter');
      }
    };
  }, [locale]);

  return <>{children}</>;
}
