// src/app/[locale]/merchant/inventory/page.tsx
"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import {
  ArrowLeft, Warehouse, Package, AlertTriangle, TrendingDown, TrendingUp,
  Search, Filter, Edit, Plus, Minus, RotateCcw, Download, Upload,
  CheckCircle, XCircle, Clock, Loader2, Eye, BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { collection, query, where, getDocs, doc, updateDoc, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { ProductDocument } from '@/types';

interface InventoryItem extends ProductDocument {
  stockStatus: 'in_stock' | 'low_stock' | 'out_of_stock';
  stockMovement: 'up' | 'down' | 'stable';
  lastUpdated: Date;
}

type FilterType = 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
type SortType = 'name' | 'stock' | 'price' | 'updated';

export default function MerchantInventoryPage() {
  const { user } = useAuth();
  const { t, locale } = useLocale();
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [sortType, setSortType] = useState<SortType>('name');
  const [products, setProducts] = useState<InventoryItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<InventoryItem | null>(null);
  const [bulkUpdateMode, setBulkUpdateMode] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());

  // جلب المنتجات
  const fetchProducts = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const productsQuery = query(
        collection(db, 'products'),
        where('merchantUid', '==', user.uid),
        orderBy('name')
      );

      const snapshot = await getDocs(productsQuery);
      const productsData = snapshot.docs.map(doc => {
        const data = doc.data() as ProductDocument;
        const stock = data.stock || 0;
        const lowStockThreshold = data.lowStockThreshold || 10;

        return {
          ...data,
          id: doc.id,
          stockStatus: stock === 0 ? 'out_of_stock' :
                      stock <= lowStockThreshold ? 'low_stock' : 'in_stock',
          stockMovement: 'stable' as const,
          lastUpdated: data.updatedAt?.toDate() || new Date()
        } as InventoryItem;
      });

      setProducts(productsData);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('حدث خطأ في جلب المنتجات');
    } finally {
      setLoading(false);
    }
  };

  // تحديث المخزون
  const updateStock = async (productId: string, newStock: number) => {
    setUpdating(productId);
    try {
      const productRef = doc(db, 'products', productId);
      await updateDoc(productRef, {
        stock: newStock,
        updatedAt: new Date()
      });

      // تحديث الحالة المحلية
      setProducts(prev => prev.map(product =>
        product.id === productId
          ? {
              ...product,
              stock: newStock,
              stockStatus: newStock === 0 ? 'out_of_stock' :
                          newStock <= (product.lowStockThreshold || 10) ? 'low_stock' : 'in_stock',
              lastUpdated: new Date()
            }
          : product
      ));

      toast.success('تم تحديث المخزون بنجاح');
    } catch (error) {
      console.error('Error updating stock:', error);
      toast.error('حدث خطأ في تحديث المخزون');
    } finally {
      setUpdating(null);
    }
  };

  // تحديث جماعي للمخزون
  const bulkUpdateStock = async (updates: { [productId: string]: number }) => {
    setUpdating('bulk');
    try {
      const promises = Object.entries(updates).map(([productId, stock]) => {
        const productRef = doc(db, 'products', productId);
        return updateDoc(productRef, {
          stock,
          updatedAt: new Date()
        });
      });

      await Promise.all(promises);
      await fetchProducts(); // إعادة جلب البيانات
      setBulkUpdateMode(false);
      setSelectedProducts(new Set());
      toast.success('تم تحديث المخزون للمنتجات المحددة بنجاح');
    } catch (error) {
      console.error('Error bulk updating stock:', error);
      toast.error('حدث خطأ في التحديث الجماعي');
    } finally {
      setUpdating(null);
    }
  };

  // فلترة وترتيب المنتجات
  const filteredAndSortedProducts = products
    .filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filterType === 'all' || product.stockStatus === filterType;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortType) {
        case 'name':
          return a.name.localeCompare(b.name, 'ar');
        case 'stock':
          return (b.stock || 0) - (a.stock || 0);
        case 'price':
          return (b.price || 0) - (a.price || 0);
        case 'updated':
          return b.lastUpdated.getTime() - a.lastUpdated.getTime();
        default:
          return 0;
      }
    });

  // إحصائيات المخزون
  const inventoryStats = {
    total: products.length,
    inStock: products.filter(p => p.stockStatus === 'in_stock').length,
    lowStock: products.filter(p => p.stockStatus === 'low_stock').length,
    outOfStock: products.filter(p => p.stockStatus === 'out_of_stock').length,
    totalValue: products.reduce((sum, p) => sum + ((p.price || 0) * (p.stock || 0)), 0)
  };

  // تحديد لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock': return 'bg-green-100 text-green-800';
      case 'low_stock': return 'bg-yellow-100 text-yellow-800';
      case 'out_of_stock': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // تحديد نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_stock': return 'متوفر';
      case 'low_stock': return 'مخزون منخفض';
      case 'out_of_stock': return 'نفد المخزون';
      default: return 'غير محدد';
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [user]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-20" />
              <div>
                <Skeleton className="h-8 w-40 mb-2" />
                <Skeleton className="h-4 w-60" />
              </div>
            </div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-16 mb-1" />
                  <Skeleton className="h-3 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Products Skeleton */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded">
                    <div className="flex items-center gap-4">
                      <Skeleton className="h-12 w-12 rounded" />
                      <div>
                        <Skeleton className="h-4 w-32 mb-2" />
                        <Skeleton className="h-3 w-20" />
                      </div>
                    </div>
                    <Skeleton className="h-8 w-24" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link href={`/${locale}/merchant/dashboard`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Warehouse className="h-8 w-8 text-primary" />
                إدارة المخزون
              </h1>
              <p className="text-muted-foreground">
                مراقبة وإدارة مخزون منتجاتك بكفاءة
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={bulkUpdateMode ? "default" : "outline"}
              onClick={() => setBulkUpdateMode(!bulkUpdateMode)}
            >
              {bulkUpdateMode ? (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  إلغاء التحديث الجماعي
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  تحديث جماعي
                </>
              )}
            </Button>

            <Button onClick={fetchProducts} variant="outline">
              <RotateCcw className="h-4 w-4 mr-2" />
              تحديث
            </Button>
          </div>
        </div>

        {/* إحصائيات المخزون */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold">{inventoryStats.total}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">متوفر</p>
                  <p className="text-2xl font-bold text-green-600">{inventoryStats.inStock}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">مخزون منخفض</p>
                  <p className="text-2xl font-bold text-yellow-600">{inventoryStats.lowStock}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">نفد المخزون</p>
                  <p className="text-2xl font-bold text-red-600">{inventoryStats.outOfStock}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">قيمة المخزون</p>
                  <p className="text-2xl font-bold">{inventoryStats.totalValue.toLocaleString('ar-SA')} ريال</p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* تنبيهات المخزون */}
        {inventoryStats.outOfStock > 0 && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>تنبيه:</strong> لديك {inventoryStats.outOfStock} منتج نفد مخزونه و {inventoryStats.lowStock} منتج بمخزون منخفض.
              يُنصح بإعادة تعبئة المخزون قريباً.
            </AlertDescription>
          </Alert>
        )}

        {/* أدوات البحث والفلترة */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="البحث في المنتجات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={filterType} onValueChange={(value: FilterType) => setFilterType(value)}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المنتجات</SelectItem>
                  <SelectItem value="in_stock">متوفر</SelectItem>
                  <SelectItem value="low_stock">مخزون منخفض</SelectItem>
                  <SelectItem value="out_of_stock">نفد المخزون</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortType} onValueChange={(value: SortType) => setSortType(value)}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">ترتيب بالاسم</SelectItem>
                  <SelectItem value="stock">ترتيب بالمخزون</SelectItem>
                  <SelectItem value="price">ترتيب بالسعر</SelectItem>
                  <SelectItem value="updated">آخر تحديث</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* قائمة المنتجات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>المنتجات ({filteredAndSortedProducts.length})</span>
              {bulkUpdateMode && selectedProducts.size > 0 && (
                <Button
                  onClick={() => {
                    // هنا يمكن إضافة منطق التحديث الجماعي
                    toast.info('ميزة التحديث الجماعي قيد التطوير');
                  }}
                  disabled={updating === 'bulk'}
                >
                  {updating === 'bulk' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  تحديث المحدد ({selectedProducts.size})
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredAndSortedProducts.length > 0 ? (
              <div className="space-y-4">
                {filteredAndSortedProducts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center gap-4">
                      {bulkUpdateMode && (
                        <input
                          type="checkbox"
                          checked={selectedProducts.has(product.id)}
                          onChange={(e) => {
                            const newSelected = new Set(selectedProducts);
                            if (e.target.checked) {
                              newSelected.add(product.id);
                            } else {
                              newSelected.delete(product.id);
                            }
                            setSelectedProducts(newSelected);
                          }}
                          className="h-4 w-4"
                        />
                      )}

                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                        {product.images && product.images.length > 0 ? (
                          <img
                            src={product.images[0]}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Package className="h-8 w-8 text-gray-400" />
                        )}
                      </div>

                      <div className="flex-1">
                        <h3 className="font-medium text-lg">{product.name}</h3>
                        <div className="flex items-center gap-4 mt-1">
                          <Badge className={getStatusColor(product.stockStatus)}>
                            {getStatusText(product.stockStatus)}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            السعر: {product.price?.toLocaleString('ar-SA')} ريال
                          </span>
                          <span className="text-sm text-muted-foreground">
                            آخر تحديث: {product.lastUpdated.toLocaleDateString('ar-SA')}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      {/* عرض المخزون الحالي */}
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {product.stock || 0}
                        </div>
                        <div className="text-xs text-muted-foreground">قطعة</div>
                      </div>

                      {/* أزرار التحكم في المخزون */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateStock(product.id, Math.max(0, (product.stock || 0) - 1))}
                          disabled={updating === product.id || (product.stock || 0) === 0}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>تحديث مخزون: {product.name}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="newStock">الكمية الجديدة</Label>
                                <Input
                                  id="newStock"
                                  type="number"
                                  min="0"
                                  defaultValue={product.stock || 0}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      const newStock = parseInt((e.target as HTMLInputElement).value);
                                      if (!isNaN(newStock) && newStock >= 0) {
                                        updateStock(product.id, newStock);
                                        (e.target as HTMLInputElement).closest('dialog')?.querySelector('button[data-close]')?.click();
                                      }
                                    }
                                  }}
                                />
                              </div>
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  onClick={() => {
                                    const input = document.getElementById('newStock') as HTMLInputElement;
                                    const newStock = parseInt(input.value);
                                    if (!isNaN(newStock) && newStock >= 0) {
                                      updateStock(product.id, newStock);
                                    }
                                  }}
                                  disabled={updating === product.id}
                                >
                                  {updating === product.id ? (
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  ) : (
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                  )}
                                  تحديث
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateStock(product.id, (product.stock || 0) + 1)}
                          disabled={updating === product.id}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* رابط عرض المنتج */}
                      <Link href={`/${locale}/merchant/products`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Package className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">لا توجد منتجات</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || filterType !== 'all'
                    ? 'لا توجد منتجات تطابق معايير البحث والفلترة'
                    : 'لم تقم بإضافة أي منتجات بعد'
                  }
                </p>
                <Link href={`/${locale}/merchant/products/add`}>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة منتج جديد
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نصائح إدارة المخزون */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-blue-600" />
              نصائح إدارة المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-medium">مراقبة المخزون:</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• راقب المنتجات ذات المخزون المنخفض بانتظام</li>
                  <li>• حدد حد أدنى مناسب لكل منتج</li>
                  <li>• تابع معدل دوران المخزون</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">تحسين المبيعات:</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• أعد تعبئة المنتجات الأكثر مبيعاً أولاً</li>
                  <li>• فكر في عروض للمنتجات بطيئة الحركة</li>
                  <li>• حافظ على تنوع المخزون</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
