'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserDocument } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UsersManagement } from '@/components/admin/UsersManagement';
import { 
  Users, 
  Shield, 
  AlertTriangle,
  ArrowLeft
} from 'lucide-react';

export default function AdminUsersPage() {
  const { user, loading: authLoading } = useAuth();
  const { locale, t } = useLocale();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userDoc, setUserDoc] = useState<UserDocument | null>(null);

  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push(`/${locale}/auth/signin`);
      return;
    }

    checkAdminAccess();
  }, [user, authLoading, locale, router]);

  const checkAdminAccess = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // جلب بيانات المستخدم من Firestore
      const userDocRef = doc(db, 'users', user.uid);
      const userSnapshot = await getDoc(userDocRef);

      if (!userSnapshot.exists()) {
        setError(t('userDocumentNotFound'));
        return;
      }

      const userData = userSnapshot.data() as UserDocument;
      setUserDoc(userData);

      // التحقق من صلاحيات الإدارة
      if (userData.userType !== 'admin' && !userData.isAdmin) {
        setError('ليس لديك صلاحية للوصول إلى إدارة المستخدمين');
        // إعادة توجيه إلى الصفحة المناسبة
        setTimeout(() => {
          if (userData.userType === 'customer') {
            router.push(`/${locale}/dashboard`);
          } else if (userData.userType === 'merchant') {
            router.push(`/${locale}/merchant/dashboard`);
          } else {
            router.push(`/${locale}`);
          }
        }, 3000);
        return;
      }

      setLoading(false);
    } catch (error) {
      console.error('Error checking admin access:', error);
      setError('حدث خطأ أثناء التحقق من الصلاحيات');
      setLoading(false);
    }
  };

  // شاشة التحميل
  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-8 w-20" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // شاشة الخطأ
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900">خطأ في الوصول</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            
            <div className="flex flex-col sm:flex-row gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.push(`/${locale}/admin/dashboard`)}
                className="flex-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </Button>
              <Button 
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                إعادة المحاولة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t('usersManagement')}
              </h1>
              <p className="text-gray-600">
                {t('usersManagementSubtitle')}
              </p>
            </div>
          </div>

          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/${locale}/admin/dashboard`)}
              className="p-0 h-auto font-normal hover:text-blue-600"
            >
              {t('adminDashboard')}
            </Button>
            <span>/</span>
            <span className="text-gray-900">{t('usersManagement')}</span>
          </nav>
        </div>

        {/* المكون الرئيسي لإدارة المستخدمين */}
        <UsersManagement />
      </div>
    </div>
  );
}
