"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star, 
  MapPin, 
  Store,
  Plus,
  Minus,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Eye,
  Truck,
  Shield,
  RotateCcw
} from "lucide-react";
import { useLocale } from "@/hooks/use-locale";
import Link from "next/link";
import ProductCard from "@/components/common/ProductCard";
import AddToCartButton from "@/components/cart/AddToCartButton";
import type { ProductDocument, StoreDocument } from "@/types";
import { doc, getDoc, collection, query, where, getDocs, limit } from "firebase/firestore";
import { db } from "@/lib/firebase";

export default function ProductPage() {
  const params = useParams();
  const productId = params?.productId as string;
  const { t, locale } = useLocale();
  
  const [product, setProduct] = useState<ProductDocument | null>(null);
  const [store, setStore] = useState<StoreDocument | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<ProductDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [selectedVariants, setSelectedVariants] = useState<{ [key: string]: string }>({});

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      setIsLoading(true);
      setError(null);

      try {
        const productDoc = await getDoc(doc(db, 'products', productId));
        
        if (productDoc.exists()) {
          const productData = { ...productDoc.data(), id: productDoc.id } as ProductDocument;
          setProduct(productData);

          // Fetch store data
          if (productData.storeId) {
            const storeDoc = await getDoc(doc(db, 'stores', productData.storeId));
            if (storeDoc.exists()) {
              setStore({ ...storeDoc.data(), id: storeDoc.id } as StoreDocument);
            }
          }

          // Fetch related products
          const relatedQuery = query(
            collection(db, 'products'),
            where('category', '==', productData.category),
            where('isActive', '==', true),
            limit(4)
          );
          
          const relatedSnapshot = await getDocs(relatedQuery);
          const relatedData: ProductDocument[] = [];
          
          relatedSnapshot.forEach((doc) => {
            const data = { ...doc.data(), id: doc.id } as ProductDocument;
            if (data.id !== productId) {
              relatedData.push(data);
            }
          });
          
          setRelatedProducts(relatedData);
        } else {
          setError(t('productNotFound'));
        }
      } catch (err) {
        console.error('Error fetching product:', err);
        setError(t('errorFetchingProduct'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [productId, t]);

  const formatPrice = (price: number, currency: string = 'SAR') => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currency === 'SAR' ? 'SAR' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const getStoreInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (product?.stockQuantity || 1)) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (!product) return;
    
    // TODO: Implement add to cart logic
    console.log('Adding to cart:', {
      productId: product.id,
      quantity,
      selectedVariants
    });
  };

  const handleToggleWishlist = () => {
    setIsInWishlist(!isInWishlist);
    // TODO: Implement wishlist logic
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.name,
        text: product?.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const nextImage = () => {
    if (product?.imageUrls) {
      setSelectedImageIndex((prev) => 
        prev === product.imageUrls.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (product?.imageUrls) {
      setSelectedImageIndex((prev) => 
        prev === 0 ? product.imageUrls.length - 1 : prev - 1
      );
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-96 w-full rounded-lg" />
            <div className="flex space-x-2 rtl:space-x-reverse">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-20 w-20 rounded-lg" />
              ))}
            </div>
          </div>
          
          {/* Content Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || t('productNotFound')}</AlertDescription>
        </Alert>
      </div>
    );
  }

  const currentImage = product.imageUrls && product.imageUrls.length > 0 
    ? product.imageUrls[selectedImageIndex] 
    : null;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-muted-foreground mb-6">
        <Link href={`/${locale}`} className="hover:text-primary">
          {t('home')}
        </Link>
        <span>/</span>
        <Link href={`/${locale}/products`} className="hover:text-primary">
          {t('products')}
        </Link>
        <span>/</span>
        <span className="text-foreground">{product.name}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-square overflow-hidden rounded-lg bg-muted">
            {currentImage ? (
              <img 
                src={currentImage}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Eye className="w-16 h-16 text-muted-foreground" />
              </div>
            )}
            
            {/* Navigation Arrows */}
            {product.imageUrls && product.imageUrls.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
                  onClick={prevImage}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
                  onClick={nextImage}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </>
            )}

            {/* Stock Status Badge */}
            {product.stockQuantity === 0 && (
              <div className="absolute top-4 left-4">
                <Badge variant="destructive">{t('outOfStock')}</Badge>
              </div>
            )}
          </div>

          {/* Thumbnail Images */}
          {product.imageUrls && product.imageUrls.length > 1 && (
            <div className="flex space-x-2 rtl:space-x-reverse overflow-x-auto">
              {product.imageUrls.map((imageUrl, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImageIndex === index 
                      ? 'border-primary' 
                      : 'border-transparent hover:border-muted-foreground'
                  }`}
                >
                  <img 
                    src={imageUrl}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
            <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
              <div className="text-3xl font-bold text-primary">
                {formatPrice(product.price, product.currency)}
              </div>
              {product.averageRating && (
                <div className="flex items-center">
                  <Star className="w-5 h-5 fill-yellow-400 text-yellow-400 mr-1" />
                  <span className="font-semibold">{product.averageRating.toFixed(1)}</span>
                  <span className="text-muted-foreground ml-1">
                    ({product.reviewCount || 0} {t('reviews')})
                  </span>
                </div>
              )}
            </div>
            
            {/* Category */}
            <Badge variant="outline" className="mb-4">
              {product.category}
            </Badge>
          </div>

          {/* Store Info */}
          {store && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={store.logoUrl} />
                      <AvatarFallback>
                        {getStoreInitials(store.storeName)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{store.storeName}</h3>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="w-3 h-3 mr-1" />
                        <span>{store.address?.city}</span>
                      </div>
                    </div>
                  </div>
                  <Link href={`/${locale}/stores/${store.merchantUid}`}>
                    <Button variant="outline" size="sm">
                      <Store className="w-4 h-4 mr-2" />
                      {t('visitStore')}
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Variants */}
          {product.variants && product.variants.length > 0 && (
            <div className="space-y-4">
              {product.variants.map((variant) => (
                <div key={variant.name}>
                  <h4 className="font-semibold mb-2">{variant.name}</h4>
                  <div className="flex flex-wrap gap-2">
                    {variant.options.map((option) => (
                      <Button
                        key={option.value}
                        variant={selectedVariants[variant.name] === option.value ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedVariants(prev => ({
                          ...prev,
                          [variant.name]: option.value
                        }))}
                      >
                        {option.value}
                        {option.priceModifier && option.priceModifier !== 0 && (
                          <span className="ml-1">
                            ({option.priceModifier > 0 ? '+' : ''}{formatPrice(option.priceModifier)})
                          </span>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Quantity and Add to Cart */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <span className="font-semibold">{t('quantity')}:</span>
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                  <Input
                    type="number"
                    value={quantity}
                    onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                    className="w-16 text-center border-0 focus-visible:ring-0"
                    min={1}
                    max={product.stockQuantity}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= product.stockQuantity}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground">
                {product.stockQuantity} {t('inStock')}
              </div>
            </div>

            <div className="flex space-x-3 rtl:space-x-reverse">
              <AddToCartButton
                product={product}
                quantity={quantity}
                selectedVariants={selectedVariants}
                variant="default"
                size="default"
                showQuantityControls={false}
                className="flex-1"
              />

              <Button variant="outline" onClick={handleToggleWishlist}>
                <Heart className={`w-4 h-4 ${isInWishlist ? 'fill-current text-red-500' : ''}`} />
              </Button>

              <Button variant="outline" onClick={handleShare}>
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Truck className="w-4 h-4 text-muted-foreground" />
              <span>{t('freeShipping')}</span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Shield className="w-4 h-4 text-muted-foreground" />
              <span>{t('securePayment')}</span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <RotateCcw className="w-4 h-4 text-muted-foreground" />
              <span>{t('returnPolicy')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <Tabs defaultValue="description" className="mb-12">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="description">{t('description')}</TabsTrigger>
          <TabsTrigger value="specifications">{t('specifications')}</TabsTrigger>
          <TabsTrigger value="reviews">{t('reviews')}</TabsTrigger>
        </TabsList>

        <TabsContent value="description" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <p className="text-muted-foreground leading-relaxed">
                {product.description}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="specifications" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-semibold">{t('category')}:</span>
                    <span className="ml-2">{product.category}</span>
                  </div>
                  {product.sku && (
                    <div>
                      <span className="font-semibold">{t('sku')}:</span>
                      <span className="ml-2">{product.sku}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-semibold">{t('stock')}:</span>
                    <span className="ml-2">{product.stockQuantity}</span>
                  </div>
                  <div>
                    <span className="font-semibold">{t('currency')}:</span>
                    <span className="ml-2">{product.currency}</span>
                  </div>
                </div>
                
                {product.tags && product.tags.length > 0 && (
                  <div>
                    <span className="font-semibold">{t('tags')}:</span>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {product.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="mt-6">
          <Card>
            <CardContent className="text-center py-12">
              <Star className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('reviewsComingSoon')}</h3>
              <p className="text-muted-foreground">
                {t('reviewsFeatureWillBeAvailable')}
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold mb-6">{t('relatedProducts')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts.map((relatedProduct) => (
              <ProductCard
                key={relatedProduct.id}
                product={relatedProduct}
                showStore={true}
                onAddToCart={(productId) => {
                  console.log('Add to cart:', productId);
                }}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
