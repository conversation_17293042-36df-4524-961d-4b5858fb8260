'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Plus, X, Info } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { couponService } from '@/services/couponService';
import { CreateCouponData, CouponType } from '@/types/coupon';
import { toast } from 'sonner';
import { useLocale } from '@/hooks/use-locale';

const couponSchema = z.object({
  code: z.string()
    .min(3, 'كود الكوبون يجب أن يكون 3 أحرف على الأقل')
    .max(20, 'كود الكوبون يجب ألا يزيد عن 20 حرف')
    .regex(/^[A-Z0-9]+$/, 'كود الكوبون يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط'),
  type: z.enum(['percentage', 'fixed', 'free_shipping']),
  value: z.number().min(0, 'القيمة يجب أن تكون أكبر من أو تساوي صفر'),
  minOrderAmount: z.number().optional(),
  maxDiscount: z.number().optional(),
  usageLimit: z.number().min(1, 'حد الاستخدام يجب أن يكون 1 على الأقل'),
  validFrom: z.date(),
  validUntil: z.date(),
  description: z.string().optional(),
  internalNotes: z.string().optional(),
  applicableProducts: z.array(z.string()).optional(),
  applicableCategories: z.array(z.string()).optional(),
  excludedProducts: z.array(z.string()).optional(),
  excludedCategories: z.array(z.string()).optional(),
  firstTimeOnly: z.boolean().optional(),
}).refine((data) => {
  if (data.type === 'percentage' && data.value > 100) {
    return false;
  }
  return true;
}, {
  message: 'نسبة الخصم يجب ألا تزيد عن 100%',
  path: ['value']
}).refine((data) => {
  return data.validUntil > data.validFrom;
}, {
  message: 'تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية',
  path: ['validUntil']
});

type CouponFormData = z.infer<typeof couponSchema>;

interface CreateCouponFormProps {
  merchantId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export function CreateCouponForm({ merchantId, onSuccess, onCancel }: CreateCouponFormProps) {
  const { t } = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const form = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      code: '',
      type: 'percentage',
      value: 10,
      usageLimit: 100,
      validFrom: new Date(),
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم من الآن
      description: '',
      internalNotes: '',
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      excludedCategories: [],
      firstTimeOnly: false,
    },
  });

  const watchedType = form.watch('type');

  const generateRandomCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setValue('code', result);
  };

  const onSubmit = async (data: CouponFormData) => {
    setIsLoading(true);
    try {
      const couponData: CreateCouponData = {
        ...data,
        customerRestrictions: data.firstTimeOnly ? { firstTimeOnly: true } : undefined,
      };

      await couponService.createCoupon(merchantId, couponData);
      
      toast.success('تم إنشاء الكوبون بنجاح');
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'فشل في إنشاء الكوبون');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            إنشاء كوبون جديد
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* المعلومات الأساسية */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>كود الكوبون *</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input 
                            placeholder="مثال: SAVE10" 
                            {...field}
                            className="uppercase"
                            onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                          />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={generateRandomCode}
                        >
                          توليد
                        </Button>
                      </div>
                      <FormDescription>
                        كود فريد يستخدمه العملاء لتطبيق الخصم
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>نوع الخصم *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نوع الخصم" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="percentage">نسبة مئوية (%)</SelectItem>
                          <SelectItem value="fixed">مبلغ ثابت (ريال)</SelectItem>
                          <SelectItem value="free_shipping">شحن مجاني</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="value"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {watchedType === 'percentage' ? 'نسبة الخصم (%)' : 
                         watchedType === 'fixed' ? 'مبلغ الخصم (ريال)' : 
                         'قيمة الشحن المجاني (ريال)'}
                      </FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step={watchedType === 'percentage' ? '0.1' : '0.01'}
                          min="0"
                          max={watchedType === 'percentage' ? '100' : undefined}
                          {...field} 
                          onChange={(e) => field.onChange(Number(e.target.value))}
                          disabled={watchedType === 'free_shipping'}
                        />
                      </FormControl>
                      {watchedType === 'percentage' && (
                        <FormDescription>
                          نسبة الخصم من إجمالي الطلب (1-100%)
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="usageLimit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>حد الاستخدام *</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          {...field} 
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        عدد المرات التي يمكن استخدام الكوبون فيها
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* التواريخ */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="validFrom"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>تاريخ البداية *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full pl-3 text-left font-normal"
                            >
                              {field.value ? (
                                format(field.value, "PPP", { locale: ar })
                              ) : (
                                <span>اختر التاريخ</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="validUntil"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>تاريخ الانتهاء *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full pl-3 text-left font-normal"
                            >
                              {field.value ? (
                                format(field.value, "PPP", { locale: ar })
                              ) : (
                                <span>اختر التاريخ</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* الشروط الإضافية */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="minOrderAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الحد الأدنى للطلب (ريال)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01"
                          min="0"
                          placeholder="اختياري"
                          {...field} 
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormDescription>
                        أقل مبلغ مطلوب لتطبيق الكوبون
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {watchedType === 'percentage' && (
                  <FormField
                    control={form.control}
                    name="maxDiscount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الحد الأقصى للخصم (ريال)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            min="0"
                            placeholder="اختياري"
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormDescription>
                          أقصى مبلغ خصم يمكن تطبيقه
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              {/* الوصف */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>وصف الكوبون</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="وصف مختصر للكوبون يظهر للعملاء"
                          className="resize-none"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        وصف يظهر للعملاء عند استخدام الكوبون
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="internalNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ملاحظات داخلية</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="ملاحظات للاستخدام الداخلي فقط"
                          className="resize-none"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        ملاحظات خاصة بك لا يراها العملاء
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* قيود العملاء */}
              <FormField
                control={form.control}
                name="firstTimeOnly"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        للعملاء الجدد فقط
                      </FormLabel>
                      <FormDescription>
                        يمكن استخدام هذا الكوبون للعملاء الذين يقومون بأول طلب فقط
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* أزرار الإجراءات */}
              <div className="flex gap-4 pt-6">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {isLoading ? 'جاري الإنشاء...' : 'إنشاء الكوبون'}
                </Button>
                <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
                  إلغاء
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
