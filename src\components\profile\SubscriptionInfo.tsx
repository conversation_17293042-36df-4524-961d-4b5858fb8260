"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { useLocale } from '@/hooks/use-locale';
import { useAuth } from '@/context/AuthContext';
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { Loader2, Crown, ExternalLink, Sparkles, TrendingUp, Shield } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import Link from 'next/link';
import type { UserDocument } from '@/types';
import { merchantPlans, customerPlans } from '@/constants/plans';

export default function SubscriptionInfo() {
  const { t, locale } = useLocale();
  const { user, initialLoadingCompleted } = useAuth();
  const [loading, setLoading] = useState(true);
  const [userType, setUserType] = useState<'customer' | 'merchant' | null>(null);
  const [planId, setPlanId] = useState<string | null>(null);
  const [planDetails, setPlanDetails] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // تحسين الأداء: استخدام useMemo لتجنب إعادة حساب الخطط
  const defaultPlans = useMemo(() => ({
    customer: customerPlans.find(p => p.id === 'customer-basic'),
    merchant: merchantPlans.find(p => p.id === 'merchant-basic')
  }), []);

  // تحسين الأداء: استخدام useCallback لتجنب إعادة إنشاء الدالة
  const fetchUserSubscription = useCallback(async (retryCount = 0) => {
    if (!user?.uid) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // استخدام الدالة المحسنة للحصول على المستند
      const { getDocumentWithRetry } = await import('@/lib/firestore-utils');
      const result = await getDocumentWithRetry<UserDocument>(
        `users/${user.uid}`,
        {
          retries: retryCount === 0 ? 2 : 1,
          timeout: retryCount === 0 ? 8000 : 12000,
          enableOffline: true
        }
      );

      if (result.error) {
        console.error("❌ Error fetching user document:", result.error);
        setError(result.error);
        return;
      }

      if (result.exists && result.data) {
        const userData = result.data;
        const currentUserType = userData.userType || 'customer';
        const defaultPlanId = currentUserType === 'customer' ? 'customer-basic' : 'merchant-basic';
        const currentPlanId = userData.planId || defaultPlanId;

        setUserType(currentUserType);
        setPlanId(currentPlanId);

        // البحث عن تفاصيل الخطة
        const plans = currentUserType === 'merchant' ? merchantPlans : customerPlans;
        const plan = plans.find(p => p.id === currentPlanId) || defaultPlans[currentUserType];

        setPlanDetails(plan);
        console.log("✅ User document found and processed successfully");
      } else {
        // في حالة عدم وجود مستند المستخدم، إنشاء مستند جديد للعملاء الذين سجلوا عبر Google
        console.warn("User document not found, creating new customer document for Google user");

        try {
          // إنشاء مستند مستخدم جديد كعميل (افتراضي للمستخدمين الذين سجلوا عبر Google)
          const { setDocumentWithRetry } = await import('@/lib/firestore-utils');
          const { serverTimestamp } = await import('firebase/firestore');

          const userDocData = {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName || user.email?.split('@')[0] || 'مستخدم',
            photoURL: user.photoURL,
            userType: 'customer' as const, // افتراضي للمستخدمين الذين سجلوا عبر Google
            planId: 'customer-basic',
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          };

          console.log("Creating user document for Google user:", {
            uid: userDocData.uid,
            email: userDocData.email,
            userType: userDocData.userType,
            planId: userDocData.planId
          });

          const createResult = await setDocumentWithRetry(`users/${user.uid}`, userDocData);

          if (!createResult.success) {
            console.error("Failed to create user document:", createResult.error);
            throw new Error(createResult.error || 'Failed to create user document');
          }
          console.log("✅ User document created successfully for Google user");

          // تعيين البيانات المحلية
          setUserType('customer');
          setPlanId('customer-basic');
          setPlanDetails(defaultPlans.customer);

          // إظهار رسالة نجاح
          const { toast } = await import('@/hooks/use-toast');
          toast({
            title: t('accountSetupComplete'),
            description: t('customerAccountCreated'),
          });

        } catch (createError) {
          console.error("Error creating user document:", createError);

          // محاولة إنشاء المستند مرة أخرى بطريقة مبسطة
          try {
            const { doc, setDoc, serverTimestamp } = await import('firebase/firestore');
            const { db } = await import('@/lib/firebase');

            const simpleUserDoc = {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName || 'مستخدم',
              userType: 'customer',
              planId: 'customer-basic',
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp(),
            };

            await setDoc(doc(db, 'users', user.uid), simpleUserDoc);
            console.log("✅ User document created with simple method");

            // تعيين البيانات المحلية
            setUserType('customer');
            setPlanId('customer-basic');
            setPlanDetails(defaultPlans.customer);

            const { toast } = await import('@/hooks/use-toast');
            toast({
              title: t('accountSetupComplete'),
              description: t('customerAccountCreated'),
            });

          } catch (fallbackError) {
            console.error("Fallback creation also failed:", fallbackError);
            setError(t('failedToCreateUserDocument'));

            // استخدام خطة افتراضية للعملاء كحل مؤقت
            setUserType('customer');
            setPlanId('customer-basic');
            setPlanDetails(defaultPlans.customer);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching subscription info:", error);

      // إعادة المحاولة مرة واحدة فقط للأخطاء غير المتعلقة بانتهاء المهلة
      if (retryCount === 0 && error instanceof Error && error.message !== 'Request timeout') {
        console.log("Retrying fetch...");
        setTimeout(() => fetchUserSubscription(1), 2000);
        return;
      }

      // في حالة انتهاء المهلة المتكرر، قم بتسجيل الخروج التلقائي لتحسين الأمان
      if (error instanceof Error && error.message === 'Request timeout' && retryCount > 0) {
        console.warn("Multiple timeout errors detected. Logging out for security.");
        try {
          const { signOut } = await import('firebase/auth');
          const { auth } = await import('@/lib/firebase');
          await signOut(auth);
          window.location.href = `/${locale}/login`;
          return;
        } catch (signOutError) {
          console.error("Error during automatic logout:", signOutError);
        }
      }

      setError(error instanceof Error ? error.message : 'Unknown error');

      // في حالة الخطأ، لا نعين نوع مستخدم افتراضي
      // بدلاً من ذلك، نترك القيم فارغة ونعرض رسالة خطأ
      console.error("Failed to fetch user subscription info:", error);

      // إعادة تعيين القيم لإظهار حالة الخطأ
      setUserType(null);
      setPlanId(null);
      setPlanDetails(null);
    } finally {
      setLoading(false);
    }
  }, [user, defaultPlans, locale]);

  useEffect(() => {
    // تحسين: عدم تحميل البيانات حتى اكتمال تحميل المصادقة
    if (initialLoadingCompleted) {
      fetchUserSubscription();
    }
  }, [user, initialLoadingCompleted, fetchUserSubscription]);

  // تنسيق عرض السعر
  const formatPriceDisplay = (plan: any) => {
    if (plan.priceDisplayKey === 'free') {
      return t('free');
    }

    if (plan.priceValue !== undefined && plan.currencyKey && plan.periodKey) {
      return t(plan.priceDisplayKey, {
        price: plan.priceValue,
        currency: t(plan.currencyKey),
        period: t(plan.periodKey)
      });
    }

    return '';
  };

  // التحقق من حالة المصادقة
  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('subscriptionInfo')}</CardTitle>
          <CardDescription>{t('loginRequired')}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">{t('loginToViewSubscription')}</p>
          <Link href={`/${locale}/login`} passHref>
            <Button>
              {t('login')}
            </Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className="overflow-hidden border-2 border-primary/20 shadow-xl">
        <CardContent className="pt-6 flex justify-center items-center min-h-[300px]">
          <div className="text-center space-y-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-full animate-pulse"></div>
              <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto relative z-10" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-medium text-foreground">{t('loadingSubscriptionInfo')}</p>
              <p className="text-sm text-muted-foreground">{t('pleaseWait')}</p>
              {error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/10 dark:border-red-800">
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {error === 'Request timeout' ? t('connectionTimeout') : error}
                  </p>
                  <p className="text-xs text-red-500 dark:text-red-500 mt-1">
                    {t('retryingConnection')}
                  </p>
                </div>
              )}
            </div>
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!planDetails) {
    // إذا لم تكن هناك تفاصيل خطة، نتحقق من نوع المستخدم ونعرض خطة افتراضية
    if (userType) {
      const defaultPlan = userType === 'customer'
        ? customerPlans.find(p => p.id === 'customer-basic')
        : merchantPlans.find(p => p.id === 'merchant-basic');

      if (defaultPlan) {
        return (
          <Card className="overflow-hidden border-2 hover:shadow-lg transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5">
              <CardTitle className="flex items-center text-xl">
                <Crown className="me-2 h-6 w-6 text-primary" />
                {t(defaultPlan.nameKey)}
              </CardTitle>
              <CardDescription className="mt-1 text-base">
                {userType === 'merchant' ? t('merchantAccount') : t('customerAccount')}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="mb-6">
                <p className="text-3xl font-bold text-primary">{t('free')}</p>
                <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mt-2">
                  {t('currentPlan')}
                </Badge>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold text-lg flex items-center">
                  <Crown className="me-2 h-5 w-5 text-primary" />
                  {t('planFeatures')}
                </h4>
                <ul className="space-y-2">
                  {defaultPlan.features.map((feature: any, index: number) => (
                    <li key={index} className="flex items-start p-2 rounded-md hover:bg-muted/20 transition-colors">
                      <span className="me-3 text-lg flex-shrink-0 text-green-500">✓</span>
                      <span className="text-sm text-foreground">{t(feature.nameKey)}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-3 pt-0">
              <Link href={`/${locale}/pricing`} passHref className="w-full">
                <Button className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground shadow-lg">
                  <Crown className="me-2 h-4 w-4" />
                  {t('upgradePlan')}
                </Button>
              </Link>
              <Link href={`/${locale}/pricing`} passHref className="w-full">
                <Button variant="outline" className="w-full">
                  <ExternalLink className="me-2 h-4 w-4" />
                  {t('viewAllPlans')}
                </Button>
              </Link>
            </CardFooter>
          </Card>
        );
      }
    }

    return (
      <Card className="border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="text-red-600 dark:text-red-400 flex items-center">
            <svg className="me-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {t('subscriptionError')}
          </CardTitle>
          <CardDescription>{error || t('subscriptionNotFound')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/10 dark:border-red-800">
            <p className="text-sm text-red-600 dark:text-red-400 mb-2">
              {t('subscriptionErrorMessage')}
            </p>
            <p className="text-xs text-red-500 dark:text-red-500">
              {t('contactSupportForHelp')}
            </p>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex flex-col space-y-2">
            <Button
              onClick={() => fetchUserSubscription(0)}
              variant="outline"
              className="w-full"
            >
              <svg className="me-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
              {t('retryLoading')}
            </Button>

            <Link href={`/${locale}/pricing`} passHref className="w-full">
              <Button variant="outline" className="w-full">
                <ExternalLink className="me-2 h-4 w-4" />
                {t('viewAllPlans')}
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border-2 border-primary/20 hover:shadow-2xl hover:border-primary/40 transition-all duration-500 bg-gradient-to-br from-background via-background to-primary/5">
      <CardHeader className="bg-gradient-to-r from-primary/15 via-primary/10 to-primary/5 relative overflow-hidden">
        {/* خلفية متحركة */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-50"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/20 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

        {planDetails.isPopular && (
          <Badge className="absolute top-4 right-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg animate-pulse z-10">
            <Sparkles className="me-1 h-3 w-3" />
            {t('popular')}
          </Badge>
        )}

        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-primary/20 rounded-full">
                <Crown className="h-8 w-8 text-primary" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  {t(planDetails.nameKey)}
                </CardTitle>
                <CardDescription className="text-base font-medium text-muted-foreground">
                  {userType === 'merchant' ? t('merchantAccount') : t('customerAccount')}
                </CardDescription>
              </div>
            </div>

            {planDetails.priceDisplayKey === 'free' && (
              <div className="text-right">
                <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg">
                  <Shield className="me-1 h-3 w-3" />
                  {t('activePlan')}
                </Badge>
              </div>
            )}
          </div>

          {/* عرض السعر بتصميم محسن */}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {formatPriceDisplay(planDetails)}
              </p>
              {planDetails.commission !== undefined && (
                <p className="text-sm text-muted-foreground mt-1 flex items-center">
                  <TrendingUp className="me-1 h-3 w-3" />
                  {t(planDetails.commissionKey || 'commission', { value: planDetails.commission })}
                </p>
              )}
            </div>

            {planDetails.priceDisplayKey === 'free' && (
              <Badge variant="secondary" className="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200 dark:from-green-900/20 dark:to-emerald-900/20 dark:text-green-300">
                {t('currentPlan')}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-8 space-y-8">
        {/* إحصائيات سريعة محسنة */}
        <div className="grid grid-cols-2 gap-6">
          <div className="relative p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border border-primary/20 hover:shadow-lg transition-all duration-300">
            <div className="absolute top-2 right-2">
              <Sparkles className="h-4 w-4 text-primary/60" />
            </div>
            <div className="text-center space-y-2">
              <p className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {planDetails.features.filter((f: any) => f.available).length}
              </p>
              <p className="text-sm font-medium text-muted-foreground">{t('availableFeatures')}</p>
              <Progress
                value={(planDetails.features.filter((f: any) => f.available).length / planDetails.features.length) * 100}
                className="h-2 bg-primary/20"
              />
            </div>
          </div>

          <div className="relative p-6 bg-gradient-to-br from-emerald-500/10 to-emerald-500/5 rounded-xl border border-emerald-500/20 hover:shadow-lg transition-all duration-300">
            <div className="absolute top-2 right-2">
              <TrendingUp className="h-4 w-4 text-emerald-500/60" />
            </div>
            <div className="text-center space-y-2">
              <p className="text-3xl font-bold bg-gradient-to-r from-emerald-500 to-emerald-600 bg-clip-text text-transparent">
                {userType === 'customer' ? '∞' : (planDetails.maxProductImages || '∞')}
              </p>
              <p className="text-sm font-medium text-muted-foreground">
                {userType === 'customer' ? t('orders') : t('productImages')}
              </p>
              {userType === 'merchant' && planDetails.maxProductImages && (
                <Progress
                  value={75}
                  className="h-2 bg-emerald-500/20"
                />
              )}
            </div>
          </div>
        </div>

        {/* قسم المميزات المحسن */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-xl font-bold flex items-center">
              <div className="p-2 bg-primary/20 rounded-lg me-3">
                <Crown className="h-5 w-5 text-primary" />
              </div>
              {t('planFeatures')}
            </h4>
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">
              {planDetails.features.filter((f: any) => f.available).length} / {planDetails.features.length}
            </Badge>
          </div>

          <div className="grid gap-3">
            {planDetails.features.map((feature: any, index: number) => (
              <div
                key={index}
                className={`flex items-center p-4 rounded-xl border transition-all duration-300 hover:shadow-md ${
                  feature.available
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 hover:border-green-300 dark:from-green-900/10 dark:to-emerald-900/10 dark:border-green-800'
                    : 'bg-gradient-to-r from-gray-50 to-slate-50 border-gray-200 dark:from-gray-900/10 dark:to-slate-900/10 dark:border-gray-700'
                }`}
              >
                <div className={`me-4 p-2 rounded-full ${
                  feature.available
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-400 text-white'
                }`}>
                  {feature.available ? (
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <span className={`font-medium ${
                  feature.available
                    ? 'text-foreground'
                    : 'text-muted-foreground line-through'
                }`}>
                  {t(feature.nameKey)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-4 pt-6 bg-gradient-to-r from-background to-primary/5">
        {/* زر الترقية للخطط المجانية */}
        {((userType === 'customer' && planId === 'customer-basic') ||
         (userType === 'merchant' && planId === 'merchant-basic')) && (
          <div className="w-full space-y-3">
            <Link href={`/${locale}/pricing`} passHref className="w-full">
              <Button className="w-full h-12 bg-gradient-to-r from-primary via-primary/90 to-primary/80 hover:from-primary/90 hover:via-primary/80 hover:to-primary/70 text-primary-foreground shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center justify-center space-x-2">
                  <Crown className="h-5 w-5" />
                  <span className="font-semibold">{t('upgradePlan')}</span>
                  <Sparkles className="h-4 w-4 animate-pulse" />
                </div>
              </Button>
            </Link>

            {/* رسالة تشجيعية محسنة */}
            <div className="text-center p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border border-amber-200 dark:from-amber-900/10 dark:to-orange-900/10 dark:border-amber-800">
              <p className="text-sm font-medium text-amber-800 dark:text-amber-300 mb-1">
                {t('upgradeForMoreFeatures')}
              </p>
              <p className="text-xs text-amber-600 dark:text-amber-400">
                {userType === 'customer' ? t('customerUpgradeBenefits') : t('merchantUpgradeBenefits')}
              </p>
            </div>
          </div>
        )}

        {/* زر عرض جميع الخطط */}
        <Link href={`/${locale}/pricing`} passHref className="w-full">
          <Button
            variant="outline"
            className="w-full h-11 border-2 border-primary/30 hover:border-primary/50 hover:bg-primary/5 transition-all duration-300"
          >
            <ExternalLink className="me-2 h-4 w-4" />
            <span className="font-medium">{t('viewAllPlans')}</span>
          </Button>
        </Link>

        {/* معلومات إضافية */}
        <div className="text-center space-y-2 pt-2">
          <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
            <div className="flex items-center">
              <Shield className="me-1 h-3 w-3" />
              <span>{t('securePayment')}</span>
            </div>
            <div className="flex items-center">
              <Crown className="me-1 h-3 w-3" />
              <span>{t('premiumSupport')}</span>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
