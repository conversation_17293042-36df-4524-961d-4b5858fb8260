'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLocale } from '@/hooks/use-locale';
import { 
  X, 
  Mail, 
  UserCheck, 
  UserX, 
  Download,
  Trash2,
  AlertTriangle
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface BulkActionsBarProps {
  selectedUsers: string[];
  onClearSelection: () => void;
  onNotificationSent: () => void;
}

export function BulkActionsBar({ 
  selectedUsers, 
  onClearSelection, 
  onNotificationSent 
}: BulkActionsBarProps) {
  const { t } = useLocale();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showDeactivateConfirm, setShowDeactivateConfirm] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleBulkActivate = async () => {
    setLoading(true);
    try {
      // تنفيذ تفعيل المستخدمين المحددين
      // يمكن إضافة API call هنا
      console.log('Activating users:', selectedUsers);
      
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onClearSelection();
    } catch (error) {
      console.error('Error activating users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDeactivate = async () => {
    setLoading(true);
    try {
      // تنفيذ إلغاء تفعيل المستخدمين المحددين
      console.log('Deactivating users:', selectedUsers);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setShowDeactivateConfirm(false);
      onClearSelection();
    } catch (error) {
      console.error('Error deactivating users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    setLoading(true);
    try {
      // تنفيذ حذف المستخدمين المحددين
      console.log('Deleting users:', selectedUsers);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setShowDeleteConfirm(false);
      onClearSelection();
    } catch (error) {
      console.error('Error deleting users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkExport = () => {
    // تصدير المستخدمين المحددين
    console.log('Exporting users:', selectedUsers);
    
    // محاكاة تصدير CSV
    const csvContent = `ID,Name,Email,Type\n${selectedUsers.map(id => `${id},User Name,<EMAIL>,customer`).join('\n')}`;
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `selected-users-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <>
      <Card className="mb-4 border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {selectedUsers.length} {t('selectedUsers')}
              </Badge>
              <span className="text-sm text-gray-600">
                {t('bulkActions')}
              </span>
            </div>

            <div className="flex items-center gap-2">
              {/* إرسال إشعار */}
              <Button
                variant="outline"
                size="sm"
                onClick={onNotificationSent}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Mail className="h-4 w-4" />
                {t('sendNotification')}
              </Button>

              {/* تفعيل */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkActivate}
                disabled={loading}
                className="flex items-center gap-2 text-green-600 hover:text-green-700"
              >
                <UserCheck className="h-4 w-4" />
                تفعيل
              </Button>

              {/* إلغاء تفعيل */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDeactivateConfirm(true)}
                disabled={loading}
                className="flex items-center gap-2 text-orange-600 hover:text-orange-700"
              >
                <UserX className="h-4 w-4" />
                إلغاء تفعيل
              </Button>

              {/* تصدير */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkExport}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير
              </Button>

              {/* حذف */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={loading}
                className="flex items-center gap-2 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
                حذف
              </Button>

              {/* إلغاء التحديد */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                إلغاء
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* تأكيد إلغاء التفعيل */}
      <AlertDialog open={showDeactivateConfirm} onOpenChange={setShowDeactivateConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              تأكيد إلغاء التفعيل
            </AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من أنك تريد إلغاء تفعيل {selectedUsers.length} مستخدم؟
              سيتم منعهم من الوصول إلى حساباتهم مؤقتاً.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDeactivate}
              disabled={loading}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {loading ? 'جاري إلغاء التفعيل...' : 'إلغاء التفعيل'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* تأكيد الحذف */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              تأكيد الحذف
            </AlertDialogTitle>
            <AlertDialogDescription>
              <div className="space-y-2">
                <p>هل أنت متأكد من أنك تريد حذف {selectedUsers.length} مستخدم نهائياً؟</p>
                <p className="text-red-600 font-medium">
                  ⚠️ هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بهم.
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? 'جاري الحذف...' : 'حذف نهائياً'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
