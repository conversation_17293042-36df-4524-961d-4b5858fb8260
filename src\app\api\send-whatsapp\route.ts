import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// تهيئة Firebase Admin
let firebaseInitialized = false;
if (!getApps().length) {
  // التحقق من وجود مفاتيح Firebase Admin
  if (process.env.FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_CLIENT_EMAIL &&
      process.env.FIREBASE_PRIVATE_KEY &&
      process.env.FIREBASE_PRIVATE_KEY.trim() !== '') {
    try {
      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        }),
      });
      firebaseInitialized = true;
    } catch (error) {
      console.warn('⚠️ فشل في تهيئة Firebase Admin، سيتم استخدام وضع المحاكاة:', error);
    }
  } else {
    console.warn('⚠️ مفاتيح Firebase Admin غير متوفرة، سيتم استخدام وضع المحاكاة');
  }
}

interface WhatsAppRequest {
  to: string;
  message: string;
  type?: 'text' | 'template';
  templateName?: string;
  templateParams?: string[];
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من المصادقة (مع دعم وضع المحاكاة)
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول' },
        { status: 401 }
      );
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken: any = null;

    // محاولة التحقق من الرمز المميز
    if (firebaseInitialized) {
      try {
        const auth = getAuth();
        decodedToken = await auth.verifyIdToken(token);
      } catch (authError) {
        console.warn('⚠️ فشل في التحقق من الرمز المميز، سيتم استخدام وضع المحاكاة:', authError);
        // في وضع المحاكاة، نستخدم UID وهمي
        decodedToken = { uid: 'mock-user-id' };
      }
    } else {
      // في وضع المحاكاة، نستخدم UID وهمي
      decodedToken = { uid: 'mock-user-id' };
    }

    // قراءة بيانات الطلب
    const whatsappData: WhatsAppRequest = await request.json();

    // التحقق من صحة البيانات
    if (!whatsappData.to || !whatsappData.message) {
      return NextResponse.json(
        { error: 'رقم الهاتف والرسالة مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من صحة رقم الهاتف (السعودية)
    const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
    if (!phoneRegex.test(whatsappData.to.replace(/\s/g, ''))) {
      return NextResponse.json(
        { error: 'رقم الهاتف غير صحيح' },
        { status: 400 }
      );
    }

    // إرسال رسالة واتساب
    const whatsappResult = await sendWhatsAppMessage(whatsappData);

    if (!whatsappResult.success) {
      throw new Error(whatsappResult.error || 'فشل في إرسال رسالة واتساب');
    }

    // تسجيل الإرسال في قاعدة البيانات (مع دعم وضع المحاكاة)
    if (firebaseInitialized) {
      try {
        const db = getFirestore();
        await db.collection('whatsapp_logs').add({
          to: whatsappData.to,
          message: whatsappData.message,
          type: whatsappData.type || 'text',
          templateName: whatsappData.templateName,
          sentBy: decodedToken.uid,
          sentAt: new Date(),
          status: 'sent',
          messageId: whatsappResult.messageId,
          cost: whatsappResult.cost || 0
        });
      } catch (dbError) {
        console.warn('⚠️ فشل في تسجيل الإرسال في قاعدة البيانات:', dbError);
      }
    } else {
      // في وضع المحاكاة، نسجل فقط في الكونسول
      console.log('📝 تسجيل محاكاة لإرسال WhatsApp:', {
        to: whatsappData.to,
        messageId: whatsappResult.messageId,
        sentBy: decodedToken.uid
      });
    }

    return NextResponse.json({
      success: true,
      messageId: whatsappResult.messageId,
      message: 'تم إرسال رسالة واتساب بنجاح'
    });

  } catch (error) {
    console.error('خطأ في إرسال رسالة واتساب:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في إرسال رسالة واتساب',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// إرسال رسالة واتساب عبر مزود الخدمة
async function sendWhatsAppMessage(whatsappData: WhatsAppRequest): Promise<{
  success: boolean;
  messageId?: string;
  cost?: number;
  error?: string;
}> {
  try {
    // يمكن استخدام WhatsApp Business API, Twilio, أو مزود محلي
    
    if (process.env.WHATSAPP_BUSINESS_API_TOKEN) {
      return await sendViaWhatsAppBusinessAPI(whatsappData);
    } else if (process.env.TWILIO_WHATSAPP_NUMBER) {
      return await sendViaTwilioWhatsApp(whatsappData);
    } else if (process.env.SAUDI_WHATSAPP_PROVIDER_API_KEY) {
      return await sendViaSaudiWhatsAppProvider(whatsappData);
    } else {
      // في حالة عدم توفر مزود خدمة، نسجل فقط
      console.log('تم محاكاة إرسال رسالة واتساب:', whatsappData);
      return {
        success: true,
        messageId: `mock-whatsapp-${Date.now()}`,
        cost: 0.10
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'خطأ في الإرسال'
    };
  }
}

// إرسال عبر WhatsApp Business API
async function sendViaWhatsAppBusinessAPI(whatsappData: WhatsAppRequest) {
  // تنسيق رقم الهاتف للمعيار الدولي
  let phoneNumber = whatsappData.to.replace(/\s/g, '');
  if (phoneNumber.startsWith('05')) {
    phoneNumber = '966' + phoneNumber.substring(1);
  } else if (phoneNumber.startsWith('5')) {
    phoneNumber = '966' + phoneNumber;
  } else if (phoneNumber.startsWith('+966')) {
    phoneNumber = phoneNumber.substring(1);
  }

  const apiUrl = `https://graph.facebook.com/v18.0/${process.env.WHATSAPP_PHONE_NUMBER_ID}/messages`;
  
  let messageData;
  
  if (whatsappData.type === 'template' && whatsappData.templateName) {
    // رسالة قالب
    messageData = {
      messaging_product: 'whatsapp',
      to: phoneNumber,
      type: 'template',
      template: {
        name: whatsappData.templateName,
        language: {
          code: 'ar'
        },
        components: whatsappData.templateParams ? [
          {
            type: 'body',
            parameters: whatsappData.templateParams.map(param => ({
              type: 'text',
              text: param
            }))
          }
        ] : []
      }
    };
  } else {
    // رسالة نصية عادية
    messageData = {
      messaging_product: 'whatsapp',
      to: phoneNumber,
      type: 'text',
      text: {
        body: whatsappData.message
      }
    };
  }

  const response = await fetch(apiUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.WHATSAPP_BUSINESS_API_TOKEN}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(messageData)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`WhatsApp API Error: ${errorData.error?.message || response.statusText}`);
  }

  const result = await response.json();
  return {
    success: true,
    messageId: result.messages[0].id,
    cost: 0.10 // تقدير تكلفة
  };
}

// إرسال عبر Twilio WhatsApp
async function sendViaTwilioWhatsApp(whatsappData: WhatsAppRequest) {
  try {
    // تحميل Twilio بشكل ديناميكي
    const twilio = await import('twilio').then(mod => mod.default || mod);
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

    // تنسيق رقم الهاتف
    let phoneNumber = whatsappData.to.replace(/\s/g, '');
    if (phoneNumber.startsWith('05')) {
      phoneNumber = '+966' + phoneNumber.substring(1);
    } else if (!phoneNumber.startsWith('+966')) {
      phoneNumber = '+966' + phoneNumber;
    }

    const message = await client.messages.create({
      body: whatsappData.message,
      from: `whatsapp:${process.env.TWILIO_WHATSAPP_NUMBER}`,
      to: `whatsapp:${phoneNumber}`
    });

    return {
      success: true,
      messageId: message.sid,
      cost: parseFloat(message.price) || 0.10
    };
  } catch (error) {
    console.warn('Twilio غير متوفر، استخدام المحاكاة:', error);
    // في حالة عدم توفر Twilio، نستخدم المحاكاة
    return {
      success: true,
      messageId: `mock-twilio-whatsapp-${Date.now()}`,
      cost: 0.10
    };
  }
}

// إرسال عبر مزود سعودي محلي
async function sendViaSaudiWhatsAppProvider(whatsappData: WhatsAppRequest) {
  // مثال لمزود سعودي (يمكن تخصيصه حسب المزود المختار)
  const response = await fetch(process.env.SAUDI_WHATSAPP_PROVIDER_URL!, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.SAUDI_WHATSAPP_PROVIDER_API_KEY}`
    },
    body: JSON.stringify({
      phone: whatsappData.to,
      message: whatsappData.message,
      type: whatsappData.type || 'text'
    })
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const result = await response.json();
  return {
    success: result.success || true,
    messageId: result.messageId || `saudi-whatsapp-${Date.now()}`,
    cost: result.cost || 0.08
  };
}
