// src/context/AuthContext.tsx
"use client";

import type { User } from 'firebase/auth';
import { auth, db } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import type { ReactNode} from 'react';
import { createContext, useContext, useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import SessionManager from '@/components/auth/SessionManager';
import { AuthLoading } from '@/components/ui/loading-states';
import type { UserDocument } from '@/types';


interface AuthContextType {
  user: User | null;
  loading: boolean;
  initialLoadingCompleted: boolean; // To track if the initial auth check has completed
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true); // True while initial check is in progress
  const [initialLoadingCompleted, setInitialLoadingCompleted] = useState(false);
  const [locale, setLocale] = useState('ar'); // افتراضي العربية


  // وظيفة محسنة للتحقق من وجود مستند المستخدم فقط (بدون إنشاء)
  const checkUserDocument = async (user: User) => {
    try {
      // استخدام الدالة المحسنة للتحقق من وجود المستند
      const { getDocumentWithRetry } = await import('@/lib/firestore-utils');
      const result = await getDocumentWithRetry(
        `users/${user.uid}`,
        {
          retries: 1, // محاولة واحدة فقط في AuthContext لتجنب التأخير
          timeout: 3000, // timeout أقصر
          enableOffline: true
        }
      );

      if (!result.success) {
        // تجاهل الأخطاء الشائعة في AuthContext
        const isCommonError = result.error?.includes('offline') ||
                             result.error?.includes('timeout') ||
                             result.error?.includes('unavailable');

        if (!isCommonError && process.env.NODE_ENV === 'development') {
          console.warn("⚠️ User document check warning:", result.error);
        }
        return null;
      }

      if (result.exists) {
        // تسجيل مبسط للنجاح (فقط في التطوير)
        return result.data;
      } else {
        // المستخدم غير موجود - هذا طبيعي للمستخدمين الجدد
        return null;
      }
    } catch (error: any) {
      // تجاهل الأخطاء الشائعة
      const isCommonError = error.message?.includes('offline') ||
                           error.message?.includes('timeout') ||
                           error.code === 'unavailable';

      if (!isCommonError && process.env.NODE_ENV === 'development') {
        console.warn("⚠️ Auth document check error:", error.message || error);
      }
      return null;
    }
  };

  useEffect(() => {
    // استخراج اللغة من URL
    if (typeof window !== 'undefined') {
      const pathLocale = window.location.pathname.split('/')[1];
      if (pathLocale === 'en' || pathLocale === 'ar') {
        setLocale(pathLocale);
      }
    }

    // إضافة timeout للتأكد من عدم التعليق في حالة التحميل
    const authTimeout = setTimeout(() => {
      if (loading && !initialLoadingCompleted) {
        if (process.env.NODE_ENV === 'development') {
          console.warn("⚠️ Auth loading timeout, forcing completion");
        }
        setLoading(false);
        setInitialLoadingCompleted(true);
      }
    }, 8000); // 8 ثوان timeout

    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      // تقليل الرسائل في وحدة التحكم
      if (process.env.NODE_ENV === 'development' && currentUser) {
        console.log("🔄 Auth state: User authenticated");
      }

      setUser(currentUser);

      // التحقق من وجود مستند المستخدم فقط (بدون إنشاء)
      if (currentUser) {
        await checkUserDocument(currentUser);
      }

      setLoading(false);
      setInitialLoadingCompleted(true);

      // مسح timeout عند اكتمال التحميل
      clearTimeout(authTimeout);
    });

    return () => {
      unsubscribe();
      clearTimeout(authTimeout);
    };
  }, []);

  // تحسين: عرض loading مبسط وسريع مع معلومات إضافية
  if (loading && !initialLoadingCompleted) {
    return <AuthLoading message="التحقق من حالة المصادقة" />;
  }

  return (
    <AuthContext.Provider value={{ user, loading, initialLoadingCompleted }}>
      <SessionManager locale={locale} />
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
