#!/usr/bin/env node

/**
 * سكريبت ذكي لإصلاح التكرارات في ملف الترجمة الإنجليزي
 * يتعامل مع التكرارات على مستويات مختلفة من البنية
 */

const fs = require('fs');
const path = require('path');

const EN_TRANSLATIONS_PATH = path.join(process.cwd(), 'src', 'locales', 'en.json');

/**
 * إصلاح التكرارات الذكي
 */
function smartFixDuplicates() {
  console.log('🧠 بدء الإصلاح الذكي للتكرارات...');
  
  try {
    // قراءة الملف
    const content = fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8');
    const data = JSON.parse(content);
    
    // إنشاء نسخة احتياطية
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${EN_TRANSLATIONS_PATH}.backup.${timestamp}`;
    fs.writeFileSync(backupPath, content);
    console.log(`   💾 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    
    let fixes = 0;
    
    // إصلاح التكرارات المحددة
    const duplicatesToFix = [
      { main: 'profile', nested: 'representative.nav.profile' },
      { main: 'dashboard', nested: 'representative.nav.dashboard' },
      { main: 'orders', nested: 'representative.nav.orders' },
      { main: 'earnings', nested: 'representative.nav.earnings' },
      { main: 'loading', nested: 'representative.dashboard.loading' },
      { main: 'active', nested: 'representative.dashboard.active' },
      { main: 'inactive', nested: 'representative.dashboard.inactive' },
      { main: 'available', nested: 'representative.dashboard.available' },
      { main: 'unavailable', nested: 'representative.dashboard.unavailable' },
      { main: 'totalDeliveries', nested: 'representative.dashboard.totalDeliveries' },
      { main: 'monthlyEarnings', nested: 'representative.dashboard.monthlyEarnings' },
      { main: 'totalEarnings', nested: 'representative.dashboard.totalEarnings' },
      { main: 'averageRating', nested: 'representative.dashboard.averageRating' },
      { main: 'reviews', nested: 'representative.dashboard.reviews' },
      { main: 'minutes', nested: 'representative.dashboard.minutes' }
    ];
    
    duplicatesToFix.forEach(({ main, nested }) => {
      const nestedPath = nested.split('.');
      let current = data;
      
      // التنقل إلى المسار المتداخل
      for (let i = 0; i < nestedPath.length - 1; i++) {
        if (current[nestedPath[i]]) {
          current = current[nestedPath[i]];
        } else {
          return; // المسار غير موجود
        }
      }
      
      const lastKey = nestedPath[nestedPath.length - 1];
      
      // إذا كان المفتاح موجود في المستوى الرئيسي والمتداخل
      if (data[main] && current[lastKey]) {
        console.log(`   ❌ إزالة تكرار: ${nested}`);
        delete current[lastKey];
        fixes++;
      }
    });
    
    // إصلاح تكرار representative.representative
    if (data.representative && data.representative.representative) {
      console.log(`   ❌ إزالة تكرار: representative.representative`);
      delete data.representative.representative;
      fixes++;
    }
    
    // إصلاح تكرار orders في مستوى أعمق
    if (data.orders && data.representative && data.representative.orders) {
      console.log(`   ❌ إزالة تكرار: representative.orders`);
      delete data.representative.orders;
      fixes++;
    }
    
    // إصلاح تكرار earnings في مستوى أعمق
    if (data.earnings && data.representative && data.representative.earnings) {
      console.log(`   ❌ إزالة تكرار: representative.earnings`);
      delete data.representative.earnings;
      fixes++;
    }
    
    // كتابة الملف المصلح
    const cleanedContent = JSON.stringify(data, null, 2);
    fs.writeFileSync(EN_TRANSLATIONS_PATH, cleanedContent);
    
    console.log(`   ✅ تم إصلاح ${fixes} تكرار`);
    
    // التحقق من صحة JSON
    JSON.parse(fs.readFileSync(EN_TRANSLATIONS_PATH, 'utf8'));
    console.log(`   ✅ الملف صحيح بعد الإصلاح`);
    
    return { success: true, fixesApplied: fixes };
    
  } catch (error) {
    console.error(`   ❌ خطأ في الإصلاح:`, error.message);
    return { success: false, fixesApplied: 0 };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء الإصلاح الذكي للتكرارات المتبقية...\n');
  
  const result = smartFixDuplicates();
  
  console.log('');
  
  if (result.success) {
    console.log('🎉 تم الإصلاح الذكي للتكرارات بنجاح!');
    console.log(`📊 إجمالي الإصلاحات: ${result.fixesApplied}`);
    
    // تشغيل التحقق النهائي
    console.log('\n🔍 تشغيل التحقق النهائي...');
    try {
      const { execSync } = require('child_process');
      execSync('node scripts/validate-translations.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('⚠️  لم يتمكن من تشغيل التحقق التلقائي، يرجى تشغيله يدوياً');
    }
  } else {
    console.log('❌ فشل في الإصلاح الذكي للتكرارات');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  smartFixDuplicates
};
