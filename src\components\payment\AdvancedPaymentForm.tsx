"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import AdvancedPaymentService, { PaymentMethod, SavedPaymentMethod } from '@/services/advancedPaymentService';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CreditCard,
  Smartphone,
  Banknote,
  Calendar,
  Shield,
  Lock,
  CheckCircle,
  AlertCircle,
  Loader2,
  QrCode,
  Apple,
  Wallet
} from 'lucide-react';
import { toast } from 'sonner';

interface AdvancedPaymentFormProps {
  orderId: string;
  amount: number;
  currency?: string;
  description?: string;
  onPaymentSuccess: (result: any) => void;
  onPaymentError: (error: string) => void;
  className?: string;
}

export default function AdvancedPaymentForm({
  orderId,
  amount,
  currency = 'SAR',
  description,
  onPaymentSuccess,
  onPaymentError,
  className
}: AdvancedPaymentFormProps) {
  const { user } = useAuth();
  const [availableMethods, setAvailableMethods] = useState<PaymentMethod[]>([]);
  const [savedMethods, setSavedMethods] = useState<SavedPaymentMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [selectedSavedMethod, setSelectedSavedMethod] = useState<string>('');
  const [useNewMethod, setUseNewMethod] = useState(true);
  const [saveMethod, setSaveMethod] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState<any>(null);

  // بيانات البطاقة
  const [cardData, setCardData] = useState({
    number: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    holderName: ''
  });

  // بيانات التقسيط
  const [installmentPlan, setInstallmentPlan] = useState({
    months: 3,
    monthlyAmount: 0,
    totalAmount: 0,
    interestRate: 0
  });

  useEffect(() => {
    loadPaymentMethods();
    if (user?.uid) {
      loadSavedMethods();
    }
  }, [amount, currency, user]);

  useEffect(() => {
    if (selectedMethod.includes('installment')) {
      calculateInstallmentPlan();
    }
  }, [selectedMethod, amount]);

  const loadPaymentMethods = () => {
    const methods = AdvancedPaymentService.getAvailablePaymentMethods(amount, currency);
    setAvailableMethods(methods);
    
    if (methods.length > 0 && !selectedMethod) {
      setSelectedMethod(methods[0].id);
    }
  };

  const loadSavedMethods = async () => {
    if (!user?.uid) return;
    
    try {
      const methods = await AdvancedPaymentService.getSavedPaymentMethods(user.uid);
      setSavedMethods(methods);
      
      if (methods.length > 0) {
        setUseNewMethod(false);
        setSelectedSavedMethod(methods.find(m => m.isDefault)?.id || methods[0].id);
      }
    } catch (error) {
      console.error('خطأ في تحميل طرق الدفع المحفوظة:', error);
    }
  };

  const calculateInstallmentPlan = () => {
    const months = parseInt(selectedMethod.split('_')[1]) || 3;
    const interestRate = months === 3 ? 3 : 5; // نسبة الفائدة
    const totalAmount = amount + (amount * interestRate / 100);
    const monthlyAmount = totalAmount / months;

    setInstallmentPlan({
      months,
      monthlyAmount: Math.round(monthlyAmount * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100,
      interestRate
    });
  };

  const handlePayment = async () => {
    if (!user?.uid) {
      onPaymentError('يجب تسجيل الدخول أولاً');
      return;
    }

    try {
      setProcessing(true);

      const paymentRequest = {
        orderId,
        amount,
        currency,
        customerId: user.uid,
        paymentMethodId: useNewMethod ? selectedMethod : selectedSavedMethod,
        description,
        installmentPlan: selectedMethod.includes('installment') ? installmentPlan : undefined
      };

      const result = await AdvancedPaymentService.processPayment(paymentRequest);
      
      setPaymentResult(result);

      if (result.status === 'completed') {
        toast.success('تم الدفع بنجاح');
        onPaymentSuccess(result);
      } else if (result.status === 'pending' || result.status === 'processing') {
        toast.info('جاري معالجة الدفع...');
        // إذا كان هناك رابط دفع، فتح نافذة جديدة
        if (result.paymentUrl) {
          window.open(result.paymentUrl, '_blank');
        }
      } else {
        throw new Error(result.errorMessage || 'فشل في معالجة الدفع');
      }

      // حفظ طريقة الدفع إذا طُلب ذلك
      if (saveMethod && useNewMethod && selectedMethod.startsWith('card')) {
        await savePaymentMethod();
      }

    } catch (error) {
      console.error('خطأ في الدفع:', error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ في معالجة الدفع';
      toast.error(errorMessage);
      onPaymentError(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  const savePaymentMethod = async () => {
    if (!user?.uid || !cardData.number) return;

    try {
      // تشفير بيانات البطاقة (في التطبيق الحقيقي)
      const maskedNumber = `****-****-****-${cardData.number.slice(-4)}`;
      
      await AdvancedPaymentService.savePaymentMethod(user.uid, {
        type: 'card',
        provider: selectedMethod.toUpperCase(),
        encryptedDetails: 'encrypted_card_data', // يجب تشفيرها فعلياً
        maskedDetails: maskedNumber,
        expiryDate: `${cardData.expiryMonth}/${cardData.expiryYear}`
      });

      toast.success('تم حفظ طريقة الدفع');
      loadSavedMethods();
    } catch (error) {
      console.error('خطأ في حفظ طريقة الدفع:', error);
    }
  };

  const getMethodIcon = (methodId: string) => {
    if (methodId.includes('apple')) return <Apple className="w-5 h-5" />;
    if (methodId.includes('google')) return <Smartphone className="w-5 h-5" />;
    if (methodId.includes('stc')) return <Wallet className="w-5 h-5" />;
    if (methodId.includes('installment')) return <Calendar className="w-5 h-5" />;
    return <CreditCard className="w-5 h-5" />;
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* ملخص الطلب */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            ملخص الدفع
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>المبلغ الأساسي:</span>
              <span className="font-semibold">{formatAmount(amount)}</span>
            </div>
            {selectedMethod.includes('installment') && (
              <>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>رسوم التقسيط ({installmentPlan.interestRate}%):</span>
                  <span>{formatAmount(installmentPlan.totalAmount - amount)}</span>
                </div>
                <div className="flex justify-between font-semibold">
                  <span>المبلغ الإجمالي:</span>
                  <span>{formatAmount(installmentPlan.totalAmount)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>قسط شهري لمدة {installmentPlan.months} أشهر:</span>
                  <span>{formatAmount(installmentPlan.monthlyAmount)}</span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* طرق الدفع المحفوظة */}
      {savedMethods.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>طرق الدفع المحفوظة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="use-saved"
                checked={!useNewMethod}
                onCheckedChange={(checked) => setUseNewMethod(!checked)}
              />
              <Label htmlFor="use-saved">استخدام طريقة دفع محفوظة</Label>
            </div>

            {!useNewMethod && (
              <Select value={selectedSavedMethod} onValueChange={setSelectedSavedMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر طريقة الدفع" />
                </SelectTrigger>
                <SelectContent>
                  {savedMethods.map(method => (
                    <SelectItem key={method.id} value={method.id}>
                      <div className="flex items-center gap-2">
                        {getMethodIcon(method.provider.toLowerCase())}
                        <span>{method.maskedDetails}</span>
                        {method.isDefault && <Badge variant="secondary">افتراضي</Badge>}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </CardContent>
        </Card>
      )}

      {/* طرق الدفع الجديدة */}
      {useNewMethod && (
        <Card>
          <CardHeader>
            <CardTitle>اختر طريقة الدفع</CardTitle>
            <CardDescription>
              جميع المعاملات محمية بأعلى معايير الأمان
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availableMethods.map(method => (
                <div
                  key={method.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedMethod === method.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedMethod(method.id)}
                >
                  <div className="flex items-center gap-3">
                    {getMethodIcon(method.id)}
                    <div className="flex-1">
                      <div className="font-medium">{method.displayName}</div>
                      <div className="text-sm text-muted-foreground">
                        رسوم: {method.fees.percentage}%
                        {method.fees.fixed > 0 && ` + ${method.fees.fixed} ريال`}
                      </div>
                    </div>
                    {selectedMethod === method.id && (
                      <CheckCircle className="w-5 h-5 text-primary" />
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* نموذج بيانات البطاقة */}
            {selectedMethod && availableMethods.find(m => m.id === selectedMethod)?.type === 'card' && (
              <div className="space-y-4 pt-4 border-t">
                <h4 className="font-medium">بيانات البطاقة</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <Label htmlFor="card-number">رقم البطاقة</Label>
                    <Input
                      id="card-number"
                      placeholder="1234 5678 9012 3456"
                      value={cardData.number}
                      onChange={(e) => setCardData(prev => ({ ...prev, number: e.target.value }))}
                      maxLength={19}
                    />
                  </div>
                  <div>
                    <Label htmlFor="expiry-month">شهر الانتهاء</Label>
                    <Select value={cardData.expiryMonth} onValueChange={(value) => setCardData(prev => ({ ...prev, expiryMonth: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="الشهر" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => (
                          <SelectItem key={i + 1} value={String(i + 1).padStart(2, '0')}>
                            {String(i + 1).padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="expiry-year">سنة الانتهاء</Label>
                    <Select value={cardData.expiryYear} onValueChange={(value) => setCardData(prev => ({ ...prev, expiryYear: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="السنة" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 10 }, (_, i) => {
                          const year = new Date().getFullYear() + i;
                          return (
                            <SelectItem key={year} value={String(year)}>
                              {year}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="cvv">رمز الأمان (CVV)</Label>
                    <Input
                      id="cvv"
                      placeholder="123"
                      value={cardData.cvv}
                      onChange={(e) => setCardData(prev => ({ ...prev, cvv: e.target.value }))}
                      maxLength={4}
                      type="password"
                    />
                  </div>
                  <div>
                    <Label htmlFor="holder-name">اسم حامل البطاقة</Label>
                    <Input
                      id="holder-name"
                      placeholder="الاسم كما هو مكتوب على البطاقة"
                      value={cardData.holderName}
                      onChange={(e) => setCardData(prev => ({ ...prev, holderName: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="save-method"
                    checked={saveMethod}
                    onCheckedChange={(checked) => setSaveMethod(checked as boolean)}
                  />
                  <Label htmlFor="save-method" className="text-sm">
                    حفظ طريقة الدفع للاستخدام المستقبلي
                  </Label>
                </div>
              </div>
            )}

            {/* معلومات التقسيط */}
            {selectedMethod.includes('installment') && (
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">تفاصيل التقسيط</h4>
                <div className="space-y-1 text-sm text-blue-800">
                  <div>عدد الأقساط: {installmentPlan.months} أشهر</div>
                  <div>القسط الشهري: {formatAmount(installmentPlan.monthlyAmount)}</div>
                  <div>المبلغ الإجمالي: {formatAmount(installmentPlan.totalAmount)}</div>
                  <div>نسبة الفائدة: {installmentPlan.interestRate}%</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* نتيجة الدفع */}
      {paymentResult && (
        <Card>
          <CardContent className="pt-6">
            {paymentResult.status === 'pending' && paymentResult.qrCode && (
              <div className="text-center space-y-4">
                <QrCode className="w-12 h-12 mx-auto text-blue-500" />
                <h3 className="font-medium">امسح الرمز للدفع</h3>
                <img src={paymentResult.qrCode} alt="QR Code" className="mx-auto max-w-48" />
                <p className="text-sm text-muted-foreground">
                  ينتهي خلال {Math.ceil((paymentResult.expiresAt - new Date()) / 60000)} دقيقة
                </p>
              </div>
            )}

            {paymentResult.status === 'processing' && (
              <div className="text-center space-y-4">
                <Loader2 className="w-12 h-12 mx-auto animate-spin text-blue-500" />
                <h3 className="font-medium">جاري معالجة الدفع</h3>
                <p className="text-sm text-muted-foreground">
                  يرجى عدم إغلاق هذه الصفحة
                </p>
              </div>
            )}

            {paymentResult.status === 'failed' && (
              <div className="text-center space-y-4">
                <AlertCircle className="w-12 h-12 mx-auto text-red-500" />
                <h3 className="font-medium text-red-700">فشل في الدفع</h3>
                <p className="text-sm text-red-600">
                  {paymentResult.errorMessage}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* زر الدفع */}
      <Button
        onClick={handlePayment}
        disabled={processing || !selectedMethod}
        className="w-full"
        size="lg"
      >
        {processing ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
            جاري المعالجة...
          </>
        ) : (
          <>
            <Lock className="w-4 h-4 mr-2" />
            دفع {formatAmount(selectedMethod.includes('installment') ? installmentPlan.totalAmount : amount)}
          </>
        )}
      </Button>

      {/* معلومات الأمان */}
      <div className="text-center text-sm text-muted-foreground">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Shield className="w-4 h-4" />
          <span>محمي بتشفير SSL 256-bit</span>
        </div>
        <p>
          جميع المعاملات آمنة ومحمية. لا نحتفظ ببيانات البطاقة على خوادمنا.
        </p>
      </div>
    </div>
  );
}
