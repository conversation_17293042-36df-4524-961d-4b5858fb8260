'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useLocale } from '@/hooks/use-locale';
import { 
  X, 
  Send, 
  Bell, 
  Users,
  Mail,
  MessageSquare
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface NotificationComposerProps {
  selectedUsers: string[];
  onClose: () => void;
  onSent: () => void;
}

export function NotificationComposer({ 
  selectedUsers, 
  onClose, 
  onSent 
}: NotificationComposerProps) {
  const { t } = useLocale();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info' as 'info' | 'warning' | 'success' | 'error',
    sendMethod: 'push' as 'push' | 'email' | 'both',
    sendToAll: selectedUsers.length === 0
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.message.trim()) {
      return;
    }

    setLoading(true);
    
    try {
      // محاكاة إرسال الإشعار
      console.log('Sending notification:', {
        ...formData,
        recipients: formData.sendToAll ? 'all' : selectedUsers
      });
      
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      onSent();
    } catch (error) {
      console.error('Error sending notification:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'success': return 'نجاح';
      case 'warning': return 'تحذير';
      case 'error': return 'خطأ';
      default: return 'معلومات';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            {t('customNotification')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* معلومات المستلمين */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Users className="h-4 w-4" />
                المستلمون
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="selected"
                    name="recipients"
                    checked={!formData.sendToAll}
                    onChange={() => handleInputChange('sendToAll', false)}
                    disabled={selectedUsers.length === 0}
                  />
                  <Label htmlFor="selected">
                    المستخدمون المحددون ({selectedUsers.length})
                  </Label>
                </div>
                
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="all"
                    name="recipients"
                    checked={formData.sendToAll}
                    onChange={() => handleInputChange('sendToAll', true)}
                  />
                  <Label htmlFor="all">جميع المستخدمين</Label>
                </div>
              </div>
              
              {formData.sendToAll && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-800">
                    ⚠️ سيتم إرسال الإشعار لجميع المستخدمين في المنصة
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* محتوى الإشعار */}
          <div className="space-y-4">
            {/* عنوان الإشعار */}
            <div className="space-y-2">
              <Label htmlFor="title">{t('notificationTitle')}</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="أدخل عنوان الإشعار"
                required
              />
            </div>

            {/* رسالة الإشعار */}
            <div className="space-y-2">
              <Label htmlFor="message">{t('notificationMessage')}</Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                placeholder="أدخل محتوى الإشعار"
                rows={4}
                required
              />
              <p className="text-xs text-gray-500">
                {formData.message.length}/500 حرف
              </p>
            </div>

            {/* نوع الإشعار */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>نوع الإشعار</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="info">معلومات</SelectItem>
                    <SelectItem value="success">نجاح</SelectItem>
                    <SelectItem value="warning">تحذير</SelectItem>
                    <SelectItem value="error">خطأ</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* طريقة الإرسال */}
              <div className="space-y-2">
                <Label>طريقة الإرسال</Label>
                <Select
                  value={formData.sendMethod}
                  onValueChange={(value) => handleInputChange('sendMethod', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="push">إشعار فوري</SelectItem>
                    <SelectItem value="email">بريد إلكتروني</SelectItem>
                    <SelectItem value="both">كلاهما</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* معاينة الإشعار */}
          {formData.title && formData.message && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">معاينة الإشعار</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg bg-gray-50">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-white rounded-lg shadow-sm">
                      <Bell className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900">
                          {formData.title}
                        </h4>
                        <Badge className={getTypeColor(formData.type)}>
                          {getTypeLabel(formData.type)}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        {formData.message}
                      </p>
                      <p className="text-xs text-gray-400 mt-2">
                        الآن • مِخْلاة
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              إلغاء
            </Button>
            
            <Button
              type="submit"
              disabled={loading || !formData.title.trim() || !formData.message.trim()}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الإرسال...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  إرسال الإشعار
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
