import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const searchQuery = searchParams.get('q');
    const searchType = searchParams.get('type') || 'all';
    const limitCount = parseInt(searchParams.get('limit') || '5');

    if (!searchQuery || searchQuery.trim().length < 2) {
      return NextResponse.json(
        { error: 'استعلام البحث قصير جداً' },
        { status: 400 }
      );
    }

    const results = [];

    // البحث في المنتجات
    if (searchType === 'products' || searchType === 'all') {
      const productResults = await searchProducts(searchQuery, limitCount);
      results.push(...productResults);
    }

    // البحث في المتاجر
    if (searchType === 'stores' || searchType === 'all') {
      const storeResults = await searchStores(searchQuery, limitCount);
      results.push(...storeResults);
    }

    // البحث في الفئات
    if (searchType === 'categories' || searchType === 'all') {
      const categoryResults = await searchCategories(searchQuery, limitCount);
      results.push(...categoryResults);
    }

    return NextResponse.json(results);

  } catch (error) {
    console.error('خطأ في البحث:', error);
    return NextResponse.json(
      { error: 'فشل في البحث' },
      { status: 500 }
    );
  }
}

// البحث في المنتجات
async function searchProducts(searchQuery: string, limitCount: number) {
  try {
    const productsRef = collection(db, 'products');
    
    // البحث بالاسم (يمكن تحسينه باستخدام Algolia أو Elasticsearch)
    const q = query(
      productsRef,
      where('isActive', '==', true),
      orderBy('name'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    const products: any[] = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      const productName = data.name?.toLowerCase() || '';
      const productDescription = data.description?.toLowerCase() || '';
      const searchTerm = searchQuery.toLowerCase();

      // فلترة النتائج التي تحتوي على كلمة البحث
      if (productName.includes(searchTerm) || productDescription.includes(searchTerm)) {
        products.push({
          id: doc.id,
          name: data.name,
          description: data.description,
          price: data.price,
          imageUrl: data.imageUrl,
          category: data.category,
          storeId: data.storeId,
          type: 'product'
        });
      }
    });

    return products.slice(0, limitCount);
  } catch (error) {
    console.error('خطأ في البحث في المنتجات:', error);
    return [];
  }
}

// البحث في المتاجر
async function searchStores(searchQuery: string, limitCount: number) {
  try {
    const storesRef = collection(db, 'stores');
    
    const q = query(
      storesRef,
      where('isActive', '==', true),
      where('approvalStatus', '==', 'approved'),
      orderBy('storeName'),
      limit(limitCount * 2) // جلب أكثر للفلترة
    );

    const snapshot = await getDocs(q);
    const stores: any[] = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      const storeName = data.storeName?.toLowerCase() || '';
      const storeDescription = data.storeDescription?.toLowerCase() || '';
      const searchTerm = searchQuery.toLowerCase();

      // فلترة النتائج التي تحتوي على كلمة البحث
      if (storeName.includes(searchTerm) || storeDescription.includes(searchTerm)) {
        stores.push({
          id: doc.id,
          storeName: data.storeName,
          storeDescription: data.storeDescription,
          logoUrl: data.logoUrl,
          address: data.address,
          rating: data.rating,
          reviewCount: data.reviewCount,
          type: 'store'
        });
      }
    });

    return stores.slice(0, limitCount);
  } catch (error) {
    console.error('خطأ في البحث في المتاجر:', error);
    return [];
  }
}

// البحث في الفئات
async function searchCategories(searchQuery: string, limitCount: number) {
  try {
    const categoriesRef = collection(db, 'categories');
    
    const q = query(
      categoriesRef,
      where('isActive', '==', true),
      orderBy('name'),
      limit(limitCount * 2)
    );

    const snapshot = await getDocs(q);
    const categories: any[] = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      const categoryName = data.name?.toLowerCase() || '';
      const categoryDescription = data.description?.toLowerCase() || '';
      const searchTerm = searchQuery.toLowerCase();

      // فلترة النتائج التي تحتوي على كلمة البحث
      if (categoryName.includes(searchTerm) || categoryDescription.includes(searchTerm)) {
        categories.push({
          id: doc.id,
          name: data.name,
          description: data.description,
          icon: data.icon,
          productCount: data.productCount || 0,
          type: 'category'
        });
      }
    });

    return categories.slice(0, limitCount);
  } catch (error) {
    console.error('خطأ في البحث في الفئات:', error);
    return [];
  }
}
