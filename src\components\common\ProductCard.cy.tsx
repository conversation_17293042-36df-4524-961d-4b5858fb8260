import React from 'react'
import { ProductCard } from './ProductCard'

const mockProduct = {
  id: '1',
  name: 'منتج تجريبي',
  description: 'وصف المنتج التجريبي',
  price: 25.99,
  image: '/test-image.jpg',
  category: 'فئة تجريبية',
  inStock: true,
  rating: 4.5,
  reviewCount: 10
}

describe('ProductCard Component', () => {
  it('يعرض معلومات المنتج بشكل صحيح', () => {
    cy.mount(<ProductCard product={mockProduct} />)
    
    cy.contains('منتج تجريبي').should('be.visible')
    cy.contains('وصف المنتج التجريبي').should('be.visible')
    cy.contains('25.99').should('be.visible')
    cy.contains('فئة تجريبية').should('be.visible')
  })

  it('يعرض صورة المنتج', () => {
    cy.mount(<ProductCard product={mockProduct} />)
    cy.get('img').should('have.attr', 'src').and('include', 'test-image.jpg')
    cy.get('img').should('have.attr', 'alt', 'منتج تجريبي')
  })

  it('يعرض التقييم وعدد المراجعات', () => {
    cy.mount(<ProductCard product={mockProduct} />)
    cy.contains('4.5').should('be.visible')
    cy.contains('10').should('be.visible')
  })

  it('يعرض حالة التوفر بشكل صحيح', () => {
    cy.mount(<ProductCard product={mockProduct} />)
    cy.contains('متوفر').should('be.visible')
    
    const outOfStockProduct = { ...mockProduct, inStock: false }
    cy.mount(<ProductCard product={outOfStockProduct} />)
    cy.contains('غير متوفر').should('be.visible')
  })

  it('يستجيب للنقر', () => {
    const onClickSpy = cy.spy().as('onClickSpy')
    cy.mount(<ProductCard product={mockProduct} onClick={onClickSpy} />)
    
    cy.get('[data-testid="product-card"]').click()
    cy.get('@onClickSpy').should('have.been.called')
  })

  it('يعرض زر إضافة للسلة', () => {
    cy.mount(<ProductCard product={mockProduct} />)
    cy.contains('إضافة للسلة').should('be.visible')
  })

  it('يتعامل مع إضافة المنتج للسلة', () => {
    const onAddToCartSpy = cy.spy().as('onAddToCartSpy')
    cy.mount(<ProductCard product={mockProduct} onAddToCart={onAddToCartSpy} />)
    
    cy.contains('إضافة للسلة').click()
    cy.get('@onAddToCartSpy').should('have.been.calledWith', mockProduct)
  })

  it('يعرض شارة الخصم عند وجودها', () => {
    const discountProduct = { ...mockProduct, discount: 20 }
    cy.mount(<ProductCard product={discountProduct} />)
    cy.contains('20%').should('be.visible')
  })

  it('يدعم الأحجام المختلفة', () => {
    cy.mount(
      <div className="space-y-4">
        <ProductCard product={mockProduct} size="sm" data-testid="small" />
        <ProductCard product={mockProduct} size="md" data-testid="medium" />
        <ProductCard product={mockProduct} size="lg" data-testid="large" />
      </div>
    )

    cy.get('[data-testid="small"]').should('be.visible')
    cy.get('[data-testid="medium"]').should('be.visible')
    cy.get('[data-testid="large"]').should('be.visible')
  })

  it('يعرض حالة التحميل', () => {
    cy.mount(<ProductCard product={mockProduct} loading />)
    cy.get('[data-testid="skeleton"]').should('be.visible')
  })

  it('يدعم الفئات المخصصة', () => {
    cy.mount(<ProductCard product={mockProduct} className="custom-product-card" />)
    cy.get('.custom-product-card').should('exist')
  })

  it('يعرض زر المفضلة', () => {
    cy.mount(<ProductCard product={mockProduct} showFavorite />)
    cy.get('[data-testid="favorite-button"]').should('be.visible')
  })

  it('يتعامل مع إضافة/إزالة المفضلة', () => {
    const onToggleFavoriteSpy = cy.spy().as('onToggleFavoriteSpy')
    cy.mount(
      <ProductCard 
        product={mockProduct} 
        showFavorite 
        onToggleFavorite={onToggleFavoriteSpy} 
      />
    )
    
    cy.get('[data-testid="favorite-button"]').click()
    cy.get('@onToggleFavoriteSpy').should('have.been.calledWith', mockProduct.id)
  })
})
