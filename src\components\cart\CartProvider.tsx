"use client";

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import type { CartItem, CartSession } from '@/types';
import { doc, setDoc, getDoc, deleteDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  isLoading: boolean;
  error: string | null;
}

type CartAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CART'; payload: CartItem[] }
  | { type: 'ADD_ITEM'; payload: CartItem }
  | { type: 'UPDATE_ITEM'; payload: { id: string; quantity: number } }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_CART' };

interface CartContextType extends CartState {
  addItem: (item: Omit<CartItem, 'id'>) => Promise<void>;
  updateItem: (id: string, quantity: number) => Promise<void>;
  removeItem: (id: string) => Promise<void>;
  clearCart: () => Promise<void>;
  getItemQuantity: (productId: string) => number;
  isInCart: (productId: string) => boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

const cartReducer = (state: CartState, action: CartAction): CartState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_CART':
      const items = action.payload;
      return {
        ...state,
        items,
        totalItems: items.reduce((sum, item) => sum + item.quantity, 0),
        totalAmount: items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        isLoading: false
      };
    
    case 'ADD_ITEM':
      const newItem = action.payload;
      const existingItemIndex = state.items.findIndex(item => 
        item.productId === newItem.productId && 
        JSON.stringify(item.selectedVariants) === JSON.stringify(newItem.selectedVariants)
      );
      
      let updatedItems: CartItem[];
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        updatedItems = state.items.map((item, index) => 
          index === existingItemIndex 
            ? { ...item, quantity: Math.min(item.quantity + newItem.quantity, item.maxQuantity) }
            : item
        );
      } else {
        // Add new item
        updatedItems = [...state.items, newItem];
      }
      
      return {
        ...state,
        items: updatedItems,
        totalItems: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
        totalAmount: updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      };
    
    case 'UPDATE_ITEM':
      const { id, quantity } = action.payload;
      const itemsAfterUpdate = state.items.map(item => 
        item.id === id 
          ? { ...item, quantity: Math.max(0, Math.min(quantity, item.maxQuantity)) }
          : item
      ).filter(item => item.quantity > 0);
      
      return {
        ...state,
        items: itemsAfterUpdate,
        totalItems: itemsAfterUpdate.reduce((sum, item) => sum + item.quantity, 0),
        totalAmount: itemsAfterUpdate.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      };
    
    case 'REMOVE_ITEM':
      const itemsAfterRemoval = state.items.filter(item => item.id !== action.payload);
      return {
        ...state,
        items: itemsAfterRemoval,
        totalItems: itemsAfterRemoval.reduce((sum, item) => sum + item.quantity, 0),
        totalAmount: itemsAfterRemoval.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      };
    
    case 'CLEAR_CART':
      return {
        ...state,
        items: [],
        totalItems: 0,
        totalAmount: 0
      };
    
    default:
      return state;
  }
};

const initialState: CartState = {
  items: [],
  totalItems: 0,
  totalAmount: 0,
  isLoading: false,
  error: null
};

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, initialState);
  const { user } = useAuth();

  // Load cart from localStorage or Firestore
  useEffect(() => {
    const loadCart = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      try {
        if (user) {
          // Load from Firestore for authenticated users
          const cartDoc = await getDoc(doc(db, 'cart_sessions', user.uid));
          if (cartDoc.exists()) {
            const cartData = cartDoc.data() as CartSession;
            dispatch({ type: 'SET_CART', payload: cartData.items });
          }
        } else {
          // Load from localStorage for guest users
          const savedCart = localStorage.getItem('mikhla_cart');
          if (savedCart) {
            try {
              const cartItems = JSON.parse(savedCart) as CartItem[];
              dispatch({ type: 'SET_CART', payload: cartItems });
            } catch (error) {
              console.error('Error parsing saved cart:', error);
              localStorage.removeItem('mikhla_cart');
            }
          }
        }
      } catch (error) {
        console.error('Error loading cart:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load cart' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    loadCart();
  }, [user]);

  // Save cart to localStorage or Firestore
  const saveCart = async (items: CartItem[]) => {
    try {
      if (user) {
        // Save to Firestore for authenticated users
        const cartSession: Omit<CartSession, 'id'> = {
          userId: user.uid,
          items,
          totalAmount: items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
          totalItems: items.reduce((sum, item) => sum + item.quantity, 0),
          createdAt: serverTimestamp() as any,
          updatedAt: serverTimestamp() as any,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) as any // 7 days
        };
        
        await setDoc(doc(db, 'cart_sessions', user.uid), cartSession);
      } else {
        // Save to localStorage for guest users
        localStorage.setItem('mikhla_cart', JSON.stringify(items));
      }
    } catch (error) {
      console.error('Error saving cart:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to save cart' });
    }
  };

  const addItem = async (itemData: Omit<CartItem, 'id'>) => {
    const newItem: CartItem = {
      ...itemData,
      id: `${itemData.productId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    dispatch({ type: 'ADD_ITEM', payload: newItem });
    
    // Save updated cart
    const updatedItems = cartReducer(state, { type: 'ADD_ITEM', payload: newItem }).items;
    await saveCart(updatedItems);
  };

  const updateItem = async (id: string, quantity: number) => {
    dispatch({ type: 'UPDATE_ITEM', payload: { id, quantity } });
    
    // Save updated cart
    const updatedItems = cartReducer(state, { type: 'UPDATE_ITEM', payload: { id, quantity } }).items;
    await saveCart(updatedItems);
  };

  const removeItem = async (id: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: id });
    
    // Save updated cart
    const updatedItems = cartReducer(state, { type: 'REMOVE_ITEM', payload: id }).items;
    await saveCart(updatedItems);
  };

  const clearCart = async () => {
    dispatch({ type: 'CLEAR_CART' });
    
    try {
      if (user) {
        await deleteDoc(doc(db, 'cart_sessions', user.uid));
      } else {
        localStorage.removeItem('mikhla_cart');
      }
    } catch (error) {
      console.error('Error clearing cart:', error);
    }
  };

  const getItemQuantity = (productId: string): number => {
    const item = state.items.find(item => item.productId === productId);
    return item ? item.quantity : 0;
  };

  const isInCart = (productId: string): boolean => {
    return state.items.some(item => item.productId === productId);
  };

  const value: CartContextType = {
    ...state,
    addItem,
    updateItem,
    removeItem,
    clearCart,
    getItemQuantity,
    isInCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
