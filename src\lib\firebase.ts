// src/lib/firebase.ts
import { initializeApp, getApps, getApp, type FirebaseOptions } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import {
  getFirestore,
  enableNetwork,
  disableNetwork,
  connectFirestoreEmulator,
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  endBefore,
  onSnapshot,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove,
  writeBatch,
  runTransaction
} from 'firebase/firestore';
import { getMessaging, isSupported } from 'firebase/messaging';

const firebaseConfig: FirebaseOptions = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  // storageBucket: تم الاستغناء عن Firebase Storage واستخدام Cloudinary بدلاً منه
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID, // Optional, but good to have
};

// Initialize Firebase
let app;
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp();
}

const auth = getAuth(app);
const db = getFirestore(app); // Initialize Firestore

// Initialize Firebase Cloud Messaging
let messaging: any = null;
if (typeof window !== 'undefined') {
  isSupported().then((supported) => {
    if (supported) {
      messaging = getMessaging(app);
    }
  }).catch((error) => {
    console.warn('Firebase Messaging not supported:', error);
  });
}

// إعداد Firebase محسن مع معالجة أفضل للأخطاء
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // تقليل الرسائل في وحدة التحكم
  try {
    // التحقق من حالة Firebase بصمت
    if (!auth.config?.emulator) {
      // استخدام Firebase Production Environment بصمت
      if (process.env.NODE_ENV === 'development') {
        console.log('🔥 Firebase Production Mode');
      }
    }
  } catch (error) {
    // تجاهل أخطاء التكوين غير المهمة
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Firebase config warning (safe to ignore)');
    }
  }
}

// إعداد Google Auth Provider محسن
const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');
googleProvider.setCustomParameters({
  prompt: 'select_account',
  hd: undefined // إزالة قيود النطاق
});

// دوال مساعدة محسنة لإدارة حالة الشبكة
export const enableFirestoreNetwork = async () => {
  try {
    await enableNetwork(db);
    // تقليل الرسائل في وحدة التحكم
  } catch (error: any) {
    // تجاهل أخطاء الشبكة المتوقعة
    if (!error.message?.includes('already enabled') && process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Firestore network enable warning:', error.code || error.message);
    }
  }
};

// دالة للتحقق من حالة الاتصال بـ Firebase
export const checkFirebaseConnection = async () => {
  try {
    // محاولة بسيطة للاتصال بـ Firestore
    const testRef = collection(db, 'test');
    const testQuery = query(testRef, limit(1));
    await getDocs(testQuery);
    return { connected: true, error: null };
  } catch (error: any) {
    console.warn('Firebase connection check failed:', error.code || error.message);
    return {
      connected: false,
      error: error.code || error.message,
      isOffline: error.code === 'unavailable' || error.message?.includes('offline')
    };
  }
};

export const disableFirestoreNetwork = async () => {
  try {
    await disableNetwork(db);
    // تقليل الرسائل في وحدة التحكم
  } catch (error: any) {
    // تجاهل أخطاء الشبكة المتوقعة
    if (!error.message?.includes('already disabled') && process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Firestore network disable warning:', error.code || error.message);
    }
  }
};

// دالة للتحقق من حالة الاتصال
export const checkNetworkStatus = () => {
  if (typeof window !== 'undefined') {
    return navigator.onLine;
  }
  return true;
};

// معالج أخطاء Firestore محسن مع تقليل الرسائل
export const handleFirestoreError = (error: any, operation: string = 'operation') => {
  // تقليل رسائل الخطأ المتكررة
  const isCommonError = error.code === 'unavailable' ||
                       error.code === 'deadline-exceeded' ||
                       error.message?.includes('offline') ||
                       error.message?.includes('timeout');

  if (process.env.NODE_ENV === 'development' && !isCommonError) {
    console.warn(`⚠️ Firestore ${operation}:`, error.code || error.message);
  }

  // تصنيف الأخطاء
  if (error.code === 'unavailable' || error.code === 'deadline-exceeded' || error.code === 'internal') {
    return {
      type: 'network',
      message: 'مشكلة في الاتصال بالشبكة، يرجى المحاولة مرة أخرى',
      retry: true
    };
  }

  if (error.code === 'permission-denied') {
    return {
      type: 'permission',
      message: 'ليس لديك صلاحية للوصول لهذه البيانات',
      retry: false
    };
  }

  if (error.code === 'not-found') {
    return {
      type: 'not-found',
      message: 'البيانات المطلوبة غير موجودة',
      retry: false
    };
  }

  if (error.code === 'cancelled') {
    return {
      type: 'cancelled',
      message: 'تم إلغاء العملية',
      retry: true
    };
  }

  if (error.code === 'unauthenticated') {
    return {
      type: 'auth',
      message: 'يجب تسجيل الدخول أولاً',
      retry: false
    };
  }

  // معالجة أخطاء الشبكة والاتصال
  if (error.message && (error.message.includes('timeout') || error.message.includes('Request timeout'))) {
    return {
      type: 'timeout',
      message: 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى',
      retry: true
    };
  }

  if (error.message && (error.message.includes('offline') || error.message.includes('network'))) {
    return {
      type: 'offline',
      message: 'مشكلة في الاتصال بالإنترنت، يرجى التحقق من الاتصال',
      retry: true
    };
  }

  return {
    type: 'unknown',
    message: error.message || 'حدث خطأ غير متوقع',
    retry: true
  };
};

export { app, auth, db, messaging, googleProvider };
