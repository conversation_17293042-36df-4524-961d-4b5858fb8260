// src/components/map/MapModal.tsx
"use client";

import { FC, useState, useEffect } from 'react';
import { X, MapPin, Navigation, RefreshCw, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useGeolocation } from '@/hooks/useGeolocation';
import LocationPermission from './LocationPermission';
import LocationMap from './LocationMap';

interface MapModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MapModal: FC<MapModalProps> = ({ isOpen, onClose }) => {
  const {
    latitude,
    longitude,
    loading,
    error,
    permissionStatus,
    requestPermission,
    getCurrentPosition
  } = useGeolocation();

  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (isOpen && permissionStatus === 'prompt') {
      setShowPermissionDialog(true);
    }
  }, [isOpen, permissionStatus]);

  const handlePermissionAllow = () => {
    setShowPermissionDialog(false);
    requestPermission();
  };

  const handlePermissionDeny = () => {
    setShowPermissionDialog(false);
    onClose();
  };

  const handleRefresh = () => {
    getCurrentPosition();
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderContent = () => {
    if (showPermissionDialog) {
      return (
        <LocationPermission
          onAllow={handlePermissionAllow}
          onDeny={handlePermissionDeny}
          loading={loading}
          error={error}
        />
      );
    }

    if (loading) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 border-4 border-primary/30 border-t-primary rounded-full animate-spin mx-auto" />
            <div>
              <p className="font-medium">جاري تحديد موقعك...</p>
              <p className="text-sm text-muted-foreground mt-1">
                يرجى الانتظار بينما نحدد موقعك الحالي
              </p>
            </div>
          </div>
        </div>
      );
    }

    if (error || permissionStatus === 'denied') {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4 max-w-md">
            <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto">
              <MapPin className="w-8 h-8 text-destructive" />
            </div>
            <div>
              <p className="font-medium text-destructive">لا يمكن الوصول للموقع</p>
              <p className="text-sm text-muted-foreground mt-2">
                {error || 'تم رفض إذن الوصول للموقع'}
              </p>
            </div>
            <div className="flex gap-2 justify-center">
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 ml-2" />
                إعادة المحاولة
              </Button>
              <Button onClick={() => setShowPermissionDialog(true)} size="sm">
                <MapPin className="w-4 h-4 ml-2" />
                طلب الإذن مرة أخرى
              </Button>
            </div>
          </div>
        </div>
      );
    }

    if (latitude && longitude) {
      return (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Navigation className="w-4 h-4 text-green-500" />
              <span>موقعك الحالي محدد بدقة</span>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4" />
              </Button>
              <Button onClick={toggleFullscreen} variant="outline" size="sm">
                {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            </div>
          </div>
          
          <div className={isFullscreen ? "h-[80vh]" : "h-96"}>
            <LocationMap
              latitude={latitude}
              longitude={longitude}
              zoom={16}
              height="h-full"
              interactive={true}
            />
          </div>

          <div className="text-center text-sm text-muted-foreground">
            <p>💡 استخدم Ctrl + scroll للتكبير، أو إصبعين للتحريك على الهاتف</p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <MapPin className="w-8 h-8 text-muted-foreground" />
          </div>
          <div>
            <p className="font-medium">جاري تحضير الخريطة...</p>
            <p className="text-sm text-muted-foreground mt-1">
              يرجى الانتظار قليلاً
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <Dialog open={isOpen && !showPermissionDialog} onOpenChange={onClose}>
        <DialogContent className={`${isFullscreen ? 'max-w-[95vw] max-h-[95vh]' : 'max-w-4xl'} p-0`}>
          <DialogHeader className="p-6 pb-4">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-bold flex items-center gap-2">
                <MapPin className="w-6 h-6 text-primary" />
                🗺️ خريطة الموقع
              </DialogTitle>
              <Button
                onClick={onClose}
                variant="ghost"
                size="icon"
                className="h-8 w-8"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </DialogHeader>
          
          <div className="px-6 pb-6">
            {renderContent()}
          </div>
        </DialogContent>
      </Dialog>

      {showPermissionDialog && renderContent()}
    </>
  );
};

export default MapModal;
