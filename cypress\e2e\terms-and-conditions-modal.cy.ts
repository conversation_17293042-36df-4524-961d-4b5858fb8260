// cypress/e2e/terms-and-conditions-modal.cy.ts
describe('اختبار عرض الشروط والأحكام في صفحة التسجيل', () => {
  beforeEach(() => {
    // زيارة صفحة التسجيل
    cy.visit('/ar/signup');
  });

  describe('اختبار العناوين الديناميكية للشروط والأحكام', () => {
    it('يجب أن يعرض "الشروط والأحكام للعملاء" عند اختيار عميل', () => {
      // اختيار نوع المستخدم: عميل
      cy.get('[data-cy="user-type-customer"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('الشروط والأحكام').click();
      
      // التحقق من العنوان الصحيح
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('[role="dialog"]').within(() => {
        cy.contains('الشروط والأحكام للعملاء').should('be.visible');
      });
      
      // التحقق من عدم ظهور رسالة "جاري تحميل الملف الشخصي"
      cy.get('[role="dialog"]').should('not.contain', 'جاري تحميل الملف الشخصي');
    });

    it('يجب أن يعرض "الشروط والأحكام للتجار" عند اختيار تاجر', () => {
      // اختيار نوع المستخدم: تاجر
      cy.get('[data-cy="user-type-merchant"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('الشروط والأحكام').click();
      
      // التحقق من العنوان الصحيح
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('[role="dialog"]').within(() => {
        cy.contains('الشروط والأحكام للتجار').should('be.visible');
      });
      
      // التحقق من عدم ظهور رسالة "جاري تحميل الملف الشخصي"
      cy.get('[role="dialog"]').should('not.contain', 'جاري تحميل الملف الشخصي');
    });

    it('يجب أن يعرض "الشروط والأحكام للمندوبين" عند اختيار مندوب', () => {
      // اختيار نوع المستخدم: مندوب
      cy.get('[data-cy="user-type-representative"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('الشروط والأحكام').click();
      
      // التحقق من العنوان الصحيح
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('[role="dialog"]').within(() => {
        cy.contains('الشروط والأحكام للمندوبين').should('be.visible');
      });
      
      // التحقق من عدم ظهور رسالة "جاري تحميل الملف الشخصي"
      cy.get('[role="dialog"]').should('not.contain', 'جاري تحميل الملف الشخصي');
    });
  });

  describe('اختبار رسالة التحميل المحسنة', () => {
    it('يجب أن يعرض "جاري تحميل الشروط والأحكام" أثناء التحميل', () => {
      // محاكاة تأخير في تحميل الشروط
      cy.intercept('GET', '**/terms**', { delay: 1000 }).as('getTerms');
      
      // اختيار نوع المستخدم والمتابعة
      cy.get('[data-cy="user-type-customer"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('الشروط والأحكام').click();
      
      // التحقق من رسالة التحميل الصحيحة
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('[role="dialog"]').within(() => {
        cy.contains('جاري تحميل الشروط والأحكام').should('be.visible');
        cy.should('not.contain', 'جاري تحميل الملف الشخصي');
      });
    });
  });

  describe('اختبار الوظائف الأساسية للنافذة المنبثقة', () => {
    it('يجب أن تفتح وتغلق النافذة المنبثقة بشكل صحيح', () => {
      // اختيار نوع المستخدم والمتابعة
      cy.get('[data-cy="user-type-customer"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('الشروط والأحكام').click();
      
      // التحقق من فتح النافذة
      cy.get('[role="dialog"]').should('be.visible');
      
      // إغلاق النافذة
      cy.get('[role="dialog"]').within(() => {
        cy.contains('إغلاق').click();
      });
      
      // التحقق من إغلاق النافذة
      cy.get('[role="dialog"]').should('not.exist');
    });

    it('يجب أن يعرض محتوى الشروط والأحكام', () => {
      // اختيار نوع المستخدم والمتابعة
      cy.get('[data-cy="user-type-customer"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('الشروط والأحكام').click();
      
      // التحقق من وجود محتوى الشروط
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('[role="dialog"]').within(() => {
        // التحقق من وجود محتوى نصي (يجب أن يحتوي على كلمات مثل "مِخْلاة" أو "منصة")
        cy.get('div').should('contain.text', 'مِخْلاة');
      });
    });
  });

  describe('اختبار التوافق مع اللغة الإنجليزية', () => {
    it('يجب أن يعرض العناوين الصحيحة باللغة الإنجليزية', () => {
      // التبديل للغة الإنجليزية
      cy.visit('/en/signup');
      
      // اختيار نوع المستخدم: عميل
      cy.get('[data-cy="user-type-customer"]').click();
      cy.get('[data-cy="next-step"]').click();
      
      // النقر على رابط الشروط والأحكام
      cy.contains('Terms and Conditions').click();
      
      // التحقق من العنوان الصحيح باللغة الإنجليزية
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('[role="dialog"]').within(() => {
        cy.contains('Terms and Conditions for Customers').should('be.visible');
      });
      
      // التحقق من رسالة التحميل الصحيحة باللغة الإنجليزية
      cy.get('[role="dialog"]').should('not.contain', 'Loading...');
    });
  });
});
