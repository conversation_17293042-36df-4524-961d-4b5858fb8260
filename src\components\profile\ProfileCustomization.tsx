// src/components/profile/ProfileCustomization.tsx
"use client";

import { useState, useEffect, type FormEvent, type ChangeEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useLocale } from '@/hooks/use-locale';
import type { UserProfile, UserDocument } from '@/types';
import { UploadCloud, RefreshCcw, Loader2, Edit3, X } from 'lucide-react';
import { auth, db } from '@/lib/firebase';
import { updateProfile } from 'firebase/auth';
import { doc, getDoc, updateDoc, serverTimestamp, type Timestamp } from 'firebase/firestore';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';

async function uploadFileToCloudinary(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  if (!process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET) {
    throw new Error("Cloudinary upload preset is not configured.");
  }
  formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET);

  const response = await fetch(
    `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME!}/image/upload`,
    {
      method: 'POST',
      body: formData,
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error.message || 'Cloudinary upload failed');
  }

  const data = await response.json();
  return data.secure_url;
}

interface ProfileCustomizationProps {
  user: UserProfile;
}

export default function ProfileCustomization({ user: initialUser }: ProfileCustomizationProps) {
  const { t } = useLocale();
  const { toast } = useToast();
  const { user: authUser, loading: authLoading } = useAuth();

  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState(initialUser.name || '');
  const [email] = useState(initialUser.email || ''); // Email is not editable
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(initialUser.avatarUrl || null);
  const [generatedAvatarText, setGeneratedAvatarText] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [userType, setUserType] = useState<'customer' | 'merchant' | null>(null);
  const [initialAvatarUrl, setInitialAvatarUrl] = useState<string | null>(initialUser.avatarUrl || null);
  const [initialDisplayName, setInitialDisplayName] = useState<string>(initialUser.name || '');


  useEffect(() => {
    if (initialUser) {
      setName(initialUser.name || '');
      setAvatarPreview(initialUser.avatarUrl || null);
      setInitialAvatarUrl(initialUser.avatarUrl || null);
      setInitialDisplayName(initialUser.name || '');
      if (!initialUser.avatarUrl && initialUser.name) {
        generateAvatarFromName(initialUser.name);
      } else if (initialUser.avatarUrl) {
        setGeneratedAvatarText('');
      }
    }

    const fetchUserType = async () => {
      if (authUser?.uid) {
        try {
          const userDocRef = doc(db, "users", authUser.uid);
          const userDocSnap = await getDoc(userDocRef);
          if (userDocSnap.exists()) {
            const userData = userDocSnap.data() as UserDocument;
            setUserType(userData.userType);
          }
        } catch (error) {
          console.error("Error fetching user type for profile customization:", error);
        }
      }
    };

    if (!authLoading && authUser) {
      fetchUserType();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialUser, authUser, authLoading]);

  const handleImageUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
        setGeneratedAvatarText('');
      };
      reader.readAsDataURL(file);
    }
  };

  const generateAvatarFromName = (currentName: string) => {
    const initials = currentName
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
    setGeneratedAvatarText(initials);
    setAvatarPreview(null);
    setAvatarFile(null);
  };

  const handleGenerateAvatar = () => {
    if (name) generateAvatarFromName(name);
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form to initial values when canceling
      setName(initialDisplayName);
      setAvatarPreview(initialAvatarUrl);
      setAvatarFile(null);
      if (!initialAvatarUrl && initialDisplayName) {
        generateAvatarFromName(initialDisplayName);
      } else if (initialAvatarUrl) {
         setGeneratedAvatarText('');
      }
    }
    setIsEditing(!isEditing);
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!auth.currentUser) {
      toast({ title: t('errorTitle'), description: t('notLoggedIn'), variant: 'destructive' });
      return;
    }
    setIsLoading(true);
    try {
      let newPhotoURL = auth.currentUser.photoURL;

      if (userType === 'merchant' && avatarFile) {
        toast({ title: t('uploadingAvatarTitle'), description: t('uploadingAvatarDesc') });
        try {
          newPhotoURL = await uploadFileToCloudinary(avatarFile);
          setAvatarPreview(newPhotoURL);
        } catch (uploadError: any) {
          console.error("Cloudinary avatar upload failed:", uploadError);
          toast({ title: t('errorTitle'), description: t('profileAvatarUploadFailed') + `: ${uploadError.message}`, variant: 'destructive' });
          setIsLoading(false);
          return;
        }
      } else if (userType === 'merchant' && !avatarPreview && generatedAvatarText) {
        // Merchant wants to use generated initials, remove photoURL
        newPhotoURL = null; 
      } else if (userType === 'merchant' && !avatarFile && avatarPreview === initialAvatarUrl) {
        // No change in avatar for merchant, keep existing
        newPhotoURL = initialAvatarUrl;
      }


      // Update Firebase Auth profile
      await updateProfile(auth.currentUser, {
        displayName: name,
        photoURL: newPhotoURL,
      });

      // Update Firestore user document
      const userDocRef = doc(db, "users", auth.currentUser.uid);
      await updateDoc(userDocRef, {
        displayName: name,
        photoURL: newPhotoURL,
        updatedAt: serverTimestamp() as Timestamp,
      });
      
      setInitialDisplayName(name); // Update initial values for next edit session
      setInitialAvatarUrl(newPhotoURL);

      toast({ title: t('profileUpdateSuccessTitle'), description: t('profileUpdateSuccessMessage') });
      setIsEditing(false); // Switch back to view mode
    } catch (error: any) {
      console.error("Profile update error:", error);
      toast({
        title: t('errorTitle'),
        description: error.message || t('profileUpdateFailed'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const canEditAvatar = userType === 'merchant';

  if (!isEditing) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col items-center space-y-4">
          <Avatar className="h-32 w-32 text-4xl">
            {avatarPreview ? (
              <AvatarImage src={avatarPreview} alt={name || t('user')} data-ai-hint="user profile" />
            ) : null}
            <AvatarFallback
              className={`${generatedAvatarText ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
            >
              {generatedAvatarText || name?.substring(0, 2).toUpperCase() || t('user')?.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          {!canEditAvatar && userType === 'customer' && (
             <p className="text-sm text-muted-foreground text-center">{t('customerAvatarInfo')}</p>
          )}
        </div>
        <div className="text-center">
          <Label htmlFor="name-display" className="block text-center">{t('username')}</Label>
          <p id="name-display" className="mt-1 text-lg font-medium text-center">{name || t('notSet')}</p>
        </div>
        <div className="text-center">
          <Label htmlFor="email-display" className="block text-center">{t('emailAddress')}</Label>
          <p id="email-display" className="mt-1 text-lg text-muted-foreground text-center">{email}</p>
        </div>
        <Button type="button" onClick={handleEditToggle} className="w-full">
          <Edit3 className="me-2 rtl:ms-2 h-4 w-4" />
          {t('editProfile')}
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex flex-col items-center space-y-4">
        <Avatar className="h-32 w-32 text-4xl">
          {avatarPreview ? (
            <AvatarImage src={avatarPreview} alt={name || t('user')} data-ai-hint="user profile" />
          ) : null}
          <AvatarFallback
            className={`${generatedAvatarText ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >
            {generatedAvatarText || name?.substring(0, 2).toUpperCase() || t('user')?.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>

        {canEditAvatar && (
          <div className="flex space-x-2 rtl:space-x-reverse">
            <Button type="button" variant="outline" onClick={() => document.getElementById('avatarUpload')?.click()} disabled={isLoading}>
              <UploadCloud className="me-2 rtl:ms-2 h-4 w-4" />{t('uploadImage')}
            </Button>
            <Input id="avatarUpload" type="file" accept="image/*" onChange={handleImageUpload} className="hidden" disabled={isLoading}/>
            <Button type="button" variant="outline" onClick={handleGenerateAvatar} disabled={isLoading || !name}>
              <RefreshCcw className="me-2 rtl:ms-2 h-4 w-4" />{t('generateAvatar')}
            </Button>
          </div>
        )}
         {!canEditAvatar && userType === 'customer' && (
             <p className="text-sm text-muted-foreground text-center">{t('customerAvatarInfo')}</p>
          )}
      </div>

      <div>
        <Label htmlFor="name">{t('username')}</Label>
        <Input id="name" type="text" value={name} onChange={(e) => setName(e.target.value)} className="mt-1" disabled={isLoading} />
      </div>
      <div>
        <Label htmlFor="email">{t('emailAddress')}</Label>
        <Input id="email" type="email" value={email} className="mt-1" disabled={true} />
      </div>

      <div className="flex flex-col sm:flex-row gap-2">
        <Button type="submit" className="w-full sm:flex-1 bg-primary hover:bg-primary/90 text-primary-foreground" disabled={isLoading}>
          {isLoading && <Loader2 className="me-2 h-4 w-4 animate-spin" />}
          {t('saveChanges')}
        </Button>
        <Button type="button" variant="outline" onClick={handleEditToggle} className="w-full sm:flex-1" disabled={isLoading}>
           <X className="me-2 rtl:ms-2 h-4 w-4" />
          {t('cancel')}
        </Button>
      </div>
    </form>
  );
}
