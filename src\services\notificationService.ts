// src/services/notificationService.ts
"use client";

import { messaging } from '@/lib/firebase';
import { getToken, onMessage } from 'firebase/messaging';
import { doc, updateDoc, collection, addDoc, serverTimestamp, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// أنواع الإشعارات
export type NotificationType = 
  | 'new_order'           // طلب جديد للتاجر
  | 'order_status_update' // تحديث حالة الطلب للعميل
  | 'order_assigned'      // تعيين طلب للمندوب
  | 'merchant_approved'   // موافقة على التاجر
  | 'representative_approved' // موافقة على المندوب
  | 'payment_received'    // استلام دفعة
  | 'delivery_completed'  // اكتمال التوصيل
  | 'system_announcement'; // إعلان من النظام

// واجهة بيانات الإشعار
export interface NotificationData {
  id?: string;
  userId: string;
  userType: 'customer' | 'merchant' | 'representative' | 'admin';
  type: NotificationType;
  title: string;
  body: string;
  data?: Record<string, any>;
  read: boolean;
  createdAt?: any;
  orderId?: string;
  actionUrl?: string;
}

// إعدادات الإشعارات للمستخدم
export interface NotificationSettings {
  userId: string;
  pushNotifications: boolean;
  emailNotifications: boolean;
  orderUpdates: boolean;
  promotions: boolean;
  systemAnnouncements: boolean;
  updatedAt?: any;
}

class NotificationService {
  private vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;

  // طلب إذن الإشعارات وتسجيل الرمز المميز
  async requestPermission(userId: string): Promise<string | null> {
    try {
      if (!messaging) {
        console.warn('Firebase Messaging not supported');
        return null;
      }

      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        const token = await getToken(messaging, {
          vapidKey: this.vapidKey
        });

        if (token) {
          // حفظ الرمز المميز في قاعدة البيانات
          await this.saveUserToken(userId, token);
          return token;
        }
      }
      return null;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return null;
    }
  }

  // حفظ رمز المستخدم في قاعدة البيانات
  private async saveUserToken(userId: string, token: string) {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        fcmToken: token,
        tokenUpdatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error saving user token:', error);
    }
  }

  // إرسال إشعار لمستخدم محدد
  async sendNotification(notificationData: Omit<NotificationData, 'id' | 'createdAt'>) {
    try {
      // حفظ الإشعار في قاعدة البيانات
      const notificationRef = await addDoc(collection(db, 'notifications'), {
        ...notificationData,
        createdAt: serverTimestamp()
      });

      // إرسال الإشعار عبر FCM (سيتم تنفيذه في Cloud Functions)
      // هنا نحفظ الإشعار فقط، والإرسال الفعلي يتم عبر Cloud Functions
      
      return notificationRef.id;
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  // إرسال إشعار طلب جديد للتاجر
  async notifyNewOrder(merchantUid: string, orderNumber: string, customerName: string, totalAmount: number) {
    return this.sendNotification({
      userId: merchantUid,
      userType: 'merchant',
      type: 'new_order',
      title: 'طلب جديد!',
      body: `طلب جديد من ${customerName} بقيمة ${totalAmount.toFixed(2)} ريال`,
      data: {
        orderNumber,
        customerName,
        totalAmount
      },
      read: false,
      actionUrl: `/merchant/orders/${orderNumber}`
    });
  }

  // إرسال إشعار تحديث حالة الطلب للعميل
  async notifyOrderStatusUpdate(customerId: string, orderNumber: string, status: string, statusArabic: string) {
    const statusMessages: Record<string, string> = {
      confirmed: 'تم تأكيد طلبك',
      preparing: 'جاري تحضير طلبك',
      ready: 'طلبك جاهز للاستلام',
      shipped: 'تم شحن طلبك',
      delivered: 'تم تسليم طلبك بنجاح'
    };

    return this.sendNotification({
      userId: customerId,
      userType: 'customer',
      type: 'order_status_update',
      title: 'تحديث حالة الطلب',
      body: `${statusMessages[status] || statusArabic} - رقم الطلب: ${orderNumber}`,
      data: {
        orderNumber,
        status,
        statusArabic
      },
      read: false,
      actionUrl: `/dashboard/orders/${orderNumber}`
    });
  }

  // إرسال إشعار تعيين طلب للمندوب
  async notifyOrderAssigned(representativeUid: string, orderNumber: string, storeName: string, deliveryFee: number) {
    return this.sendNotification({
      userId: representativeUid,
      userType: 'representative',
      type: 'order_assigned',
      title: 'طلب توصيل جديد!',
      body: `طلب توصيل من ${storeName} - عمولة: ${deliveryFee.toFixed(2)} ريال`,
      data: {
        orderNumber,
        storeName,
        deliveryFee
      },
      read: false,
      actionUrl: `/representative/orders/${orderNumber}`
    });
  }

  // إرسال إشعار موافقة على التاجر
  async notifyMerchantApproved(merchantUid: string, storeName: string) {
    return this.sendNotification({
      userId: merchantUid,
      userType: 'merchant',
      type: 'merchant_approved',
      title: 'تم قبول طلبك!',
      body: `تم قبول طلب انضمامك كتاجر. يمكنك الآن إدارة متجر ${storeName}`,
      data: {
        storeName
      },
      read: false,
      actionUrl: '/merchant/dashboard'
    });
  }

  // إرسال إشعار موافقة على المندوب
  async notifyRepresentativeApproved(representativeUid: string, representativeName: string) {
    return this.sendNotification({
      userId: representativeUid,
      userType: 'representative',
      type: 'representative_approved',
      title: 'تم قبول طلبك!',
      body: `تم قبول طلب انضمامك كمندوب توصيل. يمكنك الآن البدء في استلام الطلبات`,
      data: {
        representativeName
      },
      read: false,
      actionUrl: '/representative/dashboard'
    });
  }

  // جلب إشعارات المستخدم
  async getUserNotifications(userId: string, limitCount: number = 20) {
    try {
      const q = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const notifications: NotificationData[] = [];

      querySnapshot.forEach((doc) => {
        notifications.push({
          id: doc.id,
          ...doc.data()
        } as NotificationData);
      });

      return notifications;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  // تحديد الإشعار كمقروء
  async markAsRead(notificationId: string) {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        read: true
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // تحديد جميع إشعارات المستخدم كمقروءة
  async markAllAsRead(userId: string) {
    try {
      const q = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        where('read', '==', false)
      );

      const querySnapshot = await getDocs(q);
      const updatePromises = querySnapshot.docs.map(doc => 
        updateDoc(doc.ref, { read: true })
      );

      await Promise.all(updatePromises);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  // الاستماع للرسائل الواردة
  onMessage(callback: (payload: any) => void) {
    if (!messaging) return () => {};

    return onMessage(messaging, callback);
  }

  // حفظ إعدادات الإشعارات للمستخدم
  async saveNotificationSettings(settings: NotificationSettings) {
    try {
      const settingsRef = doc(db, 'notificationSettings', settings.userId);
      await updateDoc(settingsRef, {
        ...settings,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  }

  // جلب إعدادات الإشعارات للمستخدم
  async getNotificationSettings(userId: string): Promise<NotificationSettings | null> {
    try {
      const settingsRef = doc(db, 'notificationSettings', userId);
      const settingsDoc = await settingsRef.get();
      
      if (settingsDoc.exists()) {
        return settingsDoc.data() as NotificationSettings;
      }
      
      // إعدادات افتراضية
      const defaultSettings: NotificationSettings = {
        userId,
        pushNotifications: true,
        emailNotifications: true,
        orderUpdates: true,
        promotions: false,
        systemAnnouncements: true
      };
      
      return defaultSettings;
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      return null;
    }
  }
}

export const notificationService = new NotificationService();
export default notificationService;
