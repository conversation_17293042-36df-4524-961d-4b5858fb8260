// src/lib/encryption.ts
// 🔐 نظام التشفير المتقدم - Apex Level Security

import CryptoJS from 'crypto-js';

// ===== APEX ENCRYPTION ENGINE =====

export interface EncryptedPayload {
  payload: string;
  iv: string;
  salt: string;
  tag: string;
  algorithm: string;
  timestamp: number;
  keyDerivation: string;
}

export interface SecurityContext {
  userId?: string;
  sessionId?: string;
  deviceFingerprint?: string;
  ipAddress?: string;
  userAgent?: string;
}

export class ApexEncryptionEngine {
  private static readonly CIPHER_SUITE = 'AES-256-GCM';
  private static readonly KEY_DERIVATION = 'PBKDF2';
  private static readonly HMAC_ALGORITHM = 'SHA3-512';
  private static readonly ITERATIONS = 100000;
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly IV_LENGTH = 16; // 128 bits
  private static readonly SALT_LENGTH = 32; // 256 bits

  /**
   * تشفير متقدم مع Perfect Forward Secrecy
   */
  static async encryptWithPFS(
    data: any, 
    context: SecurityContext = {}
  ): Promise<EncryptedPayload> {
    try {
      // 1. تحويل البيانات إلى JSON
      const jsonData = JSON.stringify(data);
      
      // 2. توليد مفاتيح عشوائية
      const salt = CryptoJS.lib.WordArray.random(this.SALT_LENGTH);
      const iv = CryptoJS.lib.WordArray.random(this.IV_LENGTH);
      
      // 3. اشتقاق مفتاح التشفير
      const masterKey = this.getMasterKey();
      const derivedKey = this.deriveKey(masterKey, salt, context);
      
      // 4. تشفير البيانات
      const encrypted = CryptoJS.AES.encrypt(jsonData, derivedKey, {
        iv: iv,
        mode: CryptoJS.mode.GCM,
        padding: CryptoJS.pad.NoPadding
      });
      
      // 5. توليد MAC للتحقق من السلامة
      const tag = this.generateMAC(encrypted.toString(), derivedKey);
      
      return {
        payload: encrypted.toString(),
        iv: iv.toString(),
        salt: salt.toString(),
        tag: tag,
        algorithm: this.CIPHER_SUITE,
        timestamp: Date.now(),
        keyDerivation: this.KEY_DERIVATION
      };
    } catch (error) {
      console.error('🔴 خطأ في التشفير المتقدم:', error);
      throw new Error('فشل في التشفير');
    }
  }

  /**
   * فك التشفير مع التحقق من السلامة
   */
  static async decryptWithVerification(
    encryptedPayload: EncryptedPayload,
    context: SecurityContext = {}
  ): Promise<any> {
    try {
      // 1. التحقق من صحة البيانات
      this.validatePayload(encryptedPayload);
      
      // 2. التحقق من انتهاء الصلاحية (24 ساعة)
      const age = Date.now() - encryptedPayload.timestamp;
      if (age > 86400000) { // 24 hours
        throw new Error('انتهت صلاحية البيانات المشفرة');
      }
      
      // 3. إعادة اشتقاق المفتاح
      const masterKey = this.getMasterKey();
      const salt = CryptoJS.enc.Hex.parse(encryptedPayload.salt);
      const derivedKey = this.deriveKey(masterKey, salt, context);
      
      // 4. التحقق من MAC
      const expectedTag = this.generateMAC(encryptedPayload.payload, derivedKey);
      if (expectedTag !== encryptedPayload.tag) {
        throw new Error('فشل في التحقق من سلامة البيانات');
      }
      
      // 5. فك التشفير
      const iv = CryptoJS.enc.Hex.parse(encryptedPayload.iv);
      const decrypted = CryptoJS.AES.decrypt(encryptedPayload.payload, derivedKey, {
        iv: iv,
        mode: CryptoJS.mode.GCM,
        padding: CryptoJS.pad.NoPadding
      });
      
      const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
      return JSON.parse(decryptedText);
    } catch (error) {
      console.error('🔴 خطأ في فك التشفير:', error);
      throw new Error('فشل في فك التشفير');
    }
  }

  /**
   * تشفير سريع للبيانات الحساسة
   */
  static encryptSensitiveData(data: string): string {
    try {
      const key = this.getMasterKey();
      const encrypted = CryptoJS.AES.encrypt(data, key).toString();
      return encrypted;
    } catch (error) {
      console.error('🔴 خطأ في تشفير البيانات الحساسة:', error);
      throw new Error('فشل في التشفير السريع');
    }
  }

  /**
   * فك تشفير سريع للبيانات الحساسة
   */
  static decryptSensitiveData(encryptedData: string): string {
    try {
      const key = this.getMasterKey();
      const decrypted = CryptoJS.AES.decrypt(encryptedData, key);
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('🔴 خطأ في فك تشفير البيانات الحساسة:', error);
      throw new Error('فشل في فك التشفير السريع');
    }
  }

  /**
   * توليد hash آمن للبيانات
   */
  static generateSecureHash(data: string, salt?: string): string {
    const saltToUse = salt || CryptoJS.lib.WordArray.random(32).toString();
    const hash = CryptoJS.PBKDF2(data, saltToUse, {
      keySize: 256/32,
      iterations: this.ITERATIONS
    });
    return hash.toString();
  }

  /**
   * توليد مفتاح عشوائي آمن
   */
  static generateSecureKey(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString();
  }

  // ===== PRIVATE METHODS =====

  private static getMasterKey(): string {
    const envKey = process.env.DOCUMENT_ENCRYPTION_KEY;
    if (!envKey || envKey === 'default-key-change-in-production') {
      console.warn('⚠️ تحذير: يتم استخدام مفتاح تشفير افتراضي!');
      return 'APEX-SECURE-KEY-' + Date.now();
    }
    return envKey;
  }

  private static deriveKey(
    masterKey: string, 
    salt: CryptoJS.lib.WordArray, 
    context: SecurityContext
  ): string {
    // إضافة السياق لتقوية المفتاح
    const contextString = JSON.stringify(context);
    const combinedKey = masterKey + contextString;
    
    const derivedKey = CryptoJS.PBKDF2(combinedKey, salt, {
      keySize: this.KEY_LENGTH / 4,
      iterations: this.ITERATIONS
    });
    
    return derivedKey.toString();
  }

  private static generateMAC(data: string, key: string): string {
    return CryptoJS.HmacSHA256(data, key).toString();
  }

  private static validatePayload(payload: EncryptedPayload): void {
    const requiredFields = ['payload', 'iv', 'salt', 'tag', 'algorithm', 'timestamp'];
    for (const field of requiredFields) {
      if (!(field in payload)) {
        throw new Error(`حقل مطلوب مفقود: ${field}`);
      }
    }
    
    if (payload.algorithm !== this.CIPHER_SUITE) {
      throw new Error('خوارزمية تشفير غير مدعومة');
    }
  }
}

// ===== DOCUMENT ENCRYPTION SERVICE =====

export class DocumentEncryptionService {
  /**
   * تشفير المستندات قبل الرفع
   */
  static async encryptDocument(file: File): Promise<EncryptedPayload> {
    try {
      // تحويل الملف إلى base64
      const base64Data = await this.fileToBase64(file);
      
      // تشفير البيانات
      const context: SecurityContext = {
        deviceFingerprint: this.getDeviceFingerprint(),
        userAgent: navigator.userAgent
      };
      
      return await ApexEncryptionEngine.encryptWithPFS(base64Data, context);
    } catch (error) {
      console.error('🔴 خطأ في تشفير المستند:', error);
      throw new Error('فشل في تشفير المستند');
    }
  }

  /**
   * فك تشفير المستندات
   */
  static async decryptDocument(encryptedPayload: EncryptedPayload): Promise<string> {
    try {
      const context: SecurityContext = {
        deviceFingerprint: this.getDeviceFingerprint(),
        userAgent: navigator.userAgent
      };
      
      return await ApexEncryptionEngine.decryptWithVerification(encryptedPayload, context);
    } catch (error) {
      console.error('🔴 خطأ في فك تشفير المستند:', error);
      throw new Error('فشل في فك تشفير المستند');
    }
  }

  private static fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  private static getDeviceFingerprint(): string {
    // توليد بصمة الجهاز (مبسطة)
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx!.textBaseline = 'top';
    ctx!.font = '14px Arial';
    ctx!.fillText('Device fingerprint', 2, 2);
    
    return canvas.toDataURL();
  }
}

// ===== EXPORTS =====
export default ApexEncryptionEngine;
