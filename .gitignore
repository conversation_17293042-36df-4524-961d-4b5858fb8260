# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.genkit/*
.env*
!.env.local.example
!.env.example.secure

# firebase
firebase-debug.log
firestore-debug.log

# graphql
/src/app/api/graphql/schema.graphql

# AI Models - Large Files (تجنب رفع الملفات الكبيرة)
*.onnx
*.bin
*.safetensors
*.h5
*.pb
*.tflite
*.mlmodel
*.coreml
*.ort

# AI Models Directory - Large Files
ai-models/models/**/*.onnx
ai-models/models/**/*.bin
ai-models/models/**/*.safetensors
ai-models/models/**/*.h5
ai-models/models/**/*.pb
ai-models/models/**/*.tflite
ai-models/models/**/*.mlmodel
ai-models/models/**/*.coreml
ai-models/models/**/*.ort

# AI Models Directories
/ai-models/models/
/ai-models/wasm/
/ai-models/temp/
/ai-models/downloads/
/ai-models/cache/

# AI Temporary Files
*.tmp
*.temp
.ai-cache/
ai-models/validation-report.json
ai-models/download-log.json

# AI logs and cache
/ai-models/logs/
*.ai-log

bun.lockb
.bun.lock
bun.lock