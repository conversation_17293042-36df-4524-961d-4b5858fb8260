// مدير الذاكرة المتقدم - إدارة ذكية للموارد
export class MemoryManager {
  constructor() {
    this.allocatedMemory = new Map();
    this.memoryUsage = {
      total: 0,
      models: 0,
      cache: 0,
      other: 0
    };
    this.config = null;
    this.memoryThreshold = 0.85;
    this.maxMemory = 1024 * 1024 * 1024; // 1GB افتراضي
    this.cleanupCallbacks = [];
    this.monitoringInterval = null;
  }

  /**
   * تهيئة مدير الذاكرة
   */
  async initialize(config) {
    try {
      console.log('🧠 تهيئة مدير الذاكرة...');
      
      this.config = config;
      this.memoryThreshold = config.memory.memoryThreshold;
      this.maxMemory = this.parseMemorySize(config.memory.maxTotalMemory);
      
      // بدء مراقبة الذاكرة
      this.startMemoryMonitoring();
      
      // تسجيل callbacks للتنظيف
      this.registerCleanupCallbacks();
      
      console.log('✅ تم تهيئة مدير الذاكرة بنجاح');
      console.log(`📊 الحد الأقصى للذاكرة: ${this.formatMemorySize(this.maxMemory)}`);
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة مدير الذاكرة:', error);
      throw error;
    }
  }

  /**
   * تحويل حجم الذاكرة من نص إلى رقم
   */
  parseMemorySize(sizeStr) {
    const units = {
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };
    
    const match = sizeStr.match(/^(\d+)\s*(KB|MB|GB)$/i);
    if (!match) {
      throw new Error(`تنسيق حجم ذاكرة غير صحيح: ${sizeStr}`);
    }
    
    const [, size, unit] = match;
    return parseInt(size) * units[unit.toUpperCase()];
  }

  /**
   * تنسيق حجم الذاكرة للعرض
   */
  formatMemorySize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * التحقق من توفر الذاكرة
   */
  async checkMemoryAvailability(requiredSize) {
    const requiredBytes = typeof requiredSize === 'string' 
      ? this.parseMemorySize(requiredSize) 
      : requiredSize;
    
    const currentUsage = this.getCurrentMemoryUsage();
    const availableMemory = this.maxMemory - currentUsage;
    
    console.log(`🔍 فحص الذاكرة المطلوبة: ${this.formatMemorySize(requiredBytes)}`);
    console.log(`📊 الذاكرة المتاحة: ${this.formatMemorySize(availableMemory)}`);
    
    if (requiredBytes > availableMemory) {
      console.warn('⚠️ ذاكرة غير كافية، محاولة تحرير مساحة...');
      
      // محاولة تحرير الذاكرة
      const freedMemory = await this.freeMemory(requiredBytes);
      
      if (freedMemory < requiredBytes) {
        throw new Error(`ذاكرة غير كافية. مطلوب: ${this.formatMemorySize(requiredBytes)}, متاح: ${this.formatMemorySize(availableMemory + freedMemory)}`);
      }
    }
    
    return true;
  }

  /**
   * تخصيص ذاكرة لنموذج
   */
  allocateMemory(modelId, size, type = 'model') {
    const sizeBytes = typeof size === 'string' ? this.parseMemorySize(size) : size;
    
    this.allocatedMemory.set(modelId, {
      size: sizeBytes,
      type: type,
      allocatedAt: Date.now(),
      lastAccessed: Date.now()
    });
    
    this.memoryUsage[type] += sizeBytes;
    this.memoryUsage.total += sizeBytes;
    
    console.log(`📝 تخصيص ذاكرة للنموذج ${modelId}: ${this.formatMemorySize(sizeBytes)}`);
    this.logMemoryStatus();
  }

  /**
   * تحرير ذاكرة نموذج
   */
  releaseMemory(modelId) {
    if (this.allocatedMemory.has(modelId)) {
      const allocation = this.allocatedMemory.get(modelId);
      
      this.memoryUsage[allocation.type] -= allocation.size;
      this.memoryUsage.total -= allocation.size;
      
      this.allocatedMemory.delete(modelId);
      
      console.log(`🗑️ تحرير ذاكرة النموذج ${modelId}: ${this.formatMemorySize(allocation.size)}`);
      this.logMemoryStatus();
      
      return allocation.size;
    }
    
    return 0;
  }

  /**
   * تحديث آخر وصول للنموذج
   */
  updateLastAccessed(modelId) {
    if (this.allocatedMemory.has(modelId)) {
      const allocation = this.allocatedMemory.get(modelId);
      allocation.lastAccessed = Date.now();
    }
  }

  /**
   * الحصول على الاستخدام الحالي للذاكرة
   */
  getCurrentMemoryUsage() {
    // محاولة الحصول على معلومات الذاكرة الفعلية
    if ('memory' in performance) {
      return performance.memory.usedJSHeapSize;
    }
    
    // العودة للتقدير المحلي
    return this.memoryUsage.total;
  }

  /**
   * تحرير الذاكرة
   */
  async freeMemory(targetSize) {
    let freedMemory = 0;
    
    // 1. تنظيف النماذج غير المستخدمة
    freedMemory += await this.cleanupUnusedModels();
    
    if (freedMemory >= targetSize) {
      return freedMemory;
    }
    
    // 2. تنظيف التخزين المؤقت
    freedMemory += await this.cleanupCache();
    
    if (freedMemory >= targetSize) {
      return freedMemory;
    }
    
    // 3. تشغيل garbage collection
    freedMemory += await this.forceGarbageCollection();
    
    // 4. تنظيف النماذج الأقل استخداماً
    if (freedMemory < targetSize) {
      freedMemory += await this.cleanupLeastUsedModels(targetSize - freedMemory);
    }
    
    return freedMemory;
  }

  /**
   * تنظيف النماذج غير المستخدمة
   */
  async cleanupUnusedModels() {
    const now = Date.now();
    const unusedThreshold = 10 * 60 * 1000; // 10 دقائق
    let freedMemory = 0;
    
    const modelsToCleanup = [];
    
    for (const [modelId, allocation] of this.allocatedMemory.entries()) {
      if (now - allocation.lastAccessed > unusedThreshold) {
        modelsToCleanup.push(modelId);
      }
    }
    
    for (const modelId of modelsToCleanup) {
      // استدعاء callback للتنظيف
      for (const callback of this.cleanupCallbacks) {
        try {
          await callback(modelId);
          freedMemory += this.releaseMemory(modelId);
        } catch (error) {
          console.warn(`⚠️ خطأ في تنظيف النموذج ${modelId}:`, error);
        }
      }
    }
    
    if (freedMemory > 0) {
      console.log(`🧹 تم تنظيف النماذج غير المستخدمة: ${this.formatMemorySize(freedMemory)}`);
    }
    
    return freedMemory;
  }

  /**
   * تنظيف النماذج الأقل استخداماً
   */
  async cleanupLeastUsedModels(targetSize) {
    const modelsByUsage = Array.from(this.allocatedMemory.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
    
    let freedMemory = 0;
    
    for (const [modelId, allocation] of modelsByUsage) {
      if (freedMemory >= targetSize) {
        break;
      }
      
      // تنظيف النموذج
      for (const callback of this.cleanupCallbacks) {
        try {
          await callback(modelId);
          freedMemory += this.releaseMemory(modelId);
          break;
        } catch (error) {
          console.warn(`⚠️ خطأ في تنظيف النموذج ${modelId}:`, error);
        }
      }
    }
    
    if (freedMemory > 0) {
      console.log(`🧹 تم تنظيف النماذج الأقل استخداماً: ${this.formatMemorySize(freedMemory)}`);
    }
    
    return freedMemory;
  }

  /**
   * تنظيف التخزين المؤقت
   */
  async cleanupCache() {
    // سيتم تنفيذ هذا بواسطة CacheManager
    let freedMemory = 0;
    
    // محاولة تنظيف التخزين المؤقت للمتصفح
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
          if (cacheName.includes('ai-models')) {
            await caches.delete(cacheName);
            freedMemory += 50 * 1024 * 1024; // تقدير 50MB لكل cache
          }
        }
      } catch (error) {
        console.warn('⚠️ خطأ في تنظيف cache:', error);
      }
    }
    
    return freedMemory;
  }

  /**
   * تشغيل garbage collection قسري
   */
  async forceGarbageCollection() {
    let freedMemory = 0;
    
    if (window.gc) {
      const beforeGC = this.getCurrentMemoryUsage();
      window.gc();
      const afterGC = this.getCurrentMemoryUsage();
      freedMemory = Math.max(0, beforeGC - afterGC);
      
      console.log(`🗑️ تم تشغيل garbage collection: ${this.formatMemorySize(freedMemory)}`);
    }
    
    return freedMemory;
  }

  /**
   * بدء مراقبة الذاكرة
   */
  startMemoryMonitoring() {
    this.monitoringInterval = setInterval(() => {
      this.checkMemoryStatus();
    }, 30000); // كل 30 ثانية
    
    console.log('👁️ بدء مراقبة الذاكرة');
  }

  /**
   * فحص حالة الذاكرة
   */
  checkMemoryStatus() {
    const currentUsage = this.getCurrentMemoryUsage();
    const usagePercent = (currentUsage / this.maxMemory) * 100;
    
    if (usagePercent > this.memoryThreshold * 100) {
      console.warn(`⚠️ استخدام ذاكرة عالي: ${usagePercent.toFixed(1)}%`);
      
      // تنظيف تلقائي
      this.freeMemory(this.maxMemory * 0.1); // تحرير 10% من الذاكرة
    }
  }

  /**
   * تسجيل callbacks للتنظيف
   */
  registerCleanupCallbacks() {
    // تنظيف عند إغلاق النافذة
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
    
    // تنظيف عند فقدان التركيز
    window.addEventListener('blur', () => {
      this.cleanupUnusedModels();
    });
  }

  /**
   * إضافة callback للتنظيف
   */
  addCleanupCallback(callback) {
    this.cleanupCallbacks.push(callback);
  }

  /**
   * تسجيل حالة الذاكرة
   */
  logMemoryStatus() {
    const currentUsage = this.getCurrentMemoryUsage();
    const usagePercent = (currentUsage / this.maxMemory) * 100;
    
    console.log(`📊 حالة الذاكرة: ${this.formatMemorySize(currentUsage)} / ${this.formatMemorySize(this.maxMemory)} (${usagePercent.toFixed(1)}%)`);
  }

  /**
   * الحصول على إحصائيات الذاكرة
   */
  getUsage() {
    const currentUsage = this.getCurrentMemoryUsage();
    
    return {
      total: currentUsage,
      max: this.maxMemory,
      percentage: (currentUsage / this.maxMemory) * 100,
      breakdown: { ...this.memoryUsage },
      allocatedModels: this.allocatedMemory.size,
      formatted: {
        total: this.formatMemorySize(currentUsage),
        max: this.formatMemorySize(this.maxMemory),
        models: this.formatMemorySize(this.memoryUsage.models),
        cache: this.formatMemorySize(this.memoryUsage.cache)
      }
    };
  }

  /**
   * تنظيف شامل
   */
  async cleanup() {
    console.log('🧹 تنظيف شامل للذاكرة...');
    
    // إيقاف المراقبة
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    // تنظيف جميع النماذج
    const modelIds = Array.from(this.allocatedMemory.keys());
    for (const modelId of modelIds) {
      this.releaseMemory(modelId);
    }
    
    // تنظيف التخزين المؤقت
    await this.cleanupCache();
    
    // garbage collection نهائي
    await this.forceGarbageCollection();
    
    console.log('✅ تم التنظيف الشامل للذاكرة');
  }
}
