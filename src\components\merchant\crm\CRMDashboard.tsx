// src/components/merchant/crm/CRMDashboard.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  TrendingUp, 
  MessageSquare, 
  Target,
  UserPlus,
  UserCheck,
  UserX,
  Crown,
  Star,
  Mail,
  Phone,
  Calendar,
  BarChart3,
  Filter,
  Search,
  Plus
} from 'lucide-react';
import { useCRMData } from '@/hooks/useCRMData';
import { useAuth } from '@/context/AuthContext';
import { cn } from '@/lib/utils';

interface CRMDashboardProps {
  className?: string;
}

export const CRMDashboard: React.FC<CRMDashboardProps> = ({ className }) => {
  const { user } = useAuth();
  const { 
    customers, 
    segments, 
    analytics, 
    communications,
    loading, 
    errors,
    actions 
  } = useCRMData();

  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'quarterly'>('monthly');

  // تحميل البيانات الأولية
  useEffect(() => {
    if (user?.uid) {
      const loadInitialData = async () => {
        try {
          await Promise.all([
            actions.loadCustomers(user.uid),
            actions.loadSegments(user.uid),
            actions.loadCommunications(user.uid)
          ]);

          // إنشاء تحليلات للشهر الحالي
          const endDate = new Date();
          const startDate = new Date();
          startDate.setMonth(startDate.getMonth() - 1);

          await actions.generateAnalytics(user.uid, {
            startDate,
            endDate,
            type: selectedPeriod
          });
        } catch (error) {
          console.error('Error loading CRM data:', error);
        }
      };

      loadInitialData();
    }
  }, [user?.uid, actions, selectedPeriod]);

  // إحصائيات سريعة
  const quickStats = {
    totalCustomers: customers.length,
    newCustomers: customers.filter(c => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return c.createdAt.toDate() > weekAgo;
    }).length,
    activeCustomers: customers.filter(c => {
      const monthAgo = new Date();
      monthAgo.setDate(monthAgo.getDate() - 30);
      return c.lastInteractionDate && c.lastInteractionDate.toDate() > monthAgo;
    }).length,
    vipCustomers: customers.filter(c => c.segmentation.tier === 'platinum' || c.tags.includes('vip')).length
  };

  // توزيع العملاء حسب المستوى
  const tierDistribution = {
    bronze: customers.filter(c => c.segmentation.tier === 'bronze').length,
    silver: customers.filter(c => c.segmentation.tier === 'silver').length,
    gold: customers.filter(c => c.segmentation.tier === 'gold').length,
    platinum: customers.filter(c => c.segmentation.tier === 'platinum').length
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'bg-amber-100 text-amber-800';
      case 'silver': return 'bg-gray-100 text-gray-800';
      case 'gold': return 'bg-yellow-100 text-yellow-800';
      case 'platinum': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* العنوان والإجراءات السريعة */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">إدارة علاقات العملاء</h1>
          <p className="text-muted-foreground">
            تتبع وإدارة علاقاتك مع العملاء بشكل شامل
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 me-2" />
            فلترة
          </Button>
          <Button variant="outline" size="sm">
            <Search className="h-4 w-4 me-2" />
            بحث
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 me-2" />
            إضافة عميل
          </Button>
        </div>
      </div>

      {/* البطاقات الإحصائية السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quickStats.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              +{quickStats.newCustomers} عميل جديد هذا الأسبوع
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">العملاء النشطين</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quickStats.activeCustomers}</div>
            <p className="text-xs text-muted-foreground">
              خلال آخر 30 يوم
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">عملاء VIP</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quickStats.vipCustomers}</div>
            <p className="text-xs text-muted-foreground">
              عملاء بلاتينيوم و VIP
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">التقسيمات</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{segments.length}</div>
            <p className="text-xs text-muted-foreground">
              تقسيمات نشطة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="customers">العملاء</TabsTrigger>
          <TabsTrigger value="segments">التقسيمات</TabsTrigger>
          <TabsTrigger value="communications">التواصل</TabsTrigger>
          <TabsTrigger value="analytics">التحليلات</TabsTrigger>
        </TabsList>

        {/* نظرة عامة */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* توزيع العملاء حسب المستوى */}
            <Card>
              <CardHeader>
                <CardTitle>توزيع العملاء حسب المستوى</CardTitle>
                <CardDescription>
                  تصنيف العملاء حسب مستوى الولاء
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(tierDistribution).map(([tier, count]) => (
                  <div key={tier} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge className={getTierColor(tier)}>
                        {tier === 'bronze' && 'برونزي'}
                        {tier === 'silver' && 'فضي'}
                        {tier === 'gold' && 'ذهبي'}
                        {tier === 'platinum' && 'بلاتيني'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">{count} عميل</span>
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ 
                            width: `${quickStats.totalCustomers > 0 ? (count / quickStats.totalCustomers) * 100 : 0}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* التقسيمات النشطة */}
            <Card>
              <CardHeader>
                <CardTitle>التقسيمات النشطة</CardTitle>
                <CardDescription>
                  أهم تقسيمات العملاء
                </CardDescription>
              </CardHeader>
              <CardContent>
                {segments.length > 0 ? (
                  <div className="space-y-3">
                    {segments.slice(0, 5).map((segment) => (
                      <div key={segment.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: segment.settings.color }}
                          />
                          <div>
                            <p className="font-medium">{segment.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {segment.stats.customerCount} عميل
                            </p>
                          </div>
                        </div>
                        <Badge variant={segment.settings.isActive ? "default" : "secondary"}>
                          {segment.settings.isActive ? 'نشط' : 'غير نشط'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">لا توجد تقسيمات بعد</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => user?.uid && actions.createDefaultSegments(user.uid)}
                    >
                      إنشاء تقسيمات افتراضية
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* حملات التواصل الأخيرة */}
          <Card>
            <CardHeader>
              <CardTitle>حملات التواصل الأخيرة</CardTitle>
              <CardDescription>
                آخر حملات التواصل مع العملاء
              </CardDescription>
            </CardHeader>
            <CardContent>
              {communications.length > 0 ? (
                <div className="space-y-3">
                  {communications.slice(0, 3).map((communication) => (
                    <div key={communication.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {communication.content.type === 'email' && <Mail className="h-4 w-4 text-blue-500" />}
                        {communication.content.type === 'sms' && <Phone className="h-4 w-4 text-green-500" />}
                        {communication.content.type === 'push' && <MessageSquare className="h-4 w-4 text-purple-500" />}
                        
                        <div>
                          <p className="font-medium">{communication.content.subject || 'بدون عنوان'}</p>
                          <p className="text-sm text-muted-foreground">
                            {communication.stats.targetCount} مستهدف • {communication.stats.deliveryRate.toFixed(1)}% تم التسليم
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant={
                            communication.status === 'sent' ? 'default' :
                            communication.status === 'sending' ? 'secondary' :
                            communication.status === 'failed' ? 'destructive' :
                            'outline'
                          }
                        >
                          {communication.status === 'sent' && 'تم الإرسال'}
                          {communication.status === 'sending' && 'جاري الإرسال'}
                          {communication.status === 'draft' && 'مسودة'}
                          {communication.status === 'scheduled' && 'مجدولة'}
                          {communication.status === 'failed' && 'فشل'}
                        </Badge>
                        
                        <span className="text-xs text-muted-foreground">
                          {communication.createdAt.toDate().toLocaleDateString('ar-SA')}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">لا توجد حملات تواصل بعد</p>
                  <Button variant="outline" size="sm" className="mt-2">
                    إنشاء حملة جديدة
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* تبويب العملاء */}
        <TabsContent value="customers">
          <Card>
            <CardHeader>
              <CardTitle>قائمة العملاء</CardTitle>
              <CardDescription>
                إدارة وتتبع جميع عملائك
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading.customers ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="text-muted-foreground mt-2">جاري تحميل العملاء...</p>
                </div>
              ) : customers.length > 0 ? (
                <div className="space-y-3">
                  {customers.slice(0, 10).map((customer) => (
                    <div key={customer.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">
                            {customer.personalInfo.name.charAt(0)}
                          </span>
                        </div>
                        
                        <div>
                          <p className="font-medium">{customer.personalInfo.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {customer.personalInfo.email}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge className={getTierColor(customer.segmentation.tier)}>
                          {customer.segmentation.tier}
                        </Badge>
                        
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {customer.shoppingBehavior.totalSpent.toLocaleString()} ر.س
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {customer.shoppingBehavior.totalOrders} طلب
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">لا يوجد عملاء بعد</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* باقي التبويبات */}
        <TabsContent value="segments">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">إدارة تقسيمات العملاء قريباً</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="communications">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">إدارة حملات التواصل قريباً</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">تحليلات متقدمة قريباً</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
