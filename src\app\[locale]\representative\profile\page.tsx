'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  User, 
  Phone, 
  Mail, 
  Car, 
  FileText, 
  Calendar,
  Edit,
  Save,
  X,
  Camera
} from 'lucide-react';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { RepresentativeDocument } from '@/types/representative';
import { toast } from 'sonner';

export default function RepresentativeProfilePage() {
  const { user } = useAuth();
  const t = useTranslations();
  const [representativeData, setRepresentativeData] = useState<RepresentativeDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    displayName: '',
    phoneNumber: '',
    vehicleModel: '',
    vehicleColor: '',
    vehiclePlateNumber: ''
  });

  useEffect(() => {
    const fetchRepresentativeData = async () => {
      if (!user?.uid) return;

      try {
        const representativeDoc = await getDoc(doc(db, 'representatives', user.uid));
        if (representativeDoc.exists()) {
          const data = representativeDoc.data() as RepresentativeDocument;
          setRepresentativeData(data);
          setFormData({
            displayName: data.displayName,
            phoneNumber: data.phoneNumber,
            vehicleModel: data.vehicle.model,
            vehicleColor: data.vehicle.color,
            vehiclePlateNumber: data.vehicle.plateNumber
          });
        }
      } catch (error) {
        console.error('Error fetching representative data:', error);
        toast.error(t('representative.profile.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    fetchRepresentativeData();
  }, [user?.uid, t]);

  const handleSave = async () => {
    if (!user?.uid || !representativeData) return;

    try {
      await updateDoc(doc(db, 'representatives', user.uid), {
        displayName: formData.displayName,
        phoneNumber: formData.phoneNumber,
        'vehicle.model': formData.vehicleModel,
        'vehicle.color': formData.vehicleColor,
        'vehicle.plateNumber': formData.vehiclePlateNumber,
        updatedAt: new Date()
      });

      // تحديث البيانات المحلية
      setRepresentativeData(prev => prev ? {
        ...prev,
        displayName: formData.displayName,
        phoneNumber: formData.phoneNumber,
        vehicle: {
          ...prev.vehicle,
          model: formData.vehicleModel,
          color: formData.vehicleColor,
          plateNumber: formData.vehiclePlateNumber
        }
      } : null);

      setEditing(false);
      toast.success(t('representative.profile.updateSuccess'));
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t('representative.profile.updateError'));
    }
  };

  const handleCancel = () => {
    if (representativeData) {
      setFormData({
        displayName: representativeData.displayName,
        phoneNumber: representativeData.phoneNumber,
        vehicleModel: representativeData.vehicle.model,
        vehicleColor: representativeData.vehicle.color,
        vehiclePlateNumber: representativeData.vehicle.plateNumber
      });
    }
    setEditing(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!representativeData) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('representative.profile.noData')}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{t('representative.profile.title')}</h1>
        <p className="text-muted-foreground">
          {t('representative.profile.description')}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* الملف الشخصي الأساسي */}
        <div className="lg:col-span-2 space-y-6">
          {/* المعلومات الشخصية */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {t('representative.profile.personalInfo')}
              </CardTitle>
              {!editing ? (
                <Button variant="outline" size="sm" onClick={() => setEditing(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t('representative.profile.edit')}
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    {t('representative.profile.save')}
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCancel}>
                    <X className="h-4 w-4 mr-2" />
                    {t('representative.profile.cancel')}
                  </Button>
                </div>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="displayName">{t('representative.profile.fullName')}</Label>
                  {editing ? (
                    <Input
                      id="displayName"
                      value={formData.displayName}
                      onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">{representativeData.displayName}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="phoneNumber">{t('representative.profile.phoneNumber')}</Label>
                  {editing ? (
                    <Input
                      id="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">{representativeData.phoneNumber}</p>
                  )}
                </div>
                
                <div>
                  <Label>{t('representative.profile.email')}</Label>
                  <p className="text-sm text-muted-foreground mt-1">{representativeData.email}</p>
                </div>
                
                <div>
                  <Label>{t('representative.profile.nationalId')}</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {representativeData.nationalId} ({representativeData.nationalIdType === 'national' ? t('representative.profile.national') : t('representative.profile.resident')})
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* معلومات المركبة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Car className="h-5 w-5" />
                {t('representative.profile.vehicleInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>{t('representative.profile.vehicleType')}</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {t(`representative.profile.vehicleTypes.${representativeData.vehicle.type}`)}
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="vehicleModel">{t('representative.profile.vehicleModel')}</Label>
                  {editing ? (
                    <Input
                      id="vehicleModel"
                      value={formData.vehicleModel}
                      onChange={(e) => setFormData(prev => ({ ...prev, vehicleModel: e.target.value }))}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">{representativeData.vehicle.model}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="vehicleColor">{t('representative.profile.vehicleColor')}</Label>
                  {editing ? (
                    <Input
                      id="vehicleColor"
                      value={formData.vehicleColor}
                      onChange={(e) => setFormData(prev => ({ ...prev, vehicleColor: e.target.value }))}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">{representativeData.vehicle.color}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="vehiclePlateNumber">{t('representative.profile.plateNumber')}</Label>
                  {editing ? (
                    <Input
                      id="vehiclePlateNumber"
                      value={formData.vehiclePlateNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, vehiclePlateNumber: e.target.value }))}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">{representativeData.vehicle.plateNumber}</p>
                  )}
                </div>
                
                <div>
                  <Label>{t('representative.profile.vehicleYear')}</Label>
                  <p className="text-sm text-muted-foreground mt-1">{representativeData.vehicle.year}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* الشريط الجانبي */}
        <div className="space-y-6">
          {/* حالة الحساب */}
          <Card>
            <CardHeader>
              <CardTitle>{t('representative.profile.accountStatus')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">{t('representative.profile.approvalStatus')}</span>
                <Badge variant={representativeData.approvalStatus === 'approved' ? 'default' : 'secondary'}>
                  {t(`representative.profile.status.${representativeData.approvalStatus}`)}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm">{t('representative.profile.currentPlan')}</span>
                <Badge variant="outline">
                  {representativeData.planId === 'representative-premium' 
                    ? t('representative.profile.premiumPlan') 
                    : t('representative.profile.basicPlan')
                  }
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm">{t('representative.profile.commissionRate')}</span>
                <span className="font-medium">{representativeData.commissionRate}%</span>
              </div>
            </CardContent>
          </Card>

          {/* الوثائق */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t('representative.profile.documents')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-sm">
                <p className="font-medium">{t('representative.profile.drivingLicense')}</p>
                <p className="text-muted-foreground">
                  {t('representative.profile.licenseNumber')}: {representativeData.drivingLicense.number}
                </p>
                <p className="text-muted-foreground">
                  {t('representative.profile.expiryDate')}: {representativeData.drivingLicense.expiryDate.toDate().toLocaleDateString('ar-SA')}
                </p>
              </div>
              
              <div className="text-sm">
                <p className="font-medium">{t('representative.profile.vehicleInspection')}</p>
                <p className="text-muted-foreground">
                  {t('representative.profile.certificateNumber')}: {representativeData.vehicleInspection.certificateNumber}
                </p>
                <p className="text-muted-foreground">
                  {t('representative.profile.expiryDate')}: {representativeData.vehicleInspection.expiryDate.toDate().toLocaleDateString('ar-SA')}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* تاريخ التسجيل */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t('representative.profile.registrationInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-2">
                <div>
                  <p className="font-medium">{t('representative.profile.joinedDate')}</p>
                  <p className="text-muted-foreground">
                    {representativeData.createdAt.toDate().toLocaleDateString('ar-SA')}
                  </p>
                </div>
                
                {representativeData.approvalDate && (
                  <div>
                    <p className="font-medium">{t('representative.profile.approvalDate')}</p>
                    <p className="text-muted-foreground">
                      {representativeData.approvalDate.toDate().toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
