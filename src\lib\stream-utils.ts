// src/lib/stream-utils.ts
// مكتبة لمعالجة أخطاء Stream وتحسين الأداء

/**
 * معالج أخطاء Stream محسن
 */
export class StreamErrorHandler {
  private static instance: StreamErrorHandler;
  private errorCount = 0;
  private lastErrorTime = 0;
  private readonly maxErrors = 5;
  private readonly errorWindow = 60000; // دقيقة واحدة

  static getInstance(): StreamErrorHandler {
    if (!StreamErrorHandler.instance) {
      StreamErrorHandler.instance = new StreamErrorHandler();
    }
    return StreamErrorHandler.instance;
  }

  /**
   * معالجة أخطاء Stream محسنة
   */
  handleStreamError(error: any, context: string = ''): boolean {
    const now = Date.now();

    // إعادة تعيين العداد إذا مر وقت كافي
    if (now - this.lastErrorTime > this.errorWindow) {
      this.errorCount = 0;
    }

    this.errorCount++;
    this.lastErrorTime = now;

    // تقليل رسائل الخطأ - فقط للأخطاء غير المتوقعة
    if (process.env.NODE_ENV === 'development') {
      if (this.isStreamError(error)) {
        // تجاهل أخطاء Stream الشائعة
        if (!error.message?.includes('already ended') &&
            !error.message?.includes('pipe response')) {
          console.warn(`⚠️ Stream warning ${context}:`, error.code || error.message);
        }
      } else if (!this.isCommonNetworkError(error)) {
        console.error(`❌ Unexpected error ${context}:`, error);
      }
    }

    // إذا تجاوز عدد الأخطاء الحد المسموح
    if (this.errorCount > this.maxErrors) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('🚨 Too many stream errors, implementing fallback strategy');
      }
      return false; // يشير إلى ضرورة استخدام fallback
    }

    return true; // يمكن المتابعة
  }

  /**
   * التحقق من كون الخطأ متعلق بـ Stream
   */
  private isStreamError(error: any): boolean {
    const streamErrorCodes = [
      'ERR_STREAM_ALREADY_FINISHED',
      'ERR_STREAM_DESTROYED',
      'ERR_STREAM_WRITE_AFTER_END',
      'ERR_STREAM_PREMATURE_CLOSE'
    ];

    const streamErrorMessages = [
      'Stream is already ended',
      'failed to pipe response',
      'stream destroyed',
      'premature close'
    ];

    return (
      streamErrorCodes.includes(error.code) ||
      streamErrorMessages.some(msg => 
        error.message?.toLowerCase().includes(msg.toLowerCase())
      )
    );
  }

  /**
   * التحقق من أخطاء الشبكة الشائعة
   */
  private isCommonNetworkError(error: any): boolean {
    if (!error) return false;

    const commonNetworkErrors = [
      'offline',
      'timeout',
      'unavailable',
      'deadline-exceeded',
      'network request failed',
      'transport error'
    ];

    return commonNetworkErrors.some(msg =>
      error.message?.toLowerCase().includes(msg.toLowerCase()) ||
      error.code?.toLowerCase().includes(msg.toLowerCase())
    );
  }

  /**
   * إعادة تعيين العداد
   */
  reset(): void {
    this.errorCount = 0;
    this.lastErrorTime = 0;
  }

  /**
   * الحصول على إحصائيات الأخطاء
   */
  getStats(): { errorCount: number; lastErrorTime: number } {
    return {
      errorCount: this.errorCount,
      lastErrorTime: this.lastErrorTime
    };
  }
}

/**
 * دالة مساعدة لمعالجة أخطاء Response
 */
export function handleResponseError(error: any, context: string = ''): Response | null {
  const handler = StreamErrorHandler.getInstance();
  
  if (!handler.handleStreamError(error, context)) {
    // إنشاء response fallback
    return new Response(
      JSON.stringify({ 
        error: 'Service temporarily unavailable',
        message: 'يرجى المحاولة مرة أخرى لاحقاً' 
      }),
      {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': '60'
        }
      }
    );
  }

  return null; // لا حاجة لـ fallback
}

/**
 * دالة لتحسين Stream handling
 */
export function createSafeStream<T>(
  streamFactory: () => ReadableStream<T>,
  fallbackData: T
): ReadableStream<T> {
  try {
    return streamFactory();
  } catch (error) {
    const handler = StreamErrorHandler.getInstance();
    handler.handleStreamError(error, 'createSafeStream');
    
    // إنشاء stream fallback
    return new ReadableStream({
      start(controller) {
        controller.enqueue(fallbackData);
        controller.close();
      }
    });
  }
}

/**
 * دالة لمعالجة أخطاء Fetch مع إعادة المحاولة
 */
export async function safeFetch(
  url: string, 
  options: RequestInit = {},
  retries: number = 3
): Promise<Response> {
  const handler = StreamErrorHandler.getInstance();
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, {
        ...options,
        signal: AbortSignal.timeout(10000) // 10 ثوان timeout
      });
      
      return response;
    } catch (error: any) {
      if (attempt === retries) {
        handler.handleStreamError(error, `safeFetch (final attempt)`);
        throw error;
      }
      
      handler.handleStreamError(error, `safeFetch (attempt ${attempt})`);
      
      // انتظار قبل إعادة المحاولة
      await new Promise(resolve => 
        setTimeout(resolve, Math.min(1000 * Math.pow(2, attempt - 1), 5000))
      );
    }
  }
  
  throw new Error('All fetch attempts failed');
}

/**
 * دالة لتنظيف الموارد
 */
export function cleanupStreamResources(): void {
  const handler = StreamErrorHandler.getInstance();
  handler.reset();
  
  // تنظيف إضافي إذا لزم الأمر
  if (typeof window !== 'undefined') {
    // مسح أي event listeners متعلقة بـ streams
    window.removeEventListener('beforeunload', cleanupStreamResources);
  }
}

// تسجيل cleanup عند إغلاق الصفحة
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanupStreamResources);
}
