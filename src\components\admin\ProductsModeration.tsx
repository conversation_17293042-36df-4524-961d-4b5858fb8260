'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useLocale } from '@/hooks/use-locale';
import { 
  Package, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Eye,
  Star,
  Filter,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrls: string[];
  merchantName: string;
  merchantId: string;
  category: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  moderationNotes?: string;
  qualityScore?: number;
  reportCount?: number;
}

export function ProductsModeration() {
  const { t } = useLocale();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');

  // بيانات تجريبية للمنتجات
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'هاتف ذكي متطور',
      description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
      price: 2500,
      imageUrls: ['/api/placeholder/200/200'],
      merchantName: 'متجر التقنية',
      merchantId: 'merchant1',
      category: 'الإلكترونيات',
      status: 'pending',
      submittedAt: new Date('2024-01-15'),
      qualityScore: 85,
      reportCount: 0
    },
    {
      id: '2',
      name: 'قميص قطني أنيق',
      description: 'قميص قطني عالي الجودة مناسب للمناسبات',
      price: 150,
      imageUrls: ['/api/placeholder/200/200'],
      merchantName: 'متجر الأناقة',
      merchantId: 'merchant2',
      category: 'الأزياء',
      status: 'approved',
      submittedAt: new Date('2024-01-14'),
      reviewedAt: new Date('2024-01-15'),
      reviewedBy: 'admin1',
      qualityScore: 92
    },
    {
      id: '3',
      name: 'منتج مشكوك فيه',
      description: 'وصف غير واضح للمنتج',
      price: 50,
      imageUrls: ['/api/placeholder/200/200'],
      merchantName: 'متجر غير موثوق',
      merchantId: 'merchant3',
      category: 'متنوع',
      status: 'rejected',
      submittedAt: new Date('2024-01-13'),
      reviewedAt: new Date('2024-01-14'),
      reviewedBy: 'admin1',
      moderationNotes: 'صور غير واضحة ووصف غير كافي',
      qualityScore: 35,
      reportCount: 3
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />في الانتظار</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />معتمد</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />مرفوض</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getQualityScoreColor = (score?: number) => {
    if (!score) return 'text-gray-400';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.merchantName.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || product.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const pendingProducts = filteredProducts.filter(p => p.status === 'pending');
  const approvedProducts = filteredProducts.filter(p => p.status === 'approved');
  const rejectedProducts = filteredProducts.filter(p => p.status === 'rejected');
  const reportedProducts = filteredProducts.filter(p => (p.reportCount || 0) > 0);

  const renderProductCard = (product: Product) => (
    <Card key={product.id} className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex gap-4">
          {/* صورة المنتج */}
          <div className="flex-shrink-0">
            <img
              src={product.imageUrls[0]}
              alt={product.name}
              className="w-20 h-20 object-cover rounded-lg"
            />
          </div>

          {/* معلومات المنتج */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="font-medium text-gray-900 truncate">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-600 truncate">
                  {product.description}
                </p>
              </div>
              {getStatusBadge(product.status)}
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
              <span>{product.price.toLocaleString('ar-SA')} ر.س</span>
              <span>•</span>
              <span>{product.merchantName}</span>
              <span>•</span>
              <span>{product.category}</span>
            </div>

            {/* معلومات إضافية */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>
                  تم الإرسال: {product.submittedAt.toLocaleDateString('ar-SA')}
                </span>
                
                {product.qualityScore && (
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    <span className={getQualityScoreColor(product.qualityScore)}>
                      {product.qualityScore}%
                    </span>
                  </div>
                )}
                
                {product.reportCount && product.reportCount > 0 && (
                  <div className="flex items-center gap-1 text-red-600">
                    <AlertTriangle className="h-3 w-3" />
                    <span>{product.reportCount} تقرير</span>
                  </div>
                )}
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-1" />
                  عرض
                </Button>
                
                {product.status === 'pending' && (
                  <>
                    <Button variant="outline" size="sm" className="text-green-600">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      اعتماد
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600">
                      <XCircle className="h-4 w-4 mr-1" />
                      رفض
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* ملاحظات المراجعة */}
            {product.moderationNotes && (
              <div className="mt-3 p-2 bg-gray-50 rounded text-sm text-gray-600">
                <strong>ملاحظات المراجعة:</strong> {product.moderationNotes}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">في الانتظار</p>
                <p className="text-xl font-bold">{pendingProducts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">معتمد</p>
                <p className="text-xl font-bold">{approvedProducts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">مرفوض</p>
                <p className="text-xl font-bold">{rejectedProducts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">مبلغ عنها</p>
                <p className="text-xl font-bold">{reportedProducts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في المنتجات أو التجار..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="تصفية بالحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
                <SelectItem value="approved">معتمد</SelectItem>
                <SelectItem value="rejected">مرفوض</SelectItem>
              </SelectContent>
            </Select>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="تصفية بالفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                <SelectItem value="الإلكترونيات">الإلكترونيات</SelectItem>
                <SelectItem value="الأزياء">الأزياء</SelectItem>
                <SelectItem value="المنزل والحديقة">المنزل والحديقة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* التبويبات */}
      <Tabs defaultValue="pending">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pending" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            في الانتظار ({pendingProducts.length})
          </TabsTrigger>
          <TabsTrigger value="approved" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            معتمد ({approvedProducts.length})
          </TabsTrigger>
          <TabsTrigger value="rejected" className="flex items-center gap-2">
            <XCircle className="h-4 w-4" />
            مرفوض ({rejectedProducts.length})
          </TabsTrigger>
          <TabsTrigger value="reported" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            مبلغ عنها ({reportedProducts.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          {pendingProducts.map(renderProductCard)}
        </TabsContent>

        <TabsContent value="approved" className="space-y-4">
          {approvedProducts.map(renderProductCard)}
        </TabsContent>

        <TabsContent value="rejected" className="space-y-4">
          {rejectedProducts.map(renderProductCard)}
        </TabsContent>

        <TabsContent value="reported" className="space-y-4">
          {reportedProducts.map(renderProductCard)}
        </TabsContent>
      </Tabs>
    </div>
  );
}
