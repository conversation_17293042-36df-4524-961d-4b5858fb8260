'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Download,
  Eye,
  Clock,
  CheckCircle
} from 'lucide-react';
import { useOrders } from '@/hooks/useOrders';
import { OrderDocument } from '@/types';
import { formatCurrency } from '@/lib/utils';

interface EarningsStats {
  totalEarnings: number;
  thisMonthEarnings: number;
  thisWeekEarnings: number;
  todayEarnings: number;
  totalDeliveries: number;
  averageEarningPerDelivery: number;
  pendingPayouts: number;
  completedPayouts: number;
}

export default function RepresentativeEarningsPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<EarningsStats>({
    totalEarnings: 0,
    thisMonthEarnings: 0,
    thisWeekEarnings: 0,
    todayEarnings: 0,
    totalDeliveries: 0,
    averageEarningPerDelivery: 0,
    pendingPayouts: 0,
    completedPayouts: 0
  });

  // جلب طلبات المندوب المكتملة
  const { orders: completedOrders, loading } = useOrders({ 
    representativeUid: user?.uid,
    status: 'delivered'
  });

  // حساب الإحصائيات
  useEffect(() => {
    if (!completedOrders.length) return;

    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // افتراض رسوم التوصيل = 15 ريال لكل طلب
    // وعمولة المندوب = 90% من رسوم التوصيل (10% للتطبيق)
    const DELIVERY_FEE = 15;
    const REPRESENTATIVE_COMMISSION = 0.9; // 90%

    const totalEarnings = completedOrders.length * DELIVERY_FEE * REPRESENTATIVE_COMMISSION;
    
    const thisMonthOrders = completedOrders.filter(order => 
      order.deliveredAt && order.deliveredAt.toDate() >= startOfMonth
    );
    const thisMonthEarnings = thisMonthOrders.length * DELIVERY_FEE * REPRESENTATIVE_COMMISSION;

    const thisWeekOrders = completedOrders.filter(order => 
      order.deliveredAt && order.deliveredAt.toDate() >= startOfWeek
    );
    const thisWeekEarnings = thisWeekOrders.length * DELIVERY_FEE * REPRESENTATIVE_COMMISSION;

    const todayOrders = completedOrders.filter(order => 
      order.deliveredAt && order.deliveredAt.toDate() >= startOfDay
    );
    const todayEarnings = todayOrders.length * DELIVERY_FEE * REPRESENTATIVE_COMMISSION;

    setStats({
      totalEarnings,
      thisMonthEarnings,
      thisWeekEarnings,
      todayEarnings,
      totalDeliveries: completedOrders.length,
      averageEarningPerDelivery: DELIVERY_FEE * REPRESENTATIVE_COMMISSION,
      pendingPayouts: thisWeekEarnings, // افتراض أن الدفعات الأسبوعية معلقة
      completedPayouts: totalEarnings - thisWeekEarnings
    });
  }, [completedOrders]);

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    trend, 
    trendValue 
  }: { 
    title: string; 
    value: string; 
    icon: any; 
    trend?: 'up' | 'down'; 
    trendValue?: string;
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {trend && trendValue && (
              <div className={`flex items-center mt-1 text-sm ${
                trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp className={`h-4 w-4 mr-1 ${
                  trend === 'down' ? 'rotate-180' : ''
                }`} />
                {trendValue}
              </div>
            )}
          </div>
          <Icon className="h-8 w-8 text-muted-foreground" />
        </div>
      </CardContent>
    </Card>
  );

  const EarningsHistoryItem = ({ order }: { order: OrderDocument }) => (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center gap-3">
        <div className="p-2 bg-green-100 rounded-full">
          <CheckCircle className="h-4 w-4 text-green-600" />
        </div>
        <div>
          <p className="font-medium">
            {t('orders.orderNumber', { number: order.orderNumber })}
          </p>
          <p className="text-sm text-muted-foreground">
            {order.deliveredAt?.toDate().toLocaleDateString('ar-SA')} - 
            {order.deliveredAt?.toDate().toLocaleTimeString('ar-SA', { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </p>
        </div>
      </div>
      <div className="text-right">
        <p className="font-medium text-green-600">
          +{formatCurrency(13.5)} {/* 90% من 15 ريال */}
        </p>
        <p className="text-sm text-muted-foreground">
          {t('representative.earnings.deliveryFee')}
        </p>
      </div>
    </div>
  );

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{t('representative.earnings.title')}</h1>
        <p className="text-muted-foreground">
          {t('representative.earnings.description')}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">
            {t('representative.earnings.overview')}
          </TabsTrigger>
          <TabsTrigger value="history">
            {t('representative.earnings.history')}
          </TabsTrigger>
          <TabsTrigger value="payouts">
            {t('representative.earnings.payouts')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              title={t('representative.earnings.totalEarnings')}
              value={formatCurrency(stats.totalEarnings)}
              icon={DollarSign}
            />
            <StatCard
              title={t('representative.earnings.thisMonth')}
              value={formatCurrency(stats.thisMonthEarnings)}
              icon={Calendar}
              trend="up"
              trendValue="+12%"
            />
            <StatCard
              title={t('representative.earnings.thisWeek')}
              value={formatCurrency(stats.thisWeekEarnings)}
              icon={TrendingUp}
            />
            <StatCard
              title={t('representative.earnings.today')}
              value={formatCurrency(stats.todayEarnings)}
              icon={Clock}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('representative.earnings.deliveryStats')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {t('representative.earnings.totalDeliveries')}
                  </span>
                  <span className="font-medium">{stats.totalDeliveries}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {t('representative.earnings.averagePerDelivery')}
                  </span>
                  <span className="font-medium">
                    {formatCurrency(stats.averageEarningPerDelivery)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {t('representative.earnings.commissionRate')}
                  </span>
                  <span className="font-medium">90%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('representative.earnings.payoutInfo')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {t('representative.earnings.pendingPayouts')}
                  </span>
                  <span className="font-medium text-orange-600">
                    {formatCurrency(stats.pendingPayouts)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {t('representative.earnings.completedPayouts')}
                  </span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(stats.completedPayouts)}
                  </span>
                </div>
                <div className="pt-2">
                  <p className="text-sm text-muted-foreground">
                    {t('representative.earnings.nextPayoutDate')}
                  </p>
                  <p className="font-medium">
                    {new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">
                {t('representative.earnings.earningsHistory')}
              </h2>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                {t('representative.earnings.downloadReport')}
              </Button>
            </div>
            
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
              </div>
            ) : completedOrders.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {t('representative.earnings.noEarningsYet')}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {completedOrders
                  .sort((a, b) => (b.deliveredAt?.toMillis() || 0) - (a.deliveredAt?.toMillis() || 0))
                  .map(order => (
                    <EarningsHistoryItem key={order.id} order={order} />
                  ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="payouts" className="mt-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">
              {t('representative.earnings.payoutHistory')}
            </h2>
            
            <Card>
              <CardContent className="text-center py-8">
                <Eye className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {t('representative.earnings.payoutHistoryComingSoon')}
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  {t('representative.earnings.payoutSchedule')}
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
