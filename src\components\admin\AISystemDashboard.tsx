// src/components/admin/AISystemDashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Brain, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Users,
  Truck,
  BarChart3,
  Activity,
  Zap,
  Shield,
  RefreshCw
} from 'lucide-react';

interface SystemMetrics {
  totalProcessed: number;
  autoApproved: number;
  autoRejected: number;
  manualReview: number;
  averageConfidence: number;
  averageProcessingTime: number;
  accuracyRate: number;
  errorRate: number;
  autoApprovalRate: number;
  manualReviewRate: number;
}

interface SystemAlert {
  type: 'critical' | 'warning' | 'info';
  message: string;
  description: string;
}

interface AISystemDashboardProps {
  className?: string;
}

export function AISystemDashboard({ className = '' }: AISystemDashboardProps) {
  const [metrics, setMetrics] = useState<{
    systemStats: SystemMetrics;
    merchantStats: SystemMetrics | null;
    representativeStats: SystemMetrics | null;
    alerts: SystemAlert[];
    lastUpdated: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [selectedType, setSelectedType] = useState('all');

  useEffect(() => {
    fetchMetrics();
  }, [selectedPeriod, selectedType]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/ai/system-metrics?period=${selectedPeriod}&type=${selectedType}`);
      
      if (!response.ok) {
        throw new Error('فشل في جلب الإحصائيات');
      }

      const data = await response.json();
      setMetrics(data);
    } catch (error) {
      console.error('خطأ في جلب إحصائيات النظام:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getAlertVariant = (type: string): "default" | "destructive" => {
    return type === 'critical' ? 'destructive' : 'default';
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center space-y-4">
            <Brain className="h-12 w-12 animate-pulse text-purple-500 mx-auto" />
            <p className="text-muted-foreground">جاري تحميل إحصائيات النظام الذكي...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertTitle>خطأ في تحميل البيانات</AlertTitle>
          <AlertDescription>
            فشل في تحميل إحصائيات النظام الذكي. يرجى المحاولة مرة أخرى.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`} data-testid="ai-system-dashboard">
      {/* العنوان والتحكم */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-500" />
            لوحة مراقبة النظام الذكي
          </h2>
          <p className="text-muted-foreground">
            آخر تحديث: {new Date(metrics.lastUpdated).toLocaleString('ar-SA')}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchMetrics}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {/* فلاتر الفترة والنوع */}
      <div className="flex flex-wrap gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">الفترة الزمنية:</label>
          <div className="flex gap-2">
            {[
              { value: 'today', label: 'اليوم' },
              { value: 'week', label: 'الأسبوع' },
              { value: 'month', label: 'الشهر' },
              { value: 'all', label: 'الكل' }
            ].map(period => (
              <Button
                key={period.value}
                variant={selectedPeriod === period.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period.value)}
              >
                {period.label}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">النوع:</label>
          <div className="flex gap-2">
            {[
              { value: 'all', label: 'الكل', icon: Activity },
              { value: 'merchants', label: 'التجار', icon: Users },
              { value: 'representatives', label: 'المندوبين', icon: Truck }
            ].map(type => (
              <Button
                key={type.value}
                variant={selectedType === type.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedType(type.value)}
              >
                <type.icon className="h-4 w-4 mr-1" />
                {type.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* التنبيهات */}
      {metrics.alerts.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">تنبيهات النظام</h3>
          {metrics.alerts.map((alert, index) => (
            <Alert key={index} variant={getAlertVariant(alert.type)}>
              {getAlertIcon(alert.type)}
              <AlertTitle>{alert.message}</AlertTitle>
              <AlertDescription>{alert.description}</AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" data-testid="ai-stats">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المعالج</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.systemStats.totalProcessed}</div>
            <p className="text-xs text-muted-foreground">
              طلب تم معالجته
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الموافقة التلقائية</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{metrics.systemStats.autoApproved}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.systemStats.autoApprovalRate.toFixed(1)}% من الإجمالي
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">دقة النظام</CardTitle>
            <Shield className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {metrics.systemStats.accuracyRate.toFixed(1)}%
            </div>
            <Progress value={metrics.systemStats.accuracyRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">سرعة المعالجة</CardTitle>
            <Zap className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.systemStats.averageProcessingTime}s</div>
            <p className="text-xs text-muted-foreground">
              متوسط وقت المعالجة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* تفاصيل حسب النوع */}
      <Tabs defaultValue="overview" className="space-y-4" data-testid="ai-tabs">
        <TabsList>
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          {metrics.merchantStats && <TabsTrigger value="merchants">التجار</TabsTrigger>}
          {metrics.representativeStats && <TabsTrigger value="representatives">المندوبين</TabsTrigger>}
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">توزيع القرارات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">موافقة تلقائية</span>
                  <Badge className="bg-green-100 text-green-800">
                    {metrics.systemStats.autoApproved}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">رفض تلقائي</span>
                  <Badge className="bg-red-100 text-red-800">
                    {metrics.systemStats.autoRejected}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">مراجعة يدوية</span>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {metrics.systemStats.manualReview}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">مؤشرات الجودة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>دقة النظام</span>
                    <span>{metrics.systemStats.accuracyRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.systemStats.accuracyRate} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>متوسط الثقة</span>
                    <span>{metrics.systemStats.averageConfidence.toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.systemStats.averageConfidence} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>معدل الخطأ</span>
                    <span>{metrics.systemStats.errorRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.systemStats.errorRate} className="bg-red-100" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">الأداء</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">وقت المعالجة</span>
                  <span className="font-medium">{metrics.systemStats.averageProcessingTime}s</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">معدل الموافقة التلقائية</span>
                  <span className="font-medium">{metrics.systemStats.autoApprovalRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">معدل المراجعة اليدوية</span>
                  <span className="font-medium">{metrics.systemStats.manualReviewRate.toFixed(1)}%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {metrics.merchantStats && (
          <TabsContent value="merchants" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  إحصائيات التجار
                </CardTitle>
                <CardDescription>
                  أداء النظام الذكي في معالجة طلبات التجار
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.merchantStats.totalProcessed}</div>
                    <div className="text-sm text-muted-foreground">إجمالي المعالج</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{metrics.merchantStats.autoApproved}</div>
                    <div className="text-sm text-muted-foreground">موافقة تلقائية</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.merchantStats.averageConfidence.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">متوسط الثقة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.merchantStats.accuracyRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">دقة النظام</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {metrics.representativeStats && (
          <TabsContent value="representatives" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  إحصائيات المندوبين
                </CardTitle>
                <CardDescription>
                  أداء النظام الذكي في معالجة طلبات المندوبين
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.representativeStats.totalProcessed}</div>
                    <div className="text-sm text-muted-foreground">إجمالي المعالج</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{metrics.representativeStats.autoApproved}</div>
                    <div className="text-sm text-muted-foreground">موافقة تلقائية</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.representativeStats.averageConfidence.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">متوسط الثقة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.representativeStats.accuracyRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">دقة النظام</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
