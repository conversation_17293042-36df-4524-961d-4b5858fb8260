import { formatPrice, formatDate, formatDistance, formatPhoneNumber } from './formatters'

describe('Formatters Utils', () => {
  describe('formatPrice', () => {
    it('ينسق الأسعار بشكل صحيح', () => {
      expect(formatPrice(25.99)).to.equal('25.99 ر.س')
      expect(formatPrice(100)).to.equal('100.00 ر.س')
      expect(formatPrice(0)).to.equal('0.00 ر.س')
    })

    it('يتعامل مع الأرقام الكبيرة', () => {
      expect(formatPrice(1000)).to.equal('1,000.00 ر.س')
      expect(formatPrice(1234567.89)).to.equal('1,234,567.89 ر.س')
    })

    it('يتعامل مع القيم غير الصحيحة', () => {
      expect(formatPrice(null)).to.equal('0.00 ر.س')
      expect(formatPrice(undefined)).to.equal('0.00 ر.س')
      expect(formatPrice(NaN)).to.equal('0.00 ر.س')
    })
  })

  describe('formatDate', () => {
    it('ينسق التواريخ بشكل صحيح', () => {
      const date = new Date('2024-01-15T10:30:00')
      const formatted = formatDate(date)
      expect(formatted).to.include('2024')
      expect(formatted).to.include('يناير')
    })

    it('يتعامل مع تنسيقات مختلفة', () => {
      const date = new Date('2024-01-15T10:30:00')
      const shortFormat = formatDate(date, 'short')
      const longFormat = formatDate(date, 'long')
      
      expect(shortFormat).to.not.equal(longFormat)
    })

    it('يتعامل مع القيم غير الصحيحة', () => {
      expect(formatDate(null)).to.equal('تاريخ غير صحيح')
      expect(formatDate(undefined)).to.equal('تاريخ غير صحيح')
      expect(formatDate('invalid')).to.equal('تاريخ غير صحيح')
    })
  })

  describe('formatDistance', () => {
    it('ينسق المسافات بشكل صحيح', () => {
      expect(formatDistance(500)).to.equal('500 م')
      expect(formatDistance(1500)).to.equal('1.5 كم')
      expect(formatDistance(2000)).to.equal('2 كم')
    })

    it('يتعامل مع المسافات الصغيرة', () => {
      expect(formatDistance(50)).to.equal('50 م')
      expect(formatDistance(999)).to.equal('999 م')
    })

    it('يتعامل مع القيم غير الصحيحة', () => {
      expect(formatDistance(null)).to.equal('0 م')
      expect(formatDistance(undefined)).to.equal('0 م')
      expect(formatDistance(-100)).to.equal('0 م')
    })
  })

  describe('formatPhoneNumber', () => {
    it('ينسق أرقام الهاتف السعودية بشكل صحيح', () => {
      expect(formatPhoneNumber('0501234567')).to.equal('+966 50 123 4567')
      expect(formatPhoneNumber('966501234567')).to.equal('+966 50 123 4567')
      expect(formatPhoneNumber('+966501234567')).to.equal('+966 50 123 4567')
    })

    it('يتعامل مع تنسيقات مختلفة', () => {
      expect(formatPhoneNumber('************')).to.equal('+966 50 123 4567')
      expect(formatPhoneNumber('************')).to.equal('+966 50 123 4567')
      expect(formatPhoneNumber('(*************')).to.equal('+966 50 123 4567')
    })

    it('يتعامل مع القيم غير الصحيحة', () => {
      expect(formatPhoneNumber('')).to.equal('')
      expect(formatPhoneNumber(null)).to.equal('')
      expect(formatPhoneNumber('123')).to.equal('123')
    })
  })
})
