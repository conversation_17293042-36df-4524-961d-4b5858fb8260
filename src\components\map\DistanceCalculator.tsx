"use client";

import { memo, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Route, 
  Navigation, 
  Clock, 
  Car, 
  Bike, 
  MapPin,
  ExternalLink,
  Calculator
} from 'lucide-react';
import { useLocale } from '@/hooks/use-locale';

interface Location {
  latitude: number;
  longitude: number;
}

interface DistanceCalculatorProps {
  userLocation: Location;
  storeLocation: Location;
  storeName: string;
}

interface DistanceInfo {
  distance: number; // in kilometers
  walkingTime: number; // in minutes
  drivingTime: number; // in minutes
  cyclingTime: number; // in minutes
}

const DistanceCalculator = memo<DistanceCalculatorProps>(({ 
  userLocation, 
  storeLocation, 
  storeName 
}) => {
  const { t } = useLocale();

  // Calculate distance using Haversine formula
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Calculate estimated travel times
  const distanceInfo: DistanceInfo = useMemo(() => {
    const distance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      storeLocation.latitude,
      storeLocation.longitude
    );

    // Estimated speeds (km/h)
    const walkingSpeed = 5; // 5 km/h
    const cyclingSpeed = 15; // 15 km/h  
    const drivingSpeed = 30; // 30 km/h (city driving with traffic)

    return {
      distance,
      walkingTime: Math.round((distance / walkingSpeed) * 60),
      cyclingTime: Math.round((distance / cyclingSpeed) * 60),
      drivingTime: Math.round((distance / drivingSpeed) * 60)
    };
  }, [userLocation, storeLocation]);

  // Format time display
  const formatTime = (minutes: number): string => {
    if (minutes < 1) return '< 1 دقيقة';
    if (minutes < 60) return `${minutes} دقيقة`;
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours} ساعة`;
    }
    
    return `${hours} ساعة و ${remainingMinutes} دقيقة`;
  };

  // Get directions URLs for different services
  const getDirectionsUrls = () => {
    const { latitude: userLat, longitude: userLng } = userLocation;
    const { latitude: storeLat, longitude: storeLng } = storeLocation;
    
    return {
      google: `https://www.google.com/maps/dir/${userLat},${userLng}/${storeLat},${storeLng}`,
      apple: `http://maps.apple.com/?saddr=${userLat},${userLng}&daddr=${storeLat},${storeLng}`,
      waze: `https://waze.com/ul?ll=${storeLat},${storeLng}&navigate=yes&from=${userLat},${userLng}`
    };
  };

  const directionsUrls = getDirectionsUrls();

  // Get distance category for styling
  const getDistanceCategory = (distance: number) => {
    if (distance < 1) return { label: 'قريب جداً', color: 'bg-green-500', textColor: 'text-green-700' };
    if (distance < 3) return { label: 'قريب', color: 'bg-blue-500', textColor: 'text-blue-700' };
    if (distance < 10) return { label: 'متوسط المسافة', color: 'bg-yellow-500', textColor: 'text-yellow-700' };
    return { label: 'بعيد', color: 'bg-red-500', textColor: 'text-red-700' };
  };

  const distanceCategory = getDistanceCategory(distanceInfo.distance);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="w-5 h-5" />
          {t('distanceCalculator')}
        </CardTitle>
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            {t('distanceTo')} <span className="font-medium">{storeName}</span>
          </span>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Distance Overview */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div>
            <div className="text-2xl font-bold">
              {distanceInfo.distance.toFixed(1)} {t('km')}
            </div>
            <Badge variant="secondary" className={`${distanceCategory.color} text-white mt-1`}>
              {distanceCategory.label}
            </Badge>
          </div>
          <Route className="w-8 h-8 text-muted-foreground" />
        </div>

        {/* Travel Time Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Walking */}
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="p-2 bg-green-100 rounded-full">
              <MapPin className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <div className="font-medium text-sm">{t('walking')}</div>
              <div className="text-xs text-muted-foreground">
                {formatTime(distanceInfo.walkingTime)}
              </div>
            </div>
          </div>

          {/* Cycling */}
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="p-2 bg-blue-100 rounded-full">
              <Bike className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <div className="font-medium text-sm">{t('cycling')}</div>
              <div className="text-xs text-muted-foreground">
                {formatTime(distanceInfo.cyclingTime)}
              </div>
            </div>
          </div>

          {/* Driving */}
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="p-2 bg-purple-100 rounded-full">
              <Car className="w-4 h-4 text-purple-600" />
            </div>
            <div>
              <div className="font-medium text-sm">{t('driving')}</div>
              <div className="text-xs text-muted-foreground">
                {formatTime(distanceInfo.drivingTime)}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Apps */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm flex items-center gap-2">
            <Navigation className="w-4 h-4" />
            {t('getDirections')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              asChild
              className="justify-start"
            >
              <a 
                href={directionsUrls.google}
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Google Maps
              </a>
            </Button>

            <Button 
              variant="outline" 
              size="sm" 
              asChild
              className="justify-start"
            >
              <a 
                href={directionsUrls.apple}
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Apple Maps
              </a>
            </Button>

            <Button 
              variant="outline" 
              size="sm" 
              asChild
              className="justify-start"
            >
              <a 
                href={directionsUrls.waze}
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Waze
              </a>
            </Button>
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-xs text-muted-foreground p-3 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-1 mb-1">
            <Clock className="w-3 h-3" />
            <span className="font-medium">{t('note')}:</span>
          </div>
          <p>{t('estimatedTimesNote')}</p>
        </div>
      </CardContent>
    </Card>
  );
});

DistanceCalculator.displayName = 'DistanceCalculator';

export default DistanceCalculator;
