'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useLocale } from '@/hooks/use-locale';
import { useUsersManagement } from '@/hooks/useUsersManagement';
import { UsersList } from './UsersList';
import { UserStatsCards } from './UserStatsCards';
import { BulkActionsBar } from './BulkActionsBar';
import { UserFilters } from './UserFilters';
import { NotificationComposer } from './NotificationComposer';
import { 
  Users, 
  Search, 
  Filter, 
  Download,
  Bell,
  RefreshCw,
  Plus
} from 'lucide-react';

export function UsersManagement() {
  const { t } = useLocale();
  const {
    users,
    loading,
    error,
    stats,
    filters,
    selectedUsers,
    searchQuery,
    setSearchQuery,
    setFilters,
    setSelectedUsers,
    refreshUsers,
    exportUsers
  } = useUsersManagement();

  const [showFilters, setShowFilters] = useState(false);
  const [showNotificationComposer, setShowNotificationComposer] = useState(false);

  return (
    <div className="space-y-6">
      {/* إحصائيات المستخدمين */}
      <UserStatsCards stats={stats} loading={loading} />

      {/* شريط البحث والأدوات */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t('allUsers')}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {t('filter')}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNotificationComposer(true)}
                className="flex items-center gap-2"
              >
                <Bell className="h-4 w-4" />
                {t('sendNotification')}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={exportUsers}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {t('exportUsers')}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={refreshUsers}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                {t('refresh')}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* شريط البحث */}
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t('searchByNameEmailPhone')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* المرشحات */}
          {showFilters && (
            <UserFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClose={() => setShowFilters(false)}
            />
          )}

          {/* شريط الإجراءات الجماعية */}
          {selectedUsers.length > 0 && (
            <BulkActionsBar
              selectedUsers={selectedUsers}
              onClearSelection={() => setSelectedUsers([])}
              onNotificationSent={() => setShowNotificationComposer(true)}
            />
          )}

          {/* قائمة المستخدمين */}
          <UsersList
            users={users}
            loading={loading}
            error={error}
            selectedUsers={selectedUsers}
            onSelectionChange={setSelectedUsers}
          />
        </CardContent>
      </Card>

      {/* محرر الإشعارات */}
      {showNotificationComposer && (
        <NotificationComposer
          selectedUsers={selectedUsers}
          onClose={() => setShowNotificationComposer(false)}
          onSent={() => {
            setShowNotificationComposer(false);
            // يمكن إضافة إشعار نجاح هنا
          }}
        />
      )}
    </div>
  );
}
