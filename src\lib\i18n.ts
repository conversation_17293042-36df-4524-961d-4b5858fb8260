import { i18nConfig as i18nConfigActual, type Locale as ConfigLocaleType } from './i18n-config';

export const i18n = i18nConfigActual;
export type Locale = ConfigLocaleType;

// استيراد الملف المدموج الجديد
const mergedTranslations = () => import('@/locales/translations.json').then((module) => module.default);

export const locales = {
  en: () => mergedTranslations().then((translations) => translations.en),
  ar: () => mergedTranslations().then((translations) => translations.ar),
};

// Cache للترجمات لتجنب التحميل المتكرر
const translationCache = new Map<Locale, Record<string, string>>();

// Promise cache لتجنب التحميل المتكرر
const loadingPromises = new Map<Locale, Promise<Record<string, string>>>();

export async function getTranslation(locale: Locale) {
  // التحقق من وجود الترجمات في الـ cache أولاً
  if (translationCache.has(locale)) {
    return translationCache.get(locale)!;
  }

  // التحقق من وجود promise تحميل جاري
  if (loadingPromises.has(locale)) {
    return await loadingPromises.get(locale)!;
  }

  // إنشاء promise تحميل جديد
  const loadingPromise = loadTranslationInternal(locale);
  loadingPromises.set(locale, loadingPromise);

  try {
    const result = await loadingPromise;
    return result;
  } finally {
    // إزالة promise من الذاكرة بعد الانتهاء
    loadingPromises.delete(locale);
  }
}

async function loadTranslationInternal(locale: Locale): Promise<Record<string, string>> {
  try {
    const translations = await locales[locale]();

    // حفظ الترجمات في الـ cache
    translationCache.set(locale, translations);

    return translations;
  } catch (error) {
    // تقليل رسائل الخطأ في وحدة التحكم
    if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ Translation loading warning for ${locale}:`, error);
    }

    // Fallback to English if Arabic fails
    if (locale !== 'en') {
      try {
        const fallbackTranslations = await locales.en();
        // حفظ fallback في cache مؤقتاً
        translationCache.set(locale, fallbackTranslations);
        return fallbackTranslations;
      } catch (fallbackError) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ Fallback translation loading failed');
        }
      }
    }

    // إرجاع object فارغ كحل أخير
    const emptyTranslations = {};
    translationCache.set(locale, emptyTranslations);
    return emptyTranslations;
  }
}

// وظيفة لمسح الـ cache (مفيدة في التطوير)
export function clearTranslationCache() {
  translationCache.clear();
  if (process.env.NODE_ENV === 'development') {
    console.log('Translation cache cleared');
  }
}
