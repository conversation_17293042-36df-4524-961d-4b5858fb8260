import type { SubscriptionPlan } from '@/types';

export const merchantPlans: SubscriptionPlan[] = [
  {
    id: 'merchant-basic',
    nameKey: 'plan_basic_name',
    priceDisplayKey: 'free',
    features: [
      { nameKey: 'basicStoreManagement', available: true },
      { nameKey: 'addManageProducts', available: true },
      { nameKey: 'emailSupport', available: true },
      { nameKey: 'simpleDashboard', available: true },
      { nameKey: 'basicStats', available: true },
      { nameKey: 'basicOrderManagement', available: true },
      { nameKey: 'standardStorePage', available: true },
      { nameKey: 'productImages3', available: true },
      { nameKey: 'basicInventory', available: true },
      { nameKey: 'simpleMonthlyReports', available: true },
      { nameKey: 'basicPaymentIntegration', available: true },
      { nameKey: 'basicRatingSystem', available: true },
      { nameKey: 'basicEmailNotifications', available: true },
      { nameKey: 'basicDataExport', available: true },
      { nameKey: 'manualDataBackup', available: true },
    ],
    commission: 10,
    commissionKey: 'commission',
    ctaKey: 'getStarted',
    maxProductImages: 3,
  },
  {
    id: 'merchant-premium',
    nameKey: 'plan_premium_name',
    priceDisplayKey: 'pricePerPeriod',
    priceValue: 99.99,
    currencyKey: 'SAR',
    periodKey: 'monthly',
    features: [
      { nameKey: 'basicStoreManagement', available: true }, // As per image
      { nameKey: 'unlimitedProducts', available: true },
      { nameKey: 'prioritySupport247', available: true },
      { nameKey: 'advancedDashboard', available: true },
      { nameKey: 'fullStoreCustomization', available: true },
      { nameKey: 'advancedSalesAnalytics', available: true },
      { nameKey: 'advancedInventorySystem', available: true },
      { nameKey: 'socialMediaIntegration', available: true },
      { nameKey: 'customerLoyaltySystem', available: true },
      { nameKey: 'detailedWeeklyReports', available: true },
      { nameKey: 'advancedMarketingTools', available: true },
      { nameKey: 'multipleShippingOptions', available: true },
      { nameKey: 'couponsDiscountsSystem', available: true },
      { nameKey: 'multiFormatReportExport', available: true },
      { nameKey: 'autoDailyBackup', available: true },
    ],
    commission: 5,
    commissionKey: 'commission',
    ctaKey: 'selectPlan',
    isPopular: true,
    maxProductImages: 10, 
  },
  {
    id: 'merchant-business',
    nameKey: 'plan_business_name',
    priceDisplayKey: 'pricePerPeriod',
    priceValue: 199.99,
    currencyKey: 'SAR',
    periodKey: 'monthly',
    features: [
      { nameKey: 'unlimitedProducts', available: true },
      { nameKey: 'vipSupport', available: true }, // Translation already covers "with dedicated account manager"
      { nameKey: 'smartAnalyticsPredictions', available: true },
      { nameKey: 'erpPosIntegration', available: true },
      { nameKey: 'crmSystem', available: true },
      { nameKey: 'fullSystemCustomization', available: true },
      { nameKey: 'processMarketingAutomation', available: true },
      { nameKey: 'realtimeAdvancedAnalytics', available: true },
      { nameKey: 'multiBranchManagement', available: true },
      { nameKey: 'advancedPerformanceMonitoring', available: true },
      { nameKey: 'advancedFraudProtection', available: true },
      { nameKey: 'accountingIntegration', available: true },
      { nameKey: 'autoHourlyBackup', available: true },
      { nameKey: 'businessConsultingServices', available: true },
      { nameKey: 'customTeamTraining', available: true },
    ],
    commission: 3,
    commissionKey: 'commission',
    ctaKey: 'selectPlan',
    maxProductImages: 15,
  },
];

export const representativePlans: SubscriptionPlan[] = [
  {
    id: 'representative-basic',
    nameKey: 'plan_representative_basic_name',
    priceDisplayKey: 'free',
    features: [
      { nameKey: 'unlimitedDeliveries', available: true },
      { nameKey: 'basicSupport', available: true },
      { nameKey: 'basicStats', available: true },
      { nameKey: 'standardPriority', available: true },
      { nameKey: 'basicEarningsReports', available: true },
      { nameKey: 'mobileApp', available: true },
      { nameKey: 'gpsTracking', available: true },
      { nameKey: 'customerRatings', available: true },
      { nameKey: 'basicNotifications', available: true },
      { nameKey: 'weeklyPayouts', available: true },
      { nameKey: 'commission10Percent', available: true },
      { nameKey: 'basicTraining', available: true },
    ],
    ctaKey: 'getStartedFree',
    commission: 10,
    commissionKey: 'commission',
  },
  {
    id: 'representative-premium',
    nameKey: 'plan_representative_premium_name',
    priceDisplayKey: 'pricePerPeriod',
    priceValue: 10,
    currencyKey: 'SAR',
    periodKey: 'monthly',
    features: [
      { nameKey: 'unlimitedDeliveries', available: true },
      { nameKey: 'priorityOrders', available: true },
      { nameKey: 'advancedStats', available: true },
      { nameKey: 'premiumSupport', available: true },
      { nameKey: 'detailedReports', available: true },
      { nameKey: 'mobileApp', available: true },
      { nameKey: 'advancedGpsTracking', available: true },
      { nameKey: 'customerRatings', available: true },
      { nameKey: 'advancedNotifications', available: true },
      { nameKey: 'dailyPayouts', available: true },
      { nameKey: 'commission5Percent', available: true },
      { nameKey: 'premiumTraining', available: true },
      { nameKey: 'bonusIncentives', available: true },
      { nameKey: 'flexibleSchedule', available: true },
    ],
    isPopular: true,
    ctaKey: 'upgradeToPremium',
    commission: 5,
    commissionKey: 'commission',
  },
];

export const customerPlans: SubscriptionPlan[] = [
  {
    id: 'customer-basic',
    nameKey: 'plan_customer_basic_name',
    priceDisplayKey: 'free',
    features: [
      { nameKey: 'freeAccountCreation', available: true },
      { nameKey: 'browseProducts', available: true },
      { nameKey: 'addToCartWishlist', available: true },
      { nameKey: 'basicSearch', available: true },
      { nameKey: 'basicOrderTracking', available: true },
      { nameKey: 'ePayment', available: true },
      { nameKey: 'ratingsReviews', available: true },
      { nameKey: 'basicEmailNotifications', available: true },
      { nameKey: 'saveAddresses', available: true },
      { nameKey: 'emailSupport', available: true },
      { nameKey: 'orderHistory', available: true },
      { nameKey: 'socialSharing', available: true },
      { nameKey: 'profileManagement', available: true },
      { nameKey: 'passwordRecovery', available: true },
      { nameKey: 'basicAlerts', available: true },
    ],
    ctaKey: 'getStarted',
  },
  {
    id: 'customer-premium',
    nameKey: 'plan_customer_premium_name',
    priceDisplayKey: 'pricePerPeriod',
    priceValue: 9.99,
    currencyKey: 'SAR',
    periodKey: 'monthly',
    features: [
      { nameKey: 'unlimitedFreeShipping', available: true },
      { nameKey: 'discount20all', available: true },
      { nameKey: 'prioritySupport247', available: true },
      { nameKey: 'priorityOrders', available: true },
      { nameKey: 'exclusiveOffers', available: true },
      { nameKey: 'doubleRewardPoints', available: true },
      { nameKey: 'freeOrderCancellation', available: true },
      { nameKey: 'earlyAccessSales', available: true },
      { nameKey: 'vipCustomerService', available: true },
      { nameKey: 'advancedOrderTracking', available: true },
      { nameKey: 'instantNotifications', available: true },
      { nameKey: 'monthlyVouchers', available: true },
      { nameKey: 'cashbackPurchases', available: true },
      { nameKey: 'premiumClubMembership', available: true },
    ],
    ctaKey: 'selectPlan',
    isPopular: true,
  },
];
