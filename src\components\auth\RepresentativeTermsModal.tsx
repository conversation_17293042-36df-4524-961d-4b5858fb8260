'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useLocale } from '@/hooks/use-locale';
import { getRepresentativeTerms } from '@/services/representativeTermsService';
import ReactMarkdown from 'react-markdown';

interface RepresentativeTermsModalProps {
  children: React.ReactNode;
  onAccept?: () => void;
  showAcceptButton?: boolean;
}

export default function RepresentativeTermsModal({ 
  children, 
  onAccept, 
  showAcceptButton = false 
}: RepresentativeTermsModalProps) {
  const { t, locale } = useLocale();
  const [isOpen, setIsOpen] = useState(false);

  const termsContent = getRepresentativeTerms(locale);

  const handleAccept = () => {
    if (onAccept) {
      onAccept();
    }
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            {t('representativeTermsTitle')}
          </DialogTitle>
          <DialogDescription>
            {t('pleaseReadTermsCarefully')}
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="h-[60vh] w-full rounded-md border p-4">
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <ReactMarkdown
              components={{
                h1: ({ children }) => (
                  <h1 className="text-2xl font-bold mb-4 text-primary">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-xl font-semibold mb-3 mt-6 text-primary">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-lg font-semibold mb-2 mt-4">{children}</h3>
                ),
                h4: ({ children }) => (
                  <h4 className="text-base font-semibold mb-2 mt-3">{children}</h4>
                ),
                p: ({ children }) => (
                  <p className="mb-3 leading-relaxed text-sm">{children}</p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside mb-3 space-y-1">{children}</ul>
                ),
                li: ({ children }) => (
                  <li className="text-sm leading-relaxed">{children}</li>
                ),
                strong: ({ children }) => (
                  <strong className="font-semibold text-primary">{children}</strong>
                ),
                hr: () => (
                  <hr className="my-6 border-border" />
                ),
              }}
            >
              {termsContent}
            </ReactMarkdown>
          </div>
        </ScrollArea>

        {showAcceptButton && (
          <div className="flex justify-end gap-3 mt-4">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              {t('cancel')}
            </Button>
            <Button onClick={handleAccept}>
              {t('acceptTerms')}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
