/// <reference types="cypress" />

describe('اختبار الترجمات الجديدة المضافة', () => {
  
  describe('اختبار ترجمات البحث المتقدم الجديدة', () => {
    it('يجب أن تعمل جميع ترجمات البحث المضافة حديثاً', () => {
      // اختبار البحث المتقدم بالإنجليزية
      cy.visit('/en/search');
      
      // التحقق من الترجمات الجديدة
      cy.contains('Advanced Search').should('be.visible');
      cy.get('input').should('have.attr', 'placeholder').and('match', /Search products and stores/i);
      
      // اختبار فلاتر الترتيب
      cy.get('select, [role="combobox"]').should('exist');
      
      // محاولة البحث الفارغ لاختبار رسائل "لا توجد نتائج"
      cy.get('input[placeholder*="Search"]').clear();
      cy.get('button').contains(/Search|البحث/i).click();
      
      // التحقق من رسائل عدم وجود نتائج
      cy.get('body').should('contain.text', 'No Search Results').or('contain.text', 'لا توجد نتائج');
    });

    it('يجب أن تعمل ترجمات خيارات الترتيب', () => {
      cy.visit('/en/search?q=test');
      
      // التحقق من وجود خيارات الترتيب المترجمة
      const sortOptions = [
        'Relevance',
        'Price: Low to High', 
        'Price: High to Low',
        'Highest Rated',
        'Newest'
      ];
      
      // البحث عن خيارات الترتيب في الصفحة
      sortOptions.forEach(option => {
        cy.get('body').should('contain.text', option);
      });
    });
  });

  describe('اختبار ترجمات الفئات الجديدة', () => {
    it('يجب أن تظهر أسماء الفئات الجديدة بالإنجليزية', () => {
      cy.visit('/en/categories');
      
      const newCategoryTranslations = [
        'Food & Beverages',
        'Fashion & Clothing', 
        'Electronics',
        'Home & Garden',
        'Beauty & Health',
        'Sports & Fitness',
        'Automotive',
        'Books & Media',
        'Arts & Crafts'
      ];
      
      newCategoryTranslations.forEach(category => {
        cy.get('body').should('contain.text', category);
      });
      
      // التحقق من النصوص الوصفية
      cy.contains('Browse by Categories').should('be.visible');
      cy.contains('Discover products in different categories').should('be.visible');
    });

    it('يجب أن تعمل ترجمات حالة عدم وجود منتجات في الفئة', () => {
      // زيارة فئة قد تكون فارغة
      cy.visit('/en/categories/empty-category', { failOnStatusCode: false });
      
      // التحقق من رسائل الفئة الفارغة
      cy.get('body').should('contain.text', 'No products in this category')
        .or('contain.text', 'Try selecting a different category');
    });
  });

  describe('اختبار ترجمات المتاجر المميزة', () => {
    it('يجب أن تظهر ترجمات المتاجر المميزة', () => {
      cy.visit('/en');
      
      // التحقق من قسم المتاجر المميزة
      cy.get('body').should('contain.text', 'Featured Stores')
        .or('contain.text', 'Discover top-rated stores');
      
      // التحقق من زر عرض جميع المتاجر
      cy.get('body').should('contain.text', 'View All Stores');
    });
  });

  describe('اختبار ترجمات حالات التحميل والأخطاء', () => {
    it('يجب أن تظهر رسائل التحميل مترجمة', () => {
      cy.visit('/en/search');
      
      // محاولة البحث لاختبار حالة التحميل
      cy.get('input[placeholder*="Search"]').type('test product');
      cy.get('button').contains('Search').click();
      
      // التحقق من رسائل التحميل أو المعالجة
      cy.get('body').should('contain.text', 'Processing your request')
        .or('contain.text', 'Loading')
        .or('contain.text', 'Search results');
    });

    it('يجب أن تظهر رسائل الخطأ مترجمة', () => {
      // محاولة الوصول لصفحة تتطلب مصادقة
      cy.visit('/en/merchant/dashboard', { failOnStatusCode: false });
      
      // التحقق من رسائل المصادقة
      cy.get('body').should('contain.text', 'Authentication')
        .or('contain.text', 'Login')
        .or('contain.text', 'Sign in');
    });
  });

  describe('اختبار ترجمات التسجيل والموافقة', () => {
    it('يجب أن تعمل ترجمات عملية التسجيل', () => {
      cy.visit('/en/auth/signup');
      
      // التحقق من ترجمات التسجيل
      cy.get('body').should('contain.text', 'Sign up')
        .or('contain.text', 'Create account')
        .or('contain.text', 'Registration');
    });

    it('يجب أن تعمل ترجمات حالة الموافقة', () => {
      // محاولة الوصول لصفحة موافقة التاجر
      cy.visit('/en/merchant/approval', { failOnStatusCode: false });
      
      // التحقق من ترجمات الموافقة
      cy.get('body').should('contain.text', 'Merchant Approval')
        .or('contain.text', 'Pending Approval')
        .or('contain.text', 'Application');
    });
  });

  describe('اختبار ترجمات صفحة المتاجر والموقع', () => {
    it('يجب أن تعمل ترجمات صفحة المتاجر', () => {
      cy.visit('/en/stores', { failOnStatusCode: false });

      // التحقق من ترجمات المتاجر
      cy.get('body').should('contain.text', 'Stores')
        .or('contain.text', 'Find nearby stores')
        .or('contain.text', 'Discover local stores');
    });

    it('يجب أن تعمل ترجمات طلب الموقع', () => {
      cy.visit('/en/stores');

      // التحقق من رسائل الموقع
      cy.get('body').should('contain.text', 'Enable Location')
        .or('contain.text', 'Detecting your location')
        .or('contain.text', 'Location Required');
    });
  });

  describe('اختبار ترجمات وحدات القياس والمسافات', () => {
    it('يجب أن تظهر وحدات القياس بالإنجليزية', () => {
      cy.visit('/en/stores');
      
      // التحقق من وحدات المسافة
      cy.get('body').should('contain.text', 'km')
        .or('contain.text', 'minutes')
        .or('contain.text', 'Walking');
    });
  });

  describe('اختبار ترجمات الحالات الخاصة', () => {
    it('يجب أن تعمل ترجمات حالة عدم وجود بيانات', () => {
      // زيارة صفحة قد تكون فارغة
      cy.visit('/en/products?category=empty', { failOnStatusCode: false });
      
      // التحقق من رسائل عدم وجود منتجات
      cy.get('body').should('contain.text', 'No products found')
        .or('contain.text', 'Try adjusting filters')
        .or('contain.text', 'No results');
    });

    it('يجب أن تعمل ترجمات التوجيهات للمستخدم', () => {
      cy.visit('/en/search');
      
      // التحقق من النصوص التوجيهية
      cy.get('body').should('contain.text', 'Start Searching')
        .or('contain.text', 'Enter search terms')
        .or('contain.text', 'Try different search terms');
    });
  });

  describe('اختبار تناسق الترجمات', () => {
    it('يجب أن تكون الترجمات متناسقة عبر الصفحات', () => {
      const commonTranslations = [
        'Search',
        'Products', 
        'Stores',
        'Categories',
        'Home'
      ];
      
      const pages = ['/en', '/en/search', '/en/products', '/en/stores'];
      
      pages.forEach(page => {
        cy.visit(page);
        commonTranslations.forEach(translation => {
          cy.get('body').should('contain.text', translation);
        });
      });
    });
  });
});
