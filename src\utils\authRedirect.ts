// src/utils/authRedirect.ts
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { User } from 'firebase/auth';
import type { UserDocument, StoreDocument } from '@/types';
import type { Locale } from '@/lib/i18n';

export interface RedirectResult {
  success: boolean;
  redirectPath?: string;
  error?: string;
}

/**
 * دالة موحدة لتحديد مسار التوجيه المناسب للمستخدم
 */
export async function determineUserRedirectPath(
  user: User,
  locale: Locale
): Promise<RedirectResult> {
  try {
    console.log('Determining redirect path for user:', user.uid);
    
    // التحقق من وجود مستند المستخدم
    const userDocRef = doc(db, "users", user.uid);
    const userDocSnap = await getDoc(userDocRef);

    if (!userDocSnap.exists()) {
      console.log('User document does not exist, redirecting to profile');
      return {
        success: true,
        redirectPath: `/${locale}/profile`
      };
    }

    const userData = userDocSnap.data() as UserDocument;
    console.log('User data:', { userType: userData.userType });

    // التوجيه حسب نوع المستخدم
    if (userData.userType === 'merchant') {
      // للتجار، نحتاج للتحقق من حالة الموافقة
      const storeDocRef = doc(db, "stores", user.uid);
      const storeDocSnap = await getDoc(storeDocRef);

      if (storeDocSnap.exists()) {
        const storeData = storeDocSnap.data() as StoreDocument;
        console.log('Store data:', { 
          approvalStatus: storeData.approvalStatus, 
          isActive: storeData.isActive 
        });

        // إذا كان التاجر معتمد ومفعل
        if (storeData.approvalStatus === 'approved' && storeData.isActive) {
          return {
            success: true,
            redirectPath: `/${locale}/merchant/dashboard`
          };
        } else {
          // إذا كان في انتظار الموافقة أو تم رفضه
          return {
            success: true,
            redirectPath: `/${locale}/merchant/pending-approval`
          };
        }
      } else {
        // لا يوجد مستند متجر، توجيه لصفحة الانتظار
        console.log('Store document does not exist, redirecting to pending approval');
        return {
          success: true,
          redirectPath: `/${locale}/merchant/pending-approval`
        };
      }
    } else if (userData.userType === 'customer') {
      return {
        success: true,
        redirectPath: `/${locale}/dashboard`
      };
    } else if (userData.userType === 'representative') {
      // للمندوبين، توجيه للوحة تحكم المندوب
      return {
        success: true,
        redirectPath: `/${locale}/representative/dashboard`
      };
    } else {
      // نوع مستخدم غير معروف، توجيه للملف الشخصي
      console.log('Unknown user type, redirecting to profile');
      return {
        success: true,
        redirectPath: `/${locale}/profile`
      };
    }
  } catch (error) {
    console.error("Error determining redirect path:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * دالة لتنفيذ التوجيه مع آلية fallback
 */
export function executeRedirectWithFallback(
  redirectPath: string,
  router: any,
  fallbackDelay: number = 10000
): () => void {
  console.log('Executing redirect to:', redirectPath);
  
  // محاولة التوجيه باستخدام Next.js router
  router.replace(redirectPath);
  
  // آلية fallback: استخدام window.location إذا فشل router
  const fallbackTimeoutId = setTimeout(() => {
    console.warn('Router redirect timeout, using window.location...');
    window.location.href = redirectPath;
  }, fallbackDelay);
  
  // إرجاع دالة لمسح timeout
  return () => clearTimeout(fallbackTimeoutId);
}

/**
 * دالة مساعدة لمسح بيانات المصادقة المؤقتة
 */
export function clearAuthCache(): void {
  if (typeof window === 'undefined') return;
  
  try {
    // مسح علامات التسجيل الجديد
    sessionStorage.removeItem('justSignedUp');
    
    // مسح بيانات Firebase المؤقتة
    const firebaseKeys = Object.keys(localStorage).filter(key => 
      key.startsWith('firebase:') || 
      key.includes('authUser') || 
      key.includes('firebase')
    );
    
    firebaseKeys.forEach(key => localStorage.removeItem(key));
    
    console.log('Auth cache cleared successfully');
  } catch (error) {
    console.error('Error clearing auth cache:', error);
  }
}
