import { useState, useEffect, useCallback } from 'react';
import { reviewService } from '@/services/reviewService';
import type { CustomerReview, ReviewReport, ReviewStats } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

interface UseReviewsOptions {
  targetId?: string;
  type?: 'store' | 'product';
  limit?: number;
  autoFetch?: boolean;
}

export function useReviews(options: UseReviewsOptions = {}) {
  const { targetId, type = 'product', limit = 10, autoFetch = true } = options;
  const { user } = useAuth();
  
  const [reviews, setReviews] = useState<CustomerReview[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // جلب المراجعات
  const fetchReviews = useCallback(async (reset = false) => {
    if (!targetId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const lastDoc = reset ? undefined : reviews[reviews.length - 1];
      let result;
      
      if (type === 'store') {
        result = await reviewService.getStoreReviews(targetId, limit, lastDoc);
      } else {
        result = await reviewService.getProductReviews(targetId, limit, lastDoc);
      }
      
      if (reset) {
        setReviews(result.reviews);
      } else {
        setReviews(prev => [...prev, ...result.reviews]);
      }
      
      setHasMore(result.hasMore);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في جلب المراجعات');
      console.error('Error fetching reviews:', err);
    } finally {
      setLoading(false);
    }
  }, [targetId, type, limit, reviews]);

  // جلب الإحصائيات
  const fetchStats = useCallback(async () => {
    if (!targetId) return;
    
    try {
      const reviewStats = await reviewService.calculateReviewStats(targetId, type);
      setStats(reviewStats);
    } catch (err) {
      console.error('Error fetching review stats:', err);
    }
  }, [targetId, type]);

  // إضافة مراجعة جديدة
  const addReview = useCallback(async (reviewData: {
    rating: number;
    comment: string;
    images?: string[];
  }) => {
    if (!user || !targetId) {
      toast.error('يجب تسجيل الدخول لإضافة مراجعة');
      return false;
    }

    setSubmitting(true);
    
    try {
      const newReviewData = {
        customerId: user.uid,
        customerName: user.displayName || 'مستخدم',
        customerAvatar: user.photoURL || undefined,
        [type === 'store' ? 'storeId' : 'productId']: targetId,
        rating: reviewData.rating,
        comment: reviewData.comment,
        images: reviewData.images || [],
        isVerified: false, // يمكن تحديثها بناءً على منطق التحقق
        helpfulCount: 0
      };

      await reviewService.addReview(newReviewData);
      
      // إعادة جلب المراجعات والإحصائيات
      await Promise.all([
        fetchReviews(true),
        fetchStats()
      ]);
      
      toast.success('تم إضافة المراجعة بنجاح');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'فشل في إضافة المراجعة';
      toast.error(errorMessage);
      console.error('Error adding review:', err);
      return false;
    } finally {
      setSubmitting(false);
    }
  }, [user, targetId, type, fetchReviews, fetchStats]);

  // الإبلاغ عن مراجعة
  const reportReview = useCallback(async (reviewId: string, reason: ReviewReport['reason'], description?: string) => {
    if (!user) {
      toast.error('يجب تسجيل الدخول للإبلاغ');
      return false;
    }

    try {
      await reviewService.reportReview({
        reviewId,
        reporterId: user.uid,
        reporterName: user.displayName || 'مستخدم',
        reason,
        description
      });
      
      toast.success('تم الإبلاغ عن المراجعة بنجاح');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'فشل في الإبلاغ عن المراجعة';
      toast.error(errorMessage);
      console.error('Error reporting review:', err);
      return false;
    }
  }, [user]);

  // تحديد المراجعة كمفيدة
  const markHelpful = useCallback(async (reviewId: string) => {
    try {
      await reviewService.markReviewHelpful(reviewId);
      
      // تحديث المراجعة محلياً
      setReviews(prev => prev.map(review => 
        review.id === reviewId 
          ? { ...review, helpfulCount: review.helpfulCount + 1 }
          : review
      ));
      
      toast.success('شكراً لك على تقييمك');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'فشل في تحديث المراجعة';
      toast.error(errorMessage);
      console.error('Error marking review helpful:', err);
      return false;
    }
  }, []);

  // حذف مراجعة
  const deleteReview = useCallback(async (reviewId: string) => {
    if (!user) {
      toast.error('يجب تسجيل الدخول لحذف المراجعة');
      return false;
    }

    try {
      await reviewService.deleteReview(reviewId, user.uid);
      
      // إزالة المراجعة محلياً
      setReviews(prev => prev.filter(review => review.id !== reviewId));
      
      // إعادة حساب الإحصائيات
      await fetchStats();
      
      toast.success('تم حذف المراجعة بنجاح');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'فشل في حذف المراجعة';
      toast.error(errorMessage);
      console.error('Error deleting review:', err);
      return false;
    }
  }, [user, fetchStats]);

  // تحميل المزيد من المراجعات
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchReviews(false);
    }
  }, [loading, hasMore, fetchReviews]);

  // إعادة تحميل المراجعات
  const refresh = useCallback(() => {
    fetchReviews(true);
    fetchStats();
  }, [fetchReviews, fetchStats]);

  // جلب البيانات تلقائياً عند التحميل
  useEffect(() => {
    if (autoFetch && targetId) {
      fetchReviews(true);
      fetchStats();
    }
  }, [autoFetch, targetId, fetchReviews, fetchStats]);

  return {
    // البيانات
    reviews,
    stats,
    loading,
    submitting,
    hasMore,
    error,
    
    // الوظائف
    addReview,
    reportReview,
    markHelpful,
    deleteReview,
    loadMore,
    refresh,
    
    // التحكم اليدوي
    fetchReviews: () => fetchReviews(true),
    fetchStats
  };
}

// Hook مخصص للتحقق من إمكانية إضافة مراجعة
export function useCanReview(targetId: string, type: 'store' | 'product') {
  const { user } = useAuth();
  const [canReview, setCanReview] = useState(false);
  const [hasExistingReview, setHasExistingReview] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkReviewEligibility = async () => {
      if (!user || !targetId) {
        setCanReview(false);
        setLoading(false);
        return;
      }

      try {
        // هنا يمكن إضافة منطق للتحقق من:
        // 1. هل المستخدم اشترى المنتج/تعامل مع المتجر؟
        // 2. هل له مراجعة سابقة؟
        // 3. هل مر وقت كافي على الشراء؟
        
        // للآن، نسمح بالمراجعة للجميع
        setCanReview(true);
        setHasExistingReview(false);
      } catch (err) {
        console.error('Error checking review eligibility:', err);
        setCanReview(false);
      } finally {
        setLoading(false);
      }
    };

    checkReviewEligibility();
  }, [user, targetId, type]);

  return {
    canReview,
    hasExistingReview,
    loading
  };
}
