#!/usr/bin/env node

/**
 * اختبار بسيط للتحقق من صحة الترجمات المضافة
 */

const fs = require('fs');
const path = require('path');

// مسارات ملفات الترجمة
const AR_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/en.json');

/**
 * قراءة ملف JSON
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    return null;
  }
}

/**
 * استخراج جميع المفاتيح من كائن متداخل
 */
function extractAllKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractAllKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

/**
 * التحقق من صحة JSON
 */
function testJsonValidity() {
  console.log('🧪 اختبار صحة ملفات JSON...\n');
  
  const arTranslations = readJsonFile(AR_TRANSLATIONS_PATH);
  const enTranslations = readJsonFile(EN_TRANSLATIONS_PATH);
  
  if (!arTranslations) {
    console.log('❌ ملف الترجمة العربية غير صحيح');
    return false;
  }
  
  if (!enTranslations) {
    console.log('❌ ملف الترجمة الإنجليزية غير صحيح');
    return false;
  }
  
  console.log('✅ ملفات JSON صحيحة التنسيق');
  return { arTranslations, enTranslations };
}

/**
 * اختبار الترجمات الجديدة المضافة
 */
function testNewTranslations(enTranslations) {
  console.log('\n🔍 اختبار الترجمات الجديدة المضافة...\n');
  
  const newTranslations = [
    // ترجمات البحث المتقدم
    'advancedSearch',
    'searchProductsAndStores',
    'searchResultsFor',
    'noSearchResults',
    'tryDifferentSearchTerms',
    'relevance',
    'priceLowToHigh',
    'priceHighToLow',
    'highestRated',
    
    // ترجمات الفئات
    'browseByCategories',
    'allCategories',
    'food',
    'fashion',
    'electronics',
    'homeAndGarden',
    'beautyAndHealth',
    
    // ترجمات المتاجر المميزة
    'featuredStores',
    'viewAllStores',
    
    // ترجمات الموافقة
    'merchantApproval',
    'approvalPending',
    'approvalApproved',
    'approvalRejected',
    
    // ترجمات الخريطة
    'interactiveMap',
    'findNearbyStores',
    'detectingLocation',
    'getDirections'
  ];
  
  let passedTests = 0;
  let failedTests = 0;
  
  newTranslations.forEach(key => {
    if (enTranslations.hasOwnProperty(key)) {
      const value = enTranslations[key];
      if (value && typeof value === 'string' && value.trim().length > 0) {
        console.log(`✅ ${key}: "${value}"`);
        passedTests++;
      } else {
        console.log(`❌ ${key}: قيمة فارغة أو غير صحيحة`);
        failedTests++;
      }
    } else {
      console.log(`❌ ${key}: مفتاح مفقود`);
      failedTests++;
    }
  });
  
  console.log(`\n📊 نتائج اختبار الترجمات الجديدة:`);
  console.log(`   ✅ نجح: ${passedTests}`);
  console.log(`   ❌ فشل: ${failedTests}`);
  console.log(`   📈 معدل النجاح: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);
  
  return failedTests === 0;
}

/**
 * اختبار ترجمات المندوبين المتداخلة
 */
function testRepresentativeTranslations(enTranslations) {
  console.log('\n🚚 اختبار ترجمات المندوبين...\n');
  
  if (!enTranslations.representative) {
    console.log('❌ كائن representative مفقود');
    return false;
  }
  
  const requiredSections = ['nav', 'dashboard', 'orders', 'earnings'];
  let allSectionsExist = true;
  
  requiredSections.forEach(section => {
    if (enTranslations.representative[section]) {
      console.log(`✅ representative.${section}: موجود`);
    } else {
      console.log(`❌ representative.${section}: مفقود`);
      allSectionsExist = false;
    }
  });
  
  // اختبار مفاتيح محددة
  const specificKeys = [
    'representative.nav.dashboard',
    'representative.dashboard.welcome',
    'representative.orders.acceptOrder',
    'representative.earnings.totalEarnings'
  ];
  
  specificKeys.forEach(keyPath => {
    const keys = keyPath.split('.');
    let current = enTranslations;
    let exists = true;
    
    for (const key of keys) {
      if (current && current[key]) {
        current = current[key];
      } else {
        exists = false;
        break;
      }
    }
    
    if (exists && typeof current === 'string') {
      console.log(`✅ ${keyPath}: "${current}"`);
    } else {
      console.log(`❌ ${keyPath}: مفقود أو غير صحيح`);
      allSectionsExist = false;
    }
  });
  
  return allSectionsExist;
}

/**
 * اختبار عدم وجود قيم فارغة
 */
function testEmptyValues(translations, language) {
  console.log(`\n🔍 اختبار القيم الفارغة في الترجمة ${language}...\n`);
  
  let emptyCount = 0;
  
  function checkEmpty(obj, path = '') {
    Object.keys(obj).forEach(key => {
      const currentPath = path ? `${path}.${key}` : key;
      const value = obj[key];
      
      if (typeof value === 'string') {
        if (!value.trim()) {
          console.log(`❌ قيمة فارغة في: ${currentPath}`);
          emptyCount++;
        }
      } else if (typeof value === 'object' && value !== null) {
        checkEmpty(value, currentPath);
      }
    });
  }
  
  checkEmpty(translations);
  
  if (emptyCount === 0) {
    console.log(`✅ لا توجد قيم فارغة في الترجمة ${language}`);
    return true;
  } else {
    console.log(`❌ وُجد ${emptyCount} قيمة فارغة في الترجمة ${language}`);
    return false;
  }
}

/**
 * اختبار تناسق أسماء الفئات
 */
function testCategoryConsistency(enTranslations) {
  console.log('\n🏷️ اختبار تناسق أسماء الفئات...\n');
  
  const categories = [
    'food',
    'fashion',
    'electronics',
    'homeAndGarden',
    'beautyAndHealth',
    'sportsAndFitness',
    'automotive',
    'booksAndMedia',
    'artsAndCrafts',
    'other'
  ];
  
  let allCategoriesExist = true;
  
  categories.forEach(category => {
    if (enTranslations[category]) {
      console.log(`✅ ${category}: "${enTranslations[category]}"`);
    } else {
      console.log(`❌ ${category}: مفقود`);
      allCategoriesExist = false;
    }
  });
  
  return allCategoriesExist;
}

/**
 * إحصائيات الترجمة
 */
function getTranslationStats(arTranslations, enTranslations) {
  console.log('\n📊 إحصائيات الترجمة...\n');
  
  const arKeys = extractAllKeys(arTranslations);
  const enKeys = extractAllKeys(enTranslations);
  
  console.log(`📈 إجمالي المفاتيح العربية: ${arKeys.length}`);
  console.log(`📈 إجمالي المفاتيح الإنجليزية: ${enKeys.length}`);
  console.log(`📈 الفرق: ${arKeys.length - enKeys.length} مفتاح`);
  console.log(`📈 نسبة التغطية: ${((enKeys.length / arKeys.length) * 100).toFixed(1)}%`);
  
  return {
    arabicKeys: arKeys.length,
    englishKeys: enKeys.length,
    coverage: ((enKeys.length / arKeys.length) * 100).toFixed(1)
  };
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء اختبار الترجمات المضافة...\n');
  console.log('=' .repeat(50));
  
  // اختبار صحة JSON
  const translations = testJsonValidity();
  if (!translations) {
    process.exit(1);
  }
  
  const { arTranslations, enTranslations } = translations;
  
  // تشغيل جميع الاختبارات
  const tests = [
    () => testNewTranslations(enTranslations),
    () => testRepresentativeTranslations(enTranslations),
    () => testEmptyValues(enTranslations, 'الإنجليزية'),
    () => testCategoryConsistency(enTranslations)
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  tests.forEach((test, index) => {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.log(`❌ خطأ في الاختبار ${index + 1}:`, error.message);
    }
  });
  
  // إحصائيات نهائية
  const stats = getTranslationStats(arTranslations, enTranslations);
  
  console.log('\n' + '=' .repeat(50));
  console.log('📋 ملخص نتائج الاختبار:');
  console.log(`   🧪 الاختبارات المكتملة: ${passedTests}/${totalTests}`);
  console.log(`   📊 معدل نجاح الاختبارات: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  console.log(`   📈 تغطية الترجمة: ${stats.coverage}%`);
  console.log(`   📝 المفاتيح الإنجليزية: ${stats.englishKeys}`);
  console.log(`   📝 المفاتيح العربية: ${stats.arabicKeys}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 جميع الاختبارات نجحت! الترجمات المضافة تعمل بشكل صحيح.');
    process.exit(0);
  } else {
    console.log(`\n⚠️  ${totalTests - passedTests} اختبار فشل. يرجى مراجعة الأخطاء أعلاه.`);
    process.exit(1);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  main();
}

module.exports = {
  testJsonValidity,
  testNewTranslations,
  testRepresentativeTranslations,
  extractAllKeys
};
