import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import IntelligentChatbotService from '@/services/intelligentChatbotService';
import IntelligentTicketingService from '@/services/intelligentTicketingService';

interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  intent?: string;
  confidence?: number;
  suggestions?: string[];
}

interface UseChatbotReturn {
  // حالة المحادثة
  messages: ChatMessage[];
  isTyping: boolean;
  sessionId: string;
  isConnected: boolean;
  
  // وظائف المحادثة
  sendMessage: (message: string) => Promise<void>;
  clearChat: () => void;
  endSession: (rating?: number) => Promise<void>;
  
  // وظائف التذاكر
  createTicketFromChat: (subject: string, description?: string) => Promise<string>;
  
  // إعدادات
  isEnabled: boolean;
  setIsEnabled: (enabled: boolean) => void;
}

/**
 * Hook مخصص لإدارة الشات بوت
 * يوفر واجهة سهلة للتفاعل مع نظام الشات بوت الذكي
 */
export const useChatbot = (): UseChatbotReturn => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [isEnabled, setIsEnabled] = useState(true);

  // رسالة الترحيب الافتراضية
  const welcomeMessage: ChatMessage = {
    id: 'welcome',
    text: 'مرحباً بك في مِخْلاة! 👋\nأنا مساعدك الذكي، كيف يمكنني مساعدتك اليوم؟',
    isBot: true,
    timestamp: new Date(),
    suggestions: [
      'كيف أتتبع طلبي؟',
      'ما هي طرق الدفع المتاحة؟',
      'كيف أسجل كتاجر؟',
      'كم يستغرق التوصيل؟'
    ]
  };

  // تهيئة الشات بوت
  const initializeChatbot = useCallback(async () => {
    try {
      console.log('🤖 تهيئة الشات بوت...');
      
      // إنشاء جلسة محادثة جديدة
      const newSessionId = await IntelligentChatbotService.createChatSession(user?.uid);
      setSessionId(newSessionId);
      setIsConnected(true);

      // إضافة رسالة الترحيب
      setMessages([welcomeMessage]);

      console.log('✅ تم تهيئة الشات بوت بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تهيئة الشات بوت:', error);
      setIsConnected(false);
    }
  }, [user?.uid]);

  // إرسال رسالة
  const sendMessage = useCallback(async (messageText: string) => {
    if (!messageText.trim() || isTyping || !isConnected) {
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageText.trim(),
      isBot: false,
      timestamp: new Date()
    };

    // إضافة رسالة المستخدم
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // معالجة الرسالة بواسطة الشات بوت
      const response = await IntelligentChatbotService.processMessage(
        messageText,
        sessionId,
        user?.uid
      );

      // محاكاة وقت الكتابة (1-3 ثواني)
      const typingDelay = 1000 + Math.random() * 2000;
      await new Promise(resolve => setTimeout(resolve, typingDelay));

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.response,
        isBot: true,
        timestamp: new Date(),
        intent: response.intent,
        confidence: response.confidence,
        suggestions: response.suggestions
      };

      setMessages(prev => [...prev, botMessage]);

      // إذا كانت الرسالة تحتاج تصعيد للدعم البشري
      if (response.requiresHuman) {
        setTimeout(() => {
          const escalationMessage: ChatMessage = {
            id: (Date.now() + 2).toString(),
            text: '🎫 تم تحويل محادثتك إلى فريق الدعم المختص. سيتواصل معك أحد المختصين قريباً عبر البريد الإلكتروني أو الهاتف.',
            isBot: true,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, escalationMessage]);
        }, 2000);
      }

    } catch (error) {
      console.error('❌ خطأ في معالجة الرسالة:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'أعتذر، حدث خطأ تقني مؤقت. يرجى المحاولة مرة أخرى أو التواصل مع الدعم مباشرة على الرقم: 920000000',
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  }, [sessionId, user?.uid, isTyping, isConnected]);

  // مسح المحادثة
  const clearChat = useCallback(() => {
    setMessages([welcomeMessage]);
  }, []);

  // إنهاء الجلسة
  const endSession = useCallback(async (satisfactionRating?: number) => {
    try {
      if (sessionId) {
        await IntelligentChatbotService.endChatSession(sessionId, satisfactionRating);
        console.log('✅ تم إنهاء جلسة المحادثة');
      }
      
      setMessages([]);
      setSessionId('');
      setIsConnected(false);
    } catch (error) {
      console.error('❌ خطأ في إنهاء الجلسة:', error);
    }
  }, [sessionId]);

  // إنشاء تذكرة من المحادثة
  const createTicketFromChat = useCallback(async (
    subject: string, 
    description?: string
  ): Promise<string> => {
    try {
      // جمع سياق المحادثة
      const chatContext = messages
        .filter(msg => !msg.isBot || msg.intent !== 'greeting')
        .map(msg => `${msg.isBot ? 'المساعد' : 'العميل'}: ${msg.text}`)
        .join('\n\n');

      const ticketDescription = description || 
        `تذكرة مُنشأة من محادثة الشات بوت\n\nسياق المحادثة:\n${chatContext}`;

      // إنشاء التذكرة
      const ticket = await IntelligentTicketingService.createTicket({
        userId: user?.uid,
        sessionId: sessionId,
        subject: subject,
        description: ticketDescription,
        source: 'chatbot',
        priority: 'medium'
      });

      // إضافة رسالة تأكيد
      const confirmationMessage: ChatMessage = {
        id: Date.now().toString(),
        text: `✅ تم إنشاء تذكرة دعم برقم: ${ticket.ticketNumber}\n\nسيتم التواصل معك قريباً لحل مشكلتك. يمكنك متابعة حالة التذكرة من خلال حسابك.`,
        isBot: true,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, confirmationMessage]);

      return ticket.ticketNumber;
    } catch (error) {
      console.error('❌ خطأ في إنشاء التذكرة:', error);
      throw new Error('فشل في إنشاء تذكرة الدعم');
    }
  }, [messages, sessionId, user?.uid]);

  // تهيئة الشات بوت عند التحميل
  useEffect(() => {
    if (isEnabled && !isConnected && !sessionId) {
      initializeChatbot();
    }
  }, [isEnabled, isConnected, sessionId, initializeChatbot]);

  // تنظيف الموارد عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (sessionId) {
        // إنهاء الجلسة بصمت عند إغلاق التطبيق
        IntelligentChatbotService.endChatSession(sessionId).catch(console.error);
      }
    };
  }, [sessionId]);

  return {
    // حالة المحادثة
    messages,
    isTyping,
    sessionId,
    isConnected,
    
    // وظائف المحادثة
    sendMessage,
    clearChat,
    endSession,
    
    // وظائف التذاكر
    createTicketFromChat,
    
    // إعدادات
    isEnabled,
    setIsEnabled
  };
};

export default useChatbot;
